# 校本作业库模块产品需求分析文档

## 1. 项目概述

### 1.1 项目背景
现有教学管理系统已具备基础的试卷录入和批阅功能，但缺乏校本资源的系统化管理能力。学校需要建立自主题库，实现优质教学资源的沉淀、审核、标签化管理和复用。

### 1.2 项目目标
在现有系统基础上叠加校本资源管理能力，构建"学校自主题库"闭环，提升教学资源利用效率和质量管控水平。

### 1.3 目标用户
- **教师**：日常教学中需要快速获取和上传优质试卷
- **备课组长**：负责学科资源审核和标签管理
- **学校管理者**：需要掌握全校教学资源建设情况

## 2. 功能需求分析

### 2.1 管理者数据看板模块

#### 2.1.1 核心功能
- **题库统计可视化**
  - 按学科、年级维度统计题库总量
  - 展示资源来源分布（教师上传/外部录入/审核通过率）
  - 高频使用标签TOP10排行
  - 作业量监控（关联现有批阅系统数据）

#### 2.1.2 权限控制
- 校级管理员：全校数据查看权限
- 学科组长：仅限本学科数据查看权限

#### 2.1.3 数据导出
- 支持PDF格式简报导出
- 题库健康度指标（审核积压率、标签覆盖率）

### 2.2 备课组长审核与标签管理模块

#### 2.2.1 审核流程管理
- **分层审核机制**
  - 教师提交新卷 → 备课组长初审 → 审核通过/退回修改
  - 审核列表显示：试卷标签、上传者、提交时间
  - 支持批量审核操作
  - 退回时支持批注功能
  - 退回后教师可以再编辑重新提交审核

#### 2.2.2 标签管理系统
- **预置基础标签库**（系统级，不可删除）
  - 同步练习、单元检测、专项练习、期中复习、期末复习、易错巩固、拓展拔高
- **自定义校本标签**
  - 组长可添加/删除/重命名标签
  - 支持多级分类（如：物理/力学/压强专项）
  - 学科隔离（物理组长不能编辑语文标签）

#### 2.2.3 标签绑定规则
- 审核入库时强制关联1个主标签
- 可选关联多个辅助标签
- 批量打标签功能

### 2.3 教师端编辑与组卷模块

#### 2.3.1 系统集成
- 保留原有套卷录入界面，列表处添加“提交到校本题库”按钮。
- 新增"校本题库"入口标签页
- 复用现有试卷预览组件

#### 2.3.2 核心功能
- **智能检索**
  - 按学科/年级/标签三级筛选
  - 快速定位历史优质试卷
- **试卷操作**
  - 预览、下载、编辑功能
  - 支持微调（修改题目/分值）
- **资源管理**
  - 个人"已提交/已审核"资源追踪
  - 收藏高频使用试卷
  - 按标签快捷调用历史卷

#### 2.3.3 试卷上传流程
- 教师上传新卷，依据原有解析和组卷模块录入编辑完成后点击“提交到校本题库”。
- 学科组长再待审核列表中进行审核，通过后强制选择至少一个主标签，然后入库。
- 入库的时候复制一份试卷到校本库中，同时这两份试卷建立一个关联关系。
- 如果不符合要求，可以退回试卷，审核人可以添加退回批注。
- 教师可以再编辑重新提交审核。

#### 知识点管理
- 与现有知识点管理模块集成
- 学校可以自己管理自己的知识点树
- 试卷可以绑定知识点

#### 教材版本管理
- 与现有教材版本管理模块集成
- 学校可以自己管理自己的教材版本
- 试卷可以绑定教材版本和章节

## 3. 非功能需求

### 3.1 性能要求
- 检索响应时间 < 2秒
- 文件上传支持最大50MB
- 并发用户数支持500+

### 3.2 可用性要求
- 系统可用性 ≥ 99.5%
- 界面操作简洁直观
- 移动端适配

### 3.3 安全性要求
- 角色权限严格控制
- 数据传输加密
- 操作日志记录

## 4. 技术实现方案

### 4.1 数据库设计
- **新增表结构**
  - `school_paper`（校本资源表）试卷数据依然存在原有的 `paper`表中，`school_paper`表只保存校本库特有的数据，如标签、审核状态等。
  - `paper_tags`（标签表）
  - `paper_audit_log`（审核流水表）
- **关联现有表**
  - 与`paper`表关联
  - 与用户权限系统集成
  - 与 `knowledge_tree`、`knowledge`，`school_knowledge_tree`表关联，学校可以自己管理自己的知识点树。
    - `knowledge_tree` 表需要添加字段 `school_id`，表示该知识点树是哪个学校的。
  - 与 `textbook_version`、`textbook`、`textbook_chapter`、`textbook_version_id`表关联，学校可以自己管理自己的教材版本。该机制已经实现。
    - `textbook_version` 需要添加字段 `school_id`，表示该教材版本是哪个学校的。
    

### 4.2 系统架构
- 基于现有Yii2框架扩展
- 复用现有文件上传组件
- 集成现有权限管理系统

### 4.3 接口设计
- RESTful API设计
- 与现有试卷管理接口兼容
- 支持批量操作接口

## 5. 用户故事

### 5.1 教师用户故事
- 作为教师，我希望能在10秒内找到历史优质试卷，避免重复出卷
- 作为教师，我希望上传的新卷经过一次审核后全校都能复用
- 作为教师，我希望能按标签快速筛选所需类型的试卷

### 5.2 备课组长用户故事
- 作为备课组长，我希望能统一学科资源标准
- 作为备课组长，我希望通过标签快速整理专题库
- 作为备课组长，我希望能批量处理待审核的试卷

### 5.3 管理者用户故事
- 作为学校管理者，我希望能透视各学科资源沉淀进度
- 作为学校管理者，我希望能识别薄弱环节并及时改进
- 作为学校管理者，我希望能导出数据简报用于汇报

## 6. 验收标准

### 6.1 功能验收
- [ ] 管理者看板数据准确展示
- [ ] 审核流程完整可用
- [ ] 标签管理功能正常
- [ ] 教师端检索和上传功能正常
- [ ] 权限控制有效

### 6.2 性能验收
- [ ] 检索响应时间符合要求
- [ ] 文件上传稳定可靠
- [ ] 并发访问正常

### 6.3 集成验收
- [ ] 与现有系统无缝集成
- [ ] 数据迁移完整
- [ ] 用户体验一致

## 7. 风险评估

### 7.1 技术风险
- 现有数据库结构改动风险：中等
- 权限系统集成复杂度：中等
- 文件存储容量增长：低

### 7.2 业务风险
- 用户接受度和使用习惯：中等
- 数据质量和标签规范性：中等
- 审核流程效率：低

### 7.3 风险缓解措施
- 分阶段上线，逐步推广
- 提供用户培训和操作指南
- 建立标签规范和审核标准

## 8. 项目里程碑

### 8.1 第一阶段（4周）
- 数据库设计和创建
- 基础标签管理功能
- 简单审核流程

### 8.2 第二阶段（4周）
- 管理者看板开发
- 教师端检索功能
- 权限系统集成

### 8.3 第三阶段（3周）
- 高级功能完善
- 系统测试和优化
- 用户培训和上线

## 9. 成功指标

### 9.1 使用指标
- 教师活跃使用率 > 80%
- 试卷复用率 > 60%
- 审核通过率 > 90%

### 9.2 效率指标
- 试卷查找时间减少 70%
- 重复出卷率降低 50%
- 资源标准化程度 > 85%

### 9.3 质量指标
- 用户满意度 > 4.0/5.0
- 系统稳定性 > 99%
- 数据准确性 > 99.5%
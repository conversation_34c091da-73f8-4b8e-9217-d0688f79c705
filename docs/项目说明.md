# 这是一个面向学校老师日常教学和日常考试作业的sass服务。

## 原有项目技术选型
项目目前整体运行在 ubuntu24.0+nginx+php8.3+mysql8.0+redis之上，使用php的yii2框架进行开发，使用yii2-advanced模板架构。
1. 使用`yii2-queue`处理简单的队列任务；
2. 使用`yii2-httpclient`处理http请求；
3. 使用`yii2-redis`处理redis缓存；
4. 使用`yii2-symfonymailer`处理邮件发送；
6. 使用`endroid/qr-code`处理二维码生成；
7. 使用`khanamiryan/qrcode-detector-decoder`处理二维码解析；为了提高识别率还是用了`zbaimg`命令行工具；
8. 使用`phpoffice/phpspreadsheet`处理excel导入导出；
9. 使用`aliyuncs/oss-sdk-php`处理文件上传到阿里云oss，已经使用`league/flysystem`封装进行了封装；
10. 其他封装的 yii2 components 参考 `common/config/__autocomplete.php`


### 文件夹结构
```
common
    actions/             项目公共actions
    assets/              项目公共assets
    base/                项目公共基础类 
    behaviors/           项目公共behaviors
    components/          项目公共组件
    config/              contains shared configurations
    domains/             项目公共domain
    enums/               枚举常量
    helpers/             常用助手
    jobs/                项目公共jobs
    mail/                contains view files for e-mails
    models/              contains model classes used in both backend and frontend
    services/            services
    tests/               contains tests for common classes
    uploaders/           项目公共文件上传类
    validators/          自定义验证器   
    
console
    config/              contains console configurations
    controllers/         contains console controllers (commands)
    migrations/          contains database migrations
    models/              contains console-specific model classes
    runtime/             contains files generated during runtime
    services/            项目公共services
    storage/            项目公共存储类
backend
    assets/              contains application assets such as JavaScript and CSS
    config/              contains backend configurations
    controllers/         contains Web controller classes
    data/                数据存储
    domains/             项目后端domain
    forms/               项目后端表单类
    models/              contains backend-specific model classes
    runtime/             contains files generated during runtime
    services/            项目后端services
    tests/               contains tests for backend application    
    views/               contains view files for the Web application
    web/                 contains the entry script and Web resources
    widgets/             contains backend widgets
frontend
    assets/              contains application assets such as JavaScript and CSS
    config/              contains frontend configurations
    controllers/         contains Web controller classes
    forms/               项目前端表单类
    jobs/                项目前端jobs
    models/              contains frontend-specific model classes
    modules/             前端模块
        api/               api模块，主要为前端业务提供相对通用公共接口。
        home/              教师端主模块，主要包括首页、作业讲评、错题重练和智能作业四个业务模块
        marking/           作业批改模块
        mini/              微信小程序模块
        school/            学校管理模块
        teacher/           作业批改手机端模块
    runtime/             contains files generated during runtime
    services/            项目前端services
    tests/               contains tests for frontend application
    views/               contains view files for the Web application
    web/                 contains the entry script and Web resources
vendor/                  contains dependent 3rd-party packages
environments/            contains environment-based overrides
resources/
    assets/              前端框架资源
```

注意：前期的功能都是直接开发，未使用 augment 开发。
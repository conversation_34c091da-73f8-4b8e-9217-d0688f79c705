<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能作业 - 主页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f7fa;
            color: #333;
        }
        
        .header {
            background: #fff;
            border-bottom: 1px solid #e8eaec;
            padding: 0 20px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .nav-tabs {
            display: flex;
            gap: 20px;
        }
        
        .nav-tab {
            padding: 8px 16px;
            color: #666;
            text-decoration: none;
            border-radius: 4px;
        }
        
        .nav-tab.active {
            background: #4285f4;
            color: white;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .main-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .card {
            background: white;
            border-radius: 12px;
            padding: 0;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: transform 0.2s, box-shadow 0.2s;
            cursor: pointer;
        }
        
        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }
        
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            position: relative;
            overflow: hidden;
        }
        
        .card-header.ai-homework {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .card-header.personal-bank {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        
        .card-header.question-maker {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        
        .card-header.school-bank {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            border: 3px solid #ff6b35;
            animation: highlight 2s ease-in-out infinite;
        }
        
        @keyframes highlight {
            0%, 100% { border-color: #ff6b35; }
            50% { border-color: #ffd700; }
        }
        
        .card-header::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 100%;
            background: rgba(255,255,255,0.1);
            transform: rotate(45deg);
        }
        
        .card-icon {
            font-size: 48px;
            margin-bottom: 10px;
            display: block;
        }
        
        .card-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 8px;
            position: relative;
            z-index: 1;
        }
        
        .card-subtitle {
            font-size: 14px;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }
        
        .new-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            background: #ff4444;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        
        .card-content {
            padding: 20px;
        }
        
        .card-features {
            list-style: none;
            margin-bottom: 20px;
        }
        
        .card-features li {
            padding: 8px 0;
            color: #666;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .card-features li::before {
            content: '✓';
            color: #4caf50;
            font-weight: bold;
        }
        
        .card-actions {
            display: flex;
            gap: 10px;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: #4285f4;
            color: white;
        }
        
        .btn-primary:hover {
            background: #3367d6;
            transform: translateY(-1px);
        }
        
        .btn-outline {
            background: transparent;
            color: #4285f4;
            border: 1px solid #4285f4;
        }
        
        .btn-outline:hover {
            background: #4285f4;
            color: white;
        }
        
        .quick-actions {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
        }
        
        .section-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #1a1a1a;
        }
        
        .action-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .action-item {
            padding: 15px;
            border: 1px solid #e8eaec;
            border-radius: 8px;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            color: #333;
        }
        
        .action-item:hover {
            border-color: #4285f4;
            background: #f8f9ff;
            transform: translateY(-2px);
        }
        
        .action-icon {
            font-size: 32px;
            margin-bottom: 10px;
            display: block;
        }
        
        .action-title {
            font-weight: 500;
            margin-bottom: 5px;
        }
        
        .action-desc {
            font-size: 12px;
            color: #666;
        }
        
        .filter-bar {
            background: white;
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            gap: 15px;
            align-items: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .filter-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .filter-label {
            font-weight: 500;
            color: #666;
        }
        
        .filter-select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
        }
        
        .create-btn {
            margin-left: auto;
            background: #4285f4;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
        }
        
        .create-btn:hover {
            background: #3367d6;
        }
    </style>
</head>
<body>
    <!-- 顶部导航 -->
    <div class="header">
        <div class="nav-tabs">
            <a href="#" class="nav-tab">首页</a>
            <a href="#" class="nav-tab">作业讲评</a>
            <a href="#" class="nav-tab">错题重练</a>
            <a href="#" class="nav-tab active">智能作业</a>
            <a href="#" class="nav-tab">数字教辅</a>
            <a href="#" class="nav-tab">AI作文</a>
        </div>
        <div class="user-info">
            <span>🌐</span>
            <span>个人中心 ▼</span>
        </div>
    </div>

    <div class="container">
        <!-- 主要功能卡片 -->
        <div class="main-cards">
            <!-- 智能作业卡片 -->
            <div class="card">
                <div class="card-header ai-homework">
                    <span class="card-icon">🎓</span>
                    <h3 class="card-title">智能作业</h3>
                    <p class="card-subtitle">精品试卷 自营试卷</p>
                </div>
                <div class="card-content">
                    <ul class="card-features">
                        <li>精品试卷库</li>
                        <li>自营试卷</li>
                        <li>智能组卷</li>
                    </ul>
                    <div class="card-actions">
                        <a href="#" class="btn btn-primary">进入</a>
                        <a href="#" class="btn btn-outline">了解更多</a>
                    </div>
                </div>
            </div>

            <!-- 个人题库卡片 -->
            <div class="card">
                <div class="card-header personal-bank">
                    <span class="card-icon">📚</span>
                    <h3 class="card-title">个人题库</h3>
                    <p class="card-subtitle">我的试卷 我的试题</p>
                </div>
                <div class="card-content">
                    <ul class="card-features">
                        <li>我的试卷</li>
                        <li>我的试题</li>
                        <li>个人收藏</li>
                    </ul>
                    <div class="card-actions">
                        <a href="#" class="btn btn-primary">进入</a>
                        <a href="#" class="btn btn-outline">管理</a>
                    </div>
                </div>
            </div>

            <!-- 题卡制作卡片 -->
            <div class="card">
                <div class="card-header question-maker">
                    <span class="card-icon">🛠️</span>
                    <h3 class="card-title">题卡制作</h3>
                    <p class="card-subtitle">我的答题卡 智能作文</p>
                </div>
                <div class="card-content">
                    <ul class="card-features">
                        <li>我的答题卡</li>
                        <li>智能作文</li>
                        <li>自定义制作</li>
                    </ul>
                    <div class="card-actions">
                        <a href="#" class="btn btn-primary">进入</a>
                        <a href="#" class="btn btn-outline">制作</a>
                    </div>
                </div>
            </div>

            <!-- 校本题库卡片 - 新增 -->
            <div class="card">
                <div class="card-header school-bank">
                    <div class="new-badge">NEW</div>
                    <span class="card-icon">🏫</span>
                    <h3 class="card-title">校本题库</h3>
                    <p class="card-subtitle">学校专属 优质资源</p>
                </div>
                <div class="card-content">
                    <ul class="card-features">
                        <li>学校专属题库</li>
                        <li>优质资源共享</li>
                        <li>标签化管理</li>
                        <li>审核质量保证</li>
                    </ul>
                    <div class="card-actions">
                        <a href="试卷列表-普通教师.html" class="btn btn-primary">🔍 浏览题库</a>
                        <a href="#" class="btn btn-outline">📤 提交试卷</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 筛选栏 -->
        <div class="filter-bar">
            <span style="font-weight: 600; color: #1a1a1a;">试卷来源：</span>
            <div class="filter-group">
                <button class="btn btn-primary">全部</button>
            </div>
            <div class="filter-group">
                <button class="btn btn-outline">同步练习</button>
            </div>
            <div class="filter-group">
                <button class="btn btn-outline">单元检测</button>
            </div>
            <div class="filter-group">
                <button class="btn btn-outline">学科网题库</button>
            </div>
            <div class="filter-group">
                <button class="btn btn-outline">自营题库</button>
            </div>
            <div class="filter-group">
                <button class="btn btn-outline">校本作业</button>
            </div>
            
            <div class="filter-group" style="margin-left: 20px;">
                <span class="filter-label">类型：</span>
                <select class="filter-select">
                    <option>全部</option>
                    <option>月考试卷</option>
                    <option>单元测试</option>
                    <option>期中考试</option>
                    <option>期末考试</option>
                    <option>高考模拟</option>
                    <option>其他试卷</option>
                </select>
            </div>
            
            <div class="filter-group">
                <span class="filter-label">模块：</span>
                <select class="filter-select">
                    <option>全部</option>
                    <option>语文</option>
                    <option>数学</option>
                    <option>英语</option>
                    <option>物理</option>
                    <option>化学</option>
                </select>
            </div>
            
            <button class="create-btn">+ 新作业</button>
        </div>

        <!-- 快捷操作 -->
        <div class="quick-actions">
            <h2 class="section-title">快捷操作</h2>
            <div class="action-grid">
                <a href="#" class="action-item">
                    <span class="action-icon">📝</span>
                    <div class="action-title">创建作业</div>
                    <div class="action-desc">快速创建新作业</div>
                </a>
                <a href="#" class="action-item">
                    <span class="action-icon">📊</span>
                    <div class="action-title">作业统计</div>
                    <div class="action-desc">查看作业数据</div>
                </a>
                <a href="#" class="action-item">
                    <span class="action-icon">🔍</span>
                    <div class="action-title">试卷搜索</div>
                    <div class="action-desc">搜索试卷资源</div>
                </a>
                <a href="试卷列表-学科组长.html" class="action-item">
                    <span class="action-icon">⚙️</span>
                    <div class="action-title">题库管理</div>
                    <div class="action-desc">管理校本题库</div>
                </a>
            </div>
        </div>
    </div>
</body>
</html>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>试卷审核详情 - 校本题库</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f7fa;
            color: #333;
        }
        
        .header {
            background: #fff;
            border-bottom: 1px solid #e8eaec;
            padding: 0 20px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .nav-tabs {
            display: flex;
            gap: 20px;
        }
        
        .nav-tab {
            padding: 8px 16px;
            color: #666;
            text-decoration: none;
            border-radius: 4px;
        }
        
        .nav-tab.active {
            background: #4285f4;
            color: white;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .breadcrumb {
            margin-bottom: 20px;
            color: #666;
        }
        
        .breadcrumb a {
            color: #4285f4;
            text-decoration: none;
        }
        
        .audit-header {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .paper-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #1a1a1a;
        }
        
        .paper-meta {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .meta-item {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        
        .meta-label {
            font-weight: 500;
            color: #666;
            font-size: 14px;
        }
        
        .meta-value {
            font-size: 16px;
            color: #1a1a1a;
        }
        
        .status-badge {
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 14px;
            font-weight: 500;
            display: inline-block;
        }
        
        .status-pending {
            background: #fff3e0;
            color: #ff9800;
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
        }
        
        .paper-preview {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .preview-header {
            padding: 20px;
            border-bottom: 1px solid #e8eaec;
            display: flex;
            justify-content: between;
            align-items: center;
        }
        
        .preview-title {
            font-size: 18px;
            font-weight: 600;
        }
        
        .preview-actions {
            display: flex;
            gap: 10px;
        }
        
        .btn {
            padding: 8px 16px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
            cursor: pointer;
            text-decoration: none;
            color: #333;
            font-size: 14px;
        }
        
        .btn:hover {
            background: #f5f5f5;
        }
        
        .btn-primary {
            background: #4285f4;
            color: white;
            border-color: #4285f4;
        }
        
        .btn-primary:hover {
            background: #3367d6;
        }
        
        .preview-content {
            padding: 20px;
            max-height: 600px;
            overflow-y: auto;
        }
        
        .paper-content {
            line-height: 1.6;
            color: #333;
        }
        
        .question {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #e8eaec;
            border-radius: 6px;
        }
        
        .question-number {
            font-weight: 600;
            color: #4285f4;
            margin-bottom: 8px;
        }
        
        .question-content {
            margin-bottom: 10px;
        }
        
        .question-options {
            margin-left: 20px;
        }
        
        .question-options div {
            margin-bottom: 5px;
        }
        
        .audit-panel {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            height: fit-content;
        }
        
        .panel-header {
            padding: 20px;
            border-bottom: 1px solid #e8eaec;
            background: #fafbfc;
            border-radius: 8px 8px 0 0;
        }
        
        .panel-title {
            font-size: 18px;
            font-weight: 600;
            color: #1a1a1a;
        }
        
        .panel-content {
            padding: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }
        
        .form-control {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #4285f4;
            box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.2);
        }
        
        .tag-selector {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 10px;
        }
        
        .tag-option {
            padding: 6px 12px;
            border: 1px solid #ddd;
            border-radius: 16px;
            background: white;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s;
        }
        
        .tag-option:hover {
            border-color: #4285f4;
        }
        
        .tag-option.selected {
            background: #4285f4;
            color: white;
            border-color: #4285f4;
        }
        
        .tag-option.primary {
            background: #ff9800;
            color: white;
            border-color: #ff9800;
        }
        
        .audit-actions {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }
        
        .btn-success {
            background: #34a853;
            color: white;
            border-color: #34a853;
            flex: 1;
        }
        
        .btn-success:hover {
            background: #2d8f47;
        }
        
        .btn-danger {
            background: #f44336;
            color: white;
            border-color: #f44336;
            flex: 1;
        }
        
        .btn-danger:hover {
            background: #d32f2f;
        }
        
        .audit-history {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e8eaec;
        }
        
        .history-item {
            padding: 12px;
            border-left: 3px solid #e8eaec;
            margin-bottom: 15px;
            background: #f8f9fa;
            border-radius: 0 4px 4px 0;
        }
        
        .history-item.submitted {
            border-left-color: #2196f3;
        }
        
        .history-meta {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }
        
        .history-content {
            font-size: 14px;
            color: #333;
        }
        
        .required {
            color: #f44336;
        }
        
        .help-text {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <!-- 顶部导航 -->
    <div class="header">
        <div class="nav-tabs">
            <a href="#" class="nav-tab">首页</a>
            <a href="#" class="nav-tab">作业讲评</a>
            <a href="#" class="nav-tab">错题重练</a>
            <a href="#" class="nav-tab active">智能作业</a>
            <a href="#" class="nav-tab">数字教辅</a>
            <a href="#" class="nav-tab">AI作文</a>
        </div>
        <div class="user-info">
            <span>🌐</span>
            <span>个人中心 ▼</span>
        </div>
    </div>

    <div class="container">
        <!-- 面包屑导航 -->
        <div class="breadcrumb">
            <a href="智能作业-主页面.html">智能作业</a> > 
            <a href="试卷列表-学科组长.html">校本题库</a> > 
            试卷审核
        </div>

        <!-- 审核头部信息 -->
        <div class="audit-header">
            <h1 class="paper-title">【语文】第七单元现代文阅读专项训练</h1>
            <div class="paper-meta">
                <div class="meta-item">
                    <span class="meta-label">提交教师</span>
                    <span class="meta-value">张老师</span>
                </div>
                <div class="meta-item">
                    <span class="meta-label">提交时间</span>
                    <span class="meta-value">2025-07-20 14:30</span>
                </div>
                <div class="meta-item">
                    <span class="meta-label">学科年级</span>
                    <span class="meta-value">语文 - 八年级</span>
                </div>
                <div class="meta-item">
                    <span class="meta-label">审核状态</span>
                    <span class="status-badge status-pending">待审核</span>
                </div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- 试卷预览 -->
            <div class="paper-preview">
                <div class="preview-header">
                    <h3 class="preview-title">试卷预览</h3>
                    <div class="preview-actions">
                        <button class="btn">📄 下载原文件</button>
                        <button class="btn btn-primary">🔍 全屏预览</button>
                    </div>
                </div>
                <div class="preview-content">
                    <div class="paper-content">
                        <div class="question">
                            <div class="question-number">1. 阅读下面的文字，完成下列各题。（20分）</div>
                            <div class="question-content">
                                <p>春天的脚步</p>
                                <p>春天来了，万物复苏。柳树发芽了，小草绿了，花儿开了。春风轻拂过脸庞，带来了温暖和希望...</p>
                                <p>（1）文中描写春天景象的句子有哪些？请摘抄出来。（5分）</p>
                                <p>（2）作者通过哪些具体的事物来表现春天的到来？（8分）</p>
                                <p>（3）请结合文章内容，谈谈你对"春天的脚步"这个标题的理解。（7分）</p>
                            </div>
                        </div>
                        
                        <div class="question">
                            <div class="question-number">2. 下列词语中加点字的读音完全正确的一组是（）（3分）</div>
                            <div class="question-options">
                                <div>A. 绽放(zhàn) 嫩芽(nèn) 和煦(xù)</div>
                                <div>B. 萌发(méng) 清澈(chè) 温馨(xīn)</div>
                                <div>C. 蓬勃(péng) 滋润(zī) 惬意(qiè)</div>
                                <div>D. 盎然(àng) 葱茏(lóng) 馥郁(fù)</div>
                            </div>
                        </div>
                        
                        <div class="question">
                            <div class="question-number">3. 仿照例句，写一个句子。（5分）</div>
                            <div class="question-content">
                                <p>例句：春天是一位画家，用绿色的画笔描绘着大地。</p>
                                <p>仿句：春天是_______，_______。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 审核面板 -->
            <div class="audit-panel">
                <div class="panel-header">
                    <h3 class="panel-title">审核操作</h3>
                </div>
                <div class="panel-content">
                    <!-- 标签选择 -->
                    <div class="form-group">
                        <label class="form-label">选择标签 <span class="required">*</span></label>
                        <div class="help-text">请至少选择一个主标签（橙色），可选择多个辅助标签</div>
                        <div class="tag-selector">
                            <div class="tag-option" onclick="selectTag(this, 'primary')">单元检测</div>
                            <div class="tag-option" onclick="selectTag(this, 'primary')">专项练习</div>
                            <div class="tag-option" onclick="selectTag(this, 'primary')">期中复习</div>
                            <div class="tag-option" onclick="selectTag(this, 'primary')">期末复习</div>
                            <div class="tag-option" onclick="selectTag(this, 'secondary')">现代文阅读</div>
                            <div class="tag-option" onclick="selectTag(this, 'secondary')">语言文字运用</div>
                            <div class="tag-option" onclick="selectTag(this, 'secondary')">基础知识</div>
                            <div class="tag-option" onclick="selectTag(this, 'secondary')">写作训练</div>
                        </div>
                    </div>

                    <!-- 审核意见 -->
                    <div class="form-group">
                        <label class="form-label">审核意见</label>
                        <textarea class="form-control" rows="4" placeholder="请输入审核意见（选填）..."></textarea>
                    </div>

                    <!-- 审核操作按钮 -->
                    <div class="audit-actions">
                        <button class="btn btn-success" onclick="auditPass()">✅ 审核通过</button>
                        <button class="btn btn-danger" onclick="auditReject()">❌ 退回修改</button>
                    </div>

                    <!-- 审核历史 -->
                    <div class="audit-history">
                        <h4 style="margin-bottom: 15px; color: #666;">审核历史</h4>
                        <div class="history-item submitted">
                            <div class="history-meta">2025-07-20 14:30 - 张老师</div>
                            <div class="history-content">提交试卷到校本题库，等待审核</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let selectedPrimaryTag = null;
        let selectedSecondaryTags = [];

        function selectTag(element, type) {
            if (type === 'primary') {
                // 清除之前的主标签选择
                if (selectedPrimaryTag) {
                    selectedPrimaryTag.classList.remove('primary');
                    selectedPrimaryTag.classList.remove('selected');
                }
                
                // 设置新的主标签
                element.classList.add('primary');
                element.classList.add('selected');
                selectedPrimaryTag = element;
            } else {
                // 切换辅助标签
                if (element.classList.contains('selected')) {
                    element.classList.remove('selected');
                    selectedSecondaryTags = selectedSecondaryTags.filter(tag => tag !== element);
                } else {
                    element.classList.add('selected');
                    selectedSecondaryTags.push(element);
                }
            }
        }

        function auditPass() {
            if (!selectedPrimaryTag) {
                alert('请至少选择一个主标签！');
                return;
            }
            
            if (confirm('确认审核通过该试卷？')) {
                alert('审核通过！试卷已加入校本题库。');
                window.location.href = '试卷列表-学科组长.html';
            }
        }

        function auditReject() {
            const comment = document.querySelector('textarea').value;
            if (!comment.trim()) {
                alert('退回试卷时请填写审核意见！');
                return;
            }
            
            if (confirm('确认退回该试卷？')) {
                alert('试卷已退回，教师可重新修改后提交。');
                window.location.href = '试卷列表-学科组长.html';
            }
        }
    </script>
</body>
</html>

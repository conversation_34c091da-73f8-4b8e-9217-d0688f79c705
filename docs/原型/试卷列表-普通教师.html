<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>校本题库 - 试卷列表（普通教师）</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f7fa;
            color: #333;
        }
        
        .header {
            background: #fff;
            border-bottom: 1px solid #e8eaec;
            padding: 0 20px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .nav-tabs {
            display: flex;
            gap: 20px;
        }
        
        .nav-tab {
            padding: 8px 16px;
            color: #666;
            text-decoration: none;
            border-radius: 4px;
        }
        
        .nav-tab.active {
            background: #4285f4;
            color: white;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .page-header {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .page-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #1a1a1a;
        }
        
        .filter-section {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .filter-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .filter-label {
            font-weight: 500;
            color: #666;
            min-width: 60px;
        }
        
        .filter-select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
            min-width: 120px;
        }
        
        .search-box {
            display: flex;
            gap: 10px;
            margin-left: auto;
        }
        
        .search-input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 250px;
        }
        
        .search-btn {
            padding: 8px 16px;
            background: #4285f4;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .paper-list {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .list-header {
            padding: 15px 20px;
            border-bottom: 1px solid #e8eaec;
            font-weight: 600;
            background: #fafbfc;
            border-radius: 8px 8px 0 0;
        }
        
        .paper-item {
            padding: 20px;
            border-bottom: 1px solid #e8eaec;
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        .paper-item:last-child {
            border-bottom: none;
        }
        
        .paper-icon {
            width: 40px;
            height: 40px;
            background: #e8f4fd;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #4285f4;
            font-size: 18px;
        }
        
        .paper-info {
            flex: 1;
        }
        
        .paper-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
            color: #1a1a1a;
        }
        
        .paper-meta {
            display: flex;
            gap: 20px;
            color: #666;
            font-size: 14px;
        }
        
        .paper-tags {
            display: flex;
            gap: 8px;
            margin-top: 8px;
        }
        
        .tag {
            padding: 2px 8px;
            background: #f0f0f0;
            border-radius: 12px;
            font-size: 12px;
            color: #666;
        }
        
        .tag.primary {
            background: #e8f4fd;
            color: #4285f4;
        }
        
        .paper-actions {
            display: flex;
            gap: 10px;
        }
        
        .btn {
            padding: 8px 16px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
            cursor: pointer;
            text-decoration: none;
            color: #333;
            font-size: 14px;
        }
        
        .btn:hover {
            background: #f5f5f5;
        }
        
        .btn-primary {
            background: #4285f4;
            color: white;
            border-color: #4285f4;
        }
        
        .btn-primary:hover {
            background: #3367d6;
        }
        
        .btn-success {
            background: #34a853;
            color: white;
            border-color: #34a853;
        }
        
        .btn-success:hover {
            background: #2d8f47;
        }
        
        .pagination {
            padding: 20px;
            display: flex;
            justify-content: center;
            gap: 10px;
        }
        
        .page-btn {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
        }
        
        .page-btn.active {
            background: #4285f4;
            color: white;
            border-color: #4285f4;
        }
        
        .stats-bar {
            padding: 15px 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #e8eaec;
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <!-- 顶部导航 -->
    <div class="header">
        <div class="nav-tabs">
            <a href="#" class="nav-tab">首页</a>
            <a href="#" class="nav-tab">作业讲评</a>
            <a href="#" class="nav-tab">错题重练</a>
            <a href="#" class="nav-tab active">智能作业</a>
            <a href="#" class="nav-tab">数字教辅</a>
            <a href="#" class="nav-tab">AI作文</a>
        </div>
        <div class="user-info">
            <span>🌐</span>
            <span>个人中心 ▼</span>
        </div>
    </div>

    <div class="container">
        <!-- 页面标题和筛选 -->
        <div class="page-header">
            <h1 class="page-title">校本题库</h1>
            <div class="filter-section">
                <div class="filter-group">
                    <span class="filter-label">学科：</span>
                    <select class="filter-select">
                        <option>语文</option>
                        <option>数学</option>
                        <option>英语</option>
                        <option>物理</option>
                        <option>化学</option>
                        <option>生物</option>
                    </select>
                </div>
                <div class="filter-group">
                    <span class="filter-label">年级：</span>
                    <select class="filter-select">
                        <option>全部</option>
                        <option>七年级</option>
                        <option>八年级</option>
                        <option>九年级</option>
                    </select>
                </div>
                <div class="filter-group">
                    <span class="filter-label">标签：</span>
                    <select class="filter-select">
                        <option>全部</option>
                        <option>同步练习</option>
                        <option>单元检测</option>
                        <option>专项练习</option>
                        <option>期中复习</option>
                        <option>期末复习</option>
                        <option>易错巩固</option>
                        <option>拓展拔高</option>
                    </select>
                </div>
                <div class="search-box">
                    <input type="text" class="search-input" placeholder="搜索试卷名称...">
                    <button class="search-btn">搜索</button>
                </div>
            </div>
        </div>

        <!-- 试卷列表 -->
        <div class="paper-list">
            <div class="list-header">
                试卷列表
            </div>
            <div class="stats-bar">
                共找到 156 份试卷，当前显示第 1-10 份
            </div>
            
            <!-- 试卷项目 -->
            <div class="paper-item">
                <div class="paper-icon">📄</div>
                <div class="paper-info">
                    <div class="paper-title">【语文】第六单元-【古诗词阅读】高效突破（高教版2023职业模块）（解析版）</div>
                    <div class="paper-meta">
                        <span>加入时间：2025-07-10</span>
                        <span>来源：未设置</span>
                        <span>模块：未设置</span>
                    </div>
                    <div class="paper-tags">
                        <span class="tag primary">单元检测</span>
                        <span class="tag">古诗词</span>
                        <span class="tag">阅读理解</span>
                    </div>
                </div>
                <div class="paper-actions">
                    <button class="btn">📖 预览</button>
                    <button class="btn">⬇️ 下载</button>
                    <button class="btn btn-success">📝 组卷考试</button>
                </div>
            </div>

            <div class="paper-item">
                <div class="paper-icon">📄</div>
                <div class="paper-info">
                    <div class="paper-title">【语文】2025年06月27日高中语文试卷</div>
                    <div class="paper-meta">
                        <span>加入时间：2025-06-27</span>
                        <span>来源：未设置</span>
                        <span>模块：未设置</span>
                    </div>
                    <div class="paper-tags">
                        <span class="tag primary">期末复习</span>
                        <span class="tag">综合测试</span>
                    </div>
                </div>
                <div class="paper-actions">
                    <button class="btn">📖 预览</button>
                    <button class="btn">⬇️ 下载</button>
                    <button class="btn btn-success">📝 组卷考试</button>
                </div>
            </div>

            <div class="paper-item">
                <div class="paper-icon">📄</div>
                <div class="paper-info">
                    <div class="paper-title">【语文】第六单元-【古诗词阅读】高效突破（高教版2023职业模块）（解析版）</div>
                    <div class="paper-meta">
                        <span>加入时间：2025-06-01</span>
                        <span>来源：未设置</span>
                        <span>模块：未设置</span>
                    </div>
                    <div class="paper-tags">
                        <span class="tag primary">专项练习</span>
                        <span class="tag">古诗词</span>
                        <span class="tag">阅读理解</span>
                    </div>
                </div>
                <div class="paper-actions">
                    <button class="btn">📖 预览</button>
                    <button class="btn">⬇️ 下载</button>
                    <button class="btn btn-success">📝 组卷考试</button>
                </div>
            </div>

            <!-- 分页 -->
            <div class="pagination">
                <button class="page-btn">上一页</button>
                <button class="page-btn active">1</button>
                <button class="page-btn">2</button>
                <button class="page-btn">3</button>
                <button class="page-btn">4</button>
                <button class="page-btn">5</button>
                <button class="page-btn">下一页</button>
            </div>
        </div>
    </div>
</body>
</html>

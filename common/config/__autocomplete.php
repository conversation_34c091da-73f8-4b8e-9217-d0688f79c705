<?php

/**
 * This class only exists here for IDE (PHPStorm/Netbeans/...) autocompletion.
 * This file is never included anywhere.
 * Adjust this file to match classes configured in your application config, to enable IDE autocompletion for custom components.
 * Example: A property phpdoc can be added in `__Application` class as `@property \vendor\package\Rollbar|__Rollbar $rollbar` and adding a class in this file
 * ```php
 * // @property of \vendor\package\Rollbar goes here
 * class __Rollbar {
 * }
 * ```
 */
class Yii {
    /**
     * @var \yii\web\Application|\yii\console\Application|__Application
     */
    public static $app;
}

/**
 * @property yii\rbac\DbManager $backendAuthManager 后台权限管理
 * @property yii\rbac\DbManager $authManager 前台权限管理
 * @property yii\web\User|__WebUser $user 当前登录用户
 * @property yii\queue\redis\Queue $queue 队列
 * @property common\components\QrcodeBuilder $qrcodeBuilder 二维码生成器 使用endroid/qr-code处理二维码生成
 * @property common\base\storage\StorageInterface $localStorage 本地存储接口
 * @property common\base\storage\StorageInterface $cloudStorage 云存储接口(阿里云OSS)
 * @property common\base\storage\StorageInterface $runtimeStorage 运行时临时存储接口
 * @property common\base\sms\SmsClient $smsClient 短信客户端(阿里云短信)
 * @property common\base\Socialite $socialite 社交登录组件
 * @property common\components\PageConverter $pageConverter 网页页面转换器，使用`playwright`生成图片和pdf
 * @property common\components\CypherClient $cypherClient ne4j客户端
 * @property common\components\ZBarTool $zbar 二维码解析工具 使用zbarimg命令行工具
 * @property common\components\OpenAiClient $openAiClient OpenAI客户端
 */
class __Application {
}

/**
 * @property common\models\base\User $identity
 */
class __WebUser {
}

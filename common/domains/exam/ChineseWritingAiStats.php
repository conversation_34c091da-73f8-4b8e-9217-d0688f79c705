<?php

namespace common\domains\exam;

use common\enums\CardContentType;
use common\enums\ChineseWritingType;
use common\models\exam\Exam;
use yii\base\Model;
use yii\helpers\Json;

class ChineseWritingAiStats extends Model
{
    
     /**
      * Summary of handelAiStats
      * @param \common\models\exam\Exam $exam
      * @param array $studentList  学生信息
      * @param array $aiResults   学生Ai评语信息
      * @param array $questionInfo  作文要求
      * @return array
      */
     public static function handelAiStats(Exam $exam, array $studentList,array $aiResults,array $questionInfo):array
     {
        $cardInfo = Json::decode($exam->card_info);
        $writeType = $questionInfo['writeType'] ?? '';
        $wordNumber = $questionInfo['wordNumber'] ?? 500;

        $isEnglish = false;
        if($cardInfo['contentTypeId'] == CardContentType::EnglishWriting->value){
            $isEnglish = true;
        }
        if(!$isEnglish){
            $levelStandards = StudentLevelStats::getStandards($exam->getExamStage(),$cardInfo['full_score']);
        }else{
            $levelStandards = StudentLevelStats::getEnglishStandards($cardInfo['full_score']);
        }
        
        
        $students = [];
        $levelStandardStudents = [];
        $dimensionNames = [];
        $dimensions = [];
        $dimensionStudents = [];
        $errorStudents = [];
        $studentStrengths = [];
        $studentWeaknesses = [];
        $studentSuggestions = [];

        $studentErrors = [];
        if(!$isEnglish){
            $errorLabels = self::studentErrorLabels();
        }else{
            $errorLabels = self::studentEnglishErrorLabels();
        }
        $goodSenNum = $errorSenNum = $usageSenNum = 0;

        foreach($studentList as $student){
            if(!empty($student['absent'])){//缺考不处理
                continue;
            }
            $studentStandard = StudentLevelStats::getScoreStandard($levelStandards,$student['score']);
            $levelStandardStudents[$studentStandard][] = [
                'student_id' => $student['student_id'],
                'student_name' => $student['student_name'],
                'score' => $student['score'],
            ];
            if(!empty($aiResults[$student['student_id']])){//如果有数据
                $aiResult = $aiResults[$student['student_id']];
                if(!empty($aiResult['dimensions']) && !$isEnglish){
                    foreach($aiResult['dimensions'] as $dimension){
                        if(empty($dimensionNames) || !in_array($dimension['dimenName'],$dimensionNames)){
                            $dimensionNames[] = $dimension['dimenName'];
                            $dimensions[] = [
                                'name' => $dimension['dimenName'],
                                'fullScore' => $dimension['dimenFullScore'],
                            ];
                        }
                        $dimensionStudents[$dimension['dimenName']][] = [
                            'student_id' => $student['student_id'],
                            'student_name' => $student['student_name'],
                            'score' => $dimension['dimenScore'],
                        ];
                    }
                }
                if(!$isEnglish){
                    if(!empty($aiResult['details']['goodSen'])){
                        $goodSenNum += count($aiResult['details']['goodSen']);
                    }
                    if(!empty($aiResult['details']['errorSen'])){
                        $errorSenNum += count($aiResult['details']['errorSen']);
                    }
                    if(!empty($aiResult['details']['usageSen'])){
                        $usageSenNum += count($aiResult['details']['usageSen']);
                    }
                }
                $errors = self::getStudentErrors($wordNumber,$writeType,$aiResult,$isEnglish);
                $wrongNum = 0;
                if($isEnglish){
                    $wrongNum = !empty($aiResult['wrongWord']) ? count($aiResult['wrongWord']) : 0;
                }else{
                    $wrongNum = !empty($aiResult['details']['wrongWord']) ? count($aiResult['details']['wrongWord']) : 0;
                }
                $errorSenNum = 0;
                if($isEnglish){
                    $errorSenNum  = !empty($aiResult['inlineCorrections'])? count($aiResult['inlineCorrections']): 0;
                }else{
                    $errorSenNum = !empty($aiResult['details']['errorSen'])? count($aiResult['details']['errorSen']): 0;
                }
                if(!empty($errors)){
                    foreach($errors as $error){

                        $errorStudents[$error][] = [
                            'student_id' => $student['student_id'],
                            'student_name' => $student['student_name'],
                            'wordCount' => $aiResult['wordCount'],
                            'type' => $aiResult['type'] ?? '',
                            'wrongWordNUm' => $wrongNum,
                            'delNum' => !empty($studentResult['delBoxes']) ? count($studentResult['delBoxes']): 0,
                            'errorSenNUm' => $errorSenNum,
                        ];
                        $studentErrors[$student['student_id']][] = [
                            'error' => $error,
                            'label' => $errorLabels[$error]['label'],
                        ];
                    }
                }
                if(!empty($aiResult['evaluate']['strengths'])){
                    $studentStrengths[] = $aiResult['evaluate']['strengths'];
                }
                if(!empty($aiResult['evaluate']['weaknesses'])){
                    $studentWeaknesses[] = $aiResult['evaluate']['weaknesses'];
                }
                if(!empty($aiResult['evaluate']['suggestions'])){
                    $studentSuggestions[] = $aiResult['evaluate']['suggestions'];
                }
            }
        }
        

        return [
            'dimensions' => !$isEnglish ? self::handleDimensionStats($dimensions,$dimensionStudents) : [],
            'scoreLevels' => StudentLevelStats::handleLevelStats($levelStandards,$levelStandardStudents),
            'studentErrors' => $studentErrors,
            'strengths' => $studentStrengths,
            'weaknesses' => $studentWeaknesses,
            'suggestions' => $studentSuggestions,
            'dimensionNames' => $dimensionNames,
            'errors' => self::handelErrors($errorStudents,$isEnglish),
            'quality' => [
                'goodNum' => $goodSenNum,
                'errorNum' => $errorSenNum,
                'usageNum' => $usageSenNum
            ],
        ];
     }

     public static function handelErrors(array $errorStudents,$isEnglish):array
     {
         if($isEnglish){
             $labels = self::studentEnglishErrorLabels();
         }else{
             $labels = self::studentErrorLabels();
         }
         $data = [];
         foreach($labels as $error => $labelItem){
             $data[] = [
                 'key' => $error,
                 'label' => $labelItem['label'],
                 'desc' => $labelItem['desc'],
                 'num' => !empty($errorStudents[$error]) ? count($errorStudents[$error]) : 0,
                 'students' => !empty($errorStudents[$error]) ? $errorStudents[$error] : [],
             ];
         } 
         return $data;
     }

     public static function handleDimensionStats(array $dimensions,array $dimensionStudents):array
     {
        $scoreTypeLabels = self::getScoreTypeLabels();
        $data = [];
        foreach($dimensions as $dimension){ 
            $students = $dimensionStudents[$dimension['name']];
            $scoreTypeStudents = [];
            foreach($students as $student){
                $scoreType = self::judgeScoreType($dimension['fullScore'],$student['score']);
                $scoreTypeStudents[$scoreType][] = $student;
            }
            $scoreTypes = [];
            foreach($scoreTypeStudents as $scoreType => $scoreStudents){
                $scoreTypes[] = [
                    'label' => $scoreTypeLabels[$scoreType],
                    'level' => $scoreType,
                    'num' => count($scoreStudents),
                    'students' => $scoreStudents,
                ];
            }
            $data[] = [
                'name' => $dimension['name'],
                'fullScore' => $dimension['fullScore'],
                'scoreLevels' => $scoreTypes,
            ];
        }
        return $data;
     }


     public static function judgeScoreType($fullScore,$score):int
     {
        if($score >= ($fullScore * 0.8)){
            return self::SCORE_TYPE_EXCELLENT;
        }elseif($score >= ($fullScore * 0.6)){
            return self::SCORE_TYPE_GOOD;
        }else{
            return  self::SCORE_TYPE_GENERAL;
        }
     }


     const SCORE_TYPE_EXCELLENT = 1;
     const SCORE_TYPE_GOOD = 2;
     const SCORE_TYPE_GENERAL = 3;


     public static function getScoreTypeLabels()
     {
        return [
            self::SCORE_TYPE_EXCELLENT => '优秀',
            self::SCORE_TYPE_GOOD => '良好',
            self::SCORE_TYPE_GENERAL => '一般',
        ];
     }


     public static function getStudentErrors($wordNum,$writeType,$studentResult,$isEnglish):array
     {
         if($studentResult['status'] != 0 || !empty($studentResult['error_user'])){
            return [];
         }
         $errors = [];
         if(self::isWordNumLess($wordNum,$studentResult['wordCount'])){
            $errors[] = self::ERROR_WORD_NUM_LESS;
         }
         if(!$isEnglish && !empty($studentResult['type']) && self::isWriteTypeFail($writeType,$studentResult['type'])){
            $errors[] = self::ERROR_WRiTE_TYPE_FAIL;
         }
         $wrongNum = 0;
         if($isEnglish){
             $wrongNum = !empty($studentResult['wrongWord']) ? count($studentResult['wrongWord']) : 0;
         }else{
             $wrongNum = !empty($studentResult['details']['wrongWord']) ? count($studentResult['details']['wrongWord']) : 0;
         }
         $errorSenNum = 0;
         if($isEnglish){
             $errorSenNum  = !empty($studentResult['inlineCorrections'])? count($studentResult['inlineCorrections']): 0;
         }else{
             $errorSenNum = !empty($studentResult['details']['errorSen'])? count($studentResult['details']['errorSen']): 0;
         }
         if(!$isEnglish && self::isTypos($studentResult['wordCount'],$wrongNum)){
            $errors[] = self::ERROR_TYPOS;
         }
         if(self::isPageUntidy($studentResult['wordCount'],!empty($studentResult['delBoxes']) ? count($studentResult['delBoxes']): 0)){
            $errors[] = self::ERROR_PAGE_UNTIDY;
         }
         if(self::isErrorSen($errorSenNum)){
            $errors[] = self::ERROR_SEN_MUCH;
         }
         return $errors;
     }

     const ERROR_WORD_NUM_LESS = 1;
     const ERROR_WRiTE_TYPE_FAIL = 2;
     const ERROR_TYPOS = 3;
     const ERROR_PAGE_UNTIDY = 4;
     const ERROR_SEN_MUCH = 5;

     public static function studentErrorLabels()
     {
        return [
            self::ERROR_WORD_NUM_LESS => [
                'label' => '字数不足',
                'desc' => '字数不足规定的',
            ],
            self::ERROR_WRiTE_TYPE_FAIL => [
                'label' => '文体错误',
                'desc' => '写成错题文',
            ],
            self::ERROR_TYPOS => [
                'label' => '错别字严重',
                'desc' => '错字超过总字数20%',
            ],
            self::ERROR_PAGE_UNTIDY => [
                'label' => '卷面不整',
                'desc' => '涂抹超过总字数10%',
            ],
            self::ERROR_SEN_MUCH => [
                'label' => '语病严重',
                'desc' => '病句超过10句',
            ],
        ];
     }

    public static function studentEnglishErrorLabels()
    {
        return [
            self::ERROR_WORD_NUM_LESS => [
                'label' => '字数不足',
                'desc' => '字数不足规定的',
            ],
            self::ERROR_TYPOS => [
                'label' => '错词严重',
                'desc' => '错词超过总词数20%',
            ],
            self::ERROR_PAGE_UNTIDY => [
                'label' => '卷面不整',
                'desc' => '涂抹超过总字数10%',
            ],
            self::ERROR_SEN_MUCH => [
                'label' => '语病严重',
                'desc' => '病句超过10句',
            ],
        ];
    }


     public static function isWordNumLess($wordNum,$ocrNum):bool
     {
        if($ocrNum < ($wordNum) ){
            return true;
        }
        return false;
     }


     public static function isWriteTypeFail($writeType,$ocrType) :bool
     {
         if($writeType == ChineseWritingType::Any->value){
             return false;
         }
        return $writeType != $ocrType;
     }

     public static function isTypos($ocrNum,$wrongNum) :bool
     {
        if($wrongNum > ($ocrNum * 0.2)){
            return true;
        }
        return false;
     }

     public static function isPageUntidy($ocrNum,$untidyNum) :bool
     {
        if($untidyNum > ($ocrNum * 0.1)){
            return true;
        }
        return false;
     }

     public static function isErrorSen($errorSenNum) :bool
     {
        return $errorSenNum > 10;
     }
}

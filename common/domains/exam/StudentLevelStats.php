<?php

namespace common\domains\exam;


use common\enums\Stage;
use yii\base\Model;

class StudentLevelStats extends Model
{
    public $name;
    public $standard;
    public $num;
    public $rate;
    public array $students;


    public static function handleLevelStats(array $levelStandards,array $levelStandardStudents):array
    {   
        $data = [];
        foreach($levelStandards as $levelStandard){
            $students = !empty($levelStandardStudents[$levelStandard['name']]) ? $levelStandardStudents[$levelStandard['name']] : [];
            $data[] = [
                'name' => $levelStandard['name'],
                'standard' => $levelStandard['standard'],
                'num' => !empty($students) ? count($students) : 0,
                'students' => $students,
                'interval' => $levelStandard['interval'],
            ];
        }
        return $data;
    }
    

    public static function getScoreStandard(array $standards,$score)
    {
        $standardName = '';
        foreach($standards as $item){
            if($score >= $item['standard']){
                return $item['name'];
            }
            $standardName = $item['name'];
        }
        return $standardName;
    }



    public static function getStandards(Stage $stage,$fullScore):array
    { 
        if(Stage::isPrimarySchool($stage->value) || Stage::isMiddleSchool(($stage->value))){
            return self::getMiddleStandards($fullScore);
        }else{
            return  self::getHighStandards($fullScore);
        }
    }


    public static function getEnglishStandards($fullScore):array
    {
        $standards = [
            [
                'name' => '第一档',
                'standard' => ceil($fullScore * 0.8),
                'interval' => [
                    'start' => ceil($fullScore * 0.8),
                    'end' => $fullScore
                ]
            ],
            [
                'name' => '第二档',
                'standard' => ceil($fullScore * 0.6),
                'interval' => [
                    'start' => ceil($fullScore * 0.6),
                    'end' => ceil($fullScore * 0.8),
                ]
            ],
            [
                'name' => '第三档',
                'standard' => ceil($fullScore * 0.4),
                'interval' => [
                    'start' => ceil($fullScore * 0.4),
                    'end' => ceil($fullScore * 0.6),
                ]
            ],
            [
                'name' => '第四档',
                'standard' => ceil($fullScore * 0.2),
                'interval' => [
                    'start' => ceil($fullScore * 0.2),
                    'end' => ceil($fullScore * 0.4),
                ]
            ],
            [
                'name' => '第五档',
                'standard' => ceil($fullScore * 0),
                'interval' => [
                    'start' => 0,
                    'end' => ceil($fullScore * 0.2),
                ]
            ],
        ];
        return $standards;
    }


    public static function getMiddleStandards($fullScore):array
    {
        $standards = [
            [
                'name' => '第一档',
                'standard' => ceil($fullScore * 0.9),
                'interval' => [
                    'start' => ceil($fullScore * 0.9),
                    'end' => $fullScore
                ]
            ],
            [
                'name' => '第二档',
                'standard' => ceil($fullScore * 0.8),
                'interval' => [
                    'start' => ceil($fullScore * 0.8),
                    'end' => ceil($fullScore * 0.9),
                ]
            ],
            [
                'name' => '第三档',
                'standard' => ceil($fullScore * 0.6),
                'interval' => [
                    'start' => ceil($fullScore * 0.6),
                    'end' => ceil($fullScore * 0.8),
                ]
            ],
            [
                'name' => '第四档',
                'standard' => ceil($fullScore * 0.4),
                'interval' => [
                    'start' => ceil($fullScore * 0.4),
                    'end' => ceil($fullScore * 0.6),
                ]
            ],
            [
                'name' => '第五档',
                'standard' => ceil($fullScore * 0),
                'interval' => [
                    'start' => 0,
                    'end' => ceil($fullScore * 0.4),
                ]
            ],
        ];
        return $standards;
    }


    
    public static function getHighStandards($fullScore):array
    {
        $standards = [
            [
                'name' => '一类卷',
                'standard' => ceil($fullScore * 5 / 6),
                'interval' => [
                    'start' => ceil($fullScore * 5 / 6),
                    'end' => $fullScore
                ]
            ],
            [
                'name' => '二类卷',
                'standard' => ceil($fullScore * 4 / 6),
                'interval' => [
                    'start' => ceil($fullScore * 4 / 6),
                    'end' => ceil($fullScore * 5 / 6),
                ]
            ],
            [
                'name' => '三类卷',
                'standard' => ceil($fullScore * 0.5),
                'interval' => [
                    'start' => ceil($fullScore * 0.5),
                    'end' => ceil($fullScore * 4 / 6),
                ]
            ],
            [
                'name' => '四类卷',
                'standard' => ceil($fullScore * 0),
                'interval' => [
                    'start' => 0,
                    'end' => ceil($fullScore * 0.5),
                ]
            ], 
        ];
        return $standards;
    }
}
<?php

namespace common\domains\card;

use common\enums\CardContentType;
use common\enums\CardFormatSource;
use common\enums\CardPageLayout;
use common\models\card\Card;
use common\models\card\CardStruct;
use yii\base\InvalidValueException;
use yii\base\UserException;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;

class EnglishListeningCardXmlGenerator
{
    private const float BOUND_PADDING = 0.0;
    private int $cardId;
    private array $formatData;
    private int $uniqueId = 0;

    /**
     * @param Card $card
     * @param array|CardStruct[] $cardStructMap
     * @throws UserException
     */
    public function __construct(private readonly Card $card, private readonly array $cardStructMap)
    {
        if (empty($this->card->format_data) || empty($this->card->preview_files)) {
            throw new UserException('未找到题卡数据或题卡图片');
        }
        $this->cardId = $card->id;
        $this->formatData = Json::decode($this->card->format_data);
    }

    public function buildXMLArray(): array
    {
        
        $cardInfo = $this->formatData['cardInfo'];
        $cardSetting = $cardInfo['setting'];
        $pageLayoutId = $cardSetting['pageLayoutId'];
        $examNoTypeId = $cardSetting['examNoTypeId'];
        $examNumberLength = $cardSetting['examNumberLength'];

        $pageItems = $this->formatData['coordinateInfo'];

        $pages = [];
        $imageUrlList = $this->card->preview_files;
        foreach ($pageItems as $pageIndex => $pageItem) {
            $pageNum = $pageIndex + 1;
            $pages['page'][] = $this->buildPage($pageNum, $pageItem, $pageLayoutId, $imageUrlList[$pageIndex]);
        }

        $packageAttributes = [
            'id' => $this->cardId,
            'paperId' => $this->cardId,
            'courseCode' => $this->card->course_code,
            'courseName' => $this->card->course?->name,
            'examType' => '',
            'pageCount' => count($pageItems),
            'sheetCount' => '',
            'isZuoye' => 0,
            'isHomeworkPaper' => 0,
            'examNumType' => strtoupper($examNoTypeId),
            'examNumAlign' => 'right',
            'examNumLength' => $examNumberLength,
            'formatSource' => CardFormatSource::AutomaticDefined->value,
            'contentType' => CardContentType::EnglishListening->value,
        ];
        $package = [
            '@attributes' => $packageAttributes,
            'pages' => $pages,
        ];

        return $package;
    }

    private function buildPage($pageNum, $pageItem, $paperType, $imageUrl): array
    {
        $page = [];

        // page attributes
        $pageAttributes = [
            'id' => $this->uniqueId++,
            'sheetIndex' => $pageNum,
            'pageIndex' => $pageNum,
            'faceAB' => 'A',
            'courseCode' => '',
            'colorImageUrl' => $imageUrl,
            'grayImageUrl' => '',
            'paperType' => $paperType,
        ];
        $page['@attributes'] = $pageAttributes;


        // 二维码
        if (isset($pageItem['qrcode'])) {
            if (empty($pageItem['qrcode']['bound'])) {
                throw new InvalidValueException('未找到二维码坐标数据');
            }
            $qrcodeBound = $pageItem['qrcode']['bound'];
            $x = $this->getRealSize($qrcodeBound['x']);
            $y = $this->getRealSize($qrcodeBound['y']);
            $page['qrcode'] = [
                '@attributes' => [
                    'id' => $this->uniqueId++,
                    'x1' => $x - self::BOUND_PADDING,
                    'y1' => $y - self::BOUND_PADDING,
                    'x2' => $x + $this->getRealSize($qrcodeBound['w']) + self::BOUND_PADDING,
                    'y2' => $y + $this->getRealSize($qrcodeBound['h']) + self::BOUND_PADDING,
                ]
            ];
        }

        $omrItems = [];
        // 考号
        if (!empty($pageItem['ID'])) {
            $pointsArray = $pageItem['ID'];
            $omrItems = $this->buildExamNumber($pointsArray);
        }
        // 客观题作答区域
        if (!empty($pageItem['optionInfo'])) {
            $omrItems = ArrayHelper::merge($omrItems, $this->buildObjectiveOmrItems($pageItem['optionInfo']));
        }

        $page['helpPoints'] = $this->buildMarkers($pageItem['markers'] ?? []);
        $page['omrInfo'] = $omrItems;
        $subjectiveInfo = $this->buildAutoOcrAreaItems($pageNum, $pageItem['answerAreaInfo']);

        return array_merge($page, $subjectiveInfo);
    }

    private function buildMarkers(array $markers): array
    {
        $helpPoints = [];
        $markers = ['tl' => $markers['tl'], 'tr' => $markers['tr'], 'br' => $markers['br'], 'bl' => $markers['bl']];
        foreach ($markers as $key => $marker) {
            $x = $this->getRealSize($marker['x']);
            $y = $this->getRealSize($marker['y']);
            $helpPoints['point'][] = [
                '@attributes' => [
                    'id' => $this->uniqueId++,
                    'isMain' => (int)(strtolower($key) === 'tl'),
                    'x1' => $x - self::BOUND_PADDING,
                    'y1' => $y - self::BOUND_PADDING,
                    'x2' => $x + $this->getRealSize($marker['w']) + self::BOUND_PADDING,
                    'y2' => $y + $this->getRealSize($marker['h']) + self::BOUND_PADDING,
                ]
            ];
        }

        return $helpPoints;
    }

    private function buildExamNumber(array $pointsArray): array
    {
        // 考号
        $omrItems = [];
        foreach ($pointsArray as $points) {
            $first = $last = $newPoints = [];
            $count = count($points);
            foreach ($points as $pointIndex => $boundsAll) {
                if ($pointIndex === 0) $first = $boundsAll;
                if ($pointIndex === $count - 1) $last = $boundsAll;
                $x = $this->getRealSize($boundsAll['x']);
                $y = $this->getRealSize($boundsAll['y']);
                $newPoints[] = [
                    '@attributes' => [
                        'id' => $this->uniqueId++,
                        'value' => $boundsAll['digit'],
                        'type' => '',
                        'x1' => $x - self::BOUND_PADDING,
                        'y1' => $y - self::BOUND_PADDING,
                        'x2' => $x + $this->getRealSize($boundsAll['w']) + self::BOUND_PADDING,
                        'y2' => $y + $this->getRealSize($boundsAll['h']) + self::BOUND_PADDING,
                    ],
                ];
            }
            $omrAttributes = [
                'id' => $this->uniqueId++,
                'used' => 'examNumber',
                'name' => '',
                'selectType' => 'single',
                'pointCount' => $count,
                'sequence' => $this->uniqueId++,
                'answer' => '',
                'struct_id' => '',
                'group' => '',
                'fullScore' => '',
                'partScore' => '',
                'errorScore' => '',
                'content' => '',
                'x1' => $this->getRealSize($first['x']),
                'y1' => $this->getRealSize($first['y']),
                'x2' => $this->getRealSize($last['x']) + $this->getRealSize($last['w']),
                'y2' => $this->getRealSize($last['y']) + $this->getRealSize($last['h']),
            ];

            $omrItems['omr'][] = [
                '@attributes' => $omrAttributes,
                'point' => $newPoints,
            ];
        }

        return $omrItems;
    }

    public function buildObjectiveOmrItems(array $optionInfo): array
    {
        $omrItems = [];
        foreach ($optionInfo as $option) {
            $structUuid = $option['uuid'];
            if (empty($this->cardStructMap[$structUuid])) {
                throw new InvalidValueException('未找到对应的结构数据');
            }
            $struct = $this->cardStructMap[$structUuid];
            $optionPoints = $option['bounds'];
            $options = [];
            foreach ($optionPoints as $optionPoint) {
                $x = $this->getRealSize($optionPoint['x']);
                $y = $this->getRealSize($optionPoint['y']);
                $options[] = [
                    '@attributes' => [
                        'id' => $this->uniqueId++,
                        'value' => $optionPoint['txt'],
                        'type' => '',
                        'x1' => $x - self::BOUND_PADDING,
                        'y1' => $y - self::BOUND_PADDING,
                        'x2' => $x + $this->getRealSize($optionPoint['w']) + self::BOUND_PADDING,
                        'y2' => $y + $this->getRealSize($optionPoint['h']) + self::BOUND_PADDING,
                    ],
                ];
            }
            if (count($options) === 0) continue;

            $omrAttributes = [
                'id' => $this->uniqueId++,
                'used' => 'object',
                'name' => $struct->title,
                'selectType' => $struct->type->value,
                'pointCount' => count($optionPoints),
                'sequence' => $this->uniqueId++,
                'answer' => $struct->answer,
                'struct_id' => $struct->id,
                'group' => '', // ???
                'fullScore' => $struct->full_score,
                'partScore' => $struct->part_score ?: '',
                'errorScore' => '',
                'content' => '', // ???
            ];

            $omrItems['omr'][] = [
                '@attributes' => $omrAttributes,
                'point' => $options,
            ];
        }
        return $omrItems;
    }

    private function buildAutoOcrAreaItems($pageNum, $answerAreaInfo): array
    {
        $areaItems = [];
        foreach ($answerAreaInfo as $area) {
            $structUuid = $area['uuid'];
            if (empty($this->cardStructMap[$structUuid])) {
                throw new InvalidValueException("未找到对应的结构数据, struct uuid: $structUuid");
            }
            $struct = $this->cardStructMap[$structUuid];
            $structId = $struct->id;
            $x = $this->getRealSize($area['x']);
            $y = $this->getRealSize($area['y']);
            $area = [
                '@attributes' => [
                    'id' => $this->uniqueId++,
                    'struct_id' => $structId,
                    'name' => $struct->title,
                    'pageIndex' => $pageNum,
                    'x1' => $x - self::BOUND_PADDING,
                    'y1' => $y - self::BOUND_PADDING,
                    'x2' => $x + $this->getRealSize($area['w']) + self::BOUND_PADDING,
                    'y2' => $y + $this->getRealSize($area['h']) + self::BOUND_PADDING,
                    'answer' => $area['word'],
                ]
            ];
            $areaItems['area'][] = $area;
        }


        return ['autoOcrInfo' => $areaItems];
    }

    private function getRealSize(float $size): int
    {
        return round(CardPageLayout::A4_SCALE_RATIO * $size);

    }
}

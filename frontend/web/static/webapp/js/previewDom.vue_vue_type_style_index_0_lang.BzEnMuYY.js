import{d as z,r as w,b as k,w as l,i as O,u as c,E as $,aK as h,o as d,a as o,c as g,e as W,f as i,F as I,q as V,T as q,t as j,U as G,C as U,h as D,j as X,n as F,k as Q,l as R,m as A,B as Y,K as Z,L as ee}from"./index.ZZ6UQeaF.js";/* empty css                *//* empty css                  *//* empty css                        *//* empty css                       */import{_ as te}from"./_plugin-vue_export-helper.DlAUqK2U.js";const oe={class:"pmm2_flex_between"},ae={class:"cardSetting-box"},le={key:0},ne={class:"com"},se={style:{"align-items":"inherit"}},ie={class:"com"},de=["onClick"],re={class:"pmm2_flex_between"},ue={class:"dialog-footer"},ce=z({__name:"papeSize",props:{paperCardPageLayouts:Object,answerSheetPageLayouts:Object,paperCardPageLayoutsNo:Boolean,cardContentTypes:Object,cardPageStyles:Object,dialogWidth:Number},emits:["setCardSetting"],setup(f,{expose:M,emit:v}){const p=f,_=w(!1),a=w({}),r=w([]),C=()=>{a.value=JSON.parse(JSON.stringify(h().setting)),a.value.contentTypeId==1?r.value=p.paperCardPageLayouts:r.value=p.answerSheetPageLayouts,_.value=!0},S=n=>{n.disabled||(a.value.pageLayoutId=n.value)},T=n=>{n==2?(r.value=p.answerSheetPageLayouts,a.value.pageLayoutId=="A4_2"&&(a.value.pageLayoutId=r.value[0][0].value)):r.value=p.paperCardPageLayouts},B=v;function P(n,e){for(const u of n)for(const m of u)if(m.value===e)return m;return{}}const H=()=>{const n=a.value.pageLayoutId;let e=!1,u=h().setting.contentTypeId;a.value.contentTypeId!=u&&(e=!0);let m=!1;if(n!=h().setting.pageLayoutId&&(m=!0),!m&&!e){_.value=!1;return}a.value.pageLayout=P(r.value,n);let t=h().pageStyles,x=t.objectiveQuestionMerging||!1,b=t.DashedLine||!1,E=t.showCreatorInfo||!1;if(t=p.cardPageStyles[n],t.objectiveQuestionMerging=x,t.DashedLine=b,t.showCreatorInfo=E,t.contentLineHeightMM||(t.contentLineHeightMM=Math.round((t==null?void 0:t.contentLineHeight)/4.75)),!t.contentLineHeightMMH){let L=t.contentLineHeightMM;L<=5?t.contentLineHeightMMH=1.5:t.contentLineHeightMMH=(1.5+(L-5)*.3).toFixed(1)}h().pageStyles=t,h().setting=a.value,B("setCardSetting",{iscontentTypeId:e,ispageLayoutId:m}),_.value=!1};return M({opens:C}),(n,e)=>{const u=R("CloseBold"),m=Q,t=A,x=q,b=G,E=U,L=X,J=$;return d(),k(J,{"append-to-body":"",class:"hhypt-dialog-boxs","show-close":!1,modelValue:c(_),"onUpdate:modelValue":e[3]||(e[3]=s=>O(_)?_.value=s:null),title:"题卡设置",width:f.dialogWidth||800},{header:l(({close:s,titleClass:N})=>[o("div",oe,[o("span",{class:F(N)},"页面布局",2),i(t,{onClick:s,underline:!1},{default:l(()=>[i(m,{size:"20"},{default:l(()=>[i(u)]),_:1})]),_:2},1032,["onClick"])])]),footer:l(()=>[o("div",re,[e[8]||(e[8]=o("div",null,null,-1)),o("div",ue,[i(L,{style:{width:"70px"},onClick:e[2]||(e[2]=s=>_.value=!1)},{default:l(()=>e[6]||(e[6]=[D("取消")])),_:1,__:[6]}),i(L,{style:{width:"70px"},type:"primary",onClick:H},{default:l(()=>e[7]||(e[7]=[D(" 确定 ")])),_:1,__:[7]})])])]),default:l(()=>[o("div",ae,[o("ul",null,[f.paperCardPageLayoutsNo?W("",!0):(d(),g("li",le,[e[4]||(e[4]=o("div",{class:"name"},"版面设计",-1)),o("div",ne,[i(b,{modelValue:c(a).contentTypeId,"onUpdate:modelValue":e[0]||(e[0]=s=>c(a).contentTypeId=s),onChange:T},{default:l(()=>[(d(!0),g(I,null,V(f.cardContentTypes,s=>(d(),k(x,{value:s.value,size:"large"},{default:l(()=>[o("div",null,j(s.label),1)]),_:2},1032,["value"]))),256))]),_:1},8,["modelValue"])])])),o("li",se,[e[5]||(e[5]=o("div",{class:"name",style:{"padding-top":"15px"}},"纸张大小",-1)),o("div",ie,[i(b,{modelValue:c(a).pageLayoutId,"onUpdate:modelValue":e[1]||(e[1]=s=>c(a).pageLayoutId=s),class:"radio-boxs pmm2_flex"},{default:l(()=>[(d(!0),g(I,null,V(c(r),(s,N)=>(d(),g("div",{key:N,class:"pmm2_flex_center",style:{"flex-wrap":"wrap"}},[(d(!0),g(I,null,V(s,(y,K)=>(d(),g("div",{key:K,onClick:ge=>S(y),class:"pmm2_flex_center huiA",style:{"flex-direction":"column","margin-right":"20px"}},[i(E,{src:c(a).pageLayoutId==y.value?y.icons.on:y.icons.off,fit:"fill"},null,8,["src"]),i(x,{value:y.value},{default:l(()=>[o("div",null,j(y.label),1)]),_:2},1032,["value"])],8,de))),128))]))),128))]),_:1},8,["modelValue"])])])])])]),_:1},8,["modelValue","width"])}}}),Ce=te(ce,[["__scopeId","data-v-05c04941"]]),pe={class:"pmm2_flex_between"},_e={class:"preview-box scrollBar","align-center":""},me={class:"previewList",style:{width:"100%"}},xe=z({__name:"previewDom",props:{modelValue:{},modelModifiers:{}},emits:["update:modelValue"],setup(f,{expose:M}){const v=w(!1),p=w([]),_=Y(f,"modelValue");return M({show:async r=>{p.value=r},dialogVisible:v}),(r,C)=>{const S=R("CloseBold"),T=Q,B=A,P=U,H=$,n=ee;return d(),k(H,{"append-to-body":"",class:"hhypt-dialog-boxs hhypt-dialog-boxs-previewDom","show-close":!1,modelValue:c(v),"onUpdate:modelValue":C[0]||(C[0]=e=>O(v)?v.value=e:null),title:"预览题卡",width:"1400","align-center":""},{header:l(({close:e,titleClass:u})=>[o("div",pe,[o("span",{class:F(u)},"预览",2),i(B,{onClick:e,underline:!1},{default:l(()=>[i(T,{size:"20"},{default:l(()=>[i(S)]),_:1})]),_:2},1032,["onClick"])])]),default:l(()=>[Z((d(),g("div",_e,[o("div",me,[(d(!0),g(I,null,V(c(p),(e,u)=>(d(),k(P,{class:"demo-image-box",key:u,src:e,"preview-src-list":c(p),"initial-index":u},null,8,["src","preview-src-list","initial-index"]))),128))])])),[[n,_.value]])]),_:1},8,["modelValue"])}}});export{xe as _,Ce as p};

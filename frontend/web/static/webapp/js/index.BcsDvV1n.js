import{d as k1,r as C,V as _1,c as r,u as n,a as t,b as r1,e as K,t as h,f as I,w as Z,j as A1,h as T,i as c1,v as I1,F as U,q as N,X as y1,az as F1,aA as b1,k as B1,K as V1,a1 as S1,aB as J,O as P,o as a,s as H1,C as R1,l as L1,aC as z1}from"./index.ZZ6UQeaF.js";/* empty css                 *//* empty css                        *//* empty css                        *//* empty css                     *//* empty css                  *//* empty css                  *//* empty css                  */import{s as d1}from"./request.CsKOOJzG.js";/* empty css                       */import{_ as D1}from"./_plugin-vue_export-helper.DlAUqK2U.js";import"./index.xsH4HHeE.js";const O1=""+new URL("../img/aipy.BWI8YHu5.png",import.meta.url).href,Z1="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAC4AAAAgCAYAAABts0pHAAAAAXNSR0IArs4c6QAACEtJREFUWEfVWG1wVOUZPU+EQr5IYgIk1qlBGehoC4lCPxxmCNWpmGJIbJkR+diFjh9QKx+2jAMzEktqW4uQSKEVIbsBRW0CAZSQOjK7CQWctgOxZYqF6G6WalJkmo1s2snXPuV53/fu3iSbZMOMP3pnkr3Zezf3vOc9z3nOs4T/04NuBDczO+P8nJ+IvHHeO+g2Zk4nomCsz48auICuPAPX2QAYfQD1gcBgCoMQVu/JKygM3DcTwaJ7MWWoh4+0oODnXJuWiopYix81cN9V9iypQgH1AayBqx+1iDDAvWBZjJzn5QIVT+F5IiodCeTA68y8edcBlM6ZheCMacgnIr/9nlEBZ+bcsnfhO/43BVSzLK99IDav1t/WQratQnDWdMoYDXBmdtTUw/XWcSAlEfSLZ+DPnoh5dvCjBe7+fiUcbaI6YVYD12zbmLfek0Xk3wFU/BglRHQkHvDMvKa6HuXV9dG7J6WB169Ay+25UfBxA5dCOXIevhdPIF0BM8BFGgkDFiCAResU1rvh3gjvlByaNxLwz6+xZ/9RFHj/BIABEJAQBsZ2g8f1gpYsAs97AOuJqHw0wJ0/OgTXuYCRiRSh0bW1CKPxKPthvRsr5gPOQsXWsA7zUYBdx0/C2aCBcwKDxvYA47qBCWPAhQvQcf9DqmbiBx4Isu+R/cjlXl2EljwsWaSOBUKd5pptN0ROcm3/czh8czqVjMQ6M5dW12PzwTrAAj05HfzYarTk3jFKqTBzQdkJeOouRB3ELpecVPDGItCavUpClrvoBZpFOB8EnAuQEY81iuXWvgPXsVogOx1YswH+iZNvoDivdrJr8Rtwhv5jY1q82mh900OgB2ei6um9cDQ163ssGVlWKQDe/Hn81sjMa3dXYFv+bLTMvrc/aNm1ETUuFlj3D/jKTkTBCKt2N3ntSXhzs1DyeiN8r9RHilcVluX1cv9LTyN4z53xNyRm9gBoAvABgDwA0onL4wXufPgAXK1BW6PRjCo7zL8V2LEUTiKqutLO5Y4KrAl1ajdR7hIGWaznTwW2/wTrrIfH0ru4FwDHtWsoPnsWeVcvIz3wV+C/nwArfwZ3zlRaERfwMwH2ratDrrRz6jVOIsBFKr3AhvkILrxbNxhmznO9h3Oudwd3UUs6mx+Hd97soa3x/EXm6jrg0t+BxG5wYjcosQdI6gaefClO4MxcXNaA2mMfDmJbSSUnFahZBTeRZkGO9hB7Hn0RcztDJruIrKx66AN+8B3QU4uHtkbP+8w79ynfFuCU2AUk9QDy+sT2OIFf+jfXLjuI4n7WZ5qKyKDwTtCm7/UHIQ604yg8NY1K3zq32Lprynhg/wvwZkyIzboA37UPGC9guzXT47v1+ePxAFdFeRG+LY2KsYhMbIvgV5eh5a5baMpArQrrS36JglBI75QsQApaGoq8OosAR7EKTlJ4/Q4FvMrIpEczLlIR4I+Vx8H4deDukt9jeWuHTnomsoquhUHOTgEdfEIXZYxkl+v9AOeecyFNCtiyTRN9kTIOXFmGlydl0toYwMO/qwIJ0PFG48K6Al4B9+SRirO+mX2bvbhNMoN9uxX7faBN3wUKvwbvxTb4M1OBzGS0GOvySpORQn37NDxb30SaaUpqASoW9IGfcaJjQcFga/S8z+FX3FrfSiZd4MQeff7Dl0cALp1rw3uoPOk3Pi8DgmZa5xCduyOWKO+ljAXnfwX0rakIFn0Th6/3iOeFzU+uwPPrA8htuhC1RmJg5nRg+7ODG5JIZbdLM2xp3DpfKYxPG8YOP25nz+IazJUGpTrUgMhqWnkks1jNKMHoOScNtPJ+8PxZCnzF9Z0o9rfC8c82FDQHVMbGxAwE586G7E6//OI5w7zHZWRiOYph3ymMDwVcinJLA3x1l1RfZZUuLVcQH48WqjW6yQKiudzkE4m1OWnAtlXwf3kiZAI6YiQkDQZDZRYBvrfSsN2jpCJWyEk9IMeO4YG7H34LjtaQ9BPDuNa10rolEXsGt+/IwN2Qz+XdDsyZAeRkounWLASFDDnGJCA9OQl+e2r0nmaurNQWaPm3JZXlvxkCuLTbwxfg+9VpKFZUmDdSUa4ilmaGYdXOdRLUudz82LNJjPiro4DsivnvzmKQozjaCxoE+F5b1zRaF4dZvhNVMaUiRbm6Dq5znxrAOoJpSRiZRCZ72+hmScloPTIs26Nv5Jo1pxJIdJiSBLhfgDsrQxedAHft0a3eskFljT3Asp1wT4ql8UAH+xZV4zZiSOFHo+MAVxEHufsWUMqXgLZ2oPlTPUSYQVk8P9ItBbApWut6hHHS4NlZAjgW4mbRfeMpZvcebYOia+UsZgpasiuGVKRVb2mAxxSl2khLKTI7CoAJN4FWfxvBhV9XTiFdT8ZmkVXB+QDmHv0z8po+ArdeNd5vPN+0fgGum5kUe2SqBLKzgDe2amsU4FWv9rdCkYnMnEt3xWD8Sojdjx6CI9QV7WUGuCpMeej6OfAvmjE41FufEEe6fl56/C9wuP8AtH2mvFt/aWRF4f4at+YB3vpTdNxzF6b88RTa9+0xPm61+y5gTC946W8HaFweeOwifGUnzWxtUa1pUZrNSQYOmdw9sE3HavkfXkbp+Y/hqPGoBegRzmQVtRgDWfktwHlfBW1/FutOncK2fbuVRETnklMoqUvJLSZw55ZGVLZe07KWXzbsqtEUTYf/gWlDsx1rMWYH1jZdwsxQJwqaLwP/+gyQ8CVjYHaWJio5Scvlvm/giM+Hhd56YJzoukfHW5FJAgOFG+FOzrR1TtE3ANnm4Y4gEUkrv+HDLCTWc+TrNfn/knGG+0I18iXqiDPnDaP8gj/4PzCkuV32K9mHAAAAAElFTkSuQmCC";function T1(M){return d1({url:"/marking/exam/detail",method:"post",data:M})}function M1(M){return d1({url:"/marking/exam/score",method:"post",data:M})}const E1={key:0,class:"marking-box"},W1={class:"marking-hand-box"},Q1={class:"mian-box pmm2_flex_between"},U1={class:"left-box"},N1={class:"con pmm2_flex_acenter"},P1={class:"mz"},X1={class:"right-box pmm2_flex_acenter"},j1={class:"v1"},Y1={class:"v2",style:{"padding-left":"10px"}},K1={style:{color:"#F00"}},J1={class:"marking-main-box pmm2_flex_between"},G1={class:"left-box"},q1={class:"top-box"},$1={class:"ul scrollBar"},ee={class:"t1"},te={key:0,class:"t1",style:{"text-align":"center"}},se={key:1,class:"t1",style:{"text-align":"center"}},le={class:"t2"},ne={key:0,style:{color:"#00AE65"}},oe=["onClick"],ie=["onClick"],ae={class:"center-box"},ue={key:0,class:"center-com scrollBar"},re={class:"right-box"},ce={class:"box box1 pmm2_flex_between"},de={class:"box box2"},me={class:"divide-box"},ve=["onClick"],Ce={class:"item-com"},fe={class:"divide-input pmm2_flex_center"},pe={class:"pmm2_flex_center"},xe={class:"bottom-box pmm2_flex_between"},ge={key:0,style:{opacity:"0.5"}},we={key:2,style:{opacity:"0.5"}},he={key:1,class:"ai-marking-img huiA"},ke={key:2,class:"ai-marking-com"},_e={class:"com scrollBar"},Ae=["innerHTML"],Ie={key:1,class:"empty-box"},ye={class:"pmm2_flex_center",style:{"flex-direction":"column"}},Fe=k1({__name:"index",setup(M){const G=C(!0),u=C(-1),d=C(-1),f=C({}),m=C([]),b=C({}),E=C({}),q=_1(),$=q.params.examId,e1=q.params.structId,m1=S1(),W=()=>{window.history.replaceState(null,"",window.location.pathname),m1.go(-1)},y=C(0),x=C(0);(()=>{T1({examId:$}).then(s=>{s.code!=0&&J({title:"",message:s.msg,type:"warning"});let e=[],o=s.data||{},i=o.scanData||[],p=i.length;if(G.value=p==0,E.value=o.examInfo||{},p>0){d.value=-1;let z=o.students||[],S=o.structs||[];for(let w=0;w<p;w++){let v=i[w],c=S.find(g=>g.id==v.structId)||{},F=v.studentStructInfo.length;for(let g=0;g<F;g++){let D=v.studentStructInfo[g],O=z.find(Q=>Q.student_id==D.studentId)||{};v.studentStructInfo[g].student_name=O.student_name||"",v.studentStructInfo[g].answer_images=O.answer_images||[],e1==v.structId&&(u.value=w),e1==v.structId&&D.score===""&&d.value==-1&&(d.value=g)}e.push({...v,...c})}u.value==-1&&(u.value=0),d.value==-1&&(d.value=0);let _=e[u.value]||{studentStructInfo:[]};b.value=_,f.value=_.studentStructInfo[d.value];let A=0,H=f.value.aiResult||{};H.status==3&&H.result&&E.value.ai_score_status==3&&(y.value=1,A=f.value.aiResult.score||0),x.value=parseFloat(f.value.score||A),m.value=e}})})();const B=(s,e)=>{d.value=e,f.value=s;let o=0;y.value=0,s.aiResult&&s.aiResult.result&&s.aiResult.status==3&&E.value.ai_score_status==3&&(y.value=1,o=f.value.aiResult.score||0),x.value=parseFloat(s.score||o)},R=C(!1),t1=()=>{R.value=!0,m.value[u.value].studentStructInfo[d.value].score!==""?P.confirm("是否提交回评成绩？","提示",{confirmButtonText:"确认提交",cancelButtonText:"继续阅卷",type:"warning"}).then(()=>{s1()}).catch(()=>{R.value=!1}):s1()},v1=()=>{let s=m.value.length;if(u.value+1>=s)P.confirm("本次作业所有学生试题已评阅完成","",{confirmButtonText:"返回作业列表",cancelButtonText:"回评",type:"success"}).then(()=>{W()}).catch(()=>{});else{u.value+=1;let e=m.value[u.value],o=e.studentStructInfo.findIndex(i=>i.score==="");o=o==-1?0:o,B(e.studentStructInfo[o],o),b.value=e}},s1=()=>{let s=m.value[u.value],{studentId:e,blockInfo:o}=f.value;m.value[u.value].studentStructInfo[d.value].score=x.value;let i=m.value[u.value].studentStructInfo,p=i.length,z={examId:$,structId:s.structId,studentId:e,blockId:o.id,score:x.value,studentNum:p,scoreNum:d.value+1};M1(z).then(S=>{if(R.value=!1,x.value=0,S.code!=0)return J({title:"",message:S.msg,type:"warning"});let _=-1,A=[];for(let c=0;c<p;c++)i[c].score===""&&(_==-1&&(_=c),A.push(c));if(A.length==0){if(m.value[u.value].is_done=!0,m.value.length-1==u.value)P.confirm("本次作业所有学生试题已评阅完成","",{confirmButtonText:"返回作业列表",cancelButtonText:"回评",type:"success"}).then(()=>{W()}).catch(()=>{});else{let F=`<p>
					<svg t="1733818982564" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4264" width="20" height="20"><path d="M512 85.333333c235.637333 0 426.666667 191.029333 426.666667 426.666667S747.637333 938.666667 512 938.666667 85.333333 747.637333 85.333333 512 276.362667 85.333333 512 85.333333z m182.613333 297.354667a32 32 0 0 0-45.258666 0.032L458.922667 573.44l-84.341334-83.989333a32 32 0 0 0-45.162666 45.344l106.986666 106.549333a32 32 0 0 0 45.226667-0.064l213.013333-213.333333a32 32 0 0 0-0.032-45.258667z" fill="#67C23A" p-id="4265"></path></svg>
					本次作业的第${s.title}题已评阅完成<p><p style=" font-size:12px; color: #999;padding-left: 24px;margin-top: 5px;">是否继续评阅下一题？<p>
					`;P.alert(F,"",{confirmButtonText:"继续",cancelButtonText:"返回题目列表",dangerouslyUseHTMLString:!0}).then(()=>{v1()}).catch(()=>{})}return}let H=d.value+1;function w(c){return c>=p?_:A.includes(c)?c:(c++,w(c))}let v=w(H);B(i[v],v)})},l1=s=>{let e=d.value;s=="down"?e++:e--,B(b.value.studentStructInfo[e],e)},n1=C([]),X=C(!1),C1=()=>{X.value=!0,n1.value=f.value.answer_images},f1=()=>{X.value=!1},p1=s=>{let e=m.value[s]||{studentStructInfo:[]},o=e.studentStructInfo.findIndex(i=>i.score==="");o=o==-1?0:o,B(e.studentStructInfo[o],o),b.value=e},o1=C(null),j=s=>{var p;let e=m.value[u.value],o=parseFloat(e.full_score),i=String(x.value||0).split(".");if(i[0]==0?i[0]=s==".5"?"0"+s:s:i[0]+=s,i=i.join("."),o<parseFloat(i)){J({title:"提示",message:"分值不能超过满分："+o+"分",type:"warning"});return}x.value=parseFloat(i),(p=o1.value)==null||p.focus()};let L=1,V=0;const i1=C(null),Y=s=>{let e="";s=="+"?L+=.1:s=="-"?(L-=.1,L<=0&&(L=.1)):(V+=90,V==360&&(V=0)),i1.value.forEach((o,i)=>{V==90?e="translateX("+(50+i*100)+"%)":V==270?e="translateX("+-(50+i*100)+"%)":e="",o.style.transform=`scale(${L})  rotate(${V}deg) ${e}`})},x1=()=>{document.fullscreenElement?g1():w1()},g1=()=>{document.exitFullscreen?document.exitFullscreen():document.mozCancelFullScreen?document.mozCancelFullScreen():document.webkitExitFullscreen?document.webkitExitFullscreen():document.msExitFullscreen&&document.msExitFullscreen()},w1=()=>{let s=document.documentElement;s.requestFullscreen?s.requestFullscreen():s.mozRequestFullScreen?s.mozRequestFullScreen():s.webkitRequestFullscreen?s.webkitRequestFullscreen():s.msRequestFullscreen&&s.msRequestFullscreen()};return(s,e)=>{var w,v,c,F,g,D,O,Q,a1,u1;const o=A1,i=H1,p=I1,z=R1,S=F1,_=L1("CaretRight"),A=B1,H=z1("latexs");return n(G)?(a(),r("view",Ie,[t("div",ye,[e[30]||(e[30]=t("img",{style:{width:"200px"},src:"https://oss.hhypt.com/resources/mini-parent-helper/err.png"},null,-1)),e[31]||(e[31]=t("span",{style:{"font-size":"16px"}},"暂无作业",-1)),e[32]||(e[32]=t("span",{style:{color:"#999",margin:"10px auto"}},"当前评阅任务仅任课老师可查看",-1)),I(o,{type:"primary",style:{width:"120px"},onClick:e[14]||(e[14]=l=>W())},{default:Z(()=>e[29]||(e[29]=[T("返回作业列表")])),_:1,__:[29]})])])):(a(),r("div",E1,[t("div",W1,[t("div",{class:"back huiA pmm2_flex_acenter",onClick:e[0]||(e[0]=l=>W())},e[15]||(e[15]=[t("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 20 20",fill:"none"},[t("rect",{x:"1",y:"1",width:"18",height:"18",rx:"4",fill:"#2672FF"}),t("path",{d:"M8.74326 5.25045L5.23012 9.36617C4.92329 9.70007 4.92329 10.2413 5.23012 10.5752L8.97348 14.7496C9.2803 15.0835 9.77767 15.0835 10.0845 14.7496C10.3913 14.4157 10.3913 13.8745 10.0845 13.5406L7.68234 10.8257L15.2143 10.8257C15.6484 10.8257 16 10.4431 16 9.97067C16 9.49825 15.6484 9.11561 15.2143 9.11561L7.68234 9.11561L9.85427 6.45945C10.0075 6.29271 10.0845 6.07382 10.0845 5.85492C10.0845 5.63602 10.0079 5.41714 9.85427 5.25039C9.54745 4.91649 9.05008 4.91656 8.74326 5.25045Z",fill:"white",stroke:"#2672FF","stroke-width":"0.5"})],-1),t("span",null,"返回",-1)])),t("div",Q1,[t("div",U1,h(((w=n(E))==null?void 0:w.title)||"-"),1),t("div",N1,[t("div",P1,h(((v=n(f))==null?void 0:v.student_name)||""),1),I(o,{onClick:C1,type:"primary",plain:""},{default:Z(()=>e[16]||(e[16]=[T("查看答题卡")])),_:1,__:[16]})]),t("div",X1,[t("div",j1,"第"+h((c=n(m)[n(u)])==null?void 0:c.title)+"题（满分"+h(parseFloat((F=n(m)[n(u)])==null?void 0:F.full_score))+"分） ",1),t("div",Y1,[e[17]||(e[17]=T("批阅进度（")),t("span",K1,h(n(d)+1),1),T("/"+h(((g=n(m)[n(u)])==null?void 0:g.studentNum)||0)+"） ",1)])])])]),t("div",J1,[t("div",G1,[t("div",q1,[I(p,{class:"marking-main-top-box",modelValue:n(u),"onUpdate:modelValue":e[1]||(e[1]=l=>c1(u)?u.value=l:null),style:{width:"100%"},onChange:p1},{default:Z(()=>[(a(!0),r(U,null,N(n(m),(l,k)=>(a(),r1(i,{key:l.id,label:"第"+l.title+"题",value:k},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),t("div",$1,[(a(!0),r(U,null,N(n(b).studentStructInfo,(l,k)=>(a(),r("div",{class:"li pmm2_flex_between",key:k},[t("div",ee,h(l.student_name),1),l.score===""?(a(),r("div",te,"--")):(a(),r("div",se,h(l.score==".5"?"0.5":l.score)+"分",1)),t("div",le,[k==n(d)?(a(),r("span",ne,"批阅中")):l.score!==""?(a(),r("span",{key:1,class:"huiA",onClick:h1=>B(l,k)},"回评",8,oe)):(a(),r("span",{key:2,class:"huiA",onClick:h1=>B(l,k)},"去批阅",8,ie))])]))),128))])]),t("div",ae,[((O=(D=n(f))==null?void 0:D.answerImages)==null?void 0:O.length)>0?(a(),r("div",ue,[(a(!0),r(U,null,N(n(f).answerImages,(l,k)=>(a(),r("div",{class:"img-list",ref_for:!0,ref_key:"imgListRef",ref:i1,key:k},[I(z,{class:"img",src:l,fit:"cover","preview-teleported":"","preview-src-list":n(f).answerImages},null,8,["src","preview-src-list"])]))),128))])):K("",!0)]),t("div",re,[t("div",ce,[t("div",{class:"li huiA2",onClick:e[2]||(e[2]=l=>Y("-"))},e[18]||(e[18]=[t("svg",{class:"svg",xmlns:"http://www.w3.org/2000/svg",width:"18",height:"18",viewBox:"0 0 18 18",fill:"none"},[t("path",{d:"M16.1589 15.9255L13.7171 13.4837C14.9732 12.1697 15.75 10.3944 15.75 8.4375C15.75 4.4055 12.4695 1.125 8.4375 1.125C4.4055 1.125 1.125 4.4055 1.125 8.4375C1.125 12.4695 4.4055 15.75 8.4375 15.75C10.1076 15.75 11.6443 15.1807 12.8767 14.2346L15.363 16.7209C15.4151 16.7733 15.4771 16.8149 15.5453 16.8433C15.6136 16.8717 15.6868 16.8863 15.7607 16.8863C15.8346 16.8863 15.9078 16.8717 15.976 16.8433C16.0443 16.8149 16.1063 16.7733 16.1584 16.7209C16.2639 16.6155 16.3232 16.4725 16.3233 16.3233C16.3235 16.1741 16.2643 16.0311 16.1589 15.9255ZM2.25 8.4375C2.25 5.02594 5.02594 2.25 8.4375 2.25C11.8491 2.25 14.625 5.02594 14.625 8.4375C14.625 11.8491 11.8491 14.625 8.4375 14.625C5.02594 14.625 2.25 11.8491 2.25 8.4375Z"}),t("path",{d:"M9 7.875H11.0002C11.1494 7.875 11.2925 7.93426 11.398 8.03975C11.5035 8.14524 11.5627 8.28832 11.5627 8.4375C11.5627 8.58668 11.5035 8.72976 11.398 8.83525C11.2925 8.94074 11.1494 9 11.0002 9H9H7.875H5.90625C5.75707 9 5.61399 8.94074 5.5085 8.83525C5.40301 8.72976 5.34375 8.58668 5.34375 8.4375C5.34375 8.28832 5.40301 8.14524 5.5085 8.03975C5.61399 7.93426 5.75707 7.875 5.90625 7.875H7.875H9Z"})],-1),t("span",null,"缩小",-1)])),t("div",{class:"li huiA2",onClick:e[3]||(e[3]=l=>Y("+"))},e[19]||(e[19]=[t("svg",{class:"svg",xmlns:"http://www.w3.org/2000/svg",width:"18",height:"18",viewBox:"0 0 18 18",fill:"none"},[t("path",{d:"M16.1589 15.9255L13.7171 13.4837C14.9732 12.1697 15.75 10.3944 15.75 8.4375C15.75 4.4055 12.4695 1.125 8.4375 1.125C4.4055 1.125 1.125 4.4055 1.125 8.4375C1.125 12.4695 4.4055 15.75 8.4375 15.75C10.1076 15.75 11.6443 15.1807 12.8767 14.2346L15.363 16.7209C15.4151 16.7733 15.4771 16.8149 15.5453 16.8433C15.6136 16.8717 15.6868 16.8863 15.7607 16.8863C15.8346 16.8863 15.9078 16.8717 15.976 16.8433C16.0443 16.8149 16.1063 16.7733 16.1584 16.7209C16.2639 16.6155 16.3232 16.4725 16.3233 16.3233C16.3235 16.1741 16.2643 16.0311 16.1589 15.9255ZM2.25 8.4375C2.25 5.02594 5.02594 2.25 8.4375 2.25C11.8491 2.25 14.625 5.02594 14.625 8.4375C14.625 11.8491 11.8491 14.625 8.4375 14.625C5.02594 14.625 2.25 11.8491 2.25 8.4375Z"}),t("path",{d:"M11.0002 7.875H9V5.90625C9 5.75707 8.94074 5.61399 8.83525 5.5085C8.72976 5.40301 8.58668 5.34375 8.4375 5.34375C8.28832 5.34375 8.14524 5.40301 8.03975 5.5085C7.93426 5.61399 7.875 5.75707 7.875 5.90625V7.875H5.90625C5.75707 7.875 5.61399 7.93426 5.5085 8.03975C5.40301 8.14524 5.34375 8.28832 5.34375 8.4375C5.34375 8.58668 5.40301 8.72976 5.5085 8.83525C5.61399 8.94074 5.75707 9 5.90625 9H7.875V10.9688C7.875 11.1179 7.93426 11.261 8.03975 11.3665C8.14524 11.472 8.28832 11.5312 8.4375 11.5312C8.58668 11.5312 8.72976 11.472 8.83525 11.3665C8.94074 11.261 9 11.1179 9 10.9688V9H11.0002C11.1494 9 11.2925 8.94074 11.398 8.83525C11.5035 8.72976 11.5627 8.58668 11.5627 8.4375C11.5627 8.28832 11.5035 8.14524 11.398 8.03975C11.2925 7.93426 11.1494 7.875 11.0002 7.875Z"})],-1),t("span",null,"放大",-1)])),t("div",{class:"li huiA2",onClick:e[4]||(e[4]=l=>Y("x"))},e[20]||(e[20]=[t("svg",{class:"svg",xmlns:"http://www.w3.org/2000/svg",width:"18",height:"18",viewBox:"0 0 18 18",fill:"none"},[t("path",{d:"M11.7538 2.31903V2.36903H11.8038H14.1488C15.704 2.36903 16.95 3.67677 16.95 5.17112V5.86767C16.95 6.04671 16.8906 6.19194 16.7904 6.29228C16.6901 6.39261 16.5452 6.45198 16.3666 6.45198C16.1876 6.45198 16.0424 6.39259 15.942 6.29226C15.8417 6.19192 15.7823 6.04671 15.7823 5.86767V5.17021C15.7823 4.2566 15.0634 3.53673 14.1488 3.53673H11.8038H11.7538V3.58673V4.60089C11.7538 4.73316 11.6605 4.8452 11.53 4.90325C11.3996 4.9612 11.2461 4.9587 11.138 4.87724L9.17347 3.29426L9.1736 3.2941L9.16984 3.29159C9.09215 3.2398 9.05 3.14672 9.05 3.04041C9.05 2.93507 9.09183 2.82177 9.17571 2.73601L11.1385 1.15444C11.1385 1.15443 11.1385 1.15442 11.1386 1.1544C11.2838 1.03811 11.4378 1.02719 11.553 1.08146C11.6684 1.13584 11.7538 1.25969 11.7538 1.4321V2.31903Z",stroke:"white","stroke-width":"0.1"}),t("path",{d:"M14.5775 7.21616L14.5767 7.23099V7.24584V14.2842C14.5767 14.9422 14.0398 15.515 13.3458 15.515H3.23083C2.57346 15.515 2 14.978 2 14.2842V7.24584C2 6.58846 2.53703 6.01501 3.23083 6.01501H13.4042C14.0681 6.01501 14.6171 6.55104 14.5775 7.21616Z",fill:"white",stroke:"#666666"})],-1),t("span",null,"旋转",-1)])),t("div",{class:"li huiA2",onClick:x1},e[21]||(e[21]=[t("svg",{class:"svg",xmlns:"http://www.w3.org/2000/svg",width:"18",height:"18",viewBox:"0 0 18 18",fill:"none"},[t("path",{d:"M14.4 15.75H3.6C3.42272 15.75 3.24717 15.7151 3.08338 15.6472C2.91959 15.5794 2.77076 15.48 2.64541 15.3546C2.52005 15.2292 2.42061 15.0804 2.35276 14.9166C2.28492 14.7528 2.25 14.5773 2.25 14.4V3.6C2.25 3.42272 2.28492 3.24717 2.35276 3.08338C2.42061 2.91959 2.52005 2.77076 2.64541 2.64541C2.77076 2.52005 2.91959 2.42061 3.08338 2.35276C3.24717 2.28492 3.42272 2.25 3.6 2.25H14.4C14.5773 2.25 14.7528 2.28492 14.9166 2.35276C15.0804 2.42061 15.2292 2.52005 15.3546 2.64541C15.48 2.77076 15.5794 2.91959 15.6472 3.08338C15.7151 3.24717 15.75 3.42272 15.75 3.6V14.4C15.75 14.5773 15.7151 14.7528 15.6472 14.9166C15.5794 15.0804 15.48 15.2292 15.3546 15.3546C15.2292 15.48 15.0804 15.5794 14.9166 15.6472C14.7528 15.7151 14.5773 15.75 14.4 15.75ZM14.7375 3.6C14.7375 3.55568 14.7288 3.51179 14.7118 3.47084C14.6948 3.4299 14.67 3.39269 14.6386 3.36135C14.6073 3.33001 14.5701 3.30515 14.5292 3.28819C14.4882 3.27123 14.4443 3.2625 14.4 3.2625H3.6C3.55568 3.2625 3.51179 3.27123 3.47084 3.28819C3.4299 3.30515 3.39269 3.33001 3.36135 3.36135C3.33001 3.39269 3.30515 3.4299 3.28819 3.47084C3.27123 3.51179 3.2625 3.55568 3.2625 3.6V14.4C3.2625 14.4443 3.27123 14.4882 3.28819 14.5292C3.30515 14.5701 3.33001 14.6073 3.36135 14.6386C3.39269 14.67 3.4299 14.6948 3.47084 14.7118C3.51179 14.7288 3.55568 14.7375 3.6 14.7375H14.4C14.4443 14.7375 14.4882 14.7288 14.5292 14.7118C14.5701 14.6948 14.6073 14.67 14.6386 14.6386C14.67 14.6073 14.6948 14.5701 14.7118 14.5292C14.7288 14.4882 14.7375 14.4443 14.7375 14.4V3.6Z"}),t("path",{d:"M7.48105 5.625H5.6248V7.48125C5.6248 7.61552 5.57147 7.74429 5.47653 7.83923C5.38159 7.93417 5.25282 7.9875 5.11855 7.9875C4.98429 7.9875 4.85552 7.93417 4.76058 7.83923C4.66564 7.74429 4.6123 7.61552 4.6123 7.48125V5.11875C4.6123 4.98449 4.66564 4.85572 4.76058 4.76078C4.85552 4.66584 4.98429 4.6125 5.11855 4.6125H7.48105C7.61532 4.6125 7.74409 4.66584 7.83903 4.76078C7.93397 4.85572 7.9873 4.98449 7.9873 5.11875C7.9873 5.25302 7.93397 5.38179 7.83903 5.47673C7.74409 5.57167 7.61532 5.625 7.48105 5.625ZM12.8811 13.3875H10.5186C10.3843 13.3875 10.2555 13.3342 10.1606 13.2392C10.0656 13.1443 10.0123 13.0155 10.0123 12.8813C10.0123 12.747 10.0656 12.6182 10.1606 12.5233C10.2555 12.4283 10.3843 12.375 10.5186 12.375H12.3748V10.5188C12.3748 10.3845 12.4281 10.2557 12.5231 10.1608C12.618 10.0658 12.7468 10.0125 12.8811 10.0125C13.0153 10.0125 13.1441 10.0658 13.239 10.1608C13.334 10.2557 13.3873 10.3845 13.3873 10.5188V12.8813C13.3873 13.0155 13.334 13.1443 13.239 13.2392C13.1441 13.3342 13.0153 13.3875 12.8811 13.3875Z"})],-1),t("span",null,"全屏",-1)]))]),t("div",de,[e[27]||(e[27]=t("div",{class:"bt"},[t("div",{class:"xian"}),t("span",null,"打分区")],-1)),t("div",me,[(a(),r(U,null,N(9,l=>t("div",{class:"item-box",key:l,onClick:k=>j(String(l))},[t("div",Ce,h(l),1)],8,ve)),64)),t("div",{class:"item-box",onClick:e[5]||(e[5]=l=>j("0"))},e[22]||(e[22]=[t("div",{class:"item-com"}," 0 ",-1)])),t("div",{class:"item-box",onClick:e[6]||(e[6]=l=>j(".5"))},e[23]||(e[23]=[t("div",{class:"item-com"},"0.5",-1)])),t("div",{class:"item-box item-box2",onClick:e[7]||(e[7]=l=>x.value=0)},e[24]||(e[24]=[t("div",{class:"item-com"},"重置",-1)]))]),t("div",fe,[e[25]||(e[25]=t("div",{class:"t1"},"得分",-1)),I(S,{class:"number-box",controls:!1,modelValue:n(x),"onUpdate:modelValue":e[8]||(e[8]=l=>c1(x)?x.value=l:null),step:.5,min:0,max:parseFloat(((Q=n(m)[n(u)])==null?void 0:Q.full_score)||0),ref_key:"elInputNumberscoreValueRef",ref:o1,"step-strictly":!0,onKeydown:y1(t1,["enter","native"])},null,8,["modelValue","max"]),e[26]||(e[26]=t("div",{class:"t2"},"分",-1))]),t("div",pe,[I(o,{type:"primary",loading:n(R),style:{width:"80%"},onClick:e[9]||(e[9]=l=>t1())},{default:Z(()=>[T(h(n(R)?"提交中":"提交"),1)]),_:1},8,["loading"])]),t("div",xe,[n(d)==0?(a(),r("span",ge,"回评上一份")):(a(),r("span",{key:1,class:"huiA",onClick:e[10]||(e[10]=l=>l1("up"))},"回评上一份")),n(d)==((u1=(a1=n(b))==null?void 0:a1.studentStructInfo)==null?void 0:u1.length)-1?(a(),r("span",we,"批阅上一份")):(a(),r("span",{key:3,class:"huiA",onClick:e[11]||(e[11]=l=>l1("down"))},"批阅下一份"))])])])]),n(X)?(a(),r1(n(b1),{key:0,onClose:f1,urlList:n(n1)},null,8,["urlList"])):K("",!0),n(y)==1?(a(),r("div",he,[t("img",{src:O1,alt:"",onClick:e[12]||(e[12]=l=>y.value=2)})])):n(y)==2?(a(),r("div",ke,[t("div",{class:"colse pmm2_flex_center huiA",onClick:e[13]||(e[13]=l=>y.value=1)},[I(A,{color:"#2672FF"},{default:Z(()=>[I(_)]),_:1})]),e[28]||(e[28]=t("div",{class:"top pmm2_flex_acenter"},[t("img",{src:Z1,alt:"",style:{width:"23px"}}),t("span",null,"智能评语")],-1)),t("div",_e,[V1(t("div",{innerHTML:n(f).aiResult.result||""},null,8,Ae),[[H]])])])):K("",!0)]))}}}),Ee=D1(Fe,[["__scopeId","data-v-e5bc2a05"]]);export{Ee as default};

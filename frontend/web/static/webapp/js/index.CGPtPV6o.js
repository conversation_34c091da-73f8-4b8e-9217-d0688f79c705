import{d as ut,r as me,b as _a,w as de,i as Yr,u as oe,E as ht,aK as Ue,o as Ae,a as q,f as xe,c as Re,q as ta,F as na,C as ts,T as ns,t as Ye,U as ss,h as Qe,j as xt,n as pa,az as tc,k as Wt,l as va,m as Gt,p as Tr,e as i0,aL as is,P as nc,N as cs,S as sc,I as ic,G as cc,W as fc,K as J0,L as oc,au as Z0,M as lc,J as q0}from"./index.ZZ6UQeaF.js";/* empty css                *//* empty css                  *//* empty css                        *//* empty css                          *//* empty css                       */import{_ as dt}from"./_plugin-vue_export-helper.DlAUqK2U.js";/* empty css                 *//* empty css                        */import{a as uc,b as hc}from"./downloads.B3gkFdce.js";import{d as xc,g as dc,a as pc,b as vc,s as gc,c as mc,e as _c,f as Ec,p as Tc}from"./index.D9q4XR9F.js";/* empty css                   *//* empty css                      *//* empty css                     *//* empty css                  *//* empty css                  *//* empty css                *//* empty css                   *//* empty css                    */import{a as kc,u as wc}from"./index.6W4rOsHv.js";import"./index.xsH4HHeE.js";import"./request.CsKOOJzG.js";import"./index.pTwlcQaF.js";import"./question.IyuOoK5G.js";const Ac={class:"pmm2_flex_between"},Fc={class:"cardSetting-box"},Sc={style:{"align-items":"inherit"}},Cc={class:"com"},yc=["onClick"],Dc={class:"pmm2_flex_between"},Oc={class:"dialog-footer"},Ic=ut({__name:"papeSize",props:{answerSheetPageLayouts:Object,cardPageStyles:Object},emits:["setCardSetting"],setup(e,{expose:a,emit:r}){const n=e,t=me(!1),s=me({}),i=me([]),c=me(!0),f=()=>{var h,d;s.value=JSON.parse(JSON.stringify(Ue().setting)),i.value=n.answerSheetPageLayouts,(h=s==null?void 0:s.value)!=null&&h.pageLayoutId?c.value=!0:(s.value.pageLayoutId=(d=i==null?void 0:i.value[0][0])==null?void 0:d.value,c.value=!1),t.value=!0},l=h=>{h.disabled||(s.value.pageLayoutId=h.value)},o=r;function u(h,d){for(const x of h)for(const g of x)if(g.value===d)return g;return{}}const p=()=>{const h=s.value.pageLayoutId;s.value.pageLayout=u(i.value,h);let d=n.cardPageStyles[h];Ue().pageStyles=d,Ue().setting=s.value,o("setCardSetting",{}),t.value=!1};return a({opens:f}),(h,d)=>{const x=ts,g=ns,w=ss,y=xt,m=ht;return Ae(),_a(m,{"append-to-body":"",class:"hhypt-dialog-boxs","show-close":!1,modelValue:oe(t),"onUpdate:modelValue":d[1]||(d[1]=O=>Yr(t)?t.value=O:null),title:"题卡设置",width:"800","close-on-click-modal":!1,"close-on-press-escape":!1},{header:de(({close:O,titleClass:P})=>[q("div",Ac,[q("span",{class:pa(P)},"页面布局",2)])]),footer:de(()=>[q("div",Dc,[d[4]||(d[4]=q("div",{class:"tig"},null,-1)),q("div",Oc,[xe(y,{style:{width:"70px"},type:"primary",onClick:p},{default:de(()=>d[3]||(d[3]=[Qe(" 确定 ")])),_:1,__:[3]})])])]),default:de(()=>[q("div",Fc,[q("ul",null,[q("li",Sc,[d[2]||(d[2]=q("div",{class:"name",style:{"padding-top":"15px"}},"纸张大小",-1)),q("div",Cc,[xe(w,{modelValue:oe(s).pageLayoutId,"onUpdate:modelValue":d[0]||(d[0]=O=>oe(s).pageLayoutId=O),class:"radio-boxs pmm2_flex"},{default:de(()=>[(Ae(!0),Re(na,null,ta(oe(i),(O,P)=>(Ae(),Re("div",{key:P,class:"pmm2_flex_center",style:{"flex-wrap":"wrap"}},[(Ae(!0),Re(na,null,ta(O,(D,S)=>(Ae(),Re("div",{key:S,onClick:U=>l(D),class:"pmm2_flex_center huiA",style:{"flex-direction":"column","margin-right":"20px"}},[xe(x,{src:oe(s).pageLayoutId==D.value?D.icons.on:D.icons.off,fit:"fill"},null,8,["src"]),xe(g,{value:D.value},{default:de(()=>[q("div",null,Ye(D.label),1)]),_:2},1032,["value"])],8,yc))),128))]))),128))]),_:1},8,["modelValue"])])])])])]),_:1},8,["modelValue"])}}}),Rc=dt(Ic,[["__scopeId","data-v-788004df"]]),Nc={class:"pmm2_flex_between"},Lc={class:"upload-demo-box",style:{margin:"20px 0"}},Pc={class:"item",style:{"margin-bottom":"10px"}},bc={class:"item"},Bc={class:"bottom-box"},Mc=ut({__name:"downAudio",setup(e,{expose:a}){const r=me(!1);let n="";const t=me(1),s=me(2);let i="";const c=me(!1);a({opens:(h,d)=>{n=h,i=d,r.value=!0}});const l=()=>{c.value||(o.value="音频生成中...",c.value=!0,u())},o=me("下载音频"),u=async()=>{let h=await xc({uuid:n,interval:t.value,repeatNum:s.value});if(h.code===0)try{o.value="下载音频中...",await uc(h.data,i),setTimeout(()=>{o.value="下载音频",c.value=!1,r.value=!1},1e3)}catch{return p("请重试")}else if(h.code===2)setTimeout(()=>{u()},1e3);else return p(h==null?void 0:h.msg)},p=h=>{c.value=!1,o.value="下载音频",h&&Tr.error(h)};return(h,d)=>{const x=va("CloseBold"),g=Wt,w=Gt,y=tc,m=xt,O=ht;return Ae(),_a(O,{"append-to-body":"",class:"hhypt-dialog-boxs hhypt-dialog-boxs-downAudio","show-close":!1,modelValue:oe(r),"onUpdate:modelValue":d[3]||(d[3]=P=>Yr(r)?r.value=P:null),title:"下载音频设置",width:"500","align-center":""},{header:de(({close:P,titleClass:D})=>[q("div",Nc,[q("span",{class:pa(D)},"下载音频设置",2),xe(w,{onClick:P,underline:!1},{default:de(()=>[xe(g,{size:"20"},{default:de(()=>[xe(x)]),_:1})]),_:2},1032,["onClick"])])]),default:de(()=>[q("div",null,[q("div",Lc,[q("div",Pc,[d[4]||(d[4]=q("span",{class:"bt"},"每个单词重复次数：",-1)),xe(y,{modelValue:oe(s),"onUpdate:modelValue":d[0]||(d[0]=P=>Yr(s)?s.value=P:null),"step-strictly":!0,step:1,class:"mx-4",min:1,max:3,"controls-position":"right"},null,8,["modelValue"])]),q("div",bc,[d[5]||(d[5]=q("span",{class:"bt"},"每个单词停顿时间：",-1)),xe(y,{modelValue:oe(t),"onUpdate:modelValue":d[1]||(d[1]=P=>Yr(t)?t.value=P:null),"step-strictly":!0,step:.5,class:"mx-4",min:.5,max:5,"controls-position":"right"},null,8,["modelValue"])])]),q("div",Bc,[xe(m,{type:"primary",onClick:d[2]||(d[2]=P=>l()),loading:oe(c)},{default:de(()=>[Qe(Ye(oe(o)),1)]),_:1},8,["loading"])])])]),_:1},8,["modelValue"])}}}),Uc=dt(Mc,[["__scopeId","data-v-17424d32"]]),Vc="data:image/svg+xml,%3csvg%20width='26'%20height='26'%20viewBox='0%200%2026%2026'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M24.1233%208.78691C23.5418%208.52285%2014.424%204.39941%2014.424%204.39941C14.424%204.39941%2013.4237%203.92969%2013.0072%203.92969C12.5934%203.92969%2011.5905%204.39941%2011.5905%204.39941C11.5905%204.39941%202.47268%208.52285%201.89123%208.78691C1.30979%209.05098%201.89123%209.39121%201.89123%209.39121C1.89123%209.39121%209.69631%2012.9332%2011.1182%2013.5654C12.2811%2014.0834%2012.5401%2014.0732%2013.0072%2014.0732C13.4744%2014.0732%2013.7334%2014.0859%2014.8963%2013.5654C15.8764%2013.1287%2019.8906%2011.3107%2022.2901%2010.2215V13.2658L21.503%2014.0529L22.5998%2015.1498L23.6967%2014.0529L22.9096%2013.2658V9.94219C23.6459%209.60703%2024.1207%209.39121%2024.1207%209.39121C24.1207%209.39121%2024.7047%209.04844%2024.1233%208.78691Z'%20fill='%232672FF'/%3e%3cpath%20d='M13.0133%2015.0697C12.5461%2015.0697%2012.2871%2015.0824%2011.1242%2014.5619C10.4184%2014.2471%208.13574%2013.2137%206.01562%2012.2539V19.249C8.1332%2020.2088%2010.4184%2021.2422%2011.1242%2021.557C12.2871%2022.075%2012.5461%2022.0648%2013.0133%2022.0648C13.4805%2022.0648%2013.7395%2022.0775%2014.9023%2021.557C15.6082%2021.2422%2017.8908%2020.2088%2020.0109%2019.249V12.2539C17.8934%2013.2137%2015.6082%2014.2471%2014.9023%2014.5619C13.7395%2015.0824%2013.4805%2015.0697%2013.0133%2015.0697Z'%20fill='%236098FF'/%3e%3c/svg%3e",Hc={class:"pmm2_flex_between"},Wc={class:"chooseWords-boxs pmm2_flex_between"},Gc={class:"chooseWords-left"},$c={class:"left-boxs"},Xc={class:"top-box pmm2_flex_center"},zc={class:"t1"},Kc={class:"left-com"},Yc={class:"search-box pmm2_flex_between"},jc={class:"popover-search-box"},Jc={class:"item-boxs"},Zc={class:"ul pmm2_flex_acenter"},qc=["onClick"],Qc={key:0,class:"ul pmm2_flex_acenter"},ef=["onClick"],rf={key:0,class:"catalog-box scrollBar"},af=["title"],tf={key:1,class:"pmm2_flex_center",style:{"flex-direction":"column"}},nf={class:"chooseWords-right pmm2_flex_between"},sf={class:"right-box"},cf={class:"top-box pmm2_flex_between"},ff={class:"t2"},of={key:0,class:"com-box"},lf={class:"com scrollBar"},uf={class:"pagination-box"},hf={key:1,class:"com-box pmm2_flex_center",style:{"flex-direction":"column"}},xf={class:"buttom-box"},df={class:"right-box"},pf={class:"top-box pmm2_flex_between"},vf={class:"t2"},gf={key:0,class:"com-box"},mf={class:"com scrollBar"},_f={key:1,class:"com-box pmm2_flex_center",style:{"flex-direction":"column"}},Ef={class:"pmm2_flex_between"},Tf={class:"dialog-footer"},kf=ut({__name:"chooseWords",emits:["setParsedData"],setup(e,{expose:a,emit:r}){const n=me(!1),t=me(!1),s=me({}),i=me(null),c=me(null),f=me(null),l=me([]);a({opens:j=>{s.value=j,n.value=!0,i.value||dc({courseCode:j.courseCode,stageId:j.stageId}).then(Y=>{i.value=Y.data||[]})}});const u=me(null);let p="";const h=j=>{c.value=j.id,l.value=j.textbooks,u.value=j.name,p=j.name},d=me([]),x=me([]),g=j=>{f.value=j.id,u.value=p+"-"+j.name,t.value=!1,pc({textbookId:f.value}).then(Y=>{x.value=Y.data||[]})},w=r,y=me([]),m=me([]);let O=[];const P=(j,Y,Q)=>{Y.expanded=!Y.expanded,O=[];const se=ne=>{O.push(ne.id),ne.children&&ne.children.length>0&&ne.children.forEach(De=>{se(De)})};se(j),S(1)},D=me(0),S=j=>{vc({courseCode:s.value.courseCode,stageId:s.value.stageId,chapterIds:O,textbookId:f.value,page:j,pageSize:30}).then(Y=>{let Q=Y.data||{};d.value=Q.words||[],D.value=Q.totalCount||0})},U=j=>{S(j)},R=j=>{j=="all"&&(m.value=d.value.map(Q=>Q.name));let Y=m.value.length;if(Y>0)for(let Q=0;Q<Y;Q++){let se=m.value[Q];y.value.findIndex(De=>De==se)==-1&&y.value.push(se)}else Tr({message:"请先选中单词！",type:"warning"})},z=(j,Y)=>{y.value.splice(Y,1),m.value.findIndex(se=>se==j)!=-1&&m.value.splice(Y,1)},G=()=>{m.value=[],y.value=[]},b=()=>{let j=y.value.length;if(j>s.value.pageCountSum)return Tr({message:`最多支持${s.value.pageCountSum}个单词`,type:"warning"});Tr({message:`选择${j}个单词`,type:"success",plain:!0}),n.value=!1;let Y=y.value.map(Q=>({word:Q}));w("setParsedData",Y)};return(j,Y)=>{const Q=va("CloseBold"),se=Wt,ne=Gt,De=va("ArrowDown"),L=is,X=nc,ue=cs,A=sc,B=ic,N=xt,I=va("Close"),J=ht;return Ae(),_a(J,{"append-to-body":"",class:"hhypt-dialog-boxs hhypt-dialog-chooseWords","show-close":!1,modelValue:oe(n),"onUpdate:modelValue":Y[5]||(Y[5]=Z=>Yr(n)?n.value=Z:null),title:"选择单词",width:"1200","align-center":""},{header:de(({close:Z,titleClass:ie})=>[q("div",Hc,[q("span",{class:pa(ie)}," 选择单词("+Ye(oe(s).label)+"最多支持"+Ye(oe(s).pageCountSum)+"个单词) ",3),xe(ne,{onClick:Z,underline:!1},{default:de(()=>[xe(se,{size:"20"},{default:de(()=>[xe(Q)]),_:1})]),_:2},1032,["onClick"])])]),footer:de(()=>[q("div",Ef,[Y[20]||(Y[20]=q("div",null,null,-1)),q("div",Tf,[xe(N,{style:{width:"70px"},onClick:Y[4]||(Y[4]=Z=>n.value=!1)},{default:de(()=>Y[18]||(Y[18]=[Qe("取消")])),_:1,__:[18]}),xe(N,{style:{width:"70px"},type:"primary",onClick:b},{default:de(()=>Y[19]||(Y[19]=[Qe(" 确定 ")])),_:1,__:[19]})])])]),default:de(()=>[q("div",Wc,[q("div",Gc,[q("div",$c,[q("div",Xc,[Y[6]||(Y[6]=q("img",{class:"img",src:Vc},null,-1)),q("span",zc,Ye(oe(s).stageLabel)+"-"+Ye(oe(s).courseName),1)]),q("div",Kc,[xe(L,{width:600,teleported:!1,trigger:"click",placement:"bottom-start","hide-after":0,visible:oe(t),"onUpdate:visible":Y[0]||(Y[0]=Z=>Yr(t)?t.value=Z:null)},{reference:de(()=>[q("div",Yc,[q("span",null,Ye(oe(u)||"请选择"),1),xe(se,null,{default:de(()=>[xe(De)]),_:1})])]),default:de(()=>[q("div",jc,[q("div",Jc,[Y[9]||(Y[9]=q("div",{class:"bt"},"教材版本",-1)),q("div",Zc,[Y[7]||(Y[7]=q("div",{class:"li"}," 版本： ",-1)),(Ae(!0),Re(na,null,ta(oe(i),(Z,ie)=>(Ae(),Re("div",{class:pa(["li huiA2",{"on ":oe(c)==Z.id}]),key:ie,onClick:te=>h(Z)},Ye(Z.name),11,qc))),128))]),oe(l).length>0?(Ae(),Re("div",Qc,[Y[8]||(Y[8]=q("div",{class:"li"}," 年级： ",-1)),(Ae(!0),Re(na,null,ta(oe(l),(Z,ie)=>(Ae(),Re("div",{class:pa(["li huiA2",{on:oe(f)==Z.id}]),onClick:te=>g(Z),key:ie},Ye(Z.name),11,ef))),128))])):i0("",!0)])])]),_:1},8,["visible"]),oe(x).length>0?(Ae(),Re("div",rf,[xe(X,{data:oe(x),props:{children:"children",label:"name"},class:"hhypt-el-tree","empty-text":"暂无考点/题点数据","check-strictly":!0,"default-expand-all":"","node-key":"id",onNodeClick:P,"highlight-current":!0},{default:de(({node:Z})=>[q("span",{title:Z.label,class:"pmm2_ellipsis"},Ye(Z.label),9,af)]),_:1},8,["data"])])):(Ae(),Re("div",tf,Y[10]||(Y[10]=[q("img",{style:{width:"100px"},src:"https://oss.hhypt.com/resources/mini-parent-helper/err.png"},null,-1),q("span",{style:{color:"#999"}},"暂无数据",-1)])))])])]),q("div",nf,[q("div",sf,[q("div",cf,[Y[11]||(Y[11]=q("span",{class:"t1"},"选择单词",-1)),q("span",ff,"共"+Ye(oe(D))+"词",1)]),oe(d).length>0?(Ae(),Re("div",of,[q("div",lf,[xe(A,{modelValue:oe(m),"onUpdate:modelValue":Y[1]||(Y[1]=Z=>Yr(m)?m.value=Z:null)},{default:de(()=>[(Ae(!0),Re(na,null,ta(oe(d),Z=>(Ae(),Re("div",{class:"checkbox-box",key:Z.name},[xe(ue,{label:Z.name,value:Z.name},null,8,["label","value"])]))),128))]),_:1},8,["modelValue"])]),q("div",uf,[xe(B,{size:"small",background:"",layout:"prev, pager, next",total:oe(D),"default-page-size":30,class:"mt-4",onCurrentChange:U},null,8,["total"])])])):(Ae(),Re("div",hf,Y[12]||(Y[12]=[q("img",{style:{width:"100px"},src:"https://oss.hhypt.com/resources/mini-parent-helper/err.png"},null,-1),q("span",{style:{color:"#999"}},"暂无数据",-1)])))]),q("div",xf,[xe(N,{class:"btn",onClick:Y[2]||(Y[2]=Z=>R("one")),type:"primary"},{default:de(()=>Y[13]||(Y[13]=[Qe("添加")])),_:1,__:[13]}),xe(N,{class:"btn",onClick:Y[3]||(Y[3]=Z=>R("all")),type:"success"},{default:de(()=>Y[14]||(Y[14]=[Qe("添加此页")])),_:1,__:[14]}),oe(y).length?(Ae(),_a(N,{key:0,class:"btn",onClick:G,type:"danger"},{default:de(()=>Y[15]||(Y[15]=[Qe("全部删除")])),_:1,__:[15]})):i0("",!0)]),q("div",df,[q("div",pf,[Y[16]||(Y[16]=q("span",{class:"t1"},"已选单词",-1)),q("span",vf,"共"+Ye(oe(y).length)+"词",1)]),oe(y).length>0?(Ae(),Re("div",gf,[q("div",mf,[(Ae(!0),Re(na,null,ta(oe(y),(Z,ie)=>(Ae(),Re("div",{class:"li pmm2_flex_between",key:ie},[q("span",null,Ye(Z),1),xe(se,{class:"huiA2",onClick:te=>z(Z,ie)},{default:de(()=>[xe(I)]),_:2},1032,["onClick"])]))),128))])])):(Ae(),Re("div",_f,Y[17]||(Y[17]=[q("img",{style:{width:"100px"},src:"https://oss.hhypt.com/resources/mini-parent-helper/err.png"},null,-1),q("span",{style:{color:"#999"}},"请选择数据",-1)])))])])])]),_:1},8,["modelValue"])}}}),wf=dt(kf,[["__scopeId","data-v-3feaf303"]]),Af=""+new URL("../img/xlsxgs.CO5JGILC.png",import.meta.url).href;/*! xlsx.js (C) 2013-present SheetJS -- http://sheetjs.com */var fs=1252,Ff=[874,932,936,949,950,1250,1251,1252,1253,1254,1255,1256,1257,1258,1e4],_0={0:1252,1:65001,2:65001,77:1e4,128:932,129:949,130:1361,134:936,136:950,161:1253,162:1254,163:1258,177:1255,178:1256,186:1257,204:1251,222:874,238:1250,255:1252,69:6969},E0=function(e){Ff.indexOf(e)!=-1&&(fs=_0[0]=e)};function Sf(){E0(1252)}var Br=function(e){E0(e)};function os(){Br(1200),Sf()}function Q0(e){for(var a=[],r=0,n=e.length;r<n;++r)a[r]=e.charCodeAt(r);return a}function Cf(e){for(var a=[],r=0;r<e.length>>1;++r)a[r]=String.fromCharCode(e.charCodeAt(2*r)+(e.charCodeAt(2*r+1)<<8));return a.join("")}function ls(e){for(var a=[],r=0;r<e.length>>1;++r)a[r]=String.fromCharCode(e.charCodeAt(2*r+1)+(e.charCodeAt(2*r)<<8));return a.join("")}var $a=function(e){var a=e.charCodeAt(0),r=e.charCodeAt(1);return a==255&&r==254?Cf(e.slice(2)):a==254&&r==255?ls(e.slice(2)):a==65279?e.slice(1):e},kt=function(a){return String.fromCharCode(a)},en=function(a){return String.fromCharCode(a)},c0,sa="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";function rn(e){for(var a="",r=0,n=0,t=0,s=0,i=0,c=0,f=0,l=0;l<e.length;)r=e.charCodeAt(l++),s=r>>2,n=e.charCodeAt(l++),i=(r&3)<<4|n>>4,t=e.charCodeAt(l++),c=(n&15)<<2|t>>6,f=t&63,isNaN(n)?c=f=64:isNaN(t)&&(f=64),a+=sa.charAt(s)+sa.charAt(i)+sa.charAt(c)+sa.charAt(f);return a}function Fr(e){var a="",r=0,n=0,t=0,s=0,i=0,c=0,f=0;e=e.replace(/[^\w\+\/\=]/g,"");for(var l=0;l<e.length;)s=sa.indexOf(e.charAt(l++)),i=sa.indexOf(e.charAt(l++)),r=s<<2|i>>4,a+=String.fromCharCode(r),c=sa.indexOf(e.charAt(l++)),n=(i&15)<<4|c>>2,c!==64&&(a+=String.fromCharCode(n)),f=sa.indexOf(e.charAt(l++)),t=(c&3)<<6|f,f!==64&&(a+=String.fromCharCode(t));return a}var Se=function(){return typeof Buffer<"u"&&typeof process<"u"&&typeof process.versions<"u"&&!!process.versions.node}(),wa=function(){if(typeof Buffer<"u"){var e=!Buffer.from;if(!e)try{Buffer.from("foo","utf8")}catch{e=!0}return e?function(a,r){return r?new Buffer(a,r):new Buffer(a)}:Buffer.from.bind(Buffer)}return function(){}}();function fa(e){return Se?Buffer.alloc?Buffer.alloc(e):new Buffer(e):typeof Uint8Array<"u"?new Uint8Array(e):new Array(e)}function an(e){return Se?Buffer.allocUnsafe?Buffer.allocUnsafe(e):new Buffer(e):typeof Uint8Array<"u"?new Uint8Array(e):new Array(e)}var br=function(a){return Se?wa(a,"binary"):a.split("").map(function(r){return r.charCodeAt(0)&255})};function Aa(e){if(Array.isArray(e))return e.map(function(n){return String.fromCharCode(n)}).join("");for(var a=[],r=0;r<e.length;++r)a[r]=String.fromCharCode(e[r]);return a.join("")}function T0(e){if(typeof ArrayBuffer>"u")throw new Error("Unsupported");if(e instanceof ArrayBuffer)return T0(new Uint8Array(e));for(var a=new Array(e.length),r=0;r<e.length;++r)a[r]=e[r];return a}var aa=Se?function(e){return Buffer.concat(e.map(function(a){return Buffer.isBuffer(a)?a:wa(a)}))}:function(e){if(typeof Uint8Array<"u"){var a=0,r=0;for(a=0;a<e.length;++a)r+=e[a].length;var n=new Uint8Array(r),t=0;for(a=0,r=0;a<e.length;r+=t,++a)if(t=e[a].length,e[a]instanceof Uint8Array)n.set(e[a],r);else{if(typeof e[a]=="string")throw"wtf";n.set(new Uint8Array(e[a]),r)}return n}return[].concat.apply([],e.map(function(s){return Array.isArray(s)?s:[].slice.call(s)}))};function yf(e){for(var a=[],r=0,n=e.length+250,t=fa(e.length+255),s=0;s<e.length;++s){var i=e.charCodeAt(s);if(i<128)t[r++]=i;else if(i<2048)t[r++]=192|i>>6&31,t[r++]=128|i&63;else if(i>=55296&&i<57344){i=(i&1023)+64;var c=e.charCodeAt(++s)&1023;t[r++]=240|i>>8&7,t[r++]=128|i>>2&63,t[r++]=128|c>>6&15|(i&3)<<4,t[r++]=128|c&63}else t[r++]=224|i>>12&15,t[r++]=128|i>>6&63,t[r++]=128|i&63;r>n&&(a.push(t.slice(0,r)),r=0,t=fa(65535),n=65530)}return a.push(t.slice(0,r)),aa(a)}var gr=/\u0000/g,Xa=/[\u0001-\u0006]/g;function Ra(e){for(var a="",r=e.length-1;r>=0;)a+=e.charAt(r--);return a}function Mr(e,a){var r=""+e;return r.length>=a?r:Ve("0",a-r.length)+r}function k0(e,a){var r=""+e;return r.length>=a?r:Ve(" ",a-r.length)+r}function It(e,a){var r=""+e;return r.length>=a?r:r+Ve(" ",a-r.length)}function Df(e,a){var r=""+Math.round(e);return r.length>=a?r:Ve("0",a-r.length)+r}function Of(e,a){var r=""+e;return r.length>=a?r:Ve("0",a-r.length)+r}var tn=Math.pow(2,32);function Da(e,a){if(e>tn||e<-tn)return Df(e,a);var r=Math.round(e);return Of(r,a)}function Rt(e,a){return a=a||0,e.length>=7+a&&(e.charCodeAt(a)|32)===103&&(e.charCodeAt(a+1)|32)===101&&(e.charCodeAt(a+2)|32)===110&&(e.charCodeAt(a+3)|32)===101&&(e.charCodeAt(a+4)|32)===114&&(e.charCodeAt(a+5)|32)===97&&(e.charCodeAt(a+6)|32)===108}var nn=[["Sun","Sunday"],["Mon","Monday"],["Tue","Tuesday"],["Wed","Wednesday"],["Thu","Thursday"],["Fri","Friday"],["Sat","Saturday"]],Jt=[["J","Jan","January"],["F","Feb","February"],["M","Mar","March"],["A","Apr","April"],["M","May","May"],["J","Jun","June"],["J","Jul","July"],["A","Aug","August"],["S","Sep","September"],["O","Oct","October"],["N","Nov","November"],["D","Dec","December"]];function If(e){return e||(e={}),e[0]="General",e[1]="0",e[2]="0.00",e[3]="#,##0",e[4]="#,##0.00",e[9]="0%",e[10]="0.00%",e[11]="0.00E+00",e[12]="# ?/?",e[13]="# ??/??",e[14]="m/d/yy",e[15]="d-mmm-yy",e[16]="d-mmm",e[17]="mmm-yy",e[18]="h:mm AM/PM",e[19]="h:mm:ss AM/PM",e[20]="h:mm",e[21]="h:mm:ss",e[22]="m/d/yy h:mm",e[37]="#,##0 ;(#,##0)",e[38]="#,##0 ;[Red](#,##0)",e[39]="#,##0.00;(#,##0.00)",e[40]="#,##0.00;[Red](#,##0.00)",e[45]="mm:ss",e[46]="[h]:mm:ss",e[47]="mmss.0",e[48]="##0.0E+0",e[49]="@",e[56]='"上午/下午 "hh"時"mm"分"ss"秒 "',e}var _e={0:"General",1:"0",2:"0.00",3:"#,##0",4:"#,##0.00",9:"0%",10:"0.00%",11:"0.00E+00",12:"# ?/?",13:"# ??/??",14:"m/d/yy",15:"d-mmm-yy",16:"d-mmm",17:"mmm-yy",18:"h:mm AM/PM",19:"h:mm:ss AM/PM",20:"h:mm",21:"h:mm:ss",22:"m/d/yy h:mm",37:"#,##0 ;(#,##0)",38:"#,##0 ;[Red](#,##0)",39:"#,##0.00;(#,##0.00)",40:"#,##0.00;[Red](#,##0.00)",45:"mm:ss",46:"[h]:mm:ss",47:"mmss.0",48:"##0.0E+0",49:"@",56:'"上午/下午 "hh"時"mm"分"ss"秒 "'},sn={5:37,6:38,7:39,8:40,23:0,24:0,25:0,26:0,27:14,28:14,29:14,30:14,31:14,50:14,51:14,52:14,53:14,54:14,55:14,56:14,57:14,58:14,59:1,60:2,61:3,62:4,67:9,68:10,69:12,70:13,71:14,72:14,73:15,74:16,75:17,76:20,77:21,78:22,79:45,80:46,81:47,82:0},Rf={5:'"$"#,##0_);\\("$"#,##0\\)',63:'"$"#,##0_);\\("$"#,##0\\)',6:'"$"#,##0_);[Red]\\("$"#,##0\\)',64:'"$"#,##0_);[Red]\\("$"#,##0\\)',7:'"$"#,##0.00_);\\("$"#,##0.00\\)',65:'"$"#,##0.00_);\\("$"#,##0.00\\)',8:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',66:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',41:'_(* #,##0_);_(* \\(#,##0\\);_(* "-"_);_(@_)',42:'_("$"* #,##0_);_("$"* \\(#,##0\\);_("$"* "-"_);_(@_)',43:'_(* #,##0.00_);_(* \\(#,##0.00\\);_(* "-"??_);_(@_)',44:'_("$"* #,##0.00_);_("$"* \\(#,##0.00\\);_("$"* "-"??_);_(@_)'};function Nt(e,a,r){for(var n=e<0?-1:1,t=e*n,s=0,i=1,c=0,f=1,l=0,o=0,u=Math.floor(t);l<a&&(u=Math.floor(t),c=u*i+s,o=u*l+f,!(t-u<5e-8));)t=1/(t-u),s=i,i=c,f=l,l=o;if(o>a&&(l>a?(o=f,c=s):(o=l,c=i)),!r)return[0,n*c,o];var p=Math.floor(n*c/o);return[p,n*c-p*o,o]}function da(e,a,r){if(e>2958465||e<0)return null;var n=e|0,t=Math.floor(86400*(e-n)),s=0,i=[],c={D:n,T:t,u:86400*(e-n)-t,y:0,m:0,d:0,H:0,M:0,S:0,q:0};if(Math.abs(c.u)<1e-6&&(c.u=0),a&&a.date1904&&(n+=1462),c.u>.9999&&(c.u=0,++t==86400&&(c.T=t=0,++n,++c.D)),n===60)i=r?[1317,10,29]:[1900,2,29],s=3;else if(n===0)i=r?[1317,8,29]:[1900,1,0],s=6;else{n>60&&--n;var f=new Date(1900,0,1);f.setDate(f.getDate()+n-1),i=[f.getFullYear(),f.getMonth()+1,f.getDate()],s=f.getDay(),n<60&&(s=(s+6)%7),r&&(s=Mf(f,i))}return c.y=i[0],c.m=i[1],c.d=i[2],c.S=t%60,t=Math.floor(t/60),c.M=t%60,t=Math.floor(t/60),c.H=t,c.q=s,c}var us=new Date(1899,11,31,0,0,0),Nf=us.getTime(),Lf=new Date(1900,2,1,0,0,0);function hs(e,a){var r=e.getTime();return a?r-=1461*24*60*60*1e3:e>=Lf&&(r+=24*60*60*1e3),(r-(Nf+(e.getTimezoneOffset()-us.getTimezoneOffset())*6e4))/(24*60*60*1e3)}function w0(e){return e.indexOf(".")==-1?e:e.replace(/(?:\.0*|(\.\d*[1-9])0+)$/,"$1")}function Pf(e){return e.indexOf("E")==-1?e:e.replace(/(?:\.0*|(\.\d*[1-9])0+)[Ee]/,"$1E").replace(/(E[+-])(\d)$/,"$10$2")}function bf(e){var a=e<0?12:11,r=w0(e.toFixed(12));return r.length<=a||(r=e.toPrecision(10),r.length<=a)?r:e.toExponential(5)}function Bf(e){var a=w0(e.toFixed(11));return a.length>(e<0?12:11)||a==="0"||a==="-0"?e.toPrecision(6):a}function tt(e){var a=Math.floor(Math.log(Math.abs(e))*Math.LOG10E),r;return a>=-4&&a<=-1?r=e.toPrecision(10+a):Math.abs(a)<=9?r=bf(e):a===10?r=e.toFixed(10).substr(0,12):r=Bf(e),w0(Pf(r.toUpperCase()))}function Ea(e,a){switch(typeof e){case"string":return e;case"boolean":return e?"TRUE":"FALSE";case"number":return(e|0)===e?e.toString(10):tt(e);case"undefined":return"";case"object":if(e==null)return"";if(e instanceof Date)return Ir(14,hs(e,a&&a.date1904),a)}throw new Error("unsupported value in General format: "+e)}function Mf(e,a){a[0]-=581;var r=e.getDay();return e<60&&(r=(r+6)%7),r}function Uf(e,a,r,n){var t="",s=0,i=0,c=r.y,f,l=0;switch(e){case 98:c=r.y+543;case 121:switch(a.length){case 1:case 2:f=c%100,l=2;break;default:f=c%1e4,l=4;break}break;case 109:switch(a.length){case 1:case 2:f=r.m,l=a.length;break;case 3:return Jt[r.m-1][1];case 5:return Jt[r.m-1][0];default:return Jt[r.m-1][2]}break;case 100:switch(a.length){case 1:case 2:f=r.d,l=a.length;break;case 3:return nn[r.q][0];default:return nn[r.q][1]}break;case 104:switch(a.length){case 1:case 2:f=1+(r.H+11)%12,l=a.length;break;default:throw"bad hour format: "+a}break;case 72:switch(a.length){case 1:case 2:f=r.H,l=a.length;break;default:throw"bad hour format: "+a}break;case 77:switch(a.length){case 1:case 2:f=r.M,l=a.length;break;default:throw"bad minute format: "+a}break;case 115:if(a!="s"&&a!="ss"&&a!=".0"&&a!=".00"&&a!=".000")throw"bad second format: "+a;return r.u===0&&(a=="s"||a=="ss")?Mr(r.S,a.length):(n>=2?i=n===3?1e3:100:i=n===1?10:1,s=Math.round(i*(r.S+r.u)),s>=60*i&&(s=0),a==="s"?s===0?"0":""+s/i:(t=Mr(s,2+n),a==="ss"?t.substr(0,2):"."+t.substr(2,a.length-1)));case 90:switch(a){case"[h]":case"[hh]":f=r.D*24+r.H;break;case"[m]":case"[mm]":f=(r.D*24+r.H)*60+r.M;break;case"[s]":case"[ss]":f=((r.D*24+r.H)*60+r.M)*60+Math.round(r.S+r.u);break;default:throw"bad abstime format: "+a}l=a.length===3?1:2;break;case 101:f=c,l=1;break}var o=l>0?Mr(f,l):"";return o}function ia(e){var a=3;if(e.length<=a)return e;for(var r=e.length%a,n=e.substr(0,r);r!=e.length;r+=a)n+=(n.length>0?",":"")+e.substr(r,a);return n}var xs=/%/g;function Vf(e,a,r){var n=a.replace(xs,""),t=a.length-n.length;return jr(e,n,r*Math.pow(10,2*t))+Ve("%",t)}function Hf(e,a,r){for(var n=a.length-1;a.charCodeAt(n-1)===44;)--n;return jr(e,a.substr(0,n),r/Math.pow(10,3*(a.length-n)))}function ds(e,a){var r,n=e.indexOf("E")-e.indexOf(".")-1;if(e.match(/^#+0.0E\+0$/)){if(a==0)return"0.0E+0";if(a<0)return"-"+ds(e,-a);var t=e.indexOf(".");t===-1&&(t=e.indexOf("E"));var s=Math.floor(Math.log(a)*Math.LOG10E)%t;if(s<0&&(s+=t),r=(a/Math.pow(10,s)).toPrecision(n+1+(t+s)%t),r.indexOf("e")===-1){var i=Math.floor(Math.log(a)*Math.LOG10E);for(r.indexOf(".")===-1?r=r.charAt(0)+"."+r.substr(1)+"E+"+(i-r.length+s):r+="E+"+(i-s);r.substr(0,2)==="0.";)r=r.charAt(0)+r.substr(2,t)+"."+r.substr(2+t),r=r.replace(/^0+([1-9])/,"$1").replace(/^0+\./,"0.");r=r.replace(/\+-/,"-")}r=r.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,function(c,f,l,o){return f+l+o.substr(0,(t+s)%t)+"."+o.substr(s)+"E"})}else r=a.toExponential(n);return e.match(/E\+00$/)&&r.match(/e[+-]\d$/)&&(r=r.substr(0,r.length-1)+"0"+r.charAt(r.length-1)),e.match(/E\-/)&&r.match(/e\+/)&&(r=r.replace(/e\+/,"e")),r.replace("e","E")}var ps=/# (\?+)( ?)\/( ?)(\d+)/;function Wf(e,a,r){var n=parseInt(e[4],10),t=Math.round(a*n),s=Math.floor(t/n),i=t-s*n,c=n;return r+(s===0?"":""+s)+" "+(i===0?Ve(" ",e[1].length+1+e[4].length):k0(i,e[1].length)+e[2]+"/"+e[3]+Mr(c,e[4].length))}function Gf(e,a,r){return r+(a===0?"":""+a)+Ve(" ",e[1].length+2+e[4].length)}var vs=/^#*0*\.([0#]+)/,gs=/\).*[0#]/,ms=/\(###\) ###\\?-####/;function dr(e){for(var a="",r,n=0;n!=e.length;++n)switch(r=e.charCodeAt(n)){case 35:break;case 63:a+=" ";break;case 48:a+="0";break;default:a+=String.fromCharCode(r)}return a}function cn(e,a){var r=Math.pow(10,a);return""+Math.round(e*r)/r}function fn(e,a){var r=e-Math.floor(e),n=Math.pow(10,a);return a<(""+Math.round(r*n)).length?0:Math.round(r*n)}function $f(e,a){return a<(""+Math.round((e-Math.floor(e))*Math.pow(10,a))).length?1:0}function Xf(e){return e<2147483647&&e>-2147483648?""+(e>=0?e|0:e-1|0):""+Math.floor(e)}function Dr(e,a,r){if(e.charCodeAt(0)===40&&!a.match(gs)){var n=a.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return r>=0?Dr("n",n,r):"("+Dr("n",n,-r)+")"}if(a.charCodeAt(a.length-1)===44)return Hf(e,a,r);if(a.indexOf("%")!==-1)return Vf(e,a,r);if(a.indexOf("E")!==-1)return ds(a,r);if(a.charCodeAt(0)===36)return"$"+Dr(e,a.substr(a.charAt(1)==" "?2:1),r);var t,s,i,c,f=Math.abs(r),l=r<0?"-":"";if(a.match(/^00+$/))return l+Da(f,a.length);if(a.match(/^[#?]+$/))return t=Da(r,0),t==="0"&&(t=""),t.length>a.length?t:dr(a.substr(0,a.length-t.length))+t;if(s=a.match(ps))return Wf(s,f,l);if(a.match(/^#+0+$/))return l+Da(f,a.length-a.indexOf("0"));if(s=a.match(vs))return t=cn(r,s[1].length).replace(/^([^\.]+)$/,"$1."+dr(s[1])).replace(/\.$/,"."+dr(s[1])).replace(/\.(\d*)$/,function(d,x){return"."+x+Ve("0",dr(s[1]).length-x.length)}),a.indexOf("0.")!==-1?t:t.replace(/^0\./,".");if(a=a.replace(/^#+([0.])/,"$1"),s=a.match(/^(0*)\.(#*)$/))return l+cn(f,s[2].length).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,s[1].length?"0.":".");if(s=a.match(/^#{1,3},##0(\.?)$/))return l+ia(Da(f,0));if(s=a.match(/^#,##0\.([#0]*0)$/))return r<0?"-"+Dr(e,a,-r):ia(""+(Math.floor(r)+$f(r,s[1].length)))+"."+Mr(fn(r,s[1].length),s[1].length);if(s=a.match(/^#,#*,#0/))return Dr(e,a.replace(/^#,#*,/,""),r);if(s=a.match(/^([0#]+)(\\?-([0#]+))+$/))return t=Ra(Dr(e,a.replace(/[\\-]/g,""),r)),i=0,Ra(Ra(a.replace(/\\/g,"")).replace(/[0#]/g,function(d){return i<t.length?t.charAt(i++):d==="0"?"0":""}));if(a.match(ms))return t=Dr(e,"##########",r),"("+t.substr(0,3)+") "+t.substr(3,3)+"-"+t.substr(6);var o="";if(s=a.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return i=Math.min(s[4].length,7),c=Nt(f,Math.pow(10,i)-1,!1),t=""+l,o=jr("n",s[1],c[1]),o.charAt(o.length-1)==" "&&(o=o.substr(0,o.length-1)+"0"),t+=o+s[2]+"/"+s[3],o=It(c[2],i),o.length<s[4].length&&(o=dr(s[4].substr(s[4].length-o.length))+o),t+=o,t;if(s=a.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return i=Math.min(Math.max(s[1].length,s[4].length),7),c=Nt(f,Math.pow(10,i)-1,!0),l+(c[0]||(c[1]?"":"0"))+" "+(c[1]?k0(c[1],i)+s[2]+"/"+s[3]+It(c[2],i):Ve(" ",2*i+1+s[2].length+s[3].length));if(s=a.match(/^[#0?]+$/))return t=Da(r,0),a.length<=t.length?t:dr(a.substr(0,a.length-t.length))+t;if(s=a.match(/^([#0?]+)\.([#0]+)$/)){t=""+r.toFixed(Math.min(s[2].length,10)).replace(/([^0])0+$/,"$1"),i=t.indexOf(".");var u=a.indexOf(".")-i,p=a.length-t.length-u;return dr(a.substr(0,u)+t+a.substr(a.length-p))}if(s=a.match(/^00,000\.([#0]*0)$/))return i=fn(r,s[1].length),r<0?"-"+Dr(e,a,-r):ia(Xf(r)).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,function(d){return"00,"+(d.length<3?Mr(0,3-d.length):"")+d})+"."+Mr(i,s[1].length);switch(a){case"###,##0.00":return Dr(e,"#,##0.00",r);case"###,###":case"##,###":case"#,###":var h=ia(Da(f,0));return h!=="0"?l+h:"";case"###,###.00":return Dr(e,"###,##0.00",r).replace(/^0\./,".");case"#,###.00":return Dr(e,"#,##0.00",r).replace(/^0\./,".")}throw new Error("unsupported format |"+a+"|")}function zf(e,a,r){for(var n=a.length-1;a.charCodeAt(n-1)===44;)--n;return jr(e,a.substr(0,n),r/Math.pow(10,3*(a.length-n)))}function Kf(e,a,r){var n=a.replace(xs,""),t=a.length-n.length;return jr(e,n,r*Math.pow(10,2*t))+Ve("%",t)}function _s(e,a){var r,n=e.indexOf("E")-e.indexOf(".")-1;if(e.match(/^#+0.0E\+0$/)){if(a==0)return"0.0E+0";if(a<0)return"-"+_s(e,-a);var t=e.indexOf(".");t===-1&&(t=e.indexOf("E"));var s=Math.floor(Math.log(a)*Math.LOG10E)%t;if(s<0&&(s+=t),r=(a/Math.pow(10,s)).toPrecision(n+1+(t+s)%t),!r.match(/[Ee]/)){var i=Math.floor(Math.log(a)*Math.LOG10E);r.indexOf(".")===-1?r=r.charAt(0)+"."+r.substr(1)+"E+"+(i-r.length+s):r+="E+"+(i-s),r=r.replace(/\+-/,"-")}r=r.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,function(c,f,l,o){return f+l+o.substr(0,(t+s)%t)+"."+o.substr(s)+"E"})}else r=a.toExponential(n);return e.match(/E\+00$/)&&r.match(/e[+-]\d$/)&&(r=r.substr(0,r.length-1)+"0"+r.charAt(r.length-1)),e.match(/E\-/)&&r.match(/e\+/)&&(r=r.replace(/e\+/,"e")),r.replace("e","E")}function Wr(e,a,r){if(e.charCodeAt(0)===40&&!a.match(gs)){var n=a.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return r>=0?Wr("n",n,r):"("+Wr("n",n,-r)+")"}if(a.charCodeAt(a.length-1)===44)return zf(e,a,r);if(a.indexOf("%")!==-1)return Kf(e,a,r);if(a.indexOf("E")!==-1)return _s(a,r);if(a.charCodeAt(0)===36)return"$"+Wr(e,a.substr(a.charAt(1)==" "?2:1),r);var t,s,i,c,f=Math.abs(r),l=r<0?"-":"";if(a.match(/^00+$/))return l+Mr(f,a.length);if(a.match(/^[#?]+$/))return t=""+r,r===0&&(t=""),t.length>a.length?t:dr(a.substr(0,a.length-t.length))+t;if(s=a.match(ps))return Gf(s,f,l);if(a.match(/^#+0+$/))return l+Mr(f,a.length-a.indexOf("0"));if(s=a.match(vs))return t=(""+r).replace(/^([^\.]+)$/,"$1."+dr(s[1])).replace(/\.$/,"."+dr(s[1])),t=t.replace(/\.(\d*)$/,function(d,x){return"."+x+Ve("0",dr(s[1]).length-x.length)}),a.indexOf("0.")!==-1?t:t.replace(/^0\./,".");if(a=a.replace(/^#+([0.])/,"$1"),s=a.match(/^(0*)\.(#*)$/))return l+(""+f).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,s[1].length?"0.":".");if(s=a.match(/^#{1,3},##0(\.?)$/))return l+ia(""+f);if(s=a.match(/^#,##0\.([#0]*0)$/))return r<0?"-"+Wr(e,a,-r):ia(""+r)+"."+Ve("0",s[1].length);if(s=a.match(/^#,#*,#0/))return Wr(e,a.replace(/^#,#*,/,""),r);if(s=a.match(/^([0#]+)(\\?-([0#]+))+$/))return t=Ra(Wr(e,a.replace(/[\\-]/g,""),r)),i=0,Ra(Ra(a.replace(/\\/g,"")).replace(/[0#]/g,function(d){return i<t.length?t.charAt(i++):d==="0"?"0":""}));if(a.match(ms))return t=Wr(e,"##########",r),"("+t.substr(0,3)+") "+t.substr(3,3)+"-"+t.substr(6);var o="";if(s=a.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return i=Math.min(s[4].length,7),c=Nt(f,Math.pow(10,i)-1,!1),t=""+l,o=jr("n",s[1],c[1]),o.charAt(o.length-1)==" "&&(o=o.substr(0,o.length-1)+"0"),t+=o+s[2]+"/"+s[3],o=It(c[2],i),o.length<s[4].length&&(o=dr(s[4].substr(s[4].length-o.length))+o),t+=o,t;if(s=a.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return i=Math.min(Math.max(s[1].length,s[4].length),7),c=Nt(f,Math.pow(10,i)-1,!0),l+(c[0]||(c[1]?"":"0"))+" "+(c[1]?k0(c[1],i)+s[2]+"/"+s[3]+It(c[2],i):Ve(" ",2*i+1+s[2].length+s[3].length));if(s=a.match(/^[#0?]+$/))return t=""+r,a.length<=t.length?t:dr(a.substr(0,a.length-t.length))+t;if(s=a.match(/^([#0]+)\.([#0]+)$/)){t=""+r.toFixed(Math.min(s[2].length,10)).replace(/([^0])0+$/,"$1"),i=t.indexOf(".");var u=a.indexOf(".")-i,p=a.length-t.length-u;return dr(a.substr(0,u)+t+a.substr(a.length-p))}if(s=a.match(/^00,000\.([#0]*0)$/))return r<0?"-"+Wr(e,a,-r):ia(""+r).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,function(d){return"00,"+(d.length<3?Mr(0,3-d.length):"")+d})+"."+Mr(0,s[1].length);switch(a){case"###,###":case"##,###":case"#,###":var h=ia(""+f);return h!=="0"?l+h:"";default:if(a.match(/\.[0#?]*$/))return Wr(e,a.slice(0,a.lastIndexOf(".")),r)+dr(a.slice(a.lastIndexOf(".")))}throw new Error("unsupported format |"+a+"|")}function jr(e,a,r){return(r|0)===r?Wr(e,a,r):Dr(e,a,r)}function Yf(e){for(var a=[],r=!1,n=0,t=0;n<e.length;++n)switch(e.charCodeAt(n)){case 34:r=!r;break;case 95:case 42:case 92:++n;break;case 59:a[a.length]=e.substr(t,n-t),t=n+1}if(a[a.length]=e.substr(t),r===!0)throw new Error("Format |"+e+"| unterminated string ");return a}var Es=/\[[HhMmSs\u0E0A\u0E19\u0E17]*\]/;function Ba(e){for(var a=0,r="",n="";a<e.length;)switch(r=e.charAt(a)){case"G":Rt(e,a)&&(a+=6),a++;break;case'"':for(;e.charCodeAt(++a)!==34&&a<e.length;);++a;break;case"\\":a+=2;break;case"_":a+=2;break;case"@":++a;break;case"B":case"b":if(e.charAt(a+1)==="1"||e.charAt(a+1)==="2")return!0;case"M":case"D":case"Y":case"H":case"S":case"E":case"m":case"d":case"y":case"h":case"s":case"e":case"g":return!0;case"A":case"a":case"上":if(e.substr(a,3).toUpperCase()==="A/P"||e.substr(a,5).toUpperCase()==="AM/PM"||e.substr(a,5).toUpperCase()==="上午/下午")return!0;++a;break;case"[":for(n=r;e.charAt(a++)!=="]"&&a<e.length;)n+=e.charAt(a);if(n.match(Es))return!0;break;case".":case"0":case"#":for(;a<e.length&&("0#?.,E+-%".indexOf(r=e.charAt(++a))>-1||r=="\\"&&e.charAt(a+1)=="-"&&"0#".indexOf(e.charAt(a+2))>-1););break;case"?":for(;e.charAt(++a)===r;);break;case"*":++a,(e.charAt(a)==" "||e.charAt(a)=="*")&&++a;break;case"(":case")":++a;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(;a<e.length&&"0123456789".indexOf(e.charAt(++a))>-1;);break;case" ":++a;break;default:++a;break}return!1}function jf(e,a,r,n){for(var t=[],s="",i=0,c="",f="t",l,o,u,p="H";i<e.length;)switch(c=e.charAt(i)){case"G":if(!Rt(e,i))throw new Error("unrecognized character "+c+" in "+e);t[t.length]={t:"G",v:"General"},i+=7;break;case'"':for(s="";(u=e.charCodeAt(++i))!==34&&i<e.length;)s+=String.fromCharCode(u);t[t.length]={t:"t",v:s},++i;break;case"\\":var h=e.charAt(++i),d=h==="("||h===")"?h:"t";t[t.length]={t:d,v:h},++i;break;case"_":t[t.length]={t:"t",v:" "},i+=2;break;case"@":t[t.length]={t:"T",v:a},++i;break;case"B":case"b":if(e.charAt(i+1)==="1"||e.charAt(i+1)==="2"){if(l==null&&(l=da(a,r,e.charAt(i+1)==="2"),l==null))return"";t[t.length]={t:"X",v:e.substr(i,2)},f=c,i+=2;break}case"M":case"D":case"Y":case"H":case"S":case"E":c=c.toLowerCase();case"m":case"d":case"y":case"h":case"s":case"e":case"g":if(a<0||l==null&&(l=da(a,r),l==null))return"";for(s=c;++i<e.length&&e.charAt(i).toLowerCase()===c;)s+=c;c==="m"&&f.toLowerCase()==="h"&&(c="M"),c==="h"&&(c=p),t[t.length]={t:c,v:s},f=c;break;case"A":case"a":case"上":var x={t:c,v:c};if(l==null&&(l=da(a,r)),e.substr(i,3).toUpperCase()==="A/P"?(l!=null&&(x.v=l.H>=12?"P":"A"),x.t="T",p="h",i+=3):e.substr(i,5).toUpperCase()==="AM/PM"?(l!=null&&(x.v=l.H>=12?"PM":"AM"),x.t="T",i+=5,p="h"):e.substr(i,5).toUpperCase()==="上午/下午"?(l!=null&&(x.v=l.H>=12?"下午":"上午"),x.t="T",i+=5,p="h"):(x.t="t",++i),l==null&&x.t==="T")return"";t[t.length]=x,f=c;break;case"[":for(s=c;e.charAt(i++)!=="]"&&i<e.length;)s+=e.charAt(i);if(s.slice(-1)!=="]")throw'unterminated "[" block: |'+s+"|";if(s.match(Es)){if(l==null&&(l=da(a,r),l==null))return"";t[t.length]={t:"Z",v:s.toLowerCase()},f=s.charAt(1)}else s.indexOf("$")>-1&&(s=(s.match(/\$([^-\[\]]*)/)||[])[1]||"$",Ba(e)||(t[t.length]={t:"t",v:s}));break;case".":if(l!=null){for(s=c;++i<e.length&&(c=e.charAt(i))==="0";)s+=c;t[t.length]={t:"s",v:s};break}case"0":case"#":for(s=c;++i<e.length&&"0#?.,E+-%".indexOf(c=e.charAt(i))>-1;)s+=c;t[t.length]={t:"n",v:s};break;case"?":for(s=c;e.charAt(++i)===c;)s+=c;t[t.length]={t:c,v:s},f=c;break;case"*":++i,(e.charAt(i)==" "||e.charAt(i)=="*")&&++i;break;case"(":case")":t[t.length]={t:n===1?"t":c,v:c},++i;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(s=c;i<e.length&&"0123456789".indexOf(e.charAt(++i))>-1;)s+=e.charAt(i);t[t.length]={t:"D",v:s};break;case" ":t[t.length]={t:c,v:c},++i;break;case"$":t[t.length]={t:"t",v:"$"},++i;break;default:if(",$-+/():!^&'~{}<>=€acfijklopqrtuvwxzP".indexOf(c)===-1)throw new Error("unrecognized character "+c+" in "+e);t[t.length]={t:"t",v:c},++i;break}var g=0,w=0,y;for(i=t.length-1,f="t";i>=0;--i)switch(t[i].t){case"h":case"H":t[i].t=p,f="h",g<1&&(g=1);break;case"s":(y=t[i].v.match(/\.0+$/))&&(w=Math.max(w,y[0].length-1)),g<3&&(g=3);case"d":case"y":case"M":case"e":f=t[i].t;break;case"m":f==="s"&&(t[i].t="M",g<2&&(g=2));break;case"X":break;case"Z":g<1&&t[i].v.match(/[Hh]/)&&(g=1),g<2&&t[i].v.match(/[Mm]/)&&(g=2),g<3&&t[i].v.match(/[Ss]/)&&(g=3)}switch(g){case 0:break;case 1:l.u>=.5&&(l.u=0,++l.S),l.S>=60&&(l.S=0,++l.M),l.M>=60&&(l.M=0,++l.H);break;case 2:l.u>=.5&&(l.u=0,++l.S),l.S>=60&&(l.S=0,++l.M);break}var m="",O;for(i=0;i<t.length;++i)switch(t[i].t){case"t":case"T":case" ":case"D":break;case"X":t[i].v="",t[i].t=";";break;case"d":case"m":case"y":case"h":case"H":case"M":case"s":case"e":case"b":case"Z":t[i].v=Uf(t[i].t.charCodeAt(0),t[i].v,l,w),t[i].t="t";break;case"n":case"?":for(O=i+1;t[O]!=null&&((c=t[O].t)==="?"||c==="D"||(c===" "||c==="t")&&t[O+1]!=null&&(t[O+1].t==="?"||t[O+1].t==="t"&&t[O+1].v==="/")||t[i].t==="("&&(c===" "||c==="n"||c===")")||c==="t"&&(t[O].v==="/"||t[O].v===" "&&t[O+1]!=null&&t[O+1].t=="?"));)t[i].v+=t[O].v,t[O]={v:"",t:";"},++O;m+=t[i].v,i=O-1;break;case"G":t[i].t="t",t[i].v=Ea(a,r);break}var P="",D,S;if(m.length>0){m.charCodeAt(0)==40?(D=a<0&&m.charCodeAt(0)===45?-a:a,S=jr("n",m,D)):(D=a<0&&n>1?-a:a,S=jr("n",m,D),D<0&&t[0]&&t[0].t=="t"&&(S=S.substr(1),t[0].v="-"+t[0].v)),O=S.length-1;var U=t.length;for(i=0;i<t.length;++i)if(t[i]!=null&&t[i].t!="t"&&t[i].v.indexOf(".")>-1){U=i;break}var R=t.length;if(U===t.length&&S.indexOf("E")===-1){for(i=t.length-1;i>=0;--i)t[i]==null||"n?".indexOf(t[i].t)===-1||(O>=t[i].v.length-1?(O-=t[i].v.length,t[i].v=S.substr(O+1,t[i].v.length)):O<0?t[i].v="":(t[i].v=S.substr(0,O+1),O=-1),t[i].t="t",R=i);O>=0&&R<t.length&&(t[R].v=S.substr(0,O+1)+t[R].v)}else if(U!==t.length&&S.indexOf("E")===-1){for(O=S.indexOf(".")-1,i=U;i>=0;--i)if(!(t[i]==null||"n?".indexOf(t[i].t)===-1)){for(o=t[i].v.indexOf(".")>-1&&i===U?t[i].v.indexOf(".")-1:t[i].v.length-1,P=t[i].v.substr(o+1);o>=0;--o)O>=0&&(t[i].v.charAt(o)==="0"||t[i].v.charAt(o)==="#")&&(P=S.charAt(O--)+P);t[i].v=P,t[i].t="t",R=i}for(O>=0&&R<t.length&&(t[R].v=S.substr(0,O+1)+t[R].v),O=S.indexOf(".")+1,i=U;i<t.length;++i)if(!(t[i]==null||"n?(".indexOf(t[i].t)===-1&&i!==U)){for(o=t[i].v.indexOf(".")>-1&&i===U?t[i].v.indexOf(".")+1:0,P=t[i].v.substr(0,o);o<t[i].v.length;++o)O<S.length&&(P+=S.charAt(O++));t[i].v=P,t[i].t="t",R=i}}}for(i=0;i<t.length;++i)t[i]!=null&&"n?".indexOf(t[i].t)>-1&&(D=n>1&&a<0&&i>0&&t[i-1].v==="-"?-a:a,t[i].v=jr(t[i].t,t[i].v,D),t[i].t="t");var z="";for(i=0;i!==t.length;++i)t[i]!=null&&(z+=t[i].v);return z}var on=/\[(=|>[=]?|<[>=]?)(-?\d+(?:\.\d*)?)\]/;function ln(e,a){if(a==null)return!1;var r=parseFloat(a[2]);switch(a[1]){case"=":if(e==r)return!0;break;case">":if(e>r)return!0;break;case"<":if(e<r)return!0;break;case"<>":if(e!=r)return!0;break;case">=":if(e>=r)return!0;break;case"<=":if(e<=r)return!0;break}return!1}function Jf(e,a){var r=Yf(e),n=r.length,t=r[n-1].indexOf("@");if(n<4&&t>-1&&--n,r.length>4)throw new Error("cannot find right format for |"+r.join("|")+"|");if(typeof a!="number")return[4,r.length===4||t>-1?r[r.length-1]:"@"];switch(r.length){case 1:r=t>-1?["General","General","General",r[0]]:[r[0],r[0],r[0],"@"];break;case 2:r=t>-1?[r[0],r[0],r[0],r[1]]:[r[0],r[1],r[0],"@"];break;case 3:r=t>-1?[r[0],r[1],r[0],r[2]]:[r[0],r[1],r[2],"@"];break}var s=a>0?r[0]:a<0?r[1]:r[2];if(r[0].indexOf("[")===-1&&r[1].indexOf("[")===-1)return[n,s];if(r[0].match(/\[[=<>]/)!=null||r[1].match(/\[[=<>]/)!=null){var i=r[0].match(on),c=r[1].match(on);return ln(a,i)?[n,r[0]]:ln(a,c)?[n,r[1]]:[n,r[i!=null&&c!=null?2:1]]}return[n,s]}function Ir(e,a,r){r==null&&(r={});var n="";switch(typeof e){case"string":e=="m/d/yy"&&r.dateNF?n=r.dateNF:n=e;break;case"number":e==14&&r.dateNF?n=r.dateNF:n=(r.table!=null?r.table:_e)[e],n==null&&(n=r.table&&r.table[sn[e]]||_e[sn[e]]),n==null&&(n=Rf[e]||"General");break}if(Rt(n,0))return Ea(a,r);a instanceof Date&&(a=hs(a,r.date1904));var t=Jf(n,a);if(Rt(t[1]))return Ea(a,r);if(a===!0)a="TRUE";else if(a===!1)a="FALSE";else if(a===""||a==null)return"";return jf(t[1],a,r,t[0])}function ga(e,a){if(typeof a!="number"){a=+a||-1;for(var r=0;r<392;++r){if(_e[r]==null){a<0&&(a=r);continue}if(_e[r]==e){a=r;break}}a<0&&(a=391)}return _e[a]=e,a}function Ts(){_e=If()}var Zf={5:'"$"#,##0_);\\("$"#,##0\\)',6:'"$"#,##0_);[Red]\\("$"#,##0\\)',7:'"$"#,##0.00_);\\("$"#,##0.00\\)',8:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',23:"General",24:"General",25:"General",26:"General",27:"m/d/yy",28:"m/d/yy",29:"m/d/yy",30:"m/d/yy",31:"m/d/yy",32:"h:mm:ss",33:"h:mm:ss",34:"h:mm:ss",35:"h:mm:ss",36:"m/d/yy",41:'_(* #,##0_);_(* (#,##0);_(* "-"_);_(@_)',42:'_("$"* #,##0_);_("$"* (#,##0);_("$"* "-"_);_(@_)',43:'_(* #,##0.00_);_(* (#,##0.00);_(* "-"??_);_(@_)',44:'_("$"* #,##0.00_);_("$"* (#,##0.00);_("$"* "-"??_);_(@_)',50:"m/d/yy",51:"m/d/yy",52:"m/d/yy",53:"m/d/yy",54:"m/d/yy",55:"m/d/yy",56:"m/d/yy",57:"m/d/yy",58:"m/d/yy",59:"0",60:"0.00",61:"#,##0",62:"#,##0.00",63:'"$"#,##0_);\\("$"#,##0\\)',64:'"$"#,##0_);[Red]\\("$"#,##0\\)',65:'"$"#,##0.00_);\\("$"#,##0.00\\)',66:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',67:"0%",68:"0.00%",69:"# ?/?",70:"# ??/??",71:"m/d/yy",72:"m/d/yy",73:"d-mmm-yy",74:"d-mmm",75:"mmm-yy",76:"h:mm",77:"h:mm:ss",78:"m/d/yy h:mm",79:"mm:ss",80:"[h]:mm:ss",81:"mmss.0"},ks=/[dD]+|[mM]+|[yYeE]+|[Hh]+|[Ss]+/g;function qf(e){var a=typeof e=="number"?_e[e]:e;return a=a.replace(ks,"(\\d+)"),new RegExp("^"+a+"$")}function Qf(e,a,r){var n=-1,t=-1,s=-1,i=-1,c=-1,f=-1;(a.match(ks)||[]).forEach(function(u,p){var h=parseInt(r[p+1],10);switch(u.toLowerCase().charAt(0)){case"y":n=h;break;case"d":s=h;break;case"h":i=h;break;case"s":f=h;break;case"m":i>=0?c=h:t=h;break}}),f>=0&&c==-1&&t>=0&&(c=t,t=-1);var l=(""+(n>=0?n:new Date().getFullYear())).slice(-4)+"-"+("00"+(t>=1?t:1)).slice(-2)+"-"+("00"+(s>=1?s:1)).slice(-2);l.length==7&&(l="0"+l),l.length==8&&(l="20"+l);var o=("00"+(i>=0?i:0)).slice(-2)+":"+("00"+(c>=0?c:0)).slice(-2)+":"+("00"+(f>=0?f:0)).slice(-2);return i==-1&&c==-1&&f==-1?l:n==-1&&t==-1&&s==-1?o:l+"T"+o}var eo=function(){var e={};e.version="1.2.0";function a(){for(var S=0,U=new Array(256),R=0;R!=256;++R)S=R,S=S&1?-306674912^S>>>1:S>>>1,S=S&1?-306674912^S>>>1:S>>>1,S=S&1?-306674912^S>>>1:S>>>1,S=S&1?-306674912^S>>>1:S>>>1,S=S&1?-306674912^S>>>1:S>>>1,S=S&1?-306674912^S>>>1:S>>>1,S=S&1?-306674912^S>>>1:S>>>1,S=S&1?-306674912^S>>>1:S>>>1,U[R]=S;return typeof Int32Array<"u"?new Int32Array(U):U}var r=a();function n(S){var U=0,R=0,z=0,G=typeof Int32Array<"u"?new Int32Array(4096):new Array(4096);for(z=0;z!=256;++z)G[z]=S[z];for(z=0;z!=256;++z)for(R=S[z],U=256+z;U<4096;U+=256)R=G[U]=R>>>8^S[R&255];var b=[];for(z=1;z!=16;++z)b[z-1]=typeof Int32Array<"u"?G.subarray(z*256,z*256+256):G.slice(z*256,z*256+256);return b}var t=n(r),s=t[0],i=t[1],c=t[2],f=t[3],l=t[4],o=t[5],u=t[6],p=t[7],h=t[8],d=t[9],x=t[10],g=t[11],w=t[12],y=t[13],m=t[14];function O(S,U){for(var R=U^-1,z=0,G=S.length;z<G;)R=R>>>8^r[(R^S.charCodeAt(z++))&255];return~R}function P(S,U){for(var R=U^-1,z=S.length-15,G=0;G<z;)R=m[S[G++]^R&255]^y[S[G++]^R>>8&255]^w[S[G++]^R>>16&255]^g[S[G++]^R>>>24]^x[S[G++]]^d[S[G++]]^h[S[G++]]^p[S[G++]]^u[S[G++]]^o[S[G++]]^l[S[G++]]^f[S[G++]]^c[S[G++]]^i[S[G++]]^s[S[G++]]^r[S[G++]];for(z+=15;G<z;)R=R>>>8^r[(R^S[G++])&255];return~R}function D(S,U){for(var R=U^-1,z=0,G=S.length,b=0,j=0;z<G;)b=S.charCodeAt(z++),b<128?R=R>>>8^r[(R^b)&255]:b<2048?(R=R>>>8^r[(R^(192|b>>6&31))&255],R=R>>>8^r[(R^(128|b&63))&255]):b>=55296&&b<57344?(b=(b&1023)+64,j=S.charCodeAt(z++)&1023,R=R>>>8^r[(R^(240|b>>8&7))&255],R=R>>>8^r[(R^(128|b>>2&63))&255],R=R>>>8^r[(R^(128|j>>6&15|(b&3)<<4))&255],R=R>>>8^r[(R^(128|j&63))&255]):(R=R>>>8^r[(R^(224|b>>12&15))&255],R=R>>>8^r[(R^(128|b>>6&63))&255],R=R>>>8^r[(R^(128|b&63))&255]);return~R}return e.table=r,e.bstr=O,e.buf=P,e.str=D,e}(),Ce=function(){var a={};a.version="1.2.1";function r(v,T){for(var _=v.split("/"),E=T.split("/"),k=0,F=0,V=Math.min(_.length,E.length);k<V;++k){if(F=_[k].length-E[k].length)return F;if(_[k]!=E[k])return _[k]<E[k]?-1:1}return _.length-E.length}function n(v){if(v.charAt(v.length-1)=="/")return v.slice(0,-1).indexOf("/")===-1?v:n(v.slice(0,-1));var T=v.lastIndexOf("/");return T===-1?v:v.slice(0,T+1)}function t(v){if(v.charAt(v.length-1)=="/")return t(v.slice(0,-1));var T=v.lastIndexOf("/");return T===-1?v:v.slice(T+1)}function s(v,T){typeof T=="string"&&(T=new Date(T));var _=T.getHours();_=_<<6|T.getMinutes(),_=_<<5|T.getSeconds()>>>1,v.write_shift(2,_);var E=T.getFullYear()-1980;E=E<<4|T.getMonth()+1,E=E<<5|T.getDate(),v.write_shift(2,E)}function i(v){var T=v.read_shift(2)&65535,_=v.read_shift(2)&65535,E=new Date,k=_&31;_>>>=5;var F=_&15;_>>>=4,E.setMilliseconds(0),E.setFullYear(_+1980),E.setMonth(F-1),E.setDate(k);var V=T&31;T>>>=5;var K=T&63;return T>>>=6,E.setHours(T),E.setMinutes(K),E.setSeconds(V<<1),E}function c(v){rr(v,0);for(var T={},_=0;v.l<=v.length-4;){var E=v.read_shift(2),k=v.read_shift(2),F=v.l+k,V={};switch(E){case 21589:_=v.read_shift(1),_&1&&(V.mtime=v.read_shift(4)),k>5&&(_&2&&(V.atime=v.read_shift(4)),_&4&&(V.ctime=v.read_shift(4))),V.mtime&&(V.mt=new Date(V.mtime*1e3));break}v.l=F,T[E]=V}return T}var f;function l(){return f||(f={})}function o(v,T){if(v[0]==80&&v[1]==75)return j0(v,T);if((v[0]|32)==109&&(v[1]|32)==105)return Zi(v,T);if(v.length<512)throw new Error("CFB file size "+v.length+" < 512");var _=3,E=512,k=0,F=0,V=0,K=0,M=0,H=[],W=v.slice(0,512);rr(W,0);var re=u(W);switch(_=re[0],_){case 3:E=512;break;case 4:E=4096;break;case 0:if(re[1]==0)return j0(v,T);default:throw new Error("Major Version: Expected 3 or 4 saw "+_)}E!==512&&(W=v.slice(0,E),rr(W,28));var ce=v.slice(0,E);p(W,_);var he=W.read_shift(4,"i");if(_===3&&he!==0)throw new Error("# Directory Sectors: Expected 0 saw "+he);W.l+=4,V=W.read_shift(4,"i"),W.l+=4,W.chk("00100000","Mini Stream Cutoff Size: "),K=W.read_shift(4,"i"),k=W.read_shift(4,"i"),M=W.read_shift(4,"i"),F=W.read_shift(4,"i");for(var ae=-1,le=0;le<109&&(ae=W.read_shift(4,"i"),!(ae<0));++le)H[le]=ae;var ge=h(v,E);g(M,F,ge,E,H);var Be=y(ge,V,H,E);Be[V].name="!Directory",k>0&&K!==j&&(Be[K].name="!MiniFAT"),Be[H[0]].name="!FAT",Be.fat_addrs=H,Be.ssz=E;var Me={},nr=[],Ha=[],Wa=[];m(V,Be,ge,nr,k,Me,Ha,K),d(Ha,Wa,nr),nr.shift();var Ga={FileIndex:Ha,FullPaths:Wa};return T&&T.raw&&(Ga.raw={header:ce,sectors:ge}),Ga}function u(v){if(v[v.l]==80&&v[v.l+1]==75)return[0,0];v.chk(Y,"Header Signature: "),v.l+=16;var T=v.read_shift(2,"u");return[v.read_shift(2,"u"),T]}function p(v,T){var _=9;switch(v.l+=2,_=v.read_shift(2)){case 9:if(T!=3)throw new Error("Sector Shift: Expected 9 saw "+_);break;case 12:if(T!=4)throw new Error("Sector Shift: Expected 12 saw "+_);break;default:throw new Error("Sector Shift: Expected 9 or 12 saw "+_)}v.chk("0600","Mini Sector Shift: "),v.chk("000000000000","Reserved: ")}function h(v,T){for(var _=Math.ceil(v.length/T)-1,E=[],k=1;k<_;++k)E[k-1]=v.slice(k*T,(k+1)*T);return E[_-1]=v.slice(_*T),E}function d(v,T,_){for(var E=0,k=0,F=0,V=0,K=0,M=_.length,H=[],W=[];E<M;++E)H[E]=W[E]=E,T[E]=_[E];for(;K<W.length;++K)E=W[K],k=v[E].L,F=v[E].R,V=v[E].C,H[E]===E&&(k!==-1&&H[k]!==k&&(H[E]=H[k]),F!==-1&&H[F]!==F&&(H[E]=H[F])),V!==-1&&(H[V]=E),k!==-1&&E!=H[E]&&(H[k]=H[E],W.lastIndexOf(k)<K&&W.push(k)),F!==-1&&E!=H[E]&&(H[F]=H[E],W.lastIndexOf(F)<K&&W.push(F));for(E=1;E<M;++E)H[E]===E&&(F!==-1&&H[F]!==F?H[E]=H[F]:k!==-1&&H[k]!==k&&(H[E]=H[k]));for(E=1;E<M;++E)if(v[E].type!==0){if(K=E,K!=H[K])do K=H[K],T[E]=T[K]+"/"+T[E];while(K!==0&&H[K]!==-1&&K!=H[K]);H[E]=-1}for(T[0]+="/",E=1;E<M;++E)v[E].type!==2&&(T[E]+="/")}function x(v,T,_){for(var E=v.start,k=v.size,F=[],V=E;_&&k>0&&V>=0;)F.push(T.slice(V*b,V*b+b)),k-=b,V=xa(_,V*4);return F.length===0?je(0):aa(F).slice(0,v.size)}function g(v,T,_,E,k){var F=j;if(v===j){if(T!==0)throw new Error("DIFAT chain shorter than expected")}else if(v!==-1){var V=_[v],K=(E>>>2)-1;if(!V)return;for(var M=0;M<K&&(F=xa(V,M*4))!==j;++M)k.push(F);g(xa(V,E-4),T-1,_,E,k)}}function w(v,T,_,E,k){var F=[],V=[];k||(k=[]);var K=E-1,M=0,H=0;for(M=T;M>=0;){k[M]=!0,F[F.length]=M,V.push(v[M]);var W=_[Math.floor(M*4/E)];if(H=M*4&K,E<4+H)throw new Error("FAT boundary crossed: "+M+" 4 "+E);if(!v[W])break;M=xa(v[W],H)}return{nodes:F,data:En([V])}}function y(v,T,_,E){var k=v.length,F=[],V=[],K=[],M=[],H=E-1,W=0,re=0,ce=0,he=0;for(W=0;W<k;++W)if(K=[],ce=W+T,ce>=k&&(ce-=k),!V[ce]){M=[];var ae=[];for(re=ce;re>=0;){ae[re]=!0,V[re]=!0,K[K.length]=re,M.push(v[re]);var le=_[Math.floor(re*4/E)];if(he=re*4&H,E<4+he)throw new Error("FAT boundary crossed: "+re+" 4 "+E);if(!v[le]||(re=xa(v[le],he),ae[re]))break}F[ce]={nodes:K,data:En([M])}}return F}function m(v,T,_,E,k,F,V,K){for(var M=0,H=E.length?2:0,W=T[v].data,re=0,ce=0,he;re<W.length;re+=128){var ae=W.slice(re,re+128);rr(ae,64),ce=ae.read_shift(2),he=y0(ae,0,ce-H),E.push(he);var le={name:he,type:ae.read_shift(1),color:ae.read_shift(1),L:ae.read_shift(4,"i"),R:ae.read_shift(4,"i"),C:ae.read_shift(4,"i"),clsid:ae.read_shift(16),state:ae.read_shift(4,"i"),start:0,size:0},ge=ae.read_shift(2)+ae.read_shift(2)+ae.read_shift(2)+ae.read_shift(2);ge!==0&&(le.ct=O(ae,ae.l-8));var Be=ae.read_shift(2)+ae.read_shift(2)+ae.read_shift(2)+ae.read_shift(2);Be!==0&&(le.mt=O(ae,ae.l-8)),le.start=ae.read_shift(4,"i"),le.size=ae.read_shift(4,"i"),le.size<0&&le.start<0&&(le.size=le.type=0,le.start=j,le.name=""),le.type===5?(M=le.start,k>0&&M!==j&&(T[M].name="!StreamData")):le.size>=4096?(le.storage="fat",T[le.start]===void 0&&(T[le.start]=w(_,le.start,T.fat_addrs,T.ssz)),T[le.start].name=le.name,le.content=T[le.start].data.slice(0,le.size)):(le.storage="minifat",le.size<0?le.size=0:M!==j&&le.start!==j&&T[M]&&(le.content=x(le,T[M].data,(T[K]||{}).data))),le.content&&rr(le.content,0),F[he]=le,V.push(le)}}function O(v,T){return new Date((wr(v,T+4)/1e7*Math.pow(2,32)+wr(v,T)/1e7-11644473600)*1e3)}function P(v,T){return l(),o(f.readFileSync(v),T)}function D(v,T){var _=T&&T.type;switch(_||Se&&Buffer.isBuffer(v)&&(_="buffer"),_||"base64"){case"file":return P(v,T);case"base64":return o(br(Fr(v)),T);case"binary":return o(br(v),T)}return o(v,T)}function S(v,T){var _=T||{},E=_.root||"Root Entry";if(v.FullPaths||(v.FullPaths=[]),v.FileIndex||(v.FileIndex=[]),v.FullPaths.length!==v.FileIndex.length)throw new Error("inconsistent CFB structure");v.FullPaths.length===0&&(v.FullPaths[0]=E+"/",v.FileIndex[0]={name:E,type:5}),_.CLSID&&(v.FileIndex[0].clsid=_.CLSID),U(v)}function U(v){var T="Sh33tJ5";if(!Ce.find(v,"/"+T)){var _=je(4);_[0]=55,_[1]=_[3]=50,_[2]=54,v.FileIndex.push({name:T,type:2,content:_,size:4,L:69,R:69,C:69}),v.FullPaths.push(v.FullPaths[0]+T),R(v)}}function R(v,T){S(v);for(var _=!1,E=!1,k=v.FullPaths.length-1;k>=0;--k){var F=v.FileIndex[k];switch(F.type){case 0:E?_=!0:(v.FileIndex.pop(),v.FullPaths.pop());break;case 1:case 2:case 5:E=!0,isNaN(F.R*F.L*F.C)&&(_=!0),F.R>-1&&F.L>-1&&F.R==F.L&&(_=!0);break;default:_=!0;break}}if(!(!_&&!T)){var V=new Date(1987,1,19),K=0,M=Object.create?Object.create(null):{},H=[];for(k=0;k<v.FullPaths.length;++k)M[v.FullPaths[k]]=!0,v.FileIndex[k].type!==0&&H.push([v.FullPaths[k],v.FileIndex[k]]);for(k=0;k<H.length;++k){var W=n(H[k][0]);E=M[W],E||(H.push([W,{name:t(W).replace("/",""),type:1,clsid:se,ct:V,mt:V,content:null}]),M[W]=!0)}for(H.sort(function(he,ae){return r(he[0],ae[0])}),v.FullPaths=[],v.FileIndex=[],k=0;k<H.length;++k)v.FullPaths[k]=H[k][0],v.FileIndex[k]=H[k][1];for(k=0;k<H.length;++k){var re=v.FileIndex[k],ce=v.FullPaths[k];if(re.name=t(ce).replace("/",""),re.L=re.R=re.C=-(re.color=1),re.size=re.content?re.content.length:0,re.start=0,re.clsid=re.clsid||se,k===0)re.C=H.length>1?1:-1,re.size=0,re.type=5;else if(ce.slice(-1)=="/"){for(K=k+1;K<H.length&&n(v.FullPaths[K])!=ce;++K);for(re.C=K>=H.length?-1:K,K=k+1;K<H.length&&n(v.FullPaths[K])!=n(ce);++K);re.R=K>=H.length?-1:K,re.type=1}else n(v.FullPaths[k+1]||"")==n(ce)&&(re.R=k+1),re.type=2}}}function z(v,T){var _=T||{};if(_.fileType=="mad")return qi(v,_);switch(R(v),_.fileType){case"zip":return Xi(v,_)}var E=function(he){for(var ae=0,le=0,ge=0;ge<he.FileIndex.length;++ge){var Be=he.FileIndex[ge];if(Be.content){var Me=Be.content.length;Me>0&&(Me<4096?ae+=Me+63>>6:le+=Me+511>>9)}}for(var nr=he.FullPaths.length+3>>2,Ha=ae+7>>3,Wa=ae+127>>7,Ga=Ha+le+nr+Wa,ha=Ga+127>>7,jt=ha<=109?0:Math.ceil((ha-109)/127);Ga+ha+jt+127>>7>ha;)jt=++ha<=109?0:Math.ceil((ha-109)/127);var zr=[1,jt,ha,Wa,nr,le,ae,0];return he.FileIndex[0].size=ae<<6,zr[7]=(he.FileIndex[0].start=zr[0]+zr[1]+zr[2]+zr[3]+zr[4]+zr[5])+(zr[6]+7>>3),zr}(v),k=je(E[7]<<9),F=0,V=0;{for(F=0;F<8;++F)k.write_shift(1,Q[F]);for(F=0;F<8;++F)k.write_shift(2,0);for(k.write_shift(2,62),k.write_shift(2,3),k.write_shift(2,65534),k.write_shift(2,9),k.write_shift(2,6),F=0;F<3;++F)k.write_shift(2,0);for(k.write_shift(4,0),k.write_shift(4,E[2]),k.write_shift(4,E[0]+E[1]+E[2]+E[3]-1),k.write_shift(4,0),k.write_shift(4,4096),k.write_shift(4,E[3]?E[0]+E[1]+E[2]-1:j),k.write_shift(4,E[3]),k.write_shift(-4,E[1]?E[0]-1:j),k.write_shift(4,E[1]),F=0;F<109;++F)k.write_shift(-4,F<E[2]?E[1]+F:-1)}if(E[1])for(V=0;V<E[1];++V){for(;F<236+V*127;++F)k.write_shift(-4,F<E[2]?E[1]+F:-1);k.write_shift(-4,V===E[1]-1?j:V+1)}var K=function(he){for(V+=he;F<V-1;++F)k.write_shift(-4,F+1);he&&(++F,k.write_shift(-4,j))};for(V=F=0,V+=E[1];F<V;++F)k.write_shift(-4,ne.DIFSECT);for(V+=E[2];F<V;++F)k.write_shift(-4,ne.FATSECT);K(E[3]),K(E[4]);for(var M=0,H=0,W=v.FileIndex[0];M<v.FileIndex.length;++M)W=v.FileIndex[M],W.content&&(H=W.content.length,!(H<4096)&&(W.start=V,K(H+511>>9)));for(K(E[6]+7>>3);k.l&511;)k.write_shift(-4,ne.ENDOFCHAIN);for(V=F=0,M=0;M<v.FileIndex.length;++M)W=v.FileIndex[M],W.content&&(H=W.content.length,!(!H||H>=4096)&&(W.start=V,K(H+63>>6)));for(;k.l&511;)k.write_shift(-4,ne.ENDOFCHAIN);for(F=0;F<E[4]<<2;++F){var re=v.FullPaths[F];if(!re||re.length===0){for(M=0;M<17;++M)k.write_shift(4,0);for(M=0;M<3;++M)k.write_shift(4,-1);for(M=0;M<12;++M)k.write_shift(4,0);continue}W=v.FileIndex[F],F===0&&(W.start=W.size?W.start-1:j);var ce=F===0&&_.root||W.name;if(H=2*(ce.length+1),k.write_shift(64,ce,"utf16le"),k.write_shift(2,H),k.write_shift(1,W.type),k.write_shift(1,W.color),k.write_shift(-4,W.L),k.write_shift(-4,W.R),k.write_shift(-4,W.C),W.clsid)k.write_shift(16,W.clsid,"hex");else for(M=0;M<4;++M)k.write_shift(4,0);k.write_shift(4,W.state||0),k.write_shift(4,0),k.write_shift(4,0),k.write_shift(4,0),k.write_shift(4,0),k.write_shift(4,W.start),k.write_shift(4,W.size),k.write_shift(4,0)}for(F=1;F<v.FileIndex.length;++F)if(W=v.FileIndex[F],W.size>=4096)if(k.l=W.start+1<<9,Se&&Buffer.isBuffer(W.content))W.content.copy(k,k.l,0,W.size),k.l+=W.size+511&-512;else{for(M=0;M<W.size;++M)k.write_shift(1,W.content[M]);for(;M&511;++M)k.write_shift(1,0)}for(F=1;F<v.FileIndex.length;++F)if(W=v.FileIndex[F],W.size>0&&W.size<4096)if(Se&&Buffer.isBuffer(W.content))W.content.copy(k,k.l,0,W.size),k.l+=W.size+63&-64;else{for(M=0;M<W.size;++M)k.write_shift(1,W.content[M]);for(;M&63;++M)k.write_shift(1,0)}if(Se)k.l=k.length;else for(;k.l<k.length;)k.write_shift(1,0);return k}function G(v,T){var _=v.FullPaths.map(function(M){return M.toUpperCase()}),E=_.map(function(M){var H=M.split("/");return H[H.length-(M.slice(-1)=="/"?2:1)]}),k=!1;T.charCodeAt(0)===47?(k=!0,T=_[0].slice(0,-1)+T):k=T.indexOf("/")!==-1;var F=T.toUpperCase(),V=k===!0?_.indexOf(F):E.indexOf(F);if(V!==-1)return v.FileIndex[V];var K=!F.match(Xa);for(F=F.replace(gr,""),K&&(F=F.replace(Xa,"!")),V=0;V<_.length;++V)if((K?_[V].replace(Xa,"!"):_[V]).replace(gr,"")==F||(K?E[V].replace(Xa,"!"):E[V]).replace(gr,"")==F)return v.FileIndex[V];return null}var b=64,j=-2,Y="d0cf11e0a1b11ae1",Q=[208,207,17,224,161,177,26,225],se="00000000000000000000000000000000",ne={MAXREGSECT:-6,DIFSECT:-4,FATSECT:-3,ENDOFCHAIN:j,FREESECT:-1,HEADER_SIGNATURE:Y,HEADER_MINOR_VERSION:"3e00",MAXREGSID:-6,NOSTREAM:-1,HEADER_CLSID:se,EntryTypes:["unknown","storage","stream","lockbytes","property","root"]};function De(v,T,_){l();var E=z(v,_);f.writeFileSync(T,E)}function L(v){for(var T=new Array(v.length),_=0;_<v.length;++_)T[_]=String.fromCharCode(v[_]);return T.join("")}function X(v,T){var _=z(v,T);switch(T&&T.type||"buffer"){case"file":return l(),f.writeFileSync(T.filename,_),_;case"binary":return typeof _=="string"?_:L(_);case"base64":return rn(typeof _=="string"?_:L(_));case"buffer":if(Se)return Buffer.isBuffer(_)?_:wa(_);case"array":return typeof _=="string"?br(_):_}return _}var ue;function A(v){try{var T=v.InflateRaw,_=new T;if(_._processChunk(new Uint8Array([3,0]),_._finishFlushFlag),_.bytesRead)ue=v;else throw new Error("zlib does not expose bytesRead")}catch(E){console.error("cannot use native zlib: "+(E.message||E))}}function B(v,T){if(!ue)return K0(v,T);var _=ue.InflateRaw,E=new _,k=E._processChunk(v.slice(v.l),E._finishFlushFlag);return v.l+=E.bytesRead,k}function N(v){return ue?ue.deflateRawSync(v):Te(v)}var I=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],J=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258],Z=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577];function ie(v){var T=(v<<1|v<<11)&139536|(v<<5|v<<15)&558144;return(T>>16|T>>8|T)&255}for(var te=typeof Uint8Array<"u",ee=te?new Uint8Array(256):[],ke=0;ke<256;++ke)ee[ke]=ie(ke);function C(v,T){var _=ee[v&255];return T<=8?_>>>8-T:(_=_<<8|ee[v>>8&255],T<=16?_>>>16-T:(_=_<<8|ee[v>>16&255],_>>>24-T))}function Le(v,T){var _=T&7,E=T>>>3;return(v[E]|(_<=6?0:v[E+1]<<8))>>>_&3}function Fe(v,T){var _=T&7,E=T>>>3;return(v[E]|(_<=5?0:v[E+1]<<8))>>>_&7}function Ie(v,T){var _=T&7,E=T>>>3;return(v[E]|(_<=4?0:v[E+1]<<8))>>>_&15}function Ee(v,T){var _=T&7,E=T>>>3;return(v[E]|(_<=3?0:v[E+1]<<8))>>>_&31}function fe(v,T){var _=T&7,E=T>>>3;return(v[E]|(_<=1?0:v[E+1]<<8))>>>_&127}function Pe(v,T,_){var E=T&7,k=T>>>3,F=(1<<_)-1,V=v[k]>>>E;return _<8-E||(V|=v[k+1]<<8-E,_<16-E)||(V|=v[k+2]<<16-E,_<24-E)||(V|=v[k+3]<<24-E),V&F}function hr(v,T,_){var E=T&7,k=T>>>3;return E<=5?v[k]|=(_&7)<<E:(v[k]|=_<<E&255,v[k+1]=(_&7)>>8-E),T+3}function kr(v,T,_){var E=T&7,k=T>>>3;return _=(_&1)<<E,v[k]|=_,T+1}function Sr(v,T,_){var E=T&7,k=T>>>3;return _<<=E,v[k]|=_&255,_>>>=8,v[k+1]=_,T+8}function Qr(v,T,_){var E=T&7,k=T>>>3;return _<<=E,v[k]|=_&255,_>>>=8,v[k+1]=_&255,v[k+2]=_>>>8,T+16}function Nr(v,T){var _=v.length,E=2*_>T?2*_:T+5,k=0;if(_>=T)return v;if(Se){var F=an(E);if(v.copy)v.copy(F);else for(;k<v.length;++k)F[k]=v[k];return F}else if(te){var V=new Uint8Array(E);if(V.set)V.set(v);else for(;k<_;++k)V[k]=v[k];return V}return v.length=E,v}function we(v){for(var T=new Array(v),_=0;_<v;++_)T[_]=0;return T}function _r(v,T,_){var E=1,k=0,F=0,V=0,K=0,M=v.length,H=te?new Uint16Array(32):we(32);for(F=0;F<32;++F)H[F]=0;for(F=M;F<_;++F)v[F]=0;M=v.length;var W=te?new Uint16Array(M):we(M);for(F=0;F<M;++F)H[k=v[F]]++,E<k&&(E=k),W[F]=0;for(H[0]=0,F=1;F<=E;++F)H[F+16]=K=K+H[F-1]<<1;for(F=0;F<M;++F)K=v[F],K!=0&&(W[F]=H[K+16]++);var re=0;for(F=0;F<M;++F)if(re=v[F],re!=0)for(K=C(W[F],E)>>E-re,V=(1<<E+4-re)-1;V>=0;--V)T[K|V<<re]=re&15|F<<4;return E}var ea=te?new Uint16Array(512):we(512),Va=te?new Uint16Array(32):we(32);if(!te){for(var xr=0;xr<512;++xr)ea[xr]=0;for(xr=0;xr<32;++xr)Va[xr]=0}(function(){for(var v=[],T=0;T<32;T++)v.push(5);_r(v,Va,32);var _=[];for(T=0;T<=143;T++)_.push(8);for(;T<=255;T++)_.push(9);for(;T<=279;T++)_.push(7);for(;T<=287;T++)_.push(8);_r(_,ea,288)})();var Vr=function(){for(var T=te?new Uint8Array(32768):[],_=0,E=0;_<Z.length-1;++_)for(;E<Z[_+1];++E)T[E]=_;for(;E<32768;++E)T[E]=29;var k=te?new Uint8Array(259):[];for(_=0,E=0;_<J.length-1;++_)for(;E<J[_+1];++E)k[E]=_;function F(K,M){for(var H=0;H<K.length;){var W=Math.min(65535,K.length-H),re=H+W==K.length;for(M.write_shift(1,+re),M.write_shift(2,W),M.write_shift(2,~W&65535);W-- >0;)M[M.l++]=K[H++]}return M.l}function V(K,M){for(var H=0,W=0,re=te?new Uint16Array(32768):[];W<K.length;){var ce=Math.min(65535,K.length-W);if(ce<10){for(H=hr(M,H,+(W+ce==K.length)),H&7&&(H+=8-(H&7)),M.l=H/8|0,M.write_shift(2,ce),M.write_shift(2,~ce&65535);ce-- >0;)M[M.l++]=K[W++];H=M.l*8;continue}H=hr(M,H,+(W+ce==K.length)+2);for(var he=0;ce-- >0;){var ae=K[W];he=(he<<5^ae)&32767;var le=-1,ge=0;if((le=re[he])&&(le|=W&-32768,le>W&&(le-=32768),le<W))for(;K[le+ge]==K[W+ge]&&ge<250;)++ge;if(ge>2){ae=k[ge],ae<=22?H=Sr(M,H,ee[ae+1]>>1)-1:(Sr(M,H,3),H+=5,Sr(M,H,ee[ae-23]>>5),H+=3);var Be=ae<8?0:ae-4>>2;Be>0&&(Qr(M,H,ge-J[ae]),H+=Be),ae=T[W-le],H=Sr(M,H,ee[ae]>>3),H-=3;var Me=ae<4?0:ae-2>>1;Me>0&&(Qr(M,H,W-le-Z[ae]),H+=Me);for(var nr=0;nr<ge;++nr)re[he]=W&32767,he=(he<<5^K[W])&32767,++W;ce-=ge-1}else ae<=143?ae=ae+48:H=kr(M,H,1),H=Sr(M,H,ee[ae]),re[he]=W&32767,++W}H=Sr(M,H,0)-1}return M.l=(H+7)/8|0,M.l}return function(M,H){return M.length<8?F(M,H):V(M,H)}}();function Te(v){var T=je(50+Math.floor(v.length*1.1)),_=Vr(v,T);return T.slice(0,_)}var We=te?new Uint16Array(32768):we(32768),Cr=te?new Uint16Array(32768):we(32768),Ke=te?new Uint16Array(128):we(128),ua=1,z0=1;function Wi(v,T){var _=Ee(v,T)+257;T+=5;var E=Ee(v,T)+1;T+=5;var k=Ie(v,T)+4;T+=4;for(var F=0,V=te?new Uint8Array(19):we(19),K=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],M=1,H=te?new Uint8Array(8):we(8),W=te?new Uint8Array(8):we(8),re=V.length,ce=0;ce<k;++ce)V[I[ce]]=F=Fe(v,T),M<F&&(M=F),H[F]++,T+=3;var he=0;for(H[0]=0,ce=1;ce<=M;++ce)W[ce]=he=he+H[ce-1]<<1;for(ce=0;ce<re;++ce)(he=V[ce])!=0&&(K[ce]=W[he]++);var ae=0;for(ce=0;ce<re;++ce)if(ae=V[ce],ae!=0){he=ee[K[ce]]>>8-ae;for(var le=(1<<7-ae)-1;le>=0;--le)Ke[he|le<<ae]=ae&7|ce<<3}var ge=[];for(M=1;ge.length<_+E;)switch(he=Ke[fe(v,T)],T+=he&7,he>>>=3){case 16:for(F=3+Le(v,T),T+=2,he=ge[ge.length-1];F-- >0;)ge.push(he);break;case 17:for(F=3+Fe(v,T),T+=3;F-- >0;)ge.push(0);break;case 18:for(F=11+fe(v,T),T+=7;F-- >0;)ge.push(0);break;default:ge.push(he),M<he&&(M=he);break}var Be=ge.slice(0,_),Me=ge.slice(_);for(ce=_;ce<286;++ce)Be[ce]=0;for(ce=E;ce<30;++ce)Me[ce]=0;return ua=_r(Be,We,286),z0=_r(Me,Cr,30),T}function Gi(v,T){if(v[0]==3&&!(v[1]&3))return[fa(T),2];for(var _=0,E=0,k=an(T||1<<18),F=0,V=k.length>>>0,K=0,M=0;(E&1)==0;){if(E=Fe(v,_),_+=3,E>>>1)E>>1==1?(K=9,M=5):(_=Wi(v,_),K=ua,M=z0);else{_&7&&(_+=8-(_&7));var H=v[_>>>3]|v[(_>>>3)+1]<<8;if(_+=32,H>0)for(!T&&V<F+H&&(k=Nr(k,F+H),V=k.length);H-- >0;)k[F++]=v[_>>>3],_+=8;continue}for(;;){!T&&V<F+32767&&(k=Nr(k,F+32767),V=k.length);var W=Pe(v,_,K),re=E>>>1==1?ea[W]:We[W];if(_+=re&15,re>>>=4,(re>>>8&255)===0)k[F++]=re;else{if(re==256)break;re-=257;var ce=re<8?0:re-4>>2;ce>5&&(ce=0);var he=F+J[re];ce>0&&(he+=Pe(v,_,ce),_+=ce),W=Pe(v,_,M),re=E>>>1==1?Va[W]:Cr[W],_+=re&15,re>>>=4;var ae=re<4?0:re-2>>1,le=Z[re];for(ae>0&&(le+=Pe(v,_,ae),_+=ae),!T&&V<he&&(k=Nr(k,he+100),V=k.length);F<he;)k[F]=k[F-le],++F}}}return T?[k,_+7>>>3]:[k.slice(0,F),_+7>>>3]}function K0(v,T){var _=v.slice(v.l||0),E=Gi(_,T);return v.l+=E[1],E[0]}function Y0(v,T){if(v)typeof console<"u"&&console.error(T);else throw new Error(T)}function j0(v,T){var _=v;rr(_,0);var E=[],k=[],F={FileIndex:E,FullPaths:k};S(F,{root:T.root});for(var V=_.length-4;(_[V]!=80||_[V+1]!=75||_[V+2]!=5||_[V+3]!=6)&&V>=0;)--V;_.l=V+4,_.l+=4;var K=_.read_shift(2);_.l+=6;var M=_.read_shift(4);for(_.l=M,V=0;V<K;++V){_.l+=20;var H=_.read_shift(4),W=_.read_shift(4),re=_.read_shift(2),ce=_.read_shift(2),he=_.read_shift(2);_.l+=8;var ae=_.read_shift(4),le=c(_.slice(_.l+re,_.l+re+ce));_.l+=re+ce+he;var ge=_.l;_.l=ae+4,$i(_,H,W,F,le),_.l=ge}return F}function $i(v,T,_,E,k){v.l+=2;var F=v.read_shift(2),V=v.read_shift(2),K=i(v);if(F&8257)throw new Error("Unsupported ZIP encryption");for(var M=v.read_shift(4),H=v.read_shift(4),W=v.read_shift(4),re=v.read_shift(2),ce=v.read_shift(2),he="",ae=0;ae<re;++ae)he+=String.fromCharCode(v[v.l++]);if(ce){var le=c(v.slice(v.l,v.l+ce));(le[21589]||{}).mt&&(K=le[21589].mt),((k||{})[21589]||{}).mt&&(K=k[21589].mt)}v.l+=ce;var ge=v.slice(v.l,v.l+H);switch(V){case 8:ge=B(v,W);break;case 0:break;default:throw new Error("Unsupported ZIP Compression method "+V)}var Be=!1;F&8&&(M=v.read_shift(4),M==134695760&&(M=v.read_shift(4),Be=!0),H=v.read_shift(4),W=v.read_shift(4)),H!=T&&Y0(Be,"Bad compressed size: "+T+" != "+H),W!=_&&Y0(Be,"Bad uncompressed size: "+_+" != "+W),Yt(E,he,ge,{unsafe:!0,mt:K})}function Xi(v,T){var _=T||{},E=[],k=[],F=je(1),V=_.compression?8:0,K=0,M=0,H=0,W=0,re=0,ce=v.FullPaths[0],he=ce,ae=v.FileIndex[0],le=[],ge=0;for(M=1;M<v.FullPaths.length;++M)if(he=v.FullPaths[M].slice(ce.length),ae=v.FileIndex[M],!(!ae.size||!ae.content||he=="Sh33tJ5")){var Be=W,Me=je(he.length);for(H=0;H<he.length;++H)Me.write_shift(1,he.charCodeAt(H)&127);Me=Me.slice(0,Me.l),le[re]=eo.buf(ae.content,0);var nr=ae.content;V==8&&(nr=N(nr)),F=je(30),F.write_shift(4,67324752),F.write_shift(2,20),F.write_shift(2,K),F.write_shift(2,V),ae.mt?s(F,ae.mt):F.write_shift(4,0),F.write_shift(-4,le[re]),F.write_shift(4,nr.length),F.write_shift(4,ae.content.length),F.write_shift(2,Me.length),F.write_shift(2,0),W+=F.length,E.push(F),W+=Me.length,E.push(Me),W+=nr.length,E.push(nr),F=je(46),F.write_shift(4,33639248),F.write_shift(2,0),F.write_shift(2,20),F.write_shift(2,K),F.write_shift(2,V),F.write_shift(4,0),F.write_shift(-4,le[re]),F.write_shift(4,nr.length),F.write_shift(4,ae.content.length),F.write_shift(2,Me.length),F.write_shift(2,0),F.write_shift(2,0),F.write_shift(2,0),F.write_shift(2,0),F.write_shift(4,0),F.write_shift(4,Be),ge+=F.l,k.push(F),ge+=Me.length,k.push(Me),++re}return F=je(22),F.write_shift(4,101010256),F.write_shift(2,0),F.write_shift(2,0),F.write_shift(2,re),F.write_shift(2,re),F.write_shift(4,ge),F.write_shift(4,W),F.write_shift(2,0),aa([aa(E),aa(k),F])}var Tt={htm:"text/html",xml:"text/xml",gif:"image/gif",jpg:"image/jpeg",png:"image/png",mso:"application/x-mso",thmx:"application/vnd.ms-officetheme",sh33tj5:"application/octet-stream"};function zi(v,T){if(v.ctype)return v.ctype;var _=v.name||"",E=_.match(/\.([^\.]+)$/);return E&&Tt[E[1]]||T&&(E=(_=T).match(/[\.\\]([^\.\\])+$/),E&&Tt[E[1]])?Tt[E[1]]:"application/octet-stream"}function Ki(v){for(var T=rn(v),_=[],E=0;E<T.length;E+=76)_.push(T.slice(E,E+76));return _.join(`\r
`)+`\r
`}function Yi(v){var T=v.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF=]/g,function(H){var W=H.charCodeAt(0).toString(16).toUpperCase();return"="+(W.length==1?"0"+W:W)});T=T.replace(/ $/mg,"=20").replace(/\t$/mg,"=09"),T.charAt(0)==`
`&&(T="=0D"+T.slice(1)),T=T.replace(/\r(?!\n)/mg,"=0D").replace(/\n\n/mg,`
=0A`).replace(/([^\r\n])\n/mg,"$1=0A");for(var _=[],E=T.split(`\r
`),k=0;k<E.length;++k){var F=E[k];if(F.length==0){_.push("");continue}for(var V=0;V<F.length;){var K=76,M=F.slice(V,V+K);M.charAt(K-1)=="="?K--:M.charAt(K-2)=="="?K-=2:M.charAt(K-3)=="="&&(K-=3),M=F.slice(V,V+K),V+=K,V<F.length&&(M+="="),_.push(M)}}return _.join(`\r
`)}function ji(v){for(var T=[],_=0;_<v.length;++_){for(var E=v[_];_<=v.length&&E.charAt(E.length-1)=="=";)E=E.slice(0,E.length-1)+v[++_];T.push(E)}for(var k=0;k<T.length;++k)T[k]=T[k].replace(/[=][0-9A-Fa-f]{2}/g,function(F){return String.fromCharCode(parseInt(F.slice(1),16))});return br(T.join(`\r
`))}function Ji(v,T,_){for(var E="",k="",F="",V,K=0;K<10;++K){var M=T[K];if(!M||M.match(/^\s*$/))break;var H=M.match(/^(.*?):\s*([^\s].*)$/);if(H)switch(H[1].toLowerCase()){case"content-location":E=H[2].trim();break;case"content-type":F=H[2].trim();break;case"content-transfer-encoding":k=H[2].trim();break}}switch(++K,k.toLowerCase()){case"base64":V=br(Fr(T.slice(K).join("")));break;case"quoted-printable":V=ji(T.slice(K));break;default:throw new Error("Unsupported Content-Transfer-Encoding "+k)}var W=Yt(v,E.slice(_.length),V,{unsafe:!0});F&&(W.ctype=F)}function Zi(v,T){if(L(v.slice(0,13)).toLowerCase()!="mime-version:")throw new Error("Unsupported MAD header");var _=T&&T.root||"",E=(Se&&Buffer.isBuffer(v)?v.toString("binary"):L(v)).split(`\r
`),k=0,F="";for(k=0;k<E.length;++k)if(F=E[k],!!/^Content-Location:/i.test(F)&&(F=F.slice(F.indexOf("file")),_||(_=F.slice(0,F.lastIndexOf("/")+1)),F.slice(0,_.length)!=_))for(;_.length>0&&(_=_.slice(0,_.length-1),_=_.slice(0,_.lastIndexOf("/")+1),F.slice(0,_.length)!=_););var V=(E[1]||"").match(/boundary="(.*?)"/);if(!V)throw new Error("MAD cannot find boundary");var K="--"+(V[1]||""),M=[],H=[],W={FileIndex:M,FullPaths:H};S(W);var re,ce=0;for(k=0;k<E.length;++k){var he=E[k];he!==K&&he!==K+"--"||(ce++&&Ji(W,E.slice(re,k),_),re=k)}return W}function qi(v,T){var _=T||{},E=_.boundary||"SheetJS";E="------="+E;for(var k=["MIME-Version: 1.0",'Content-Type: multipart/related; boundary="'+E.slice(2)+'"',"","",""],F=v.FullPaths[0],V=F,K=v.FileIndex[0],M=1;M<v.FullPaths.length;++M)if(V=v.FullPaths[M].slice(F.length),K=v.FileIndex[M],!(!K.size||!K.content||V=="Sh33tJ5")){V=V.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF]/g,function(ge){return"_x"+ge.charCodeAt(0).toString(16)+"_"}).replace(/[\u0080-\uFFFF]/g,function(ge){return"_u"+ge.charCodeAt(0).toString(16)+"_"});for(var H=K.content,W=Se&&Buffer.isBuffer(H)?H.toString("binary"):L(H),re=0,ce=Math.min(1024,W.length),he=0,ae=0;ae<=ce;++ae)(he=W.charCodeAt(ae))>=32&&he<128&&++re;var le=re>=ce*4/5;k.push(E),k.push("Content-Location: "+(_.root||"file:///C:/SheetJS/")+V),k.push("Content-Transfer-Encoding: "+(le?"quoted-printable":"base64")),k.push("Content-Type: "+zi(K,V)),k.push(""),k.push(le?Yi(W):Ki(W))}return k.push(E+`--\r
`),k.join(`\r
`)}function Qi(v){var T={};return S(T,v),T}function Yt(v,T,_,E){var k=E&&E.unsafe;k||S(v);var F=!k&&Ce.find(v,T);if(!F){var V=v.FullPaths[0];T.slice(0,V.length)==V?V=T:(V.slice(-1)!="/"&&(V+="/"),V=(V+T).replace("//","/")),F={name:t(T),type:2},v.FileIndex.push(F),v.FullPaths.push(V),k||Ce.utils.cfb_gc(v)}return F.content=_,F.size=_?_.length:0,E&&(E.CLSID&&(F.clsid=E.CLSID),E.mt&&(F.mt=E.mt),E.ct&&(F.ct=E.ct)),F}function ec(v,T){S(v);var _=Ce.find(v,T);if(_){for(var E=0;E<v.FileIndex.length;++E)if(v.FileIndex[E]==_)return v.FileIndex.splice(E,1),v.FullPaths.splice(E,1),!0}return!1}function rc(v,T,_){S(v);var E=Ce.find(v,T);if(E){for(var k=0;k<v.FileIndex.length;++k)if(v.FileIndex[k]==E)return v.FileIndex[k].name=t(_),v.FullPaths[k]=_,!0}return!1}function ac(v){R(v,!0)}return a.find=G,a.read=D,a.parse=o,a.write=X,a.writeFile=De,a.utils={cfb_new:Qi,cfb_add:Yt,cfb_del:ec,cfb_mov:rc,cfb_gc:ac,ReadShift:Ya,CheckField:$s,prep_blob:rr,bconcat:aa,use_zlib:A,_deflateRaw:Te,_inflateRaw:K0,consts:ne},a}();function ro(e){if(typeof Deno<"u")return Deno.readFileSync(e);if(typeof $<"u"&&typeof File<"u"&&typeof Folder<"u")try{var a=File(e);a.open("r"),a.encoding="binary";var r=a.read();return a.close(),r}catch(n){if(!n.message||!n.message.match(/onstruct/))throw n}throw new Error("Cannot access file "+e)}function Gr(e){for(var a=Object.keys(e),r=[],n=0;n<a.length;++n)Object.prototype.hasOwnProperty.call(e,a[n])&&r.push(a[n]);return r}function A0(e){for(var a=[],r=Gr(e),n=0;n!==r.length;++n)a[e[r[n]]]=r[n];return a}var Lt=new Date(1899,11,30,0,0,0);function mr(e,a){var r=e.getTime(),n=Lt.getTime()+(e.getTimezoneOffset()-Lt.getTimezoneOffset())*6e4;return(r-n)/(24*60*60*1e3)}var ws=new Date,ao=Lt.getTime()+(ws.getTimezoneOffset()-Lt.getTimezoneOffset())*6e4,un=ws.getTimezoneOffset();function $t(e){var a=new Date;return a.setTime(e*24*60*60*1e3+ao),a.getTimezoneOffset()!==un&&a.setTime(a.getTime()+(a.getTimezoneOffset()-un)*6e4),a}function to(e){var a=0,r=0,n=!1,t=e.match(/P([0-9\.]+Y)?([0-9\.]+M)?([0-9\.]+D)?T([0-9\.]+H)?([0-9\.]+M)?([0-9\.]+S)?/);if(!t)throw new Error("|"+e+"| is not an ISO8601 Duration");for(var s=1;s!=t.length;++s)if(t[s]){switch(r=1,s>3&&(n=!0),t[s].slice(t[s].length-1)){case"Y":throw new Error("Unsupported ISO Duration Field: "+t[s].slice(t[s].length-1));case"D":r*=24;case"H":r*=60;case"M":if(n)r*=60;else throw new Error("Unsupported ISO Duration Field: M")}a+=r*parseInt(t[s],10)}return a}var hn=new Date("2017-02-19T19:06:09.000Z"),As=isNaN(hn.getFullYear())?new Date("2/19/17"):hn,no=As.getFullYear()==2017;function er(e,a){var r=new Date(e);if(no)return a>0?r.setTime(r.getTime()+r.getTimezoneOffset()*60*1e3):a<0&&r.setTime(r.getTime()-r.getTimezoneOffset()*60*1e3),r;if(e instanceof Date)return e;if(As.getFullYear()==1917&&!isNaN(r.getFullYear())){var n=r.getFullYear();return e.indexOf(""+n)>-1||r.setFullYear(r.getFullYear()+100),r}var t=e.match(/\d+/g)||["2017","2","19","0","0","0"],s=new Date(+t[0],+t[1]-1,+t[2],+t[3]||0,+t[4]||0,+t[5]||0);return e.indexOf("Z")>-1&&(s=new Date(s.getTime()-s.getTimezoneOffset()*60*1e3)),s}function Ta(e,a){if(Se&&Buffer.isBuffer(e)){if(a){if(e[0]==255&&e[1]==254)return Ka(e.slice(2).toString("utf16le"));if(e[1]==254&&e[2]==255)return Ka(ls(e.slice(2).toString("binary")))}return e.toString("binary")}if(typeof TextDecoder<"u")try{if(a){if(e[0]==255&&e[1]==254)return Ka(new TextDecoder("utf-16le").decode(e.slice(2)));if(e[0]==254&&e[1]==255)return Ka(new TextDecoder("utf-16be").decode(e.slice(2)))}var r={"€":"","‚":"",ƒ:"","„":"","…":"","†":"","‡":"","ˆ":"","‰":"",Š:"","‹":"",Œ:"",Ž:"","‘":"","’":"","“":"","”":"","•":"","–":"","—":"","˜":"","™":"",š:"","›":"",œ:"",ž:"",Ÿ:""};return Array.isArray(e)&&(e=new Uint8Array(e)),new TextDecoder("latin1").decode(e).replace(/[€‚ƒ„…†‡ˆ‰Š‹ŒŽ‘’“”•–—˜™š›œžŸ]/g,function(s){return r[s]||s})}catch{}for(var n=[],t=0;t!=e.length;++t)n.push(String.fromCharCode(e[t]));return n.join("")}function ar(e){if(typeof JSON<"u"&&!Array.isArray(e))return JSON.parse(JSON.stringify(e));if(typeof e!="object"||e==null)return e;if(e instanceof Date)return new Date(e.getTime());var a={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(a[r]=ar(e[r]));return a}function Ve(e,a){for(var r="";r.length<a;)r+=e;return r}function Ur(e){var a=Number(e);if(!isNaN(a))return isFinite(a)?a:NaN;if(!/\d/.test(e))return a;var r=1,n=e.replace(/([\d]),([\d])/g,"$1$2").replace(/[$]/g,"").replace(/[%]/g,function(){return r*=100,""});return!isNaN(a=Number(n))||(n=n.replace(/[(](.*)[)]/,function(t,s){return r=-r,s}),!isNaN(a=Number(n)))?a/r:a}var so=["january","february","march","april","may","june","july","august","september","october","november","december"];function Pa(e){var a=new Date(e),r=new Date(NaN),n=a.getYear(),t=a.getMonth(),s=a.getDate();if(isNaN(s))return r;var i=e.toLowerCase();if(i.match(/jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec/)){if(i=i.replace(/[^a-z]/g,"").replace(/([^a-z]|^)[ap]m?([^a-z]|$)/,""),i.length>3&&so.indexOf(i)==-1)return r}else if(i.match(/[a-z]/))return r;return n<0||n>8099?r:(t>0||s>1)&&n!=101?a:e.match(/[^-0-9:,\/\\]/)?r:a}var io=function(){var e="abacaba".split(/(:?b)/i).length==5;return function(r,n,t){if(e||typeof n=="string")return r.split(n);for(var s=r.split(n),i=[s[0]],c=1;c<s.length;++c)i.push(t),i.push(s[c]);return i}}();function Fs(e){return e?e.content&&e.type?Ta(e.content,!0):e.data?$a(e.data):e.asNodeBuffer&&Se?$a(e.asNodeBuffer().toString("binary")):e.asBinary?$a(e.asBinary()):e._data&&e._data.getContent?$a(Ta(Array.prototype.slice.call(e._data.getContent(),0))):null:null}function Ss(e){if(!e)return null;if(e.data)return Q0(e.data);if(e.asNodeBuffer&&Se)return e.asNodeBuffer();if(e._data&&e._data.getContent){var a=e._data.getContent();return typeof a=="string"?Q0(a):Array.prototype.slice.call(a)}return e.content&&e.type?e.content:null}function co(e){return e&&e.name.slice(-4)===".bin"?Ss(e):Fs(e)}function Or(e,a){for(var r=e.FullPaths||Gr(e.files),n=a.toLowerCase().replace(/[\/]/g,"\\"),t=n.replace(/\\/g,"/"),s=0;s<r.length;++s){var i=r[s].replace(/^Root Entry[\/]/,"").toLowerCase();if(n==i||t==i)return e.files?e.files[r[s]]:e.FileIndex[s]}return null}function F0(e,a){var r=Or(e,a);if(r==null)throw new Error("Cannot find file "+a+" in zip");return r}function Xe(e,a,r){if(!r)return co(F0(e,a));if(!a)return null;try{return Xe(e,a)}catch{return null}}function Ar(e,a,r){if(!r)return Fs(F0(e,a));if(!a)return null;try{return Ar(e,a)}catch{return null}}function fo(e,a,r){return Ss(F0(e,a))}function xn(e){for(var a=e.FullPaths||Gr(e.files),r=[],n=0;n<a.length;++n)a[n].slice(-1)!="/"&&r.push(a[n].replace(/^Root Entry[\/]/,""));return r.sort()}function oo(e,a,r){if(e.FullPaths){if(typeof r=="string"){var n;return Se?n=wa(r):n=yf(r),Ce.utils.cfb_add(e,a,n)}Ce.utils.cfb_add(e,a,r)}else e.file(a,r)}function Cs(e,a){switch(a.type){case"base64":return Ce.read(e,{type:"base64"});case"binary":return Ce.read(e,{type:"binary"});case"buffer":case"array":return Ce.read(e,{type:"buffer"})}throw new Error("Unrecognized type "+a.type)}function za(e,a){if(e.charAt(0)=="/")return e.slice(1);var r=a.split("/");a.slice(-1)!="/"&&r.pop();for(var n=e.split("/");n.length!==0;){var t=n.shift();t===".."?r.pop():t!=="."&&r.push(t)}return r.join("/")}var ys=`<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\r
`,lo=/([^"\s?>\/]+)\s*=\s*((?:")([^"]*)(?:")|(?:')([^']*)(?:')|([^'">\s]+))/g,dn=/<[\/\?]?[a-zA-Z0-9:_-]+(?:\s+[^"\s?>\/]+\s*=\s*(?:"[^"]*"|'[^']*'|[^'">\s=]+))*\s*[\/\?]?>/mg,uo=/<[^>]*>/g,ur=ys.match(dn)?dn:uo,ho=/<\w*:/,xo=/<(\/?)\w+:/;function pe(e,a,r){for(var n={},t=0,s=0;t!==e.length&&!((s=e.charCodeAt(t))===32||s===10||s===13);++t);if(a||(n[0]=e.slice(0,t)),t===e.length)return n;var i=e.match(lo),c=0,f="",l=0,o="",u="",p=1;if(i)for(l=0;l!=i.length;++l){for(u=i[l],s=0;s!=u.length&&u.charCodeAt(s)!==61;++s);for(o=u.slice(0,s).trim();u.charCodeAt(s+1)==32;)++s;for(p=(t=u.charCodeAt(s+1))==34||t==39?1:0,f=u.slice(s+1+p,u.length-p),c=0;c!=o.length&&o.charCodeAt(c)!==58;++c);if(c===o.length)o.indexOf("_")>0&&(o=o.slice(0,o.indexOf("_"))),n[o]=f,n[o.toLowerCase()]=f;else{var h=(c===5&&o.slice(0,5)==="xmlns"?"xmlns":"")+o.slice(c+1);if(n[h]&&o.slice(c-3,c)=="ext")continue;n[h]=f,n[h.toLowerCase()]=f}}return n}function $r(e){return e.replace(xo,"<$1")}var Ds={"&quot;":'"',"&apos;":"'","&gt;":">","&lt;":"<","&amp;":"&"},po=A0(Ds),Oe=function(){var e=/&(?:quot|apos|gt|lt|amp|#x?([\da-fA-F]+));/ig,a=/_x([\da-fA-F]{4})_/ig;return function r(n){var t=n+"",s=t.indexOf("<![CDATA[");if(s==-1)return t.replace(e,function(c,f){return Ds[c]||String.fromCharCode(parseInt(f,c.indexOf("x")>-1?16:10))||c}).replace(a,function(c,f){return String.fromCharCode(parseInt(f,16))});var i=t.indexOf("]]>");return r(t.slice(0,s))+t.slice(s+9,i)+r(t.slice(i+3))}}(),vo=/[&<>'"]/g,go=/[\u0000-\u001f]/g;function S0(e){var a=e+"";return a.replace(vo,function(r){return po[r]}).replace(/\n/g,"<br/>").replace(go,function(r){return"&#x"+("000"+r.charCodeAt(0).toString(16)).slice(-4)+";"})}var pn=function(){var e=/&#(\d+);/g;function a(r,n){return String.fromCharCode(parseInt(n,10))}return function(n){return n.replace(e,a)}}();function be(e){switch(e){case 1:case!0:case"1":case"true":case"TRUE":return!0;default:return!1}}function Zt(e){for(var a="",r=0,n=0,t=0,s=0,i=0,c=0;r<e.length;){if(n=e.charCodeAt(r++),n<128){a+=String.fromCharCode(n);continue}if(t=e.charCodeAt(r++),n>191&&n<224){i=(n&31)<<6,i|=t&63,a+=String.fromCharCode(i);continue}if(s=e.charCodeAt(r++),n<240){a+=String.fromCharCode((n&15)<<12|(t&63)<<6|s&63);continue}i=e.charCodeAt(r++),c=((n&7)<<18|(t&63)<<12|(s&63)<<6|i&63)-65536,a+=String.fromCharCode(55296+(c>>>10&1023)),a+=String.fromCharCode(56320+(c&1023))}return a}function vn(e){var a=fa(2*e.length),r,n,t=1,s=0,i=0,c;for(n=0;n<e.length;n+=t)t=1,(c=e.charCodeAt(n))<128?r=c:c<224?(r=(c&31)*64+(e.charCodeAt(n+1)&63),t=2):c<240?(r=(c&15)*4096+(e.charCodeAt(n+1)&63)*64+(e.charCodeAt(n+2)&63),t=3):(t=4,r=(c&7)*262144+(e.charCodeAt(n+1)&63)*4096+(e.charCodeAt(n+2)&63)*64+(e.charCodeAt(n+3)&63),r-=65536,i=55296+(r>>>10&1023),r=56320+(r&1023)),i!==0&&(a[s++]=i&255,a[s++]=i>>>8,i=0),a[s++]=r%256,a[s++]=r>>>8;return a.slice(0,s).toString("ucs2")}function gn(e){return wa(e,"binary").toString("utf8")}var wt="foo bar bazâð£",Ne=Se&&(gn(wt)==Zt(wt)&&gn||vn(wt)==Zt(wt)&&vn)||Zt,Ka=Se?function(e){return wa(e,"utf8").toString("binary")}:function(e){for(var a=[],r=0,n=0,t=0;r<e.length;)switch(n=e.charCodeAt(r++),!0){case n<128:a.push(String.fromCharCode(n));break;case n<2048:a.push(String.fromCharCode(192+(n>>6))),a.push(String.fromCharCode(128+(n&63)));break;case(n>=55296&&n<57344):n-=55296,t=e.charCodeAt(r++)-56320+(n<<10),a.push(String.fromCharCode(240+(t>>18&7))),a.push(String.fromCharCode(144+(t>>12&63))),a.push(String.fromCharCode(128+(t>>6&63))),a.push(String.fromCharCode(128+(t&63)));break;default:a.push(String.fromCharCode(224+(n>>12))),a.push(String.fromCharCode(128+(n>>6&63))),a.push(String.fromCharCode(128+(n&63)))}return a.join("")},nt=function(){var e={};return function(r,n){var t=r+"|"+(n||"");return e[t]?e[t]:e[t]=new RegExp("<(?:\\w+:)?"+r+'(?: xml:space="preserve")?(?:[^>]*)>([\\s\\S]*?)</(?:\\w+:)?'+r+">",n||"")}}(),Os=function(){var e=[["nbsp"," "],["middot","·"],["quot",'"'],["apos","'"],["gt",">"],["lt","<"],["amp","&"]].map(function(a){return[new RegExp("&"+a[0]+";","ig"),a[1]]});return function(r){for(var n=r.replace(/^[\t\n\r ]+/,"").replace(/[\t\n\r ]+$/,"").replace(/>\s+/g,">").replace(/\s+</g,"<").replace(/[\t\n\r ]+/g," ").replace(/<\s*[bB][rR]\s*\/?>/g,`
`).replace(/<[^>]*>/g,""),t=0;t<e.length;++t)n=n.replace(e[t][0],e[t][1]);return n}}(),mo=function(){var e={};return function(r){return e[r]!==void 0?e[r]:e[r]=new RegExp("<(?:vt:)?"+r+">([\\s\\S]*?)</(?:vt:)?"+r+">","g")}}(),_o=/<\/?(?:vt:)?variant>/g,Eo=/<(?:vt:)([^>]*)>([\s\S]*)</;function mn(e,a){var r=pe(e),n=e.match(mo(r.baseType))||[],t=[];if(n.length!=r.size){if(a.WTF)throw new Error("unexpected vector length "+n.length+" != "+r.size);return t}return n.forEach(function(s){var i=s.replace(_o,"").match(Eo);i&&t.push({v:Ne(i[2]),t:i[1]})}),t}var To=/(^\s|\s$|\n)/;function ko(e){return Gr(e).map(function(a){return" "+a+'="'+e[a]+'"'}).join("")}function wo(e,a,r){return"<"+e+(r!=null?ko(r):"")+(a!=null?(a.match(To)?' xml:space="preserve"':"")+">"+a+"</"+e:"/")+">"}function C0(e){if(Se&&Buffer.isBuffer(e))return e.toString("utf8");if(typeof e=="string")return e;if(typeof Uint8Array<"u"&&e instanceof Uint8Array)return Ne(Aa(T0(e)));throw new Error("Bad input format: expected Buffer or string")}var st=/<(\/?)([^\s?><!\/:]*:|)([^\s?<>:\/]+)(?:[\s?:\/][^>]*)?>/mg,Ao={CT:"http://schemas.openxmlformats.org/package/2006/content-types"},Fo=["http://schemas.openxmlformats.org/spreadsheetml/2006/main","http://purl.oclc.org/ooxml/spreadsheetml/main","http://schemas.microsoft.com/office/excel/2006/main","http://schemas.microsoft.com/office/excel/2006/2"];function So(e,a){for(var r=1-2*(e[a+7]>>>7),n=((e[a+7]&127)<<4)+(e[a+6]>>>4&15),t=e[a+6]&15,s=5;s>=0;--s)t=t*256+e[a+s];return n==2047?t==0?r*(1/0):NaN:(n==0?n=-1022:(n-=1023,t+=Math.pow(2,52)),r*Math.pow(2,n-52)*t)}function Co(e,a,r){var n=(a<0||1/a==-1/0?1:0)<<7,t=0,s=0,i=n?-a:a;isFinite(i)?i==0?t=s=0:(t=Math.floor(Math.log(i)/Math.LN2),s=i*Math.pow(2,52-t),t<=-1023&&(!isFinite(s)||s<Math.pow(2,52))?t=-1022:(s-=Math.pow(2,52),t+=1023)):(t=2047,s=isNaN(a)?26985:0);for(var c=0;c<=5;++c,s/=256)e[r+c]=s&255;e[r+6]=(t&15)<<4|s&15,e[r+7]=t>>4|n}var _n=function(e){for(var a=[],r=10240,n=0;n<e[0].length;++n)if(e[0][n])for(var t=0,s=e[0][n].length;t<s;t+=r)a.push.apply(a,e[0][n].slice(t,t+r));return a},En=Se?function(e){return e[0].length>0&&Buffer.isBuffer(e[0][0])?Buffer.concat(e[0].map(function(a){return Buffer.isBuffer(a)?a:wa(a)})):_n(e)}:_n,Tn=function(e,a,r){for(var n=[],t=a;t<r;t+=2)n.push(String.fromCharCode(Kr(e,t)));return n.join("").replace(gr,"")},y0=Se?function(e,a,r){return Buffer.isBuffer(e)?e.toString("utf16le",a,r).replace(gr,""):Tn(e,a,r)}:Tn,kn=function(e,a,r){for(var n=[],t=a;t<a+r;++t)n.push(("0"+e[t].toString(16)).slice(-2));return n.join("")},Is=Se?function(e,a,r){return Buffer.isBuffer(e)?e.toString("hex",a,a+r):kn(e,a,r)}:kn,wn=function(e,a,r){for(var n=[],t=a;t<r;t++)n.push(String.fromCharCode(Oa(e,t)));return n.join("")},pt=Se?function(a,r,n){return Buffer.isBuffer(a)?a.toString("utf8",r,n):wn(a,r,n)}:wn,Rs=function(e,a){var r=wr(e,a);return r>0?pt(e,a+4,a+4+r-1):""},Ns=Rs,Ls=function(e,a){var r=wr(e,a);return r>0?pt(e,a+4,a+4+r-1):""},Ps=Ls,bs=function(e,a){var r=2*wr(e,a);return r>0?pt(e,a+4,a+4+r-1):""},Bs=bs,Ms=function(a,r){var n=wr(a,r);return n>0?y0(a,r+4,r+4+n):""},Us=Ms,Vs=function(e,a){var r=wr(e,a);return r>0?pt(e,a+4,a+4+r):""},Hs=Vs,Ws=function(e,a){return So(e,a)},Pt=Ws,Gs=function(a){return Array.isArray(a)||typeof Uint8Array<"u"&&a instanceof Uint8Array};Se&&(Ns=function(a,r){if(!Buffer.isBuffer(a))return Rs(a,r);var n=a.readUInt32LE(r);return n>0?a.toString("utf8",r+4,r+4+n-1):""},Ps=function(a,r){if(!Buffer.isBuffer(a))return Ls(a,r);var n=a.readUInt32LE(r);return n>0?a.toString("utf8",r+4,r+4+n-1):""},Bs=function(a,r){if(!Buffer.isBuffer(a))return bs(a,r);var n=2*a.readUInt32LE(r);return a.toString("utf16le",r+4,r+4+n-1)},Us=function(a,r){if(!Buffer.isBuffer(a))return Ms(a,r);var n=a.readUInt32LE(r);return a.toString("utf16le",r+4,r+4+n)},Hs=function(a,r){if(!Buffer.isBuffer(a))return Vs(a,r);var n=a.readUInt32LE(r);return a.toString("utf8",r+4,r+4+n)},Pt=function(a,r){return Buffer.isBuffer(a)?a.readDoubleLE(r):Ws(a,r)},Gs=function(a){return Buffer.isBuffer(a)||Array.isArray(a)||typeof Uint8Array<"u"&&a instanceof Uint8Array});var Oa=function(e,a){return e[a]},Kr=function(e,a){return e[a+1]*256+e[a]},yo=function(e,a){var r=e[a+1]*256+e[a];return r<32768?r:(65535-r+1)*-1},wr=function(e,a){return e[a+3]*(1<<24)+(e[a+2]<<16)+(e[a+1]<<8)+e[a]},xa=function(e,a){return e[a+3]<<24|e[a+2]<<16|e[a+1]<<8|e[a]},Do=function(e,a){return e[a]<<24|e[a+1]<<16|e[a+2]<<8|e[a+3]};function Ya(e,a){var r="",n,t,s=[],i,c,f,l;switch(a){case"dbcs":if(l=this.l,Se&&Buffer.isBuffer(this))r=this.slice(this.l,this.l+2*e).toString("utf16le");else for(f=0;f<e;++f)r+=String.fromCharCode(Kr(this,l)),l+=2;e*=2;break;case"utf8":r=pt(this,this.l,this.l+e);break;case"utf16le":e*=2,r=y0(this,this.l,this.l+e);break;case"wstr":return Ya.call(this,e,"dbcs");case"lpstr-ansi":r=Ns(this,this.l),e=4+wr(this,this.l);break;case"lpstr-cp":r=Ps(this,this.l),e=4+wr(this,this.l);break;case"lpwstr":r=Bs(this,this.l),e=4+2*wr(this,this.l);break;case"lpp4":e=4+wr(this,this.l),r=Us(this,this.l),e&2&&(e+=2);break;case"8lpp4":e=4+wr(this,this.l),r=Hs(this,this.l),e&3&&(e+=4-(e&3));break;case"cstr":for(e=0,r="";(i=Oa(this,this.l+e++))!==0;)s.push(kt(i));r=s.join("");break;case"_wstr":for(e=0,r="";(i=Kr(this,this.l+e))!==0;)s.push(kt(i)),e+=2;e+=2,r=s.join("");break;case"dbcs-cont":for(r="",l=this.l,f=0;f<e;++f){if(this.lens&&this.lens.indexOf(l)!==-1)return i=Oa(this,l),this.l=l+1,c=Ya.call(this,e-f,i?"dbcs-cont":"sbcs-cont"),s.join("")+c;s.push(kt(Kr(this,l))),l+=2}r=s.join(""),e*=2;break;case"cpstr":case"sbcs-cont":for(r="",l=this.l,f=0;f!=e;++f){if(this.lens&&this.lens.indexOf(l)!==-1)return i=Oa(this,l),this.l=l+1,c=Ya.call(this,e-f,i?"dbcs-cont":"sbcs-cont"),s.join("")+c;s.push(kt(Oa(this,l))),l+=1}r=s.join("");break;default:switch(e){case 1:return n=Oa(this,this.l),this.l++,n;case 2:return n=(a==="i"?yo:Kr)(this,this.l),this.l+=2,n;case 4:case-4:return a==="i"||(this[this.l+3]&128)===0?(n=(e>0?xa:Do)(this,this.l),this.l+=4,n):(t=wr(this,this.l),this.l+=4,t);case 8:case-8:if(a==="f")return e==8?t=Pt(this,this.l):t=Pt([this[this.l+7],this[this.l+6],this[this.l+5],this[this.l+4],this[this.l+3],this[this.l+2],this[this.l+1],this[this.l+0]],0),this.l+=8,t;e=8;case 16:r=Is(this,this.l,e);break}}return this.l+=e,r}var Oo=function(e,a,r){e[r]=a&255,e[r+1]=a>>>8&255,e[r+2]=a>>>16&255,e[r+3]=a>>>24&255},Io=function(e,a,r){e[r]=a&255,e[r+1]=a>>8&255,e[r+2]=a>>16&255,e[r+3]=a>>24&255},Ro=function(e,a,r){e[r]=a&255,e[r+1]=a>>>8&255};function No(e,a,r){var n=0,t=0;if(r==="dbcs"){for(t=0;t!=a.length;++t)Ro(this,a.charCodeAt(t),this.l+2*t);n=2*a.length}else if(r==="sbcs"){for(a=a.replace(/[^\x00-\x7F]/g,"_"),t=0;t!=a.length;++t)this[this.l+t]=a.charCodeAt(t)&255;n=a.length}else if(r==="hex"){for(;t<e;++t)this[this.l++]=parseInt(a.slice(2*t,2*t+2),16)||0;return this}else if(r==="utf16le"){var s=Math.min(this.l+e,this.length);for(t=0;t<Math.min(a.length,e);++t){var i=a.charCodeAt(t);this[this.l++]=i&255,this[this.l++]=i>>8}for(;this.l<s;)this[this.l++]=0;return this}else switch(e){case 1:n=1,this[this.l]=a&255;break;case 2:n=2,this[this.l]=a&255,a>>>=8,this[this.l+1]=a&255;break;case 3:n=3,this[this.l]=a&255,a>>>=8,this[this.l+1]=a&255,a>>>=8,this[this.l+2]=a&255;break;case 4:n=4,Oo(this,a,this.l);break;case 8:if(n=8,r==="f"){Co(this,a,this.l);break}case 16:break;case-4:n=4,Io(this,a,this.l);break}return this.l+=n,this}function $s(e,a){var r=Is(this,this.l,e.length>>1);if(r!==e)throw new Error(a+"Expected "+e+" saw "+r);this.l+=e.length>>1}function rr(e,a){e.l=a,e.read_shift=Ya,e.chk=$s,e.write_shift=No}function lr(e,a){e.l+=a}function je(e){var a=fa(e);return rr(a,0),a}function qr(e,a,r){if(e){var n,t,s;rr(e,e.l||0);for(var i=e.length,c=0,f=0;e.l<i;){c=e.read_shift(1),c&128&&(c=(c&127)+((e.read_shift(1)&127)<<7));var l=Ht[c]||Ht[65535];for(n=e.read_shift(1),s=n&127,t=1;t<4&&n&128;++t)s+=((n=e.read_shift(1))&127)<<7*t;f=e.l+s;var o=l.f&&l.f(e,s,r);if(e.l=f,a(o,l,c))return}}}function f0(){var e=[],a=Se?256:2048,r=function(l){var o=je(l);return rr(o,0),o},n=r(a),t=function(){n&&(n.length>n.l&&(n=n.slice(0,n.l),n.l=n.length),n.length>0&&e.push(n),n=null)},s=function(l){return n&&l<n.length-n.l?n:(t(),n=r(Math.max(l+1,a)))},i=function(){return t(),aa(e)},c=function(l){t(),n=l,n.l==null&&(n.l=n.length),s(a)};return{next:s,push:c,end:i,_bufs:e}}function ja(e,a,r){var n=ar(e);if(a.s?(n.cRel&&(n.c+=a.s.c),n.rRel&&(n.r+=a.s.r)):(n.cRel&&(n.c+=a.c),n.rRel&&(n.r+=a.r)),!r||r.biff<12){for(;n.c>=256;)n.c-=256;for(;n.r>=65536;)n.r-=65536}return n}function An(e,a,r){var n=ar(e);return n.s=ja(n.s,a.s,r),n.e=ja(n.e,a.s,r),n}function Ja(e,a){if(e.cRel&&e.c<0)for(e=ar(e);e.c<0;)e.c+=a>8?16384:256;if(e.rRel&&e.r<0)for(e=ar(e);e.r<0;)e.r+=a>8?1048576:a>5?65536:16384;var r=ve(e);return!e.cRel&&e.cRel!=null&&(r=bo(r)),!e.rRel&&e.rRel!=null&&(r=Lo(r)),r}function qt(e,a){return e.s.r==0&&!e.s.rRel&&e.e.r==(a.biff>=12?1048575:a.biff>=8?65536:16384)&&!e.e.rRel?(e.s.cRel?"":"$")+Je(e.s.c)+":"+(e.e.cRel?"":"$")+Je(e.e.c):e.s.c==0&&!e.s.cRel&&e.e.c==(a.biff>=12?16383:255)&&!e.e.cRel?(e.s.rRel?"":"$")+tr(e.s.r)+":"+(e.e.rRel?"":"$")+tr(e.e.r):Ja(e.s,a.biff)+":"+Ja(e.e,a.biff)}function D0(e){return parseInt(Po(e),10)-1}function tr(e){return""+(e+1)}function Lo(e){return e.replace(/([A-Z]|^)(\d+)$/,"$1$$$2")}function Po(e){return e.replace(/\$(\d+)$/,"$1")}function O0(e){for(var a=Bo(e),r=0,n=0;n!==a.length;++n)r=26*r+a.charCodeAt(n)-64;return r-1}function Je(e){if(e<0)throw new Error("invalid column "+e);var a="";for(++e;e;e=Math.floor((e-1)/26))a=String.fromCharCode((e-1)%26+65)+a;return a}function bo(e){return e.replace(/^([A-Z])/,"$$$1")}function Bo(e){return e.replace(/^\$([A-Z])/,"$1")}function Mo(e){return e.replace(/(\$?[A-Z]*)(\$?\d*)/,"$1,$2").split(",")}function vr(e){for(var a=0,r=0,n=0;n<e.length;++n){var t=e.charCodeAt(n);t>=48&&t<=57?a=10*a+(t-48):t>=65&&t<=90&&(r=26*r+(t-64))}return{c:r-1,r:a-1}}function ve(e){for(var a=e.c+1,r="";a;a=(a-1)/26|0)r=String.fromCharCode((a-1)%26+65)+r;return r+(e.r+1)}function Ma(e){var a=e.indexOf(":");return a==-1?{s:vr(e),e:vr(e)}:{s:vr(e.slice(0,a)),e:vr(e.slice(a+1))}}function ye(e,a){return typeof a>"u"||typeof a=="number"?ye(e.s,e.e):(typeof e!="string"&&(e=ve(e)),typeof a!="string"&&(a=ve(a)),e==a?e:e+":"+a)}function He(e){var a={s:{c:0,r:0},e:{c:0,r:0}},r=0,n=0,t=0,s=e.length;for(r=0;n<s&&!((t=e.charCodeAt(n)-64)<1||t>26);++n)r=26*r+t;for(a.s.c=--r,r=0;n<s&&!((t=e.charCodeAt(n)-48)<0||t>9);++n)r=10*r+t;if(a.s.r=--r,n===s||t!=10)return a.e.c=a.s.c,a.e.r=a.s.r,a;for(++n,r=0;n!=s&&!((t=e.charCodeAt(n)-64)<1||t>26);++n)r=26*r+t;for(a.e.c=--r,r=0;n!=s&&!((t=e.charCodeAt(n)-48)<0||t>9);++n)r=10*r+t;return a.e.r=--r,a}function Fn(e,a){var r=e.t=="d"&&a instanceof Date;if(e.z!=null)try{return e.w=Ir(e.z,r?mr(a):a)}catch{}try{return e.w=Ir((e.XF||{}).numFmtId||(r?14:0),r?mr(a):a)}catch{return""+a}}function Zr(e,a,r){return e==null||e.t==null||e.t=="z"?"":e.w!==void 0?e.w:(e.t=="d"&&!e.z&&r&&r.dateNF&&(e.z=r.dateNF),e.t=="e"?Ca[e.v]||e.v:a==null?Fn(e,e.v):Fn(e,a))}function la(e,a){var r=a&&a.sheet?a.sheet:"Sheet1",n={};return n[r]=e,{SheetNames:[r],Sheets:n}}function Xs(e,a,r){var n=r||{},t=e?Array.isArray(e):n.dense,s=e||(t?[]:{}),i=0,c=0;if(s&&n.origin!=null){if(typeof n.origin=="number")i=n.origin;else{var f=typeof n.origin=="string"?vr(n.origin):n.origin;i=f.r,c=f.c}s["!ref"]||(s["!ref"]="A1:A1")}var l={s:{c:1e7,r:1e7},e:{c:0,r:0}};if(s["!ref"]){var o=He(s["!ref"]);l.s.c=o.s.c,l.s.r=o.s.r,l.e.c=Math.max(l.e.c,o.e.c),l.e.r=Math.max(l.e.r,o.e.r),i==-1&&(l.e.r=i=o.e.r+1)}for(var u=0;u!=a.length;++u)if(a[u]){if(!Array.isArray(a[u]))throw new Error("aoa_to_sheet expects an array of arrays");for(var p=0;p!=a[u].length;++p)if(!(typeof a[u][p]>"u")){var h={v:a[u][p]},d=i+u,x=c+p;if(l.s.r>d&&(l.s.r=d),l.s.c>x&&(l.s.c=x),l.e.r<d&&(l.e.r=d),l.e.c<x&&(l.e.c=x),a[u][p]&&typeof a[u][p]=="object"&&!Array.isArray(a[u][p])&&!(a[u][p]instanceof Date))h=a[u][p];else if(Array.isArray(h.v)&&(h.f=a[u][p][1],h.v=h.v[0]),h.v===null)if(h.f)h.t="n";else if(n.nullError)h.t="e",h.v=0;else if(n.sheetStubs)h.t="z";else continue;else typeof h.v=="number"?h.t="n":typeof h.v=="boolean"?h.t="b":h.v instanceof Date?(h.z=n.dateNF||_e[14],n.cellDates?(h.t="d",h.w=Ir(h.z,mr(h.v))):(h.t="n",h.v=mr(h.v),h.w=Ir(h.z,h.v))):h.t="s";if(t)s[d]||(s[d]=[]),s[d][x]&&s[d][x].z&&(h.z=s[d][x].z),s[d][x]=h;else{var g=ve({c:x,r:d});s[g]&&s[g].z&&(h.z=s[g].z),s[g]=h}}}return l.s.c<1e7&&(s["!ref"]=ye(l)),s}function Ua(e,a){return Xs(null,e,a)}function Uo(e){return e.read_shift(4,"i")}function or(e){var a=e.read_shift(4);return a===0?"":e.read_shift(a,"dbcs")}function Vo(e){return{ich:e.read_shift(2),ifnt:e.read_shift(2)}}function I0(e,a){var r=e.l,n=e.read_shift(1),t=or(e),s=[],i={t,h:t};if((n&1)!==0){for(var c=e.read_shift(4),f=0;f!=c;++f)s.push(Vo(e));i.r=s}else i.r=[{ich:0,ifnt:0}];return e.l=r+a,i}var Ho=I0;function Rr(e){var a=e.read_shift(4),r=e.read_shift(2);return r+=e.read_shift(1)<<16,e.l++,{c:a,iStyleRef:r}}function Fa(e){var a=e.read_shift(2);return a+=e.read_shift(1)<<16,e.l++,{c:-1,iStyleRef:a}}var Wo=or;function R0(e){var a=e.read_shift(4);return a===0||a===4294967295?"":e.read_shift(a,"dbcs")}var Go=or,o0=R0;function N0(e){var a=e.slice(e.l,e.l+4),r=a[0]&1,n=a[0]&2;e.l+=4;var t=n===0?Pt([0,0,0,0,a[0]&252,a[1],a[2],a[3]],0):xa(a,0)>>2;return r?t/100:t}function zs(e){var a={s:{},e:{}};return a.s.r=e.read_shift(4),a.e.r=e.read_shift(4),a.s.c=e.read_shift(4),a.e.c=e.read_shift(4),a}var Sa=zs;function cr(e){if(e.length-e.l<8)throw"XLS Xnum Buffer underflow";return e.read_shift(8,"f")}function $o(e){var a={},r=e.read_shift(1),n=r>>>1,t=e.read_shift(1),s=e.read_shift(2,"i"),i=e.read_shift(1),c=e.read_shift(1),f=e.read_shift(1);switch(e.l++,n){case 0:a.auto=1;break;case 1:a.index=t;var l=ma[t];l&&(a.rgb=ct(l));break;case 2:a.rgb=ct([i,c,f]);break;case 3:a.theme=t;break}return s!=0&&(a.tint=s>0?s/32767:s/32768),a}function Xo(e){var a=e.read_shift(1);e.l++;var r={fBold:a&1,fItalic:a&2,fUnderline:a&4,fStrikeout:a&8,fOutline:a&16,fShadow:a&32,fCondense:a&64,fExtend:a&128};return r}function Ks(e,a){var r={2:"BITMAP",3:"METAFILEPICT",8:"DIB",14:"ENHMETAFILE"},n=e.read_shift(4);switch(n){case 0:return"";case 4294967295:case 4294967294:return r[e.read_shift(4)]||""}if(n>400)throw new Error("Unsupported Clipboard: "+n.toString(16));return e.l-=4,e.read_shift(0,a==1?"lpstr":"lpwstr")}function zo(e){return Ks(e,1)}function Ko(e){return Ks(e,2)}var L0=2,Er=3,At=11,Sn=12,bt=19,Ft=64,Yo=65,jo=71,Jo=4108,Zo=4126,qe=80,Ys=81,qo=[qe,Ys],Qo={1:{n:"CodePage",t:L0},2:{n:"Category",t:qe},3:{n:"PresentationFormat",t:qe},4:{n:"ByteCount",t:Er},5:{n:"LineCount",t:Er},6:{n:"ParagraphCount",t:Er},7:{n:"SlideCount",t:Er},8:{n:"NoteCount",t:Er},9:{n:"HiddenCount",t:Er},10:{n:"MultimediaClipCount",t:Er},11:{n:"ScaleCrop",t:At},12:{n:"HeadingPairs",t:Jo},13:{n:"TitlesOfParts",t:Zo},14:{n:"Manager",t:qe},15:{n:"Company",t:qe},16:{n:"LinksUpToDate",t:At},17:{n:"CharacterCount",t:Er},19:{n:"SharedDoc",t:At},22:{n:"HyperlinksChanged",t:At},23:{n:"AppVersion",t:Er,p:"version"},24:{n:"DigSig",t:Yo},26:{n:"ContentType",t:qe},27:{n:"ContentStatus",t:qe},28:{n:"Language",t:qe},29:{n:"Version",t:qe},255:{},2147483648:{n:"Locale",t:bt},2147483651:{n:"Behavior",t:bt},1919054434:{}},el={1:{n:"CodePage",t:L0},2:{n:"Title",t:qe},3:{n:"Subject",t:qe},4:{n:"Author",t:qe},5:{n:"Keywords",t:qe},6:{n:"Comments",t:qe},7:{n:"Template",t:qe},8:{n:"LastAuthor",t:qe},9:{n:"RevNumber",t:qe},10:{n:"EditTime",t:Ft},11:{n:"LastPrinted",t:Ft},12:{n:"CreatedDate",t:Ft},13:{n:"ModifiedDate",t:Ft},14:{n:"PageCount",t:Er},15:{n:"WordCount",t:Er},16:{n:"CharCount",t:Er},17:{n:"Thumbnail",t:jo},18:{n:"Application",t:qe},19:{n:"DocSecurity",t:Er},255:{},2147483648:{n:"Locale",t:bt},2147483651:{n:"Behavior",t:bt},1919054434:{}},Cn={1:"US",2:"CA",3:"",7:"RU",20:"EG",30:"GR",31:"NL",32:"BE",33:"FR",34:"ES",36:"HU",39:"IT",41:"CH",43:"AT",44:"GB",45:"DK",46:"SE",47:"NO",48:"PL",49:"DE",52:"MX",55:"BR",61:"AU",64:"NZ",66:"TH",81:"JP",82:"KR",84:"VN",86:"CN",90:"TR",105:"JS",213:"DZ",216:"MA",218:"LY",351:"PT",354:"IS",358:"FI",420:"CZ",886:"TW",961:"LB",962:"JO",963:"SY",964:"IQ",965:"KW",966:"SA",971:"AE",972:"IL",974:"QA",981:"IR",65535:"US"},rl=[null,"solid","mediumGray","darkGray","lightGray","darkHorizontal","darkVertical","darkDown","darkUp","darkGrid","darkTrellis","lightHorizontal","lightVertical","lightDown","lightUp","lightGrid","lightTrellis","gray125","gray0625"];function al(e){return e.map(function(a){return[a>>16&255,a>>8&255,a&255]})}var tl=al([0,16777215,16711680,65280,255,16776960,16711935,65535,0,16777215,16711680,65280,255,16776960,16711935,65535,8388608,32768,128,8421376,8388736,32896,12632256,8421504,10066431,10040166,16777164,13434879,6684774,16744576,26316,13421823,128,16711935,16776960,65535,8388736,8388608,32896,255,52479,13434879,13434828,16777113,10079487,16751052,13408767,16764057,3368703,3394764,10079232,16763904,16750848,16737792,6710937,9868950,13158,3381606,13056,3355392,10040064,10040166,3355545,3355443,16777215,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]),ma=ar(tl),Ca={0:"#NULL!",7:"#DIV/0!",15:"#VALUE!",23:"#REF!",29:"#NAME?",36:"#NUM!",42:"#N/A",43:"#GETTING_DATA",255:"#WTF?"},js={"#NULL!":0,"#DIV/0!":7,"#VALUE!":15,"#REF!":23,"#NAME?":29,"#NUM!":36,"#N/A":42,"#GETTING_DATA":43,"#WTF?":255},yn={"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml":"workbooks","application/vnd.ms-excel.sheet.macroEnabled.main+xml":"workbooks","application/vnd.ms-excel.sheet.binary.macroEnabled.main":"workbooks","application/vnd.ms-excel.addin.macroEnabled.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml":"sheets","application/vnd.ms-excel.worksheet":"sheets","application/vnd.ms-excel.binIndexWs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml":"charts","application/vnd.ms-excel.chartsheet":"charts","application/vnd.ms-excel.macrosheet+xml":"macros","application/vnd.ms-excel.macrosheet":"macros","application/vnd.ms-excel.intlmacrosheet":"TODO","application/vnd.ms-excel.binIndexMs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml":"dialogs","application/vnd.ms-excel.dialogsheet":"dialogs","application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml":"strs","application/vnd.ms-excel.sharedStrings":"strs","application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml":"styles","application/vnd.ms-excel.styles":"styles","application/vnd.openxmlformats-package.core-properties+xml":"coreprops","application/vnd.openxmlformats-officedocument.custom-properties+xml":"custprops","application/vnd.openxmlformats-officedocument.extended-properties+xml":"extprops","application/vnd.openxmlformats-officedocument.customXmlProperties+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.customProperty":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml":"comments","application/vnd.ms-excel.comments":"comments","application/vnd.ms-excel.threadedcomments+xml":"threadedcomments","application/vnd.ms-excel.person+xml":"people","application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml":"metadata","application/vnd.ms-excel.sheetMetadata":"metadata","application/vnd.ms-excel.pivotTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotTable+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.chart+xml":"TODO","application/vnd.ms-office.chartcolorstyle+xml":"TODO","application/vnd.ms-office.chartstyle+xml":"TODO","application/vnd.ms-office.chartex+xml":"TODO","application/vnd.ms-excel.calcChain":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.calcChain+xml":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.printerSettings":"TODO","application/vnd.ms-office.activeX":"TODO","application/vnd.ms-office.activeX+xml":"TODO","application/vnd.ms-excel.attachedToolbars":"TODO","application/vnd.ms-excel.connections":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.connections+xml":"TODO","application/vnd.ms-excel.externalLink":"links","application/vnd.openxmlformats-officedocument.spreadsheetml.externalLink+xml":"links","application/vnd.ms-excel.pivotCacheDefinition":"TODO","application/vnd.ms-excel.pivotCacheRecords":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheDefinition+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheRecords+xml":"TODO","application/vnd.ms-excel.queryTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.queryTable+xml":"TODO","application/vnd.ms-excel.userNames":"TODO","application/vnd.ms-excel.revisionHeaders":"TODO","application/vnd.ms-excel.revisionLog":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionHeaders+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionLog+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.userNames+xml":"TODO","application/vnd.ms-excel.tableSingleCells":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.tableSingleCells+xml":"TODO","application/vnd.ms-excel.slicer":"TODO","application/vnd.ms-excel.slicerCache":"TODO","application/vnd.ms-excel.slicer+xml":"TODO","application/vnd.ms-excel.slicerCache+xml":"TODO","application/vnd.ms-excel.wsSortMap":"TODO","application/vnd.ms-excel.table":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml":"TODO","application/vnd.openxmlformats-officedocument.theme+xml":"themes","application/vnd.openxmlformats-officedocument.themeOverride+xml":"TODO","application/vnd.ms-excel.Timeline+xml":"TODO","application/vnd.ms-excel.TimelineCache+xml":"TODO","application/vnd.ms-office.vbaProject":"vba","application/vnd.ms-office.vbaProjectSignature":"TODO","application/vnd.ms-office.volatileDependencies":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.volatileDependencies+xml":"TODO","application/vnd.ms-excel.controlproperties+xml":"TODO","application/vnd.openxmlformats-officedocument.model+data":"TODO","application/vnd.ms-excel.Survey+xml":"TODO","application/vnd.openxmlformats-officedocument.drawing+xml":"drawings","application/vnd.openxmlformats-officedocument.drawingml.chartshapes+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramColors+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramData+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramLayout+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramStyle+xml":"TODO","application/vnd.openxmlformats-officedocument.vmlDrawing":"TODO","application/vnd.openxmlformats-package.relationships+xml":"rels","application/vnd.openxmlformats-officedocument.oleObject":"TODO","image/png":"TODO",sheet:"js"};function nl(){return{workbooks:[],sheets:[],charts:[],dialogs:[],macros:[],rels:[],strs:[],comments:[],threadedcomments:[],links:[],coreprops:[],extprops:[],custprops:[],themes:[],styles:[],calcchains:[],vba:[],drawings:[],metadata:[],people:[],TODO:[],xmlns:""}}function sl(e){var a=nl();if(!e||!e.match)return a;var r={};if((e.match(ur)||[]).forEach(function(n){var t=pe(n);switch(t[0].replace(ho,"<")){case"<?xml":break;case"<Types":a.xmlns=t["xmlns"+(t[0].match(/<(\w+):/)||["",""])[1]];break;case"<Default":r[t.Extension]=t.ContentType;break;case"<Override":a[yn[t.ContentType]]!==void 0&&a[yn[t.ContentType]].push(t.PartName);break}}),a.xmlns!==Ao.CT)throw new Error("Unknown Namespace: "+a.xmlns);return a.calcchain=a.calcchains.length>0?a.calcchains[0]:"",a.sst=a.strs.length>0?a.strs[0]:"",a.style=a.styles.length>0?a.styles[0]:"",a.defaults=r,delete a.calcchains,a}var Ia={WB:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument",SHEET:"http://sheetjs.openxmlformats.org/officeDocument/2006/relationships/officeDocument",HLINK:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink",VML:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/vmlDrawing",XPATH:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/externalLinkPath",XMISS:"http://schemas.microsoft.com/office/2006/relationships/xlExternalLinkPath/xlPathMissing",XLINK:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/externalLink",CXML:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/customXml",CXMLP:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/customXmlProps",CMNT:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/comments",CORE_PROPS:"http://schemas.openxmlformats.org/package/2006/relationships/metadata/core-properties",EXT_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/extended-properties",CUST_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/custom-properties",SST:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sharedStrings",STY:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles",THEME:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme",CHART:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/chart",CHARTEX:"http://schemas.microsoft.com/office/2014/relationships/chartEx",CS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/chartsheet",WS:["http://schemas.openxmlformats.org/officeDocument/2006/relationships/worksheet","http://purl.oclc.org/ooxml/officeDocument/relationships/worksheet"],DS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/dialogsheet",MS:"http://schemas.microsoft.com/office/2006/relationships/xlMacrosheet",IMG:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/image",DRAW:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/drawing",XLMETA:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sheetMetadata",TCMNT:"http://schemas.microsoft.com/office/2017/10/relationships/threadedComment",PEOPLE:"http://schemas.microsoft.com/office/2017/10/relationships/person",VBA:"http://schemas.microsoft.com/office/2006/relationships/vbaProject"};function l0(e){var a=e.lastIndexOf("/");return e.slice(0,a+1)+"_rels/"+e.slice(a+1)+".rels"}function Za(e,a){var r={"!id":{}};if(!e)return r;a.charAt(0)!=="/"&&(a="/"+a);var n={};return(e.match(ur)||[]).forEach(function(t){var s=pe(t);if(s[0]==="<Relationship"){var i={};i.Type=s.Type,i.Target=s.Target,i.Id=s.Id,s.TargetMode&&(i.TargetMode=s.TargetMode);var c=s.TargetMode==="External"?s.Target:za(s.Target,a);r[c]=i,n[s.Id]=i}}),r["!id"]=n,r}var il="application/vnd.oasis.opendocument.spreadsheet";function cl(e,a){for(var r=C0(e),n,t;n=st.exec(r);)switch(n[3]){case"manifest":break;case"file-entry":if(t=pe(n[0],!1),t.path=="/"&&t.type!==il)throw new Error("This OpenDocument is not a spreadsheet");break;case"encryption-data":case"algorithm":case"start-key-generation":case"key-derivation":throw new Error("Unsupported ODS Encryption");default:if(a&&a.WTF)throw n}}var qa=[["cp:category","Category"],["cp:contentStatus","ContentStatus"],["cp:keywords","Keywords"],["cp:lastModifiedBy","LastAuthor"],["cp:lastPrinted","LastPrinted"],["cp:revision","RevNumber"],["cp:version","Version"],["dc:creator","Author"],["dc:description","Comments"],["dc:identifier","Identifier"],["dc:language","Language"],["dc:subject","Subject"],["dc:title","Title"],["dcterms:created","CreatedDate","date"],["dcterms:modified","ModifiedDate","date"]],fl=function(){for(var e=new Array(qa.length),a=0;a<qa.length;++a){var r=qa[a],n="(?:"+r[0].slice(0,r[0].indexOf(":"))+":)"+r[0].slice(r[0].indexOf(":")+1);e[a]=new RegExp("<"+n+"[^>]*>([\\s\\S]*?)</"+n+">")}return e}();function Js(e){var a={};e=Ne(e);for(var r=0;r<qa.length;++r){var n=qa[r],t=e.match(fl[r]);t!=null&&t.length>0&&(a[n[1]]=Oe(t[1])),n[2]==="date"&&a[n[1]]&&(a[n[1]]=er(a[n[1]]))}return a}var ol=[["Application","Application","string"],["AppVersion","AppVersion","string"],["Company","Company","string"],["DocSecurity","DocSecurity","string"],["Manager","Manager","string"],["HyperlinksChanged","HyperlinksChanged","bool"],["SharedDoc","SharedDoc","bool"],["LinksUpToDate","LinksUpToDate","bool"],["ScaleCrop","ScaleCrop","bool"],["HeadingPairs","HeadingPairs","raw"],["TitlesOfParts","TitlesOfParts","raw"]];function Zs(e,a,r,n){var t=[];if(typeof e=="string")t=mn(e,n);else for(var s=0;s<e.length;++s)t=t.concat(e[s].map(function(o){return{v:o}}));var i=typeof a=="string"?mn(a,n).map(function(o){return o.v}):a,c=0,f=0;if(i.length>0)for(var l=0;l!==t.length;l+=2){switch(f=+t[l+1].v,t[l].v){case"Worksheets":case"工作表":case"Листы":case"أوراق العمل":case"ワークシート":case"גליונות עבודה":case"Arbeitsblätter":case"Çalışma Sayfaları":case"Feuilles de calcul":case"Fogli di lavoro":case"Folhas de cálculo":case"Planilhas":case"Regneark":case"Hojas de cálculo":case"Werkbladen":r.Worksheets=f,r.SheetNames=i.slice(c,c+f);break;case"Named Ranges":case"Rangos con nombre":case"名前付き一覧":case"Benannte Bereiche":case"Navngivne områder":r.NamedRanges=f,r.DefinedNames=i.slice(c,c+f);break;case"Charts":case"Diagramme":r.Chartsheets=f,r.ChartNames=i.slice(c,c+f);break}c+=f}}function ll(e,a,r){var n={};return a||(a={}),e=Ne(e),ol.forEach(function(t){var s=(e.match(nt(t[0]))||[])[1];switch(t[2]){case"string":s&&(a[t[1]]=Oe(s));break;case"bool":a[t[1]]=s==="true";break;case"raw":var i=e.match(new RegExp("<"+t[0]+"[^>]*>([\\s\\S]*?)</"+t[0]+">"));i&&i.length>0&&(n[t[1]]=i[1]);break}}),n.HeadingPairs&&n.TitlesOfParts&&Zs(n.HeadingPairs,n.TitlesOfParts,a,r),a}var ul=/<[^>]+>[^<]*/g;function hl(e,a){var r={},n="",t=e.match(ul);if(t)for(var s=0;s!=t.length;++s){var i=t[s],c=pe(i);switch(c[0]){case"<?xml":break;case"<Properties":break;case"<property":n=Oe(c.name);break;case"</property>":n=null;break;default:if(i.indexOf("<vt:")===0){var f=i.split(">"),l=f[0].slice(4),o=f[1];switch(l){case"lpstr":case"bstr":case"lpwstr":r[n]=Oe(o);break;case"bool":r[n]=be(o);break;case"i1":case"i2":case"i4":case"i8":case"int":case"uint":r[n]=parseInt(o,10);break;case"r4":case"r8":case"decimal":r[n]=parseFloat(o);break;case"filetime":case"date":r[n]=er(o);break;case"cy":case"error":r[n]=Oe(o);break;default:if(l.slice(-1)=="/")break;a.WTF&&typeof console<"u"&&console.warn("Unexpected",i,l,f)}}else if(i.slice(0,2)!=="</"){if(a.WTF)throw new Error(i)}}}return r}var xl={Title:"Title",Subject:"Subject",Author:"Author",Keywords:"Keywords",Comments:"Description",LastAuthor:"LastAuthor",RevNumber:"Revision",Application:"AppName",LastPrinted:"LastPrinted",CreatedDate:"Created",ModifiedDate:"LastSaved",Category:"Category",Manager:"Manager",Company:"Company",AppVersion:"Version",ContentStatus:"ContentStatus",Identifier:"Identifier",Language:"Language"},Qt;function dl(e,a,r){Qt||(Qt=A0(xl)),a=Qt[a]||a,e[a]=r}function P0(e){var a=e.read_shift(4),r=e.read_shift(4);return new Date((r/1e7*Math.pow(2,32)+a/1e7-11644473600)*1e3).toISOString().replace(/\.000/,"")}function qs(e,a,r){var n=e.l,t=e.read_shift(0,"lpstr-cp");if(r)for(;e.l-n&3;)++e.l;return t}function Qs(e,a,r){var n=e.read_shift(0,"lpwstr");return n}function ei(e,a,r){return a===31?Qs(e):qs(e,a,r)}function u0(e,a,r){return ei(e,a,r===!1?0:4)}function pl(e,a){if(!a)throw new Error("VtUnalignedString must have positive length");return ei(e,a,0)}function vl(e){for(var a=e.read_shift(4),r=[],n=0;n!=a;++n){var t=e.l;r[n]=e.read_shift(0,"lpwstr").replace(gr,""),e.l-t&2&&(e.l+=2)}return r}function gl(e){for(var a=e.read_shift(4),r=[],n=0;n!=a;++n)r[n]=e.read_shift(0,"lpstr-cp").replace(gr,"");return r}function ml(e){var a=e.l,r=Bt(e,Ys);e[e.l]==0&&e[e.l+1]==0&&e.l-a&2&&(e.l+=2);var n=Bt(e,Er);return[r,n]}function _l(e){for(var a=e.read_shift(4),r=[],n=0;n<a/2;++n)r.push(ml(e));return r}function Dn(e,a){for(var r=e.read_shift(4),n={},t=0;t!=r;++t){var s=e.read_shift(4),i=e.read_shift(4);n[s]=e.read_shift(i,a===1200?"utf16le":"utf8").replace(gr,"").replace(Xa,"!"),a===1200&&i%2&&(e.l+=2)}return e.l&3&&(e.l=e.l>>3<<2),n}function ri(e){var a=e.read_shift(4),r=e.slice(e.l,e.l+a);return e.l+=a,(a&3)>0&&(e.l+=4-(a&3)&3),r}function El(e){var a={};return a.Size=e.read_shift(4),e.l+=a.Size+3-(a.Size-1)%4,a}function Bt(e,a,r){var n=e.read_shift(2),t,s=r||{};if(e.l+=2,a!==Sn&&n!==a&&qo.indexOf(a)===-1&&!((a&65534)==4126&&(n&65534)==4126))throw new Error("Expected type "+a+" saw "+n);switch(a===Sn?n:a){case 2:return t=e.read_shift(2,"i"),s.raw||(e.l+=2),t;case 3:return t=e.read_shift(4,"i"),t;case 11:return e.read_shift(4)!==0;case 19:return t=e.read_shift(4),t;case 30:return qs(e,n,4).replace(gr,"");case 31:return Qs(e);case 64:return P0(e);case 65:return ri(e);case 71:return El(e);case 80:return u0(e,n,!s.raw).replace(gr,"");case 81:return pl(e,n).replace(gr,"");case 4108:return _l(e);case 4126:case 4127:return n==4127?vl(e):gl(e);default:throw new Error("TypedPropertyValue unrecognized type "+a+" "+n)}}function On(e,a){var r=e.l,n=e.read_shift(4),t=e.read_shift(4),s=[],i=0,c=0,f=-1,l={};for(i=0;i!=t;++i){var o=e.read_shift(4),u=e.read_shift(4);s[i]=[o,u+r]}s.sort(function(y,m){return y[1]-m[1]});var p={};for(i=0;i!=t;++i){if(e.l!==s[i][1]){var h=!0;if(i>0&&a)switch(a[s[i-1][0]].t){case 2:e.l+2===s[i][1]&&(e.l+=2,h=!1);break;case 80:e.l<=s[i][1]&&(e.l=s[i][1],h=!1);break;case 4108:e.l<=s[i][1]&&(e.l=s[i][1],h=!1);break}if((!a||i==0)&&e.l<=s[i][1]&&(h=!1,e.l=s[i][1]),h)throw new Error("Read Error: Expected address "+s[i][1]+" at "+e.l+" :"+i)}if(a){var d=a[s[i][0]];if(p[d.n]=Bt(e,d.t,{raw:!0}),d.p==="version"&&(p[d.n]=String(p[d.n]>>16)+"."+("0000"+String(p[d.n]&65535)).slice(-4)),d.n=="CodePage")switch(p[d.n]){case 0:p[d.n]=1252;case 874:case 932:case 936:case 949:case 950:case 1250:case 1251:case 1253:case 1254:case 1255:case 1256:case 1257:case 1258:case 1e4:case 1200:case 1201:case 1252:case 65e3:case-536:case 65001:case-535:Br(c=p[d.n]>>>0&65535);break;default:throw new Error("Unsupported CodePage: "+p[d.n])}}else if(s[i][0]===1){if(c=p.CodePage=Bt(e,L0),Br(c),f!==-1){var x=e.l;e.l=s[f][1],l=Dn(e,c),e.l=x}}else if(s[i][0]===0){if(c===0){f=i,e.l=s[i+1][1];continue}l=Dn(e,c)}else{var g=l[s[i][0]],w;switch(e[e.l]){case 65:e.l+=4,w=ri(e);break;case 30:e.l+=4,w=u0(e,e[e.l-4]).replace(/\u0000+$/,"");break;case 31:e.l+=4,w=u0(e,e[e.l-4]).replace(/\u0000+$/,"");break;case 3:e.l+=4,w=e.read_shift(4,"i");break;case 19:e.l+=4,w=e.read_shift(4);break;case 5:e.l+=4,w=e.read_shift(8,"f");break;case 11:e.l+=4,w=Ge(e,4);break;case 64:e.l+=4,w=er(P0(e));break;default:throw new Error("unparsed value: "+e[e.l])}p[g]=w}}return e.l=r+n,p}function In(e,a,r){var n=e.content;if(!n)return{};rr(n,0);var t,s,i,c,f=0;n.chk("feff","Byte Order: "),n.read_shift(2);var l=n.read_shift(4),o=n.read_shift(16);if(o!==Ce.utils.consts.HEADER_CLSID&&o!==r)throw new Error("Bad PropertySet CLSID "+o);if(t=n.read_shift(4),t!==1&&t!==2)throw new Error("Unrecognized #Sets: "+t);if(s=n.read_shift(16),c=n.read_shift(4),t===1&&c!==n.l)throw new Error("Length mismatch: "+c+" !== "+n.l);t===2&&(i=n.read_shift(16),f=n.read_shift(4));var u=On(n,a),p={SystemIdentifier:l};for(var h in u)p[h]=u[h];if(p.FMTID=s,t===1)return p;if(f-n.l==2&&(n.l+=2),n.l!==f)throw new Error("Length mismatch 2: "+n.l+" !== "+f);var d;try{d=On(n,null)}catch{}for(h in d)p[h]=d[h];return p.FMTID=[s,i],p}function ra(e,a){return e.read_shift(a),null}function Tl(e,a,r){for(var n=[],t=e.l+a;e.l<t;)n.push(r(e,t-e.l));if(t!==e.l)throw new Error("Slurp error");return n}function Ge(e,a){return e.read_shift(a)===1}function ze(e){return e.read_shift(2,"u")}function ai(e,a){return Tl(e,a,ze)}function kl(e){var a=e.read_shift(1),r=e.read_shift(1);return r===1?a:a===1}function vt(e,a,r){var n=e.read_shift(r&&r.biff>=12?2:1),t="sbcs-cont";if(r&&r.biff>=8,!r||r.biff==8){var s=e.read_shift(1);s&&(t="dbcs-cont")}else r.biff==12&&(t="wstr");r.biff>=2&&r.biff<=5&&(t="cpstr");var i=n?e.read_shift(n,t):"";return i}function wl(e){var a=e.read_shift(2),r=e.read_shift(1),n=r&4,t=r&8,s=1+(r&1),i=0,c,f={};t&&(i=e.read_shift(2)),n&&(c=e.read_shift(4));var l=s==2?"dbcs-cont":"sbcs-cont",o=a===0?"":e.read_shift(a,l);return t&&(e.l+=4*i),n&&(e.l+=c),f.t=o,t||(f.raw="<t>"+f.t+"</t>",f.r=f.t),f}function ka(e,a,r){var n;if(r){if(r.biff>=2&&r.biff<=5)return e.read_shift(a,"cpstr");if(r.biff>=12)return e.read_shift(a,"dbcs-cont")}var t=e.read_shift(1);return t===0?n=e.read_shift(a,"sbcs-cont"):n=e.read_shift(a,"dbcs-cont"),n}function gt(e,a,r){var n=e.read_shift(r&&r.biff==2?1:2);return n===0?(e.l++,""):ka(e,n,r)}function ya(e,a,r){if(r.biff>5)return gt(e,a,r);var n=e.read_shift(1);return n===0?(e.l++,""):e.read_shift(n,r.biff<=4||!e.lens?"cpstr":"sbcs-cont")}function Al(e){var a=e.read_shift(1);e.l++;var r=e.read_shift(2);return e.l+=2,[a,r]}function Fl(e){var a=e.read_shift(4),r=e.l,n=!1;a>24&&(e.l+=a-24,e.read_shift(16)==="795881f43b1d7f48af2c825dc4852763"&&(n=!0),e.l=r);var t=e.read_shift((n?a-24:a)>>1,"utf16le").replace(gr,"");return n&&(e.l+=24),t}function Sl(e){for(var a=e.read_shift(2),r="";a-- >0;)r+="../";var n=e.read_shift(0,"lpstr-ansi");if(e.l+=2,e.read_shift(2)!=57005)throw new Error("Bad FileMoniker");var t=e.read_shift(4);if(t===0)return r+n.replace(/\\/g,"/");var s=e.read_shift(4);if(e.read_shift(2)!=3)throw new Error("Bad FileMoniker");var i=e.read_shift(s>>1,"utf16le").replace(gr,"");return r+i}function Cl(e,a){var r=e.read_shift(16);switch(r){case"e0c9ea79f9bace118c8200aa004ba90b":return Fl(e);case"0303000000000000c000000000000046":return Sl(e);default:throw new Error("Unsupported Moniker "+r)}}function St(e){var a=e.read_shift(4),r=a>0?e.read_shift(a,"utf16le").replace(gr,""):"";return r}function yl(e,a){var r=e.l+a,n=e.read_shift(4);if(n!==2)throw new Error("Unrecognized streamVersion: "+n);var t=e.read_shift(2);e.l+=2;var s,i,c,f,l="",o,u;t&16&&(s=St(e,r-e.l)),t&128&&(i=St(e,r-e.l)),(t&257)===257&&(c=St(e,r-e.l)),(t&257)===1&&(f=Cl(e,r-e.l)),t&8&&(l=St(e,r-e.l)),t&32&&(o=e.read_shift(16)),t&64&&(u=P0(e)),e.l=r;var p=i||c||f||"";p&&l&&(p+="#"+l),p||(p="#"+l),t&2&&p.charAt(0)=="/"&&p.charAt(1)!="/"&&(p="file://"+p);var h={Target:p};return o&&(h.guid=o),u&&(h.time=u),s&&(h.Tooltip=s),h}function ti(e){var a=e.read_shift(1),r=e.read_shift(1),n=e.read_shift(1),t=e.read_shift(1);return[a,r,n,t]}function ni(e,a){var r=ti(e);return r[3]=0,r}function Xr(e){var a=e.read_shift(2),r=e.read_shift(2),n=e.read_shift(2);return{r:a,c:r,ixfe:n}}function Dl(e){var a=e.read_shift(2),r=e.read_shift(2);return e.l+=8,{type:a,flags:r}}function Ol(e,a,r){return a===0?"":ya(e,a,r)}function Il(e,a,r){var n=r.biff>8?4:2,t=e.read_shift(n),s=e.read_shift(n,"i"),i=e.read_shift(n,"i");return[t,s,i]}function si(e){var a=e.read_shift(2),r=N0(e);return[a,r]}function Rl(e,a,r){e.l+=4,a-=4;var n=e.l+a,t=vt(e,a,r),s=e.read_shift(2);if(n-=e.l,s!==n)throw new Error("Malformed AddinUdf: padding = "+n+" != "+s);return e.l+=s,t}function Xt(e){var a=e.read_shift(2),r=e.read_shift(2),n=e.read_shift(2),t=e.read_shift(2);return{s:{c:n,r:a},e:{c:t,r}}}function ii(e){var a=e.read_shift(2),r=e.read_shift(2),n=e.read_shift(1),t=e.read_shift(1);return{s:{c:n,r:a},e:{c:t,r}}}var Nl=ii;function ci(e){e.l+=4;var a=e.read_shift(2),r=e.read_shift(2),n=e.read_shift(2);return e.l+=12,[r,a,n]}function Ll(e){var a={};return e.l+=4,e.l+=16,a.fSharedNote=e.read_shift(2),e.l+=4,a}function Pl(e){var a={};return e.l+=4,e.cf=e.read_shift(2),a}function sr(e){e.l+=2,e.l+=e.read_shift(2)}var bl={0:sr,4:sr,5:sr,6:sr,7:Pl,8:sr,9:sr,10:sr,11:sr,12:sr,13:Ll,14:sr,15:sr,16:sr,17:sr,18:sr,19:sr,20:sr,21:ci};function Bl(e,a){for(var r=e.l+a,n=[];e.l<r;){var t=e.read_shift(2);e.l-=2;try{n.push(bl[t](e,r-e.l))}catch{return e.l=r,n}}return e.l!=r&&(e.l=r),n}function Ct(e,a){var r={BIFFVer:0,dt:0};switch(r.BIFFVer=e.read_shift(2),a-=2,a>=2&&(r.dt=e.read_shift(2),e.l-=2),r.BIFFVer){case 1536:case 1280:case 1024:case 768:case 512:case 2:case 7:break;default:if(a>6)throw new Error("Unexpected BIFF Ver "+r.BIFFVer)}return e.read_shift(a),r}function Ml(e,a){return a===0||e.read_shift(2),1200}function Ul(e,a,r){if(r.enc)return e.l+=a,"";var n=e.l,t=ya(e,0,r);return e.read_shift(a+n-e.l),t}function Vl(e,a,r){var n=r&&r.biff==8||a==2?e.read_shift(2):(e.l+=a,0);return{fDialog:n&16,fBelow:n&64,fRight:n&128}}function Hl(e,a,r){var n=e.read_shift(4),t=e.read_shift(1)&3,s=e.read_shift(1);switch(s){case 0:s="Worksheet";break;case 1:s="Macrosheet";break;case 2:s="Chartsheet";break;case 6:s="VBAModule";break}var i=vt(e,0,r);return i.length===0&&(i="Sheet1"),{pos:n,hs:t,dt:s,name:i}}function Wl(e,a){for(var r=e.l+a,n=e.read_shift(4),t=e.read_shift(4),s=[],i=0;i!=t&&e.l<r;++i)s.push(wl(e));return s.Count=n,s.Unique=t,s}function Gl(e,a){var r={};return r.dsst=e.read_shift(2),e.l+=a-2,r}function $l(e){var a={};a.r=e.read_shift(2),a.c=e.read_shift(2),a.cnt=e.read_shift(2)-a.c;var r=e.read_shift(2);e.l+=4;var n=e.read_shift(1);return e.l+=3,n&7&&(a.level=n&7),n&32&&(a.hidden=!0),n&64&&(a.hpt=r/20),a}function Xl(e){var a=Dl(e);if(a.type!=2211)throw new Error("Invalid Future Record "+a.type);var r=e.read_shift(4);return r!==0}function zl(e){return e.read_shift(2),e.read_shift(4)}function Rn(e,a,r){var n=0;r&&r.biff==2||(n=e.read_shift(2));var t=e.read_shift(2);r&&r.biff==2&&(n=1-(t>>15),t&=32767);var s={Unsynced:n&1,DyZero:(n&2)>>1,ExAsc:(n&4)>>2,ExDsc:(n&8)>>3};return[s,t]}function Kl(e){var a=e.read_shift(2),r=e.read_shift(2),n=e.read_shift(2),t=e.read_shift(2),s=e.read_shift(2),i=e.read_shift(2),c=e.read_shift(2),f=e.read_shift(2),l=e.read_shift(2);return{Pos:[a,r],Dim:[n,t],Flags:s,CurTab:i,FirstTab:c,Selected:f,TabRatio:l}}function Yl(e,a,r){if(r&&r.biff>=2&&r.biff<5)return{};var n=e.read_shift(2);return{RTL:n&64}}function jl(){}function Jl(e,a,r){var n={dyHeight:e.read_shift(2),fl:e.read_shift(2)};switch(r&&r.biff||8){case 2:break;case 3:case 4:e.l+=2;break;default:e.l+=10;break}return n.name=vt(e,0,r),n}function Zl(e){var a=Xr(e);return a.isst=e.read_shift(4),a}function ql(e,a,r){r.biffguess&&r.biff==2&&(r.biff=5);var n=e.l+a,t=Xr(e);r.biff==2&&e.l++;var s=gt(e,n-e.l,r);return t.val=s,t}function Ql(e,a,r){var n=e.read_shift(2),t=ya(e,0,r);return[n,t]}var e1=ya;function Nn(e,a,r){var n=e.l+a,t=r.biff==8||!r.biff?4:2,s=e.read_shift(t),i=e.read_shift(t),c=e.read_shift(2),f=e.read_shift(2);return e.l=n,{s:{r:s,c},e:{r:i,c:f}}}function r1(e){var a=e.read_shift(2),r=e.read_shift(2),n=si(e);return{r:a,c:r,ixfe:n[0],rknum:n[1]}}function a1(e,a){for(var r=e.l+a-2,n=e.read_shift(2),t=e.read_shift(2),s=[];e.l<r;)s.push(si(e));if(e.l!==r)throw new Error("MulRK read error");var i=e.read_shift(2);if(s.length!=i-t+1)throw new Error("MulRK length mismatch");return{r:n,c:t,C:i,rkrec:s}}function t1(e,a){for(var r=e.l+a-2,n=e.read_shift(2),t=e.read_shift(2),s=[];e.l<r;)s.push(e.read_shift(2));if(e.l!==r)throw new Error("MulBlank read error");var i=e.read_shift(2);if(s.length!=i-t+1)throw new Error("MulBlank length mismatch");return{r:n,c:t,C:i,ixfe:s}}function n1(e,a,r,n){var t={},s=e.read_shift(4),i=e.read_shift(4),c=e.read_shift(4),f=e.read_shift(2);return t.patternType=rl[c>>26],n.cellStyles&&(t.alc=s&7,t.fWrap=s>>3&1,t.alcV=s>>4&7,t.fJustLast=s>>7&1,t.trot=s>>8&255,t.cIndent=s>>16&15,t.fShrinkToFit=s>>20&1,t.iReadOrder=s>>22&2,t.fAtrNum=s>>26&1,t.fAtrFnt=s>>27&1,t.fAtrAlc=s>>28&1,t.fAtrBdr=s>>29&1,t.fAtrPat=s>>30&1,t.fAtrProt=s>>31&1,t.dgLeft=i&15,t.dgRight=i>>4&15,t.dgTop=i>>8&15,t.dgBottom=i>>12&15,t.icvLeft=i>>16&127,t.icvRight=i>>23&127,t.grbitDiag=i>>30&3,t.icvTop=c&127,t.icvBottom=c>>7&127,t.icvDiag=c>>14&127,t.dgDiag=c>>21&15,t.icvFore=f&127,t.icvBack=f>>7&127,t.fsxButton=f>>14&1),t}function s1(e,a,r){var n={};return n.ifnt=e.read_shift(2),n.numFmtId=e.read_shift(2),n.flags=e.read_shift(2),n.fStyle=n.flags>>2&1,a-=6,n.data=n1(e,a,n.fStyle,r),n}function i1(e){e.l+=4;var a=[e.read_shift(2),e.read_shift(2)];if(a[0]!==0&&a[0]--,a[1]!==0&&a[1]--,a[0]>7||a[1]>7)throw new Error("Bad Gutters: "+a.join("|"));return a}function Ln(e,a,r){var n=Xr(e);(r.biff==2||a==9)&&++e.l;var t=kl(e);return n.val=t,n.t=t===!0||t===!1?"b":"e",n}function c1(e,a,r){r.biffguess&&r.biff==2&&(r.biff=5);var n=Xr(e),t=cr(e);return n.val=t,n}var Pn=Ol;function f1(e,a,r){var n=e.l+a,t=e.read_shift(2),s=e.read_shift(2);if(r.sbcch=s,s==1025||s==14849)return[s,t];if(s<1||s>255)throw new Error("Unexpected SupBook type: "+s);for(var i=ka(e,s),c=[];n>e.l;)c.push(gt(e));return[s,t,i,c]}function bn(e,a,r){var n=e.read_shift(2),t,s={fBuiltIn:n&1,fWantAdvise:n>>>1&1,fWantPict:n>>>2&1,fOle:n>>>3&1,fOleLink:n>>>4&1,cf:n>>>5&1023,fIcon:n>>>15&1};return r.sbcch===14849&&(t=Rl(e,a-2,r)),s.body=t||e.read_shift(a-2),typeof t=="string"&&(s.Name=t),s}var o1=["_xlnm.Consolidate_Area","_xlnm.Auto_Open","_xlnm.Auto_Close","_xlnm.Extract","_xlnm.Database","_xlnm.Criteria","_xlnm.Print_Area","_xlnm.Print_Titles","_xlnm.Recorder","_xlnm.Data_Form","_xlnm.Auto_Activate","_xlnm.Auto_Deactivate","_xlnm.Sheet_Title","_xlnm._FilterDatabase"];function Bn(e,a,r){var n=e.l+a,t=e.read_shift(2),s=e.read_shift(1),i=e.read_shift(1),c=e.read_shift(r&&r.biff==2?1:2),f=0;(!r||r.biff>=5)&&(r.biff!=5&&(e.l+=2),f=e.read_shift(2),r.biff==5&&(e.l+=2),e.l+=4);var l=ka(e,i,r);t&32&&(l=o1[l.charCodeAt(0)]);var o=n-e.l;r&&r.biff==2&&--o;var u=n==e.l||c===0||!(o>0)?[]:$x(e,o,r,c);return{chKey:s,Name:l,itab:f,rgce:u}}function fi(e,a,r){if(r.biff<8)return l1(e,a,r);for(var n=[],t=e.l+a,s=e.read_shift(r.biff>8?4:2);s--!==0;)n.push(Il(e,r.biff>8?12:6,r));if(e.l!=t)throw new Error("Bad ExternSheet: "+e.l+" != "+t);return n}function l1(e,a,r){e[e.l+1]==3&&e[e.l]++;var n=vt(e,a,r);return n.charCodeAt(0)==3?n.slice(1):n}function u1(e,a,r){if(r.biff<8){e.l+=a;return}var n=e.read_shift(2),t=e.read_shift(2),s=ka(e,n,r),i=ka(e,t,r);return[s,i]}function h1(e,a,r){var n=ii(e);e.l++;var t=e.read_shift(1);return a-=8,[Xx(e,a,r),t,n]}function Mn(e,a,r){var n=Nl(e);switch(r.biff){case 2:e.l++,a-=7;break;case 3:case 4:e.l+=2,a-=8;break;default:e.l+=6,a-=12}return[n,Wx(e,a,r)]}function x1(e){var a=e.read_shift(4)!==0,r=e.read_shift(4)!==0,n=e.read_shift(4);return[a,r,n]}function d1(e,a,r){if(!(r.biff<8)){var n=e.read_shift(2),t=e.read_shift(2),s=e.read_shift(2),i=e.read_shift(2),c=ya(e,0,r);return r.biff<8&&e.read_shift(1),[{r:n,c:t},c,i,s]}}function p1(e,a,r){return d1(e,a,r)}function v1(e,a){for(var r=[],n=e.read_shift(2);n--;)r.push(Xt(e));return r}function g1(e,a,r){if(r&&r.biff<8)return _1(e,a,r);var n=ci(e),t=Bl(e,a-22,n[1]);return{cmo:n,ft:t}}var m1={8:function(e,a){var r=e.l+a;e.l+=10;var n=e.read_shift(2);e.l+=4,e.l+=2,e.l+=2,e.l+=2,e.l+=4;var t=e.read_shift(1);return e.l+=t,e.l=r,{fmt:n}}};function _1(e,a,r){e.l+=4;var n=e.read_shift(2),t=e.read_shift(2),s=e.read_shift(2);e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=6,a-=36;var i=[];return i.push((m1[n]||lr)(e,a,r)),{cmo:[t,n,s],ft:i}}function E1(e,a,r){var n=e.l,t="";try{e.l+=4;var s=(r.lastobj||{cmo:[0,0]}).cmo[1],i;[0,5,7,11,12,14].indexOf(s)==-1?e.l+=6:i=Al(e,6,r);var c=e.read_shift(2);e.read_shift(2),ze(e,2);var f=e.read_shift(2);e.l+=f;for(var l=1;l<e.lens.length-1;++l){if(e.l-n!=e.lens[l])throw new Error("TxO: bad continue record");var o=e[e.l],u=ka(e,e.lens[l+1]-e.lens[l]-1);if(t+=u,t.length>=(o?c:2*c))break}if(t.length!==c&&t.length!==c*2)throw new Error("cchText: "+c+" != "+t.length);return e.l=n+a,{t}}catch{return e.l=n+a,{t}}}function T1(e,a){var r=Xt(e);e.l+=16;var n=yl(e,a-24);return[r,n]}function k1(e,a){e.read_shift(2);var r=Xt(e),n=e.read_shift((a-10)/2,"dbcs-cont");return n=n.replace(gr,""),[r,n]}function w1(e){var a=[0,0],r;return r=e.read_shift(2),a[0]=Cn[r]||r,r=e.read_shift(2),a[1]=Cn[r]||r,a}function A1(e){for(var a=e.read_shift(2),r=[];a-- >0;)r.push(ni(e));return r}function F1(e){for(var a=e.read_shift(2),r=[];a-- >0;)r.push(ni(e));return r}function S1(e){e.l+=2;var a={cxfs:0,crc:0};return a.cxfs=e.read_shift(2),a.crc=e.read_shift(4),a}function oi(e,a,r){if(!r.cellStyles)return lr(e,a);var n=r&&r.biff>=12?4:2,t=e.read_shift(n),s=e.read_shift(n),i=e.read_shift(n),c=e.read_shift(n),f=e.read_shift(2);n==2&&(e.l+=2);var l={s:t,e:s,w:i,ixfe:c,flags:f};return(r.biff>=5||!r.biff)&&(l.level=f>>8&7),l}function C1(e,a){var r={};return a<32||(e.l+=16,r.header=cr(e),r.footer=cr(e),e.l+=2),r}function y1(e,a,r){var n={area:!1};if(r.biff!=5)return e.l+=a,n;var t=e.read_shift(1);return e.l+=3,t&16&&(n.area=!0),n}var D1=Xr,O1=ai,I1=gt;function R1(e){var a=e.read_shift(2),r=e.read_shift(2),n=e.read_shift(4),t={fmt:a,env:r,len:n,data:e.slice(e.l,e.l+n)};return e.l+=n,t}function N1(e,a,r){r.biffguess&&r.biff==5&&(r.biff=2);var n=Xr(e);++e.l;var t=ya(e,a-7,r);return n.t="str",n.val=t,n}function L1(e){var a=Xr(e);++e.l;var r=cr(e);return a.t="n",a.val=r,a}function P1(e){var a=Xr(e);++e.l;var r=e.read_shift(2);return a.t="n",a.val=r,a}function b1(e){var a=e.read_shift(1);return a===0?(e.l++,""):e.read_shift(a,"sbcs-cont")}function B1(e,a){e.l+=6,e.l+=2,e.l+=1,e.l+=3,e.l+=1,e.l+=a-13}function M1(e,a,r){var n=e.l+a,t=Xr(e),s=e.read_shift(2),i=ka(e,s,r);return e.l=n,t.t="str",t.val=i,t}var U1=[2,3,48,49,131,139,140,245],Un=function(){var e={1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127,8:865,9:437,10:850,11:437,13:437,14:850,15:437,16:850,17:437,18:850,19:932,20:850,21:437,22:850,23:865,24:437,25:437,26:850,27:437,28:863,29:850,31:852,34:852,35:852,36:860,37:850,38:866,55:850,64:852,77:936,78:949,79:950,80:874,87:1252,88:1252,89:1252,108:863,134:737,135:852,136:857,204:1257,255:16969},a=A0({1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127});function r(c,f){var l=[],o=fa(1);switch(f.type){case"base64":o=br(Fr(c));break;case"binary":o=br(c);break;case"buffer":case"array":o=c;break}rr(o,0);var u=o.read_shift(1),p=!!(u&136),h=!1,d=!1;switch(u){case 2:break;case 3:break;case 48:h=!0,p=!0;break;case 49:h=!0,p=!0;break;case 131:break;case 139:break;case 140:d=!0;break;case 245:break;default:throw new Error("DBF Unsupported Version: "+u.toString(16))}var x=0,g=521;u==2&&(x=o.read_shift(2)),o.l+=3,u!=2&&(x=o.read_shift(4)),x>1048576&&(x=1e6),u!=2&&(g=o.read_shift(2));var w=o.read_shift(2),y=f.codepage||1252;u!=2&&(o.l+=16,o.read_shift(1),o[o.l]!==0&&(y=e[o[o.l]]),o.l+=1,o.l+=2),d&&(o.l+=36);for(var m=[],O={},P=Math.min(o.length,u==2?521:g-10-(h?264:0)),D=d?32:11;o.l<P&&o[o.l]!=13;)switch(O={},O.name=c0.utils.decode(y,o.slice(o.l,o.l+D)).replace(/[\u0000\r\n].*$/g,""),o.l+=D,O.type=String.fromCharCode(o.read_shift(1)),u!=2&&!d&&(O.offset=o.read_shift(4)),O.len=o.read_shift(1),u==2&&(O.offset=o.read_shift(2)),O.dec=o.read_shift(1),O.name.length&&m.push(O),u!=2&&(o.l+=d?13:14),O.type){case"B":(!h||O.len!=8)&&f.WTF&&console.log("Skipping "+O.name+":"+O.type);break;case"G":case"P":f.WTF&&console.log("Skipping "+O.name+":"+O.type);break;case"+":case"0":case"@":case"C":case"D":case"F":case"I":case"L":case"M":case"N":case"O":case"T":case"Y":break;default:throw new Error("Unknown Field Type: "+O.type)}if(o[o.l]!==13&&(o.l=g-1),o.read_shift(1)!==13)throw new Error("DBF Terminator not found "+o.l+" "+o[o.l]);o.l=g;var S=0,U=0;for(l[0]=[],U=0;U!=m.length;++U)l[0][U]=m[U].name;for(;x-- >0;){if(o[o.l]===42){o.l+=w;continue}for(++o.l,l[++S]=[],U=0,U=0;U!=m.length;++U){var R=o.slice(o.l,o.l+m[U].len);o.l+=m[U].len,rr(R,0);var z=c0.utils.decode(y,R);switch(m[U].type){case"C":z.trim().length&&(l[S][U]=z.replace(/\s+$/,""));break;case"D":z.length===8?l[S][U]=new Date(+z.slice(0,4),+z.slice(4,6)-1,+z.slice(6,8)):l[S][U]=z;break;case"F":l[S][U]=parseFloat(z.trim());break;case"+":case"I":l[S][U]=d?R.read_shift(-4,"i")^2147483648:R.read_shift(4,"i");break;case"L":switch(z.trim().toUpperCase()){case"Y":case"T":l[S][U]=!0;break;case"N":case"F":l[S][U]=!1;break;case"":case"?":break;default:throw new Error("DBF Unrecognized L:|"+z+"|")}break;case"M":if(!p)throw new Error("DBF Unexpected MEMO for type "+u.toString(16));l[S][U]="##MEMO##"+(d?parseInt(z.trim(),10):R.read_shift(4));break;case"N":z=z.replace(/\u0000/g,"").trim(),z&&z!="."&&(l[S][U]=+z||0);break;case"@":l[S][U]=new Date(R.read_shift(-8,"f")-621356832e5);break;case"T":l[S][U]=new Date((R.read_shift(4)-2440588)*864e5+R.read_shift(4));break;case"Y":l[S][U]=R.read_shift(4,"i")/1e4+R.read_shift(4,"i")/1e4*Math.pow(2,32);break;case"O":l[S][U]=-R.read_shift(-8,"f");break;case"B":if(h&&m[U].len==8){l[S][U]=R.read_shift(8,"f");break}case"G":case"P":R.l+=m[U].len;break;case"0":if(m[U].name==="_NullFlags")break;default:throw new Error("DBF Unsupported data type "+m[U].type)}}}if(u!=2&&o.l<o.length&&o[o.l++]!=26)throw new Error("DBF EOF Marker missing "+(o.l-1)+" of "+o.length+" "+o[o.l-1].toString(16));return f&&f.sheetRows&&(l=l.slice(0,f.sheetRows)),f.DBF=m,l}function n(c,f){var l=f||{};l.dateNF||(l.dateNF="yyyymmdd");var o=Ua(r(c,l),l);return o["!cols"]=l.DBF.map(function(u){return{wch:u.len,DBF:u}}),delete l.DBF,o}function t(c,f){try{return la(n(c,f),f)}catch(l){if(f&&f.WTF)throw l}return{SheetNames:[],Sheets:{}}}var s={B:8,C:250,L:1,D:8,"?":0,"":0};function i(c,f){var l=f||{};if(+l.codepage>=0&&Br(+l.codepage),l.type=="string")throw new Error("Cannot write DBF to JS string");var o=f0(),u=m0(c,{header:1,raw:!0,cellDates:!0}),p=u[0],h=u.slice(1),d=c["!cols"]||[],x=0,g=0,w=0,y=1;for(x=0;x<p.length;++x){if(((d[x]||{}).DBF||{}).name){p[x]=d[x].DBF.name,++w;continue}if(p[x]!=null){if(++w,typeof p[x]=="number"&&(p[x]=p[x].toString(10)),typeof p[x]!="string")throw new Error("DBF Invalid column name "+p[x]+" |"+typeof p[x]+"|");if(p.indexOf(p[x])!==x){for(g=0;g<1024;++g)if(p.indexOf(p[x]+"_"+g)==-1){p[x]+="_"+g;break}}}}var m=He(c["!ref"]),O=[],P=[],D=[];for(x=0;x<=m.e.c-m.s.c;++x){var S="",U="",R=0,z=[];for(g=0;g<h.length;++g)h[g][x]!=null&&z.push(h[g][x]);if(z.length==0||p[x]==null){O[x]="?";continue}for(g=0;g<z.length;++g){switch(typeof z[g]){case"number":U="B";break;case"string":U="C";break;case"boolean":U="L";break;case"object":U=z[g]instanceof Date?"D":"C";break;default:U="C"}R=Math.max(R,String(z[g]).length),S=S&&S!=U?"C":U}R>250&&(R=250),U=((d[x]||{}).DBF||{}).type,U=="C"&&d[x].DBF.len>R&&(R=d[x].DBF.len),S=="B"&&U=="N"&&(S="N",D[x]=d[x].DBF.dec,R=d[x].DBF.len),P[x]=S=="C"||U=="N"?R:s[S]||0,y+=P[x],O[x]=S}var G=o.next(32);for(G.write_shift(4,318902576),G.write_shift(4,h.length),G.write_shift(2,296+32*w),G.write_shift(2,y),x=0;x<4;++x)G.write_shift(4,0);for(G.write_shift(4,0|(+a[fs]||3)<<8),x=0,g=0;x<p.length;++x)if(p[x]!=null){var b=o.next(32),j=(p[x].slice(-10)+"\0\0\0\0\0\0\0\0\0\0\0").slice(0,11);b.write_shift(1,j,"sbcs"),b.write_shift(1,O[x]=="?"?"C":O[x],"sbcs"),b.write_shift(4,g),b.write_shift(1,P[x]||s[O[x]]||0),b.write_shift(1,D[x]||0),b.write_shift(1,2),b.write_shift(4,0),b.write_shift(1,0),b.write_shift(4,0),b.write_shift(4,0),g+=P[x]||s[O[x]]||0}var Y=o.next(264);for(Y.write_shift(4,13),x=0;x<65;++x)Y.write_shift(4,0);for(x=0;x<h.length;++x){var Q=o.next(y);for(Q.write_shift(1,0),g=0;g<p.length;++g)if(p[g]!=null)switch(O[g]){case"L":Q.write_shift(1,h[x][g]==null?63:h[x][g]?84:70);break;case"B":Q.write_shift(8,h[x][g]||0,"f");break;case"N":var se="0";for(typeof h[x][g]=="number"&&(se=h[x][g].toFixed(D[g]||0)),w=0;w<P[g]-se.length;++w)Q.write_shift(1,32);Q.write_shift(1,se,"sbcs");break;case"D":h[x][g]?(Q.write_shift(4,("0000"+h[x][g].getFullYear()).slice(-4),"sbcs"),Q.write_shift(2,("00"+(h[x][g].getMonth()+1)).slice(-2),"sbcs"),Q.write_shift(2,("00"+h[x][g].getDate()).slice(-2),"sbcs")):Q.write_shift(8,"00000000","sbcs");break;case"C":var ne=String(h[x][g]!=null?h[x][g]:"").slice(0,P[g]);for(Q.write_shift(1,ne,"sbcs"),w=0;w<P[g]-ne.length;++w)Q.write_shift(1,32);break}}return o.next(1).write_shift(1,26),o.end()}return{to_workbook:t,to_sheet:n,from_sheet:i}}(),V1=function(){var e={AA:"À",BA:"Á",CA:"Â",DA:195,HA:"Ä",JA:197,AE:"È",BE:"É",CE:"Ê",HE:"Ë",AI:"Ì",BI:"Í",CI:"Î",HI:"Ï",AO:"Ò",BO:"Ó",CO:"Ô",DO:213,HO:"Ö",AU:"Ù",BU:"Ú",CU:"Û",HU:"Ü",Aa:"à",Ba:"á",Ca:"â",Da:227,Ha:"ä",Ja:229,Ae:"è",Be:"é",Ce:"ê",He:"ë",Ai:"ì",Bi:"í",Ci:"î",Hi:"ï",Ao:"ò",Bo:"ó",Co:"ô",Do:245,Ho:"ö",Au:"ù",Bu:"ú",Cu:"û",Hu:"ü",KC:"Ç",Kc:"ç",q:"æ",z:"œ",a:"Æ",j:"Œ",DN:209,Dn:241,Hy:255,S:169,c:170,R:174,"B ":180,0:176,1:177,2:178,3:179,5:181,6:182,7:183,Q:185,k:186,b:208,i:216,l:222,s:240,y:248,"!":161,'"':162,"#":163,"(":164,"%":165,"'":167,"H ":168,"+":171,";":187,"<":188,"=":189,">":190,"?":191,"{":223},a=new RegExp("\x1BN("+Gr(e).join("|").replace(/\|\|\|/,"|\\||").replace(/([?()+])/g,"\\$1")+"|\\|)","gm"),r=function(p,h){var d=e[h];return typeof d=="number"?en(d):d},n=function(p,h,d){var x=h.charCodeAt(0)-32<<4|d.charCodeAt(0)-48;return x==59?p:en(x)};e["|"]=254;function t(p,h){switch(h.type){case"base64":return s(Fr(p),h);case"binary":return s(p,h);case"buffer":return s(Se&&Buffer.isBuffer(p)?p.toString("binary"):Aa(p),h);case"array":return s(Ta(p),h)}throw new Error("Unrecognized type "+h.type)}function s(p,h){var d=p.split(/[\n\r]+/),x=-1,g=-1,w=0,y=0,m=[],O=[],P=null,D={},S=[],U=[],R=[],z=0,G;for(+h.codepage>=0&&Br(+h.codepage);w!==d.length;++w){z=0;var b=d[w].trim().replace(/\x1B([\x20-\x2F])([\x30-\x3F])/g,n).replace(a,r),j=b.replace(/;;/g,"\0").split(";").map(function(I){return I.replace(/\u0000/g,";")}),Y=j[0],Q;if(b.length>0)switch(Y){case"ID":break;case"E":break;case"B":break;case"O":break;case"W":break;case"P":j[1].charAt(0)=="P"&&O.push(b.slice(3).replace(/;;/g,";"));break;case"C":var se=!1,ne=!1,De=!1,L=!1,X=-1,ue=-1;for(y=1;y<j.length;++y)switch(j[y].charAt(0)){case"A":break;case"X":g=parseInt(j[y].slice(1))-1,ne=!0;break;case"Y":for(x=parseInt(j[y].slice(1))-1,ne||(g=0),G=m.length;G<=x;++G)m[G]=[];break;case"K":Q=j[y].slice(1),Q.charAt(0)==='"'?Q=Q.slice(1,Q.length-1):Q==="TRUE"?Q=!0:Q==="FALSE"?Q=!1:isNaN(Ur(Q))?isNaN(Pa(Q).getDate())||(Q=er(Q)):(Q=Ur(Q),P!==null&&Ba(P)&&(Q=$t(Q))),se=!0;break;case"E":L=!0;var A=Na(j[y].slice(1),{r:x,c:g});m[x][g]=[m[x][g],A];break;case"S":De=!0,m[x][g]=[m[x][g],"S5S"];break;case"G":break;case"R":X=parseInt(j[y].slice(1))-1;break;case"C":ue=parseInt(j[y].slice(1))-1;break;default:if(h&&h.WTF)throw new Error("SYLK bad record "+b)}if(se&&(m[x][g]&&m[x][g].length==2?m[x][g][0]=Q:m[x][g]=Q,P=null),De){if(L)throw new Error("SYLK shared formula cannot have own formula");var B=X>-1&&m[X][ue];if(!B||!B[1])throw new Error("SYLK shared formula cannot find base");m[x][g][1]=_i(B[1],{r:x-X,c:g-ue})}break;case"F":var N=0;for(y=1;y<j.length;++y)switch(j[y].charAt(0)){case"X":g=parseInt(j[y].slice(1))-1,++N;break;case"Y":for(x=parseInt(j[y].slice(1))-1,G=m.length;G<=x;++G)m[G]=[];break;case"M":z=parseInt(j[y].slice(1))/20;break;case"F":break;case"G":break;case"P":P=O[parseInt(j[y].slice(1))];break;case"S":break;case"D":break;case"N":break;case"W":for(R=j[y].slice(1).split(" "),G=parseInt(R[0],10);G<=parseInt(R[1],10);++G)z=parseInt(R[2],10),U[G-1]=z===0?{hidden:!0}:{wch:z},ba(U[G-1]);break;case"C":g=parseInt(j[y].slice(1))-1,U[g]||(U[g]={});break;case"R":x=parseInt(j[y].slice(1))-1,S[x]||(S[x]={}),z>0?(S[x].hpt=z,S[x].hpx=ft(z)):z===0&&(S[x].hidden=!0);break;default:if(h&&h.WTF)throw new Error("SYLK bad record "+b)}N<1&&(P=null);break;default:if(h&&h.WTF)throw new Error("SYLK bad record "+b)}}return S.length>0&&(D["!rows"]=S),U.length>0&&(D["!cols"]=U),h&&h.sheetRows&&(m=m.slice(0,h.sheetRows)),[m,D]}function i(p,h){var d=t(p,h),x=d[0],g=d[1],w=Ua(x,h);return Gr(g).forEach(function(y){w[y]=g[y]}),w}function c(p,h){return la(i(p,h),h)}function f(p,h,d,x){var g="C;Y"+(d+1)+";X"+(x+1)+";K";switch(p.t){case"n":g+=p.v||0,p.f&&!p.F&&(g+=";E"+Sh(p.f,{r:d,c:x}));break;case"b":g+=p.v?"TRUE":"FALSE";break;case"e":g+=p.w||p.v;break;case"d":g+='"'+(p.w||p.v)+'"';break;case"s":g+='"'+p.v.replace(/"/g,"").replace(/;/g,";;")+'"';break}return g}function l(p,h){h.forEach(function(d,x){var g="F;W"+(x+1)+" "+(x+1)+" ";d.hidden?g+="0":(typeof d.width=="number"&&!d.wpx&&(d.wpx=Ut(d.width)),typeof d.wpx=="number"&&!d.wch&&(d.wch=Vt(d.wpx)),typeof d.wch=="number"&&(g+=Math.round(d.wch))),g.charAt(g.length-1)!=" "&&p.push(g)})}function o(p,h){h.forEach(function(d,x){var g="F;";d.hidden?g+="M0;":d.hpt?g+="M"+20*d.hpt+";":d.hpx&&(g+="M"+20*vi(d.hpx)+";"),g.length>2&&p.push(g+"R"+(x+1))})}function u(p,h){var d=["ID;PWXL;N;E"],x=[],g=He(p["!ref"]),w,y=Array.isArray(p),m=`\r
`;d.push("P;PGeneral"),d.push("F;P0;DG0G8;M255"),p["!cols"]&&l(d,p["!cols"]),p["!rows"]&&o(d,p["!rows"]),d.push("B;Y"+(g.e.r-g.s.r+1)+";X"+(g.e.c-g.s.c+1)+";D"+[g.s.c,g.s.r,g.e.c,g.e.r].join(" "));for(var O=g.s.r;O<=g.e.r;++O)for(var P=g.s.c;P<=g.e.c;++P){var D=ve({r:O,c:P});w=y?(p[O]||[])[P]:p[D],!(!w||w.v==null&&(!w.f||w.F))&&x.push(f(w,p,O,P))}return d.join(m)+m+x.join(m)+m+"E"+m}return{to_workbook:c,to_sheet:i,from_sheet:u}}(),H1=function(){function e(s,i){switch(i.type){case"base64":return a(Fr(s),i);case"binary":return a(s,i);case"buffer":return a(Se&&Buffer.isBuffer(s)?s.toString("binary"):Aa(s),i);case"array":return a(Ta(s),i)}throw new Error("Unrecognized type "+i.type)}function a(s,i){for(var c=s.split(`
`),f=-1,l=-1,o=0,u=[];o!==c.length;++o){if(c[o].trim()==="BOT"){u[++f]=[],l=0;continue}if(!(f<0)){var p=c[o].trim().split(","),h=p[0],d=p[1];++o;for(var x=c[o]||"";(x.match(/["]/g)||[]).length&1&&o<c.length-1;)x+=`
`+c[++o];switch(x=x.trim(),+h){case-1:if(x==="BOT"){u[++f]=[],l=0;continue}else if(x!=="EOD")throw new Error("Unrecognized DIF special command "+x);break;case 0:x==="TRUE"?u[f][l]=!0:x==="FALSE"?u[f][l]=!1:isNaN(Ur(d))?isNaN(Pa(d).getDate())?u[f][l]=d:u[f][l]=er(d):u[f][l]=Ur(d),++l;break;case 1:x=x.slice(1,x.length-1),x=x.replace(/""/g,'"'),x&&x.match(/^=".*"$/)&&(x=x.slice(2,-1)),u[f][l++]=x!==""?x:null;break}if(x==="EOD")break}}return i&&i.sheetRows&&(u=u.slice(0,i.sheetRows)),u}function r(s,i){return Ua(e(s,i),i)}function n(s,i){return la(r(s,i),i)}var t=function(){var s=function(f,l,o,u,p){f.push(l),f.push(o+","+u),f.push('"'+p.replace(/"/g,'""')+'"')},i=function(f,l,o,u){f.push(l+","+o),f.push(l==1?'"'+u.replace(/"/g,'""')+'"':u)};return function(f){var l=[],o=He(f["!ref"]),u,p=Array.isArray(f);s(l,"TABLE",0,1,"sheetjs"),s(l,"VECTORS",0,o.e.r-o.s.r+1,""),s(l,"TUPLES",0,o.e.c-o.s.c+1,""),s(l,"DATA",0,0,"");for(var h=o.s.r;h<=o.e.r;++h){i(l,-1,0,"BOT");for(var d=o.s.c;d<=o.e.c;++d){var x=ve({r:h,c:d});if(u=p?(f[h]||[])[d]:f[x],!u){i(l,1,0,"");continue}switch(u.t){case"n":var g=u.w;!g&&u.v!=null&&(g=u.v),g==null?u.f&&!u.F?i(l,1,0,"="+u.f):i(l,1,0,""):i(l,0,g,"V");break;case"b":i(l,0,u.v?1:0,u.v?"TRUE":"FALSE");break;case"s":i(l,1,0,isNaN(u.v)?u.v:'="'+u.v+'"');break;case"d":u.w||(u.w=Ir(u.z||_e[14],mr(er(u.v)))),i(l,0,u.w,"V");break;default:i(l,1,0,"")}}}i(l,-1,0,"EOD");var w=`\r
`,y=l.join(w);return y}}();return{to_workbook:n,to_sheet:r,from_sheet:t}}(),W1=function(){function e(u){return u.replace(/\\b/g,"\\").replace(/\\c/g,":").replace(/\\n/g,`
`)}function a(u){return u.replace(/\\/g,"\\b").replace(/:/g,"\\c").replace(/\n/g,"\\n")}function r(u,p){for(var h=u.split(`
`),d=-1,x=-1,g=0,w=[];g!==h.length;++g){var y=h[g].trim().split(":");if(y[0]==="cell"){var m=vr(y[1]);if(w.length<=m.r)for(d=w.length;d<=m.r;++d)w[d]||(w[d]=[]);switch(d=m.r,x=m.c,y[2]){case"t":w[d][x]=e(y[3]);break;case"v":w[d][x]=+y[3];break;case"vtf":var O=y[y.length-1];case"vtc":switch(y[3]){case"nl":w[d][x]=!!+y[4];break;default:w[d][x]=+y[4];break}y[2]=="vtf"&&(w[d][x]=[w[d][x],O])}}}return p&&p.sheetRows&&(w=w.slice(0,p.sheetRows)),w}function n(u,p){return Ua(r(u,p),p)}function t(u,p){return la(n(u,p),p)}var s=["socialcalc:version:1.5","MIME-Version: 1.0","Content-Type: multipart/mixed; boundary=SocialCalcSpreadsheetControlSave"].join(`
`),i=["--SocialCalcSpreadsheetControlSave","Content-type: text/plain; charset=UTF-8"].join(`
`)+`
`,c=["# SocialCalc Spreadsheet Control Save","part:sheet"].join(`
`),f="--SocialCalcSpreadsheetControlSave--";function l(u){if(!u||!u["!ref"])return"";for(var p=[],h=[],d,x="",g=Ma(u["!ref"]),w=Array.isArray(u),y=g.s.r;y<=g.e.r;++y)for(var m=g.s.c;m<=g.e.c;++m)if(x=ve({r:y,c:m}),d=w?(u[y]||[])[m]:u[x],!(!d||d.v==null||d.t==="z")){switch(h=["cell",x,"t"],d.t){case"s":case"str":h.push(a(d.v));break;case"n":d.f?(h[2]="vtf",h[3]="n",h[4]=d.v,h[5]=a(d.f)):(h[2]="v",h[3]=d.v);break;case"b":h[2]="vt"+(d.f?"f":"c"),h[3]="nl",h[4]=d.v?"1":"0",h[5]=a(d.f||(d.v?"TRUE":"FALSE"));break;case"d":var O=mr(er(d.v));h[2]="vtc",h[3]="nd",h[4]=""+O,h[5]=d.w||Ir(d.z||_e[14],O);break;case"e":continue}p.push(h.join(":"))}return p.push("sheet:c:"+(g.e.c-g.s.c+1)+":r:"+(g.e.r-g.s.r+1)+":tvf:1"),p.push("valueformat:1:text-wiki"),p.join(`
`)}function o(u){return[s,i,c,i,l(u),f].join(`
`)}return{to_workbook:t,to_sheet:n,from_sheet:o}}(),it=function(){function e(o,u,p,h,d){d.raw?u[p][h]=o:o===""||(o==="TRUE"?u[p][h]=!0:o==="FALSE"?u[p][h]=!1:isNaN(Ur(o))?isNaN(Pa(o).getDate())?u[p][h]=o:u[p][h]=er(o):u[p][h]=Ur(o))}function a(o,u){var p=u||{},h=[];if(!o||o.length===0)return h;for(var d=o.split(/[\r\n]/),x=d.length-1;x>=0&&d[x].length===0;)--x;for(var g=10,w=0,y=0;y<=x;++y)w=d[y].indexOf(" "),w==-1?w=d[y].length:w++,g=Math.max(g,w);for(y=0;y<=x;++y){h[y]=[];var m=0;for(e(d[y].slice(0,g).trim(),h,y,m,p),m=1;m<=(d[y].length-g)/10+1;++m)e(d[y].slice(g+(m-1)*10,g+m*10).trim(),h,y,m,p)}return p.sheetRows&&(h=h.slice(0,p.sheetRows)),h}var r={44:",",9:"	",59:";",124:"|"},n={44:3,9:2,59:1,124:0};function t(o){for(var u={},p=!1,h=0,d=0;h<o.length;++h)(d=o.charCodeAt(h))==34?p=!p:!p&&d in r&&(u[d]=(u[d]||0)+1);d=[];for(h in u)Object.prototype.hasOwnProperty.call(u,h)&&d.push([u[h],h]);if(!d.length){u=n;for(h in u)Object.prototype.hasOwnProperty.call(u,h)&&d.push([u[h],h])}return d.sort(function(x,g){return x[0]-g[0]||n[x[1]]-n[g[1]]}),r[d.pop()[1]]||44}function s(o,u){var p=u||{},h="",d=p.dense?[]:{},x={s:{c:0,r:0},e:{c:0,r:0}};o.slice(0,4)=="sep="?o.charCodeAt(5)==13&&o.charCodeAt(6)==10?(h=o.charAt(4),o=o.slice(7)):o.charCodeAt(5)==13||o.charCodeAt(5)==10?(h=o.charAt(4),o=o.slice(6)):h=t(o.slice(0,1024)):p&&p.FS?h=p.FS:h=t(o.slice(0,1024));var g=0,w=0,y=0,m=0,O=0,P=h.charCodeAt(0),D=!1,S=0,U=o.charCodeAt(0);o=o.replace(/\r\n/mg,`
`);var R=p.dateNF!=null?qf(p.dateNF):null;function z(){var G=o.slice(m,O),b={};if(G.charAt(0)=='"'&&G.charAt(G.length-1)=='"'&&(G=G.slice(1,-1).replace(/""/g,'"')),G.length===0)b.t="z";else if(p.raw)b.t="s",b.v=G;else if(G.trim().length===0)b.t="s",b.v=G;else if(G.charCodeAt(0)==61)G.charCodeAt(1)==34&&G.charCodeAt(G.length-1)==34?(b.t="s",b.v=G.slice(2,-1).replace(/""/g,'"')):yh(G)?(b.t="n",b.f=G.slice(1)):(b.t="s",b.v=G);else if(G=="TRUE")b.t="b",b.v=!0;else if(G=="FALSE")b.t="b",b.v=!1;else if(!isNaN(y=Ur(G)))b.t="n",p.cellText!==!1&&(b.w=G),b.v=y;else if(!isNaN(Pa(G).getDate())||R&&G.match(R)){b.z=p.dateNF||_e[14];var j=0;R&&G.match(R)&&(G=Qf(G,p.dateNF,G.match(R)||[]),j=1),p.cellDates?(b.t="d",b.v=er(G,j)):(b.t="n",b.v=mr(er(G,j))),p.cellText!==!1&&(b.w=Ir(b.z,b.v instanceof Date?mr(b.v):b.v)),p.cellNF||delete b.z}else b.t="s",b.v=G;if(b.t=="z"||(p.dense?(d[g]||(d[g]=[]),d[g][w]=b):d[ve({c:w,r:g})]=b),m=O+1,U=o.charCodeAt(m),x.e.c<w&&(x.e.c=w),x.e.r<g&&(x.e.r=g),S==P)++w;else if(w=0,++g,p.sheetRows&&p.sheetRows<=g)return!0}e:for(;O<o.length;++O)switch(S=o.charCodeAt(O)){case 34:U===34&&(D=!D);break;case P:case 10:case 13:if(!D&&z())break e;break}return O-m>0&&z(),d["!ref"]=ye(x),d}function i(o,u){return!(u&&u.PRN)||u.FS||o.slice(0,4)=="sep="||o.indexOf("	")>=0||o.indexOf(",")>=0||o.indexOf(";")>=0?s(o,u):Ua(a(o,u),u)}function c(o,u){var p="",h=u.type=="string"?[0,0,0,0]:G0(o,u);switch(u.type){case"base64":p=Fr(o);break;case"binary":p=o;break;case"buffer":u.codepage==65001?p=o.toString("utf8"):(u.codepage,p=Se&&Buffer.isBuffer(o)?o.toString("binary"):Aa(o));break;case"array":p=Ta(o);break;case"string":p=o;break;default:throw new Error("Unrecognized type "+u.type)}return h[0]==239&&h[1]==187&&h[2]==191?p=Ne(p.slice(3)):u.type!="string"&&u.type!="buffer"&&u.codepage==65001?p=Ne(p):u.type=="binary",p.slice(0,19)=="socialcalc:version:"?W1.to_sheet(u.type=="string"?p:Ne(p),u):i(p,u)}function f(o,u){return la(c(o,u),u)}function l(o){for(var u=[],p=He(o["!ref"]),h,d=Array.isArray(o),x=p.s.r;x<=p.e.r;++x){for(var g=[],w=p.s.c;w<=p.e.c;++w){var y=ve({r:x,c:w});if(h=d?(o[x]||[])[w]:o[y],!h||h.v==null){g.push("          ");continue}for(var m=(h.w||(Zr(h),h.w)||"").slice(0,10);m.length<10;)m+=" ";g.push(m+(w===0?" ":""))}u.push(g.join(""))}return u.join(`
`)}return{to_workbook:f,to_sheet:c,from_sheet:l}}();function G1(e,a){var r=a||{},n=!!r.WTF;r.WTF=!0;try{var t=V1.to_workbook(e,r);return r.WTF=n,t}catch(s){if(r.WTF=n,!s.message.match(/SYLK bad record ID/)&&n)throw s;return it.to_workbook(e,a)}}var Qa=function(){function e(A,B,N){if(A){rr(A,A.l||0);for(var I=N.Enum||X;A.l<A.length;){var J=A.read_shift(2),Z=I[J]||I[65535],ie=A.read_shift(2),te=A.l+ie,ee=Z.f&&Z.f(A,ie,N);if(A.l=te,B(ee,Z,J))return}}}function a(A,B){switch(B.type){case"base64":return r(br(Fr(A)),B);case"binary":return r(br(A),B);case"buffer":case"array":return r(A,B)}throw"Unsupported type "+B.type}function r(A,B){if(!A)return A;var N=B||{},I=N.dense?[]:{},J="Sheet1",Z="",ie=0,te={},ee=[],ke=[],C={s:{r:0,c:0},e:{r:0,c:0}},Le=N.sheetRows||0;if(A[2]==0&&(A[3]==8||A[3]==9)&&A.length>=16&&A[14]==5&&A[15]===108)throw new Error("Unsupported Works 3 for Mac file");if(A[2]==2)N.Enum=X,e(A,function(fe,Pe,hr){switch(hr){case 0:N.vers=fe,fe>=4096&&(N.qpro=!0);break;case 6:C=fe;break;case 204:fe&&(Z=fe);break;case 222:Z=fe;break;case 15:case 51:N.qpro||(fe[1].v=fe[1].v.slice(1));case 13:case 14:case 16:hr==14&&(fe[2]&112)==112&&(fe[2]&15)>1&&(fe[2]&15)<15&&(fe[1].z=N.dateNF||_e[14],N.cellDates&&(fe[1].t="d",fe[1].v=$t(fe[1].v))),N.qpro&&fe[3]>ie&&(I["!ref"]=ye(C),te[J]=I,ee.push(J),I=N.dense?[]:{},C={s:{r:0,c:0},e:{r:0,c:0}},ie=fe[3],J=Z||"Sheet"+(ie+1),Z="");var kr=N.dense?(I[fe[0].r]||[])[fe[0].c]:I[ve(fe[0])];if(kr){kr.t=fe[1].t,kr.v=fe[1].v,fe[1].z!=null&&(kr.z=fe[1].z),fe[1].f!=null&&(kr.f=fe[1].f);break}N.dense?(I[fe[0].r]||(I[fe[0].r]=[]),I[fe[0].r][fe[0].c]=fe[1]):I[ve(fe[0])]=fe[1];break}},N);else if(A[2]==26||A[2]==14)N.Enum=ue,A[2]==14&&(N.qpro=!0,A.l=0),e(A,function(fe,Pe,hr){switch(hr){case 204:J=fe;break;case 22:fe[1].v=fe[1].v.slice(1);case 23:case 24:case 25:case 37:case 39:case 40:if(fe[3]>ie&&(I["!ref"]=ye(C),te[J]=I,ee.push(J),I=N.dense?[]:{},C={s:{r:0,c:0},e:{r:0,c:0}},ie=fe[3],J="Sheet"+(ie+1)),Le>0&&fe[0].r>=Le)break;N.dense?(I[fe[0].r]||(I[fe[0].r]=[]),I[fe[0].r][fe[0].c]=fe[1]):I[ve(fe[0])]=fe[1],C.e.c<fe[0].c&&(C.e.c=fe[0].c),C.e.r<fe[0].r&&(C.e.r=fe[0].r);break;case 27:fe[14e3]&&(ke[fe[14e3][0]]=fe[14e3][1]);break;case 1537:ke[fe[0]]=fe[1],fe[0]==ie&&(J=fe[1]);break}},N);else throw new Error("Unrecognized LOTUS BOF "+A[2]);if(I["!ref"]=ye(C),te[Z||J]=I,ee.push(Z||J),!ke.length)return{SheetNames:ee,Sheets:te};for(var Fe={},Ie=[],Ee=0;Ee<ke.length;++Ee)te[ee[Ee]]?(Ie.push(ke[Ee]||ee[Ee]),Fe[ke[Ee]]=te[ke[Ee]]||te[ee[Ee]]):(Ie.push(ke[Ee]),Fe[ke[Ee]]={"!ref":"A1"});return{SheetNames:Ie,Sheets:Fe}}function n(A,B){var N=B||{};if(+N.codepage>=0&&Br(+N.codepage),N.type=="string")throw new Error("Cannot write WK1 to JS string");var I=f0(),J=He(A["!ref"]),Z=Array.isArray(A),ie=[];Pr(I,0,s(1030)),Pr(I,6,f(J));for(var te=Math.min(J.e.r,8191),ee=J.s.r;ee<=te;++ee)for(var ke=tr(ee),C=J.s.c;C<=J.e.c;++C){ee===J.s.r&&(ie[C]=Je(C));var Le=ie[C]+ke,Fe=Z?(A[ee]||[])[C]:A[Le];if(!(!Fe||Fe.t=="z"))if(Fe.t=="n")(Fe.v|0)==Fe.v&&Fe.v>=-32768&&Fe.v<=32767?Pr(I,13,h(ee,C,Fe.v)):Pr(I,14,x(ee,C,Fe.v));else{var Ie=Zr(Fe);Pr(I,15,u(ee,C,Ie.slice(0,239)))}}return Pr(I,1),I.end()}function t(A,B){var N=B||{};if(+N.codepage>=0&&Br(+N.codepage),N.type=="string")throw new Error("Cannot write WK3 to JS string");var I=f0();Pr(I,0,i(A));for(var J=0,Z=0;J<A.SheetNames.length;++J)(A.Sheets[A.SheetNames[J]]||{})["!ref"]&&Pr(I,27,L(A.SheetNames[J],Z++));var ie=0;for(J=0;J<A.SheetNames.length;++J){var te=A.Sheets[A.SheetNames[J]];if(!(!te||!te["!ref"])){for(var ee=He(te["!ref"]),ke=Array.isArray(te),C=[],Le=Math.min(ee.e.r,8191),Fe=ee.s.r;Fe<=Le;++Fe)for(var Ie=tr(Fe),Ee=ee.s.c;Ee<=ee.e.c;++Ee){Fe===ee.s.r&&(C[Ee]=Je(Ee));var fe=C[Ee]+Ie,Pe=ke?(te[Fe]||[])[Ee]:te[fe];if(!(!Pe||Pe.t=="z"))if(Pe.t=="n")Pr(I,23,z(Fe,Ee,ie,Pe.v));else{var hr=Zr(Pe);Pr(I,22,S(Fe,Ee,ie,hr.slice(0,239)))}}++ie}}return Pr(I,1),I.end()}function s(A){var B=je(2);return B.write_shift(2,A),B}function i(A){var B=je(26);B.write_shift(2,4096),B.write_shift(2,4),B.write_shift(4,0);for(var N=0,I=0,J=0,Z=0;Z<A.SheetNames.length;++Z){var ie=A.SheetNames[Z],te=A.Sheets[ie];if(!(!te||!te["!ref"])){++J;var ee=Ma(te["!ref"]);N<ee.e.r&&(N=ee.e.r),I<ee.e.c&&(I=ee.e.c)}}return N>8191&&(N=8191),B.write_shift(2,N),B.write_shift(1,J),B.write_shift(1,I),B.write_shift(2,0),B.write_shift(2,0),B.write_shift(1,1),B.write_shift(1,2),B.write_shift(4,0),B.write_shift(4,0),B}function c(A,B,N){var I={s:{c:0,r:0},e:{c:0,r:0}};return B==8&&N.qpro?(I.s.c=A.read_shift(1),A.l++,I.s.r=A.read_shift(2),I.e.c=A.read_shift(1),A.l++,I.e.r=A.read_shift(2),I):(I.s.c=A.read_shift(2),I.s.r=A.read_shift(2),B==12&&N.qpro&&(A.l+=2),I.e.c=A.read_shift(2),I.e.r=A.read_shift(2),B==12&&N.qpro&&(A.l+=2),I.s.c==65535&&(I.s.c=I.e.c=I.s.r=I.e.r=0),I)}function f(A){var B=je(8);return B.write_shift(2,A.s.c),B.write_shift(2,A.s.r),B.write_shift(2,A.e.c),B.write_shift(2,A.e.r),B}function l(A,B,N){var I=[{c:0,r:0},{t:"n",v:0},0,0];return N.qpro&&N.vers!=20768?(I[0].c=A.read_shift(1),I[3]=A.read_shift(1),I[0].r=A.read_shift(2),A.l+=2):(I[2]=A.read_shift(1),I[0].c=A.read_shift(2),I[0].r=A.read_shift(2)),I}function o(A,B,N){var I=A.l+B,J=l(A,B,N);if(J[1].t="s",N.vers==20768){A.l++;var Z=A.read_shift(1);return J[1].v=A.read_shift(Z,"utf8"),J}return N.qpro&&A.l++,J[1].v=A.read_shift(I-A.l,"cstr"),J}function u(A,B,N){var I=je(7+N.length);I.write_shift(1,255),I.write_shift(2,B),I.write_shift(2,A),I.write_shift(1,39);for(var J=0;J<I.length;++J){var Z=N.charCodeAt(J);I.write_shift(1,Z>=128?95:Z)}return I.write_shift(1,0),I}function p(A,B,N){var I=l(A,B,N);return I[1].v=A.read_shift(2,"i"),I}function h(A,B,N){var I=je(7);return I.write_shift(1,255),I.write_shift(2,B),I.write_shift(2,A),I.write_shift(2,N,"i"),I}function d(A,B,N){var I=l(A,B,N);return I[1].v=A.read_shift(8,"f"),I}function x(A,B,N){var I=je(13);return I.write_shift(1,255),I.write_shift(2,B),I.write_shift(2,A),I.write_shift(8,N,"f"),I}function g(A,B,N){var I=A.l+B,J=l(A,B,N);if(J[1].v=A.read_shift(8,"f"),N.qpro)A.l=I;else{var Z=A.read_shift(2);O(A.slice(A.l,A.l+Z),J),A.l+=Z}return J}function w(A,B,N){var I=B&32768;return B&=-32769,B=(I?A:0)+(B>=8192?B-16384:B),(I?"":"$")+(N?Je(B):tr(B))}var y={51:["FALSE",0],52:["TRUE",0],70:["LEN",1],80:["SUM",69],81:["AVERAGEA",69],82:["COUNTA",69],83:["MINA",69],84:["MAXA",69],111:["T",1]},m=["","","","","","","","","","+","-","*","/","^","=","<>","<=",">=","<",">","","","","","&","","","","","","",""];function O(A,B){rr(A,0);for(var N=[],I=0,J="",Z="",ie="",te="";A.l<A.length;){var ee=A[A.l++];switch(ee){case 0:N.push(A.read_shift(8,"f"));break;case 1:Z=w(B[0].c,A.read_shift(2),!0),J=w(B[0].r,A.read_shift(2),!1),N.push(Z+J);break;case 2:{var ke=w(B[0].c,A.read_shift(2),!0),C=w(B[0].r,A.read_shift(2),!1);Z=w(B[0].c,A.read_shift(2),!0),J=w(B[0].r,A.read_shift(2),!1),N.push(ke+C+":"+Z+J)}break;case 3:if(A.l<A.length){console.error("WK1 premature formula end");return}break;case 4:N.push("("+N.pop()+")");break;case 5:N.push(A.read_shift(2));break;case 6:{for(var Le="";ee=A[A.l++];)Le+=String.fromCharCode(ee);N.push('"'+Le.replace(/"/g,'""')+'"')}break;case 8:N.push("-"+N.pop());break;case 23:N.push("+"+N.pop());break;case 22:N.push("NOT("+N.pop()+")");break;case 20:case 21:te=N.pop(),ie=N.pop(),N.push(["AND","OR"][ee-20]+"("+ie+","+te+")");break;default:if(ee<32&&m[ee])te=N.pop(),ie=N.pop(),N.push(ie+m[ee]+te);else if(y[ee]){if(I=y[ee][1],I==69&&(I=A[A.l++]),I>N.length){console.error("WK1 bad formula parse 0x"+ee.toString(16)+":|"+N.join("|")+"|");return}var Fe=N.slice(-I);N.length-=I,N.push(y[ee][0]+"("+Fe.join(",")+")")}else return ee<=7?console.error("WK1 invalid opcode "+ee.toString(16)):ee<=24?console.error("WK1 unsupported op "+ee.toString(16)):ee<=30?console.error("WK1 invalid opcode "+ee.toString(16)):ee<=115?console.error("WK1 unsupported function opcode "+ee.toString(16)):console.error("WK1 unrecognized opcode "+ee.toString(16))}}N.length==1?B[1].f=""+N[0]:console.error("WK1 bad formula parse |"+N.join("|")+"|")}function P(A){var B=[{c:0,r:0},{t:"n",v:0},0];return B[0].r=A.read_shift(2),B[3]=A[A.l++],B[0].c=A[A.l++],B}function D(A,B){var N=P(A);return N[1].t="s",N[1].v=A.read_shift(B-4,"cstr"),N}function S(A,B,N,I){var J=je(6+I.length);J.write_shift(2,A),J.write_shift(1,N),J.write_shift(1,B),J.write_shift(1,39);for(var Z=0;Z<I.length;++Z){var ie=I.charCodeAt(Z);J.write_shift(1,ie>=128?95:ie)}return J.write_shift(1,0),J}function U(A,B){var N=P(A);N[1].v=A.read_shift(2);var I=N[1].v>>1;if(N[1].v&1)switch(I&7){case 0:I=(I>>3)*5e3;break;case 1:I=(I>>3)*500;break;case 2:I=(I>>3)/20;break;case 3:I=(I>>3)/200;break;case 4:I=(I>>3)/2e3;break;case 5:I=(I>>3)/2e4;break;case 6:I=(I>>3)/16;break;case 7:I=(I>>3)/64;break}return N[1].v=I,N}function R(A,B){var N=P(A),I=A.read_shift(4),J=A.read_shift(4),Z=A.read_shift(2);if(Z==65535)return I===0&&J===3221225472?(N[1].t="e",N[1].v=15):I===0&&J===3489660928?(N[1].t="e",N[1].v=42):N[1].v=0,N;var ie=Z&32768;return Z=(Z&32767)-16446,N[1].v=(1-ie*2)*(J*Math.pow(2,Z+32)+I*Math.pow(2,Z)),N}function z(A,B,N,I){var J=je(14);if(J.write_shift(2,A),J.write_shift(1,N),J.write_shift(1,B),I==0)return J.write_shift(4,0),J.write_shift(4,0),J.write_shift(2,65535),J;var Z=0,ie=0,te=0,ee=0;return I<0&&(Z=1,I=-I),ie=Math.log2(I)|0,I/=Math.pow(2,ie-31),ee=I>>>0,(ee&2147483648)==0&&(I/=2,++ie,ee=I>>>0),I-=ee,ee|=2147483648,ee>>>=0,I*=Math.pow(2,32),te=I>>>0,J.write_shift(4,te),J.write_shift(4,ee),ie+=16383+(Z?32768:0),J.write_shift(2,ie),J}function G(A,B){var N=R(A);return A.l+=B-14,N}function b(A,B){var N=P(A),I=A.read_shift(4);return N[1].v=I>>6,N}function j(A,B){var N=P(A),I=A.read_shift(8,"f");return N[1].v=I,N}function Y(A,B){var N=j(A);return A.l+=B-10,N}function Q(A,B){return A[A.l+B-1]==0?A.read_shift(B,"cstr"):""}function se(A,B){var N=A[A.l++];N>B-1&&(N=B-1);for(var I="";I.length<N;)I+=String.fromCharCode(A[A.l++]);return I}function ne(A,B,N){if(!(!N.qpro||B<21)){var I=A.read_shift(1);A.l+=17,A.l+=1,A.l+=2;var J=A.read_shift(B-21,"cstr");return[I,J]}}function De(A,B){for(var N={},I=A.l+B;A.l<I;){var J=A.read_shift(2);if(J==14e3){for(N[J]=[0,""],N[J][0]=A.read_shift(2);A[A.l];)N[J][1]+=String.fromCharCode(A[A.l]),A.l++;A.l++}}return N}function L(A,B){var N=je(5+A.length);N.write_shift(2,14e3),N.write_shift(2,B);for(var I=0;I<A.length;++I){var J=A.charCodeAt(I);N[N.l++]=J>127?95:J}return N[N.l++]=0,N}var X={0:{n:"BOF",f:ze},1:{n:"EOF"},2:{n:"CALCMODE"},3:{n:"CALCORDER"},4:{n:"SPLIT"},5:{n:"SYNC"},6:{n:"RANGE",f:c},7:{n:"WINDOW1"},8:{n:"COLW1"},9:{n:"WINTWO"},10:{n:"COLW2"},11:{n:"NAME"},12:{n:"BLANK"},13:{n:"INTEGER",f:p},14:{n:"NUMBER",f:d},15:{n:"LABEL",f:o},16:{n:"FORMULA",f:g},24:{n:"TABLE"},25:{n:"ORANGE"},26:{n:"PRANGE"},27:{n:"SRANGE"},28:{n:"FRANGE"},29:{n:"KRANGE1"},32:{n:"HRANGE"},35:{n:"KRANGE2"},36:{n:"PROTEC"},37:{n:"FOOTER"},38:{n:"HEADER"},39:{n:"SETUP"},40:{n:"MARGINS"},41:{n:"LABELFMT"},42:{n:"TITLES"},43:{n:"SHEETJS"},45:{n:"GRAPH"},46:{n:"NGRAPH"},47:{n:"CALCCOUNT"},48:{n:"UNFORMATTED"},49:{n:"CURSORW12"},50:{n:"WINDOW"},51:{n:"STRING",f:o},55:{n:"PASSWORD"},56:{n:"LOCKED"},60:{n:"QUERY"},61:{n:"QUERYNAME"},62:{n:"PRINT"},63:{n:"PRINTNAME"},64:{n:"GRAPH2"},65:{n:"GRAPHNAME"},66:{n:"ZOOM"},67:{n:"SYMSPLIT"},68:{n:"NSROWS"},69:{n:"NSCOLS"},70:{n:"RULER"},71:{n:"NNAME"},72:{n:"ACOMM"},73:{n:"AMACRO"},74:{n:"PARSE"},102:{n:"PRANGES??"},103:{n:"RRANGES??"},104:{n:"FNAME??"},105:{n:"MRANGES??"},204:{n:"SHEETNAMECS",f:Q},222:{n:"SHEETNAMELP",f:se},65535:{n:""}},ue={0:{n:"BOF"},1:{n:"EOF"},2:{n:"PASSWORD"},3:{n:"CALCSET"},4:{n:"WINDOWSET"},5:{n:"SHEETCELLPTR"},6:{n:"SHEETLAYOUT"},7:{n:"COLUMNWIDTH"},8:{n:"HIDDENCOLUMN"},9:{n:"USERRANGE"},10:{n:"SYSTEMRANGE"},11:{n:"ZEROFORCE"},12:{n:"SORTKEYDIR"},13:{n:"FILESEAL"},14:{n:"DATAFILLNUMS"},15:{n:"PRINTMAIN"},16:{n:"PRINTSTRING"},17:{n:"GRAPHMAIN"},18:{n:"GRAPHSTRING"},19:{n:"??"},20:{n:"ERRCELL"},21:{n:"NACELL"},22:{n:"LABEL16",f:D},23:{n:"NUMBER17",f:R},24:{n:"NUMBER18",f:U},25:{n:"FORMULA19",f:G},26:{n:"FORMULA1A"},27:{n:"XFORMAT",f:De},28:{n:"DTLABELMISC"},29:{n:"DTLABELCELL"},30:{n:"GRAPHWINDOW"},31:{n:"CPA"},32:{n:"LPLAUTO"},33:{n:"QUERY"},34:{n:"HIDDENSHEET"},35:{n:"??"},37:{n:"NUMBER25",f:b},38:{n:"??"},39:{n:"NUMBER27",f:j},40:{n:"FORMULA28",f:Y},142:{n:"??"},147:{n:"??"},150:{n:"??"},151:{n:"??"},152:{n:"??"},153:{n:"??"},154:{n:"??"},155:{n:"??"},156:{n:"??"},163:{n:"??"},174:{n:"??"},175:{n:"??"},176:{n:"??"},177:{n:"??"},184:{n:"??"},185:{n:"??"},186:{n:"??"},187:{n:"??"},188:{n:"??"},195:{n:"??"},201:{n:"??"},204:{n:"SHEETNAMECS",f:Q},205:{n:"??"},206:{n:"??"},207:{n:"??"},208:{n:"??"},256:{n:"??"},259:{n:"??"},260:{n:"??"},261:{n:"??"},262:{n:"??"},263:{n:"??"},265:{n:"??"},266:{n:"??"},267:{n:"??"},268:{n:"??"},270:{n:"??"},271:{n:"??"},384:{n:"??"},389:{n:"??"},390:{n:"??"},393:{n:"??"},396:{n:"??"},512:{n:"??"},514:{n:"??"},513:{n:"??"},516:{n:"??"},517:{n:"??"},640:{n:"??"},641:{n:"??"},642:{n:"??"},643:{n:"??"},644:{n:"??"},645:{n:"??"},646:{n:"??"},647:{n:"??"},648:{n:"??"},658:{n:"??"},659:{n:"??"},660:{n:"??"},661:{n:"??"},662:{n:"??"},665:{n:"??"},666:{n:"??"},768:{n:"??"},772:{n:"??"},1537:{n:"SHEETINFOQP",f:ne},1600:{n:"??"},1602:{n:"??"},1793:{n:"??"},1794:{n:"??"},1795:{n:"??"},1796:{n:"??"},1920:{n:"??"},2048:{n:"??"},2049:{n:"??"},2052:{n:"??"},2688:{n:"??"},10998:{n:"??"},12849:{n:"??"},28233:{n:"??"},28484:{n:"??"},65535:{n:""}};return{sheet_to_wk1:n,book_to_wk3:t,to_workbook:a}}();function $1(e){var a={},r=e.match(ur),n=0,t=!1;if(r)for(;n!=r.length;++n){var s=pe(r[n]);switch(s[0].replace(/\w*:/g,"")){case"<condense":break;case"<extend":break;case"<shadow":if(!s.val)break;case"<shadow>":case"<shadow/>":a.shadow=1;break;case"</shadow>":break;case"<charset":if(s.val=="1")break;a.cp=_0[parseInt(s.val,10)];break;case"<outline":if(!s.val)break;case"<outline>":case"<outline/>":a.outline=1;break;case"</outline>":break;case"<rFont":a.name=s.val;break;case"<sz":a.sz=s.val;break;case"<strike":if(!s.val)break;case"<strike>":case"<strike/>":a.strike=1;break;case"</strike>":break;case"<u":if(!s.val)break;switch(s.val){case"double":a.uval="double";break;case"singleAccounting":a.uval="single-accounting";break;case"doubleAccounting":a.uval="double-accounting";break}case"<u>":case"<u/>":a.u=1;break;case"</u>":break;case"<b":if(s.val=="0")break;case"<b>":case"<b/>":a.b=1;break;case"</b>":break;case"<i":if(s.val=="0")break;case"<i>":case"<i/>":a.i=1;break;case"</i>":break;case"<color":s.rgb&&(a.color=s.rgb.slice(2,8));break;case"<color>":case"<color/>":case"</color>":break;case"<family":a.family=s.val;break;case"<family>":case"<family/>":case"</family>":break;case"<vertAlign":a.valign=s.val;break;case"<vertAlign>":case"<vertAlign/>":case"</vertAlign>":break;case"<scheme":break;case"<scheme>":case"<scheme/>":case"</scheme>":break;case"<extLst":case"<extLst>":case"</extLst>":break;case"<ext":t=!0;break;case"</ext>":t=!1;break;default:if(s[0].charCodeAt(1)!==47&&!t)throw new Error("Unrecognized rich format "+s[0])}}return a}var X1=function(){var e=nt("t"),a=nt("rPr");function r(s){var i=s.match(e);if(!i)return{t:"s",v:""};var c={t:"s",v:Oe(i[1])},f=s.match(a);return f&&(c.s=$1(f[1])),c}var n=/<(?:\w+:)?r>/g,t=/<\/(?:\w+:)?r>/;return function(i){return i.replace(n,"").split(t).map(r).filter(function(c){return c.v})}}(),z1=function(){var a=/(\r\n|\n)/g;function r(t,s,i){var c=[];t.u&&c.push("text-decoration: underline;"),t.uval&&c.push("text-underline-style:"+t.uval+";"),t.sz&&c.push("font-size:"+t.sz+"pt;"),t.outline&&c.push("text-effect: outline;"),t.shadow&&c.push("text-shadow: auto;"),s.push('<span style="'+c.join("")+'">'),t.b&&(s.push("<b>"),i.push("</b>")),t.i&&(s.push("<i>"),i.push("</i>")),t.strike&&(s.push("<s>"),i.push("</s>"));var f=t.valign||"";return f=="superscript"||f=="super"?f="sup":f=="subscript"&&(f="sub"),f!=""&&(s.push("<"+f+">"),i.push("</"+f+">")),i.push("</span>"),t}function n(t){var s=[[],t.v,[]];return t.v?(t.s&&r(t.s,s[0],s[2]),s[0].join("")+s[1].replace(a,"<br/>")+s[2].join("")):""}return function(s){return s.map(n).join("")}}(),K1=/<(?:\w+:)?t[^>]*>([^<]*)<\/(?:\w+:)?t>/g,Y1=/<(?:\w+:)?r>/,j1=/<(?:\w+:)?rPh.*?>([\s\S]*?)<\/(?:\w+:)?rPh>/g;function b0(e,a){var r=a?a.cellHTML:!0,n={};return e?(e.match(/^\s*<(?:\w+:)?t[^>]*>/)?(n.t=Oe(Ne(e.slice(e.indexOf(">")+1).split(/<\/(?:\w+:)?t>/)[0]||"")),n.r=Ne(e),r&&(n.h=S0(n.t))):e.match(Y1)&&(n.r=Ne(e),n.t=Oe(Ne((e.replace(j1,"").match(K1)||[]).join("").replace(ur,""))),r&&(n.h=z1(X1(n.r)))),n):{t:""}}var J1=/<(?:\w+:)?sst([^>]*)>([\s\S]*)<\/(?:\w+:)?sst>/,Z1=/<(?:\w+:)?(?:si|sstItem)>/g,q1=/<\/(?:\w+:)?(?:si|sstItem)>/;function Q1(e,a){var r=[],n="";if(!e)return r;var t=e.match(J1);if(t){n=t[2].replace(Z1,"").split(q1);for(var s=0;s!=n.length;++s){var i=b0(n[s].trim(),a);i!=null&&(r[r.length]=i)}t=pe(t[1]),r.Count=t.count,r.Unique=t.uniqueCount}return r}function eu(e){return[e.read_shift(4),e.read_shift(4)]}function ru(e,a){var r=[],n=!1;return qr(e,function(s,i,c){switch(c){case 159:r.Count=s[0],r.Unique=s[1];break;case 19:r.push(s);break;case 160:return!0;case 35:n=!0;break;case 36:n=!1;break;default:if(i.T,!n||a.WTF)throw new Error("Unexpected record 0x"+c.toString(16))}}),r}function li(e){for(var a=[],r=e.split(""),n=0;n<r.length;++n)a[n]=r[n].charCodeAt(0);return a}function Jr(e,a){var r={};return r.Major=e.read_shift(2),r.Minor=e.read_shift(2),a>=4&&(e.l+=a-4),r}function au(e){var a={};return a.id=e.read_shift(0,"lpp4"),a.R=Jr(e,4),a.U=Jr(e,4),a.W=Jr(e,4),a}function tu(e){for(var a=e.read_shift(4),r=e.l+a-4,n={},t=e.read_shift(4),s=[];t-- >0;)s.push({t:e.read_shift(4),v:e.read_shift(0,"lpp4")});if(n.name=e.read_shift(0,"lpp4"),n.comps=s,e.l!=r)throw new Error("Bad DataSpaceMapEntry: "+e.l+" != "+r);return n}function nu(e){var a=[];e.l+=4;for(var r=e.read_shift(4);r-- >0;)a.push(tu(e));return a}function su(e){var a=[];e.l+=4;for(var r=e.read_shift(4);r-- >0;)a.push(e.read_shift(0,"lpp4"));return a}function iu(e){var a={};return e.read_shift(4),e.l+=4,a.id=e.read_shift(0,"lpp4"),a.name=e.read_shift(0,"lpp4"),a.R=Jr(e,4),a.U=Jr(e,4),a.W=Jr(e,4),a}function cu(e){var a=iu(e);if(a.ename=e.read_shift(0,"8lpp4"),a.blksz=e.read_shift(4),a.cmode=e.read_shift(4),e.read_shift(4)!=4)throw new Error("Bad !Primary record");return a}function ui(e,a){var r=e.l+a,n={};n.Flags=e.read_shift(4)&63,e.l+=4,n.AlgID=e.read_shift(4);var t=!1;switch(n.AlgID){case 26126:case 26127:case 26128:t=n.Flags==36;break;case 26625:t=n.Flags==4;break;case 0:t=n.Flags==16||n.Flags==4||n.Flags==36;break;default:throw"Unrecognized encryption algorithm: "+n.AlgID}if(!t)throw new Error("Encryption Flags/AlgID mismatch");return n.AlgIDHash=e.read_shift(4),n.KeySize=e.read_shift(4),n.ProviderType=e.read_shift(4),e.l+=8,n.CSPName=e.read_shift(r-e.l>>1,"utf16le"),e.l=r,n}function hi(e,a){var r={},n=e.l+a;return e.l+=4,r.Salt=e.slice(e.l,e.l+16),e.l+=16,r.Verifier=e.slice(e.l,e.l+16),e.l+=16,e.read_shift(4),r.VerifierHash=e.slice(e.l,n),e.l=n,r}function fu(e){var a=Jr(e);switch(a.Minor){case 2:return[a.Minor,ou(e)];case 3:return[a.Minor,lu()];case 4:return[a.Minor,uu(e)]}throw new Error("ECMA-376 Encrypted file unrecognized Version: "+a.Minor)}function ou(e){var a=e.read_shift(4);if((a&63)!=36)throw new Error("EncryptionInfo mismatch");var r=e.read_shift(4),n=ui(e,r),t=hi(e,e.length-e.l);return{t:"Std",h:n,v:t}}function lu(){throw new Error("File is password-protected: ECMA-376 Extensible")}function uu(e){var a=["saltSize","blockSize","keyBits","hashSize","cipherAlgorithm","cipherChaining","hashAlgorithm","saltValue"];e.l+=4;var r=e.read_shift(e.length-e.l,"utf8"),n={};return r.replace(ur,function(s){var i=pe(s);switch($r(i[0])){case"<?xml":break;case"<encryption":case"</encryption>":break;case"<keyData":a.forEach(function(c){n[c]=i[c]});break;case"<dataIntegrity":n.encryptedHmacKey=i.encryptedHmacKey,n.encryptedHmacValue=i.encryptedHmacValue;break;case"<keyEncryptors>":case"<keyEncryptors":n.encs=[];break;case"</keyEncryptors>":break;case"<keyEncryptor":n.uri=i.uri;break;case"</keyEncryptor>":break;case"<encryptedKey":n.encs.push(i);break;default:throw i[0]}}),n}function hu(e,a){var r={},n=r.EncryptionVersionInfo=Jr(e,4);if(a-=4,n.Minor!=2)throw new Error("unrecognized minor version code: "+n.Minor);if(n.Major>4||n.Major<2)throw new Error("unrecognized major version code: "+n.Major);r.Flags=e.read_shift(4),a-=4;var t=e.read_shift(4);return a-=4,r.EncryptionHeader=ui(e,t),a-=t,r.EncryptionVerifier=hi(e,a),r}function xu(e){var a={},r=a.EncryptionVersionInfo=Jr(e,4);if(r.Major!=1||r.Minor!=1)throw"unrecognized version code "+r.Major+" : "+r.Minor;return a.Salt=e.read_shift(16),a.EncryptedVerifier=e.read_shift(16),a.EncryptedVerifierHash=e.read_shift(16),a}function du(e){var a=0,r,n=li(e),t=n.length+1,s,i,c,f,l;for(r=fa(t),r[0]=n.length,s=1;s!=t;++s)r[s]=n[s-1];for(s=t-1;s>=0;--s)i=r[s],c=(a&16384)===0?0:1,f=a<<1&32767,l=c|f,a=l^i;return a^52811}var xi=function(){var e=[187,255,255,186,255,255,185,128,0,190,15,0,191,15,0],a=[57840,7439,52380,33984,4364,3600,61902,12606,6258,57657,54287,34041,10252,43370,20163],r=[44796,19929,39858,10053,20106,40212,10761,31585,63170,64933,60267,50935,40399,11199,17763,35526,1453,2906,5812,11624,23248,885,1770,3540,7080,14160,28320,56640,55369,41139,20807,41614,21821,43642,17621,28485,56970,44341,19019,38038,14605,29210,60195,50791,40175,10751,21502,43004,24537,18387,36774,3949,7898,15796,31592,63184,47201,24803,49606,37805,14203,28406,56812,17824,35648,1697,3394,6788,13576,27152,43601,17539,35078,557,1114,2228,4456,30388,60776,51953,34243,7079,14158,28316,14128,28256,56512,43425,17251,34502,7597,13105,26210,52420,35241,883,1766,3532,4129,8258,16516,33032,4657,9314,18628],n=function(i){return(i/2|i*128)&255},t=function(i,c){return n(i^c)},s=function(i){for(var c=a[i.length-1],f=104,l=i.length-1;l>=0;--l)for(var o=i[l],u=0;u!=7;++u)o&64&&(c^=r[f]),o*=2,--f;return c};return function(i){for(var c=li(i),f=s(c),l=c.length,o=fa(16),u=0;u!=16;++u)o[u]=0;var p,h,d;for((l&1)===1&&(p=f>>8,o[l]=t(e[0],p),--l,p=f&255,h=c[c.length-1],o[l]=t(h,p));l>0;)--l,p=f>>8,o[l]=t(c[l],p),--l,p=f&255,o[l]=t(c[l],p);for(l=15,d=15-c.length;d>0;)p=f>>8,o[l]=t(e[d],p),--l,--d,p=f&255,o[l]=t(c[l],p),--l,--d;return o}}(),pu=function(e,a,r,n,t){t||(t=a),n||(n=xi(e));var s,i;for(s=0;s!=a.length;++s)i=a[s],i^=n[r],i=(i>>5|i<<3)&255,t[s]=i,++r;return[t,r,n]},vu=function(e){var a=0,r=xi(e);return function(n){var t=pu("",n,a,r);return a=t[1],t[0]}};function gu(e,a,r,n){var t={key:ze(e),verificationBytes:ze(e)};return r.password&&(t.verifier=du(r.password)),n.valid=t.verificationBytes===t.verifier,n.valid&&(n.insitu=vu(r.password)),t}function mu(e,a,r){var n=r||{};return n.Info=e.read_shift(2),e.l-=2,n.Info===1?n.Data=xu(e):n.Data=hu(e,a),n}function _u(e,a,r){var n={Type:r.biff>=8?e.read_shift(2):0};return n.Type?mu(e,a-2,n):gu(e,r.biff>=8?a:a-2,r,n),n}var Eu=function(){function e(t,s){switch(s.type){case"base64":return a(Fr(t),s);case"binary":return a(t,s);case"buffer":return a(Se&&Buffer.isBuffer(t)?t.toString("binary"):Aa(t),s);case"array":return a(Ta(t),s)}throw new Error("Unrecognized type "+s.type)}function a(t,s){var i=s||{},c=i.dense?[]:{},f=t.match(/\\trowd.*?\\row\b/g);if(!f.length)throw new Error("RTF missing table");var l={s:{c:0,r:0},e:{c:0,r:f.length-1}};return f.forEach(function(o,u){Array.isArray(c)&&(c[u]=[]);for(var p=/\\\w+\b/g,h=0,d,x=-1;d=p.exec(o);){switch(d[0]){case"\\cell":var g=o.slice(h,p.lastIndex-d[0].length);if(g[0]==" "&&(g=g.slice(1)),++x,g.length){var w={v:g,t:"s"};Array.isArray(c)?c[u][x]=w:c[ve({r:u,c:x})]=w}break}h=p.lastIndex}x>l.e.c&&(l.e.c=x)}),c["!ref"]=ye(l),c}function r(t,s){return la(e(t,s),s)}function n(t){for(var s=["{\\rtf1\\ansi"],i=He(t["!ref"]),c,f=Array.isArray(t),l=i.s.r;l<=i.e.r;++l){s.push("\\trowd\\trautofit1");for(var o=i.s.c;o<=i.e.c;++o)s.push("\\cellx"+(o+1));for(s.push("\\pard\\intbl"),o=i.s.c;o<=i.e.c;++o){var u=ve({r:l,c:o});c=f?(t[l]||[])[o]:t[u],!(!c||c.v==null&&(!c.f||c.F))&&(s.push(" "+(c.w||(Zr(c),c.w))),s.push("\\cell"))}s.push("\\pard\\intbl\\row")}return s.join("")+"}"}return{to_workbook:r,to_sheet:e,from_sheet:n}}();function Tu(e){var a=e.slice(e[0]==="#"?1:0).slice(0,6);return[parseInt(a.slice(0,2),16),parseInt(a.slice(2,4),16),parseInt(a.slice(4,6),16)]}function ct(e){for(var a=0,r=1;a!=3;++a)r=r*256+(e[a]>255?255:e[a]<0?0:e[a]);return r.toString(16).toUpperCase().slice(1)}function ku(e){var a=e[0]/255,r=e[1]/255,n=e[2]/255,t=Math.max(a,r,n),s=Math.min(a,r,n),i=t-s;if(i===0)return[0,0,a];var c=0,f=0,l=t+s;switch(f=i/(l>1?2-l:l),t){case a:c=((r-n)/i+6)%6;break;case r:c=(n-a)/i+2;break;case n:c=(a-r)/i+4;break}return[c/6,f,l/2]}function wu(e){var a=e[0],r=e[1],n=e[2],t=r*2*(n<.5?n:1-n),s=n-t/2,i=[s,s,s],c=6*a,f;if(r!==0)switch(c|0){case 0:case 6:f=t*c,i[0]+=t,i[1]+=f;break;case 1:f=t*(2-c),i[0]+=f,i[1]+=t;break;case 2:f=t*(c-2),i[1]+=t,i[2]+=f;break;case 3:f=t*(4-c),i[1]+=f,i[2]+=t;break;case 4:f=t*(c-4),i[2]+=t,i[0]+=f;break;case 5:f=t*(6-c),i[2]+=f,i[0]+=t;break}for(var l=0;l!=3;++l)i[l]=Math.round(i[l]*255);return i}function Mt(e,a){if(a===0)return e;var r=ku(Tu(e));return a<0?r[2]=r[2]*(1+a):r[2]=1-(1-r[2])*(1-a),ct(wu(r))}var di=6,Au=15,Fu=1,pr=di;function Ut(e){return Math.floor((e+Math.round(128/pr)/256)*pr)}function Vt(e){return Math.floor((e-5)/pr*100+.5)/100}function h0(e){return Math.round((e*pr+5)/pr*256)/256}function e0(e){return h0(Vt(Ut(e)))}function B0(e){var a=Math.abs(e-e0(e)),r=pr;if(a>.005)for(pr=Fu;pr<Au;++pr)Math.abs(e-e0(e))<=a&&(a=Math.abs(e-e0(e)),r=pr);pr=r}function ba(e){e.width?(e.wpx=Ut(e.width),e.wch=Vt(e.wpx),e.MDW=pr):e.wpx?(e.wch=Vt(e.wpx),e.width=h0(e.wch),e.MDW=pr):typeof e.wch=="number"&&(e.width=h0(e.wch),e.wpx=Ut(e.width),e.MDW=pr),e.customWidth&&delete e.customWidth}var Su=96,pi=Su;function vi(e){return e*96/pi}function ft(e){return e*pi/96}var Cu={None:"none",Solid:"solid",Gray50:"mediumGray",Gray75:"darkGray",Gray25:"lightGray",HorzStripe:"darkHorizontal",VertStripe:"darkVertical",ReverseDiagStripe:"darkDown",DiagStripe:"darkUp",DiagCross:"darkGrid",ThickDiagCross:"darkTrellis",ThinHorzStripe:"lightHorizontal",ThinVertStripe:"lightVertical",ThinReverseDiagStripe:"lightDown",ThinHorzCross:"lightGrid"};function yu(e,a,r,n){a.Borders=[];var t={},s=!1;(e[0].match(ur)||[]).forEach(function(i){var c=pe(i);switch($r(c[0])){case"<borders":case"<borders>":case"</borders>":break;case"<border":case"<border>":case"<border/>":t={},c.diagonalUp&&(t.diagonalUp=be(c.diagonalUp)),c.diagonalDown&&(t.diagonalDown=be(c.diagonalDown)),a.Borders.push(t);break;case"</border>":break;case"<left/>":break;case"<left":case"<left>":break;case"</left>":break;case"<right/>":break;case"<right":case"<right>":break;case"</right>":break;case"<top/>":break;case"<top":case"<top>":break;case"</top>":break;case"<bottom/>":break;case"<bottom":case"<bottom>":break;case"</bottom>":break;case"<diagonal":case"<diagonal>":case"<diagonal/>":break;case"</diagonal>":break;case"<horizontal":case"<horizontal>":case"<horizontal/>":break;case"</horizontal>":break;case"<vertical":case"<vertical>":case"<vertical/>":break;case"</vertical>":break;case"<start":case"<start>":case"<start/>":break;case"</start>":break;case"<end":case"<end>":case"<end/>":break;case"</end>":break;case"<color":case"<color>":break;case"<color/>":case"</color>":break;case"<extLst":case"<extLst>":case"</extLst>":break;case"<ext":s=!0;break;case"</ext>":s=!1;break;default:if(n&&n.WTF&&!s)throw new Error("unrecognized "+c[0]+" in borders")}})}function Du(e,a,r,n){a.Fills=[];var t={},s=!1;(e[0].match(ur)||[]).forEach(function(i){var c=pe(i);switch($r(c[0])){case"<fills":case"<fills>":case"</fills>":break;case"<fill>":case"<fill":case"<fill/>":t={},a.Fills.push(t);break;case"</fill>":break;case"<gradientFill>":break;case"<gradientFill":case"</gradientFill>":a.Fills.push(t),t={};break;case"<patternFill":case"<patternFill>":c.patternType&&(t.patternType=c.patternType);break;case"<patternFill/>":case"</patternFill>":break;case"<bgColor":t.bgColor||(t.bgColor={}),c.indexed&&(t.bgColor.indexed=parseInt(c.indexed,10)),c.theme&&(t.bgColor.theme=parseInt(c.theme,10)),c.tint&&(t.bgColor.tint=parseFloat(c.tint)),c.rgb&&(t.bgColor.rgb=c.rgb.slice(-6));break;case"<bgColor/>":case"</bgColor>":break;case"<fgColor":t.fgColor||(t.fgColor={}),c.theme&&(t.fgColor.theme=parseInt(c.theme,10)),c.tint&&(t.fgColor.tint=parseFloat(c.tint)),c.rgb!=null&&(t.fgColor.rgb=c.rgb.slice(-6));break;case"<fgColor/>":case"</fgColor>":break;case"<stop":case"<stop/>":break;case"</stop>":break;case"<color":case"<color/>":break;case"</color>":break;case"<extLst":case"<extLst>":case"</extLst>":break;case"<ext":s=!0;break;case"</ext>":s=!1;break;default:if(n&&n.WTF&&!s)throw new Error("unrecognized "+c[0]+" in fills")}})}function Ou(e,a,r,n){a.Fonts=[];var t={},s=!1;(e[0].match(ur)||[]).forEach(function(i){var c=pe(i);switch($r(c[0])){case"<fonts":case"<fonts>":case"</fonts>":break;case"<font":case"<font>":break;case"</font>":case"<font/>":a.Fonts.push(t),t={};break;case"<name":c.val&&(t.name=Ne(c.val));break;case"<name/>":case"</name>":break;case"<b":t.bold=c.val?be(c.val):1;break;case"<b/>":t.bold=1;break;case"<i":t.italic=c.val?be(c.val):1;break;case"<i/>":t.italic=1;break;case"<u":switch(c.val){case"none":t.underline=0;break;case"single":t.underline=1;break;case"double":t.underline=2;break;case"singleAccounting":t.underline=33;break;case"doubleAccounting":t.underline=34;break}break;case"<u/>":t.underline=1;break;case"<strike":t.strike=c.val?be(c.val):1;break;case"<strike/>":t.strike=1;break;case"<outline":t.outline=c.val?be(c.val):1;break;case"<outline/>":t.outline=1;break;case"<shadow":t.shadow=c.val?be(c.val):1;break;case"<shadow/>":t.shadow=1;break;case"<condense":t.condense=c.val?be(c.val):1;break;case"<condense/>":t.condense=1;break;case"<extend":t.extend=c.val?be(c.val):1;break;case"<extend/>":t.extend=1;break;case"<sz":c.val&&(t.sz=+c.val);break;case"<sz/>":case"</sz>":break;case"<vertAlign":c.val&&(t.vertAlign=c.val);break;case"<vertAlign/>":case"</vertAlign>":break;case"<family":c.val&&(t.family=parseInt(c.val,10));break;case"<family/>":case"</family>":break;case"<scheme":c.val&&(t.scheme=c.val);break;case"<scheme/>":case"</scheme>":break;case"<charset":if(c.val=="1")break;c.codepage=_0[parseInt(c.val,10)];break;case"<color":if(t.color||(t.color={}),c.auto&&(t.color.auto=be(c.auto)),c.rgb)t.color.rgb=c.rgb.slice(-6);else if(c.indexed){t.color.index=parseInt(c.indexed,10);var f=ma[t.color.index];t.color.index==81&&(f=ma[1]),f||(f=ma[1]),t.color.rgb=f[0].toString(16)+f[1].toString(16)+f[2].toString(16)}else c.theme&&(t.color.theme=parseInt(c.theme,10),c.tint&&(t.color.tint=parseFloat(c.tint)),c.theme&&r.themeElements&&r.themeElements.clrScheme&&(t.color.rgb=Mt(r.themeElements.clrScheme[t.color.theme].rgb,t.color.tint||0)));break;case"<color/>":case"</color>":break;case"<AlternateContent":s=!0;break;case"</AlternateContent>":s=!1;break;case"<extLst":case"<extLst>":case"</extLst>":break;case"<ext":s=!0;break;case"</ext>":s=!1;break;default:if(n&&n.WTF&&!s)throw new Error("unrecognized "+c[0]+" in fonts")}})}function Iu(e,a,r){a.NumberFmt=[];for(var n=Gr(_e),t=0;t<n.length;++t)a.NumberFmt[n[t]]=_e[n[t]];var s=e[0].match(ur);if(s)for(t=0;t<s.length;++t){var i=pe(s[t]);switch($r(i[0])){case"<numFmts":case"</numFmts>":case"<numFmts/>":case"<numFmts>":break;case"<numFmt":{var c=Oe(Ne(i.formatCode)),f=parseInt(i.numFmtId,10);if(a.NumberFmt[f]=c,f>0){if(f>392){for(f=392;f>60&&a.NumberFmt[f]!=null;--f);a.NumberFmt[f]=c}ga(c,f)}}break;case"</numFmt>":break;default:if(r.WTF)throw new Error("unrecognized "+i[0]+" in numFmts")}}}var yt=["numFmtId","fillId","fontId","borderId","xfId"],Dt=["applyAlignment","applyBorder","applyFill","applyFont","applyNumberFormat","applyProtection","pivotButton","quotePrefix"];function Ru(e,a,r){a.CellXf=[];var n,t=!1;(e[0].match(ur)||[]).forEach(function(s){var i=pe(s),c=0;switch($r(i[0])){case"<cellXfs":case"<cellXfs>":case"<cellXfs/>":case"</cellXfs>":break;case"<xf":case"<xf/>":for(n=i,delete n[0],c=0;c<yt.length;++c)n[yt[c]]&&(n[yt[c]]=parseInt(n[yt[c]],10));for(c=0;c<Dt.length;++c)n[Dt[c]]&&(n[Dt[c]]=be(n[Dt[c]]));if(a.NumberFmt&&n.numFmtId>392){for(c=392;c>60;--c)if(a.NumberFmt[n.numFmtId]==a.NumberFmt[c]){n.numFmtId=c;break}}a.CellXf.push(n);break;case"</xf>":break;case"<alignment":case"<alignment/>":var f={};i.vertical&&(f.vertical=i.vertical),i.horizontal&&(f.horizontal=i.horizontal),i.textRotation!=null&&(f.textRotation=i.textRotation),i.indent&&(f.indent=i.indent),i.wrapText&&(f.wrapText=be(i.wrapText)),n.alignment=f;break;case"</alignment>":break;case"<protection":break;case"</protection>":case"<protection/>":break;case"<AlternateContent":t=!0;break;case"</AlternateContent>":t=!1;break;case"<extLst":case"<extLst>":case"</extLst>":break;case"<ext":t=!0;break;case"</ext>":t=!1;break;default:if(r&&r.WTF&&!t)throw new Error("unrecognized "+i[0]+" in cellXfs")}})}var Nu=function(){var a=/<(?:\w+:)?numFmts([^>]*)>[\S\s]*?<\/(?:\w+:)?numFmts>/,r=/<(?:\w+:)?cellXfs([^>]*)>[\S\s]*?<\/(?:\w+:)?cellXfs>/,n=/<(?:\w+:)?fills([^>]*)>[\S\s]*?<\/(?:\w+:)?fills>/,t=/<(?:\w+:)?fonts([^>]*)>[\S\s]*?<\/(?:\w+:)?fonts>/,s=/<(?:\w+:)?borders([^>]*)>[\S\s]*?<\/(?:\w+:)?borders>/;return function(c,f,l){var o={};if(!c)return o;c=c.replace(/<!--([\s\S]*?)-->/mg,"").replace(/<!DOCTYPE[^\[]*\[[^\]]*\]>/gm,"");var u;return(u=c.match(a))&&Iu(u,o,l),(u=c.match(t))&&Ou(u,o,f,l),(u=c.match(n))&&Du(u,o,f,l),(u=c.match(s))&&yu(u,o,f,l),(u=c.match(r))&&Ru(u,o,l),o}}();function Lu(e,a){var r=e.read_shift(2),n=or(e);return[r,n]}function Pu(e,a,r){var n={};n.sz=e.read_shift(2)/20;var t=Xo(e);t.fItalic&&(n.italic=1),t.fCondense&&(n.condense=1),t.fExtend&&(n.extend=1),t.fShadow&&(n.shadow=1),t.fOutline&&(n.outline=1),t.fStrikeout&&(n.strike=1);var s=e.read_shift(2);switch(s===700&&(n.bold=1),e.read_shift(2)){case 1:n.vertAlign="superscript";break;case 2:n.vertAlign="subscript";break}var i=e.read_shift(1);i!=0&&(n.underline=i);var c=e.read_shift(1);c>0&&(n.family=c);var f=e.read_shift(1);switch(f>0&&(n.charset=f),e.l++,n.color=$o(e),e.read_shift(1)){case 1:n.scheme="major";break;case 2:n.scheme="minor";break}return n.name=or(e),n}var bu=lr;function Bu(e,a){var r=e.l+a,n=e.read_shift(2),t=e.read_shift(2);return e.l=r,{ixfe:n,numFmtId:t}}var Mu=lr;function Uu(e,a,r){var n={};n.NumberFmt=[];for(var t in _e)n.NumberFmt[t]=_e[t];n.CellXf=[],n.Fonts=[];var s=[],i=!1;return qr(e,function(f,l,o){switch(o){case 44:n.NumberFmt[f[0]]=f[1],ga(f[1],f[0]);break;case 43:n.Fonts.push(f),f.color.theme!=null&&a&&a.themeElements&&a.themeElements.clrScheme&&(f.color.rgb=Mt(a.themeElements.clrScheme[f.color.theme].rgb,f.color.tint||0));break;case 1025:break;case 45:break;case 46:break;case 47:s[s.length-1]==617&&n.CellXf.push(f);break;case 48:case 507:case 572:case 475:break;case 1171:case 2102:case 1130:case 512:case 2095:case 3072:break;case 35:i=!0;break;case 36:i=!1;break;case 37:s.push(o),i=!0;break;case 38:s.pop(),i=!1;break;default:if(l.T>0)s.push(o);else if(l.T<0)s.pop();else if(!i||r.WTF&&s[s.length-1]!=37)throw new Error("Unexpected record 0x"+o.toString(16))}}),n}var Vu=["</a:lt1>","</a:dk1>","</a:lt2>","</a:dk2>","</a:accent1>","</a:accent2>","</a:accent3>","</a:accent4>","</a:accent5>","</a:accent6>","</a:hlink>","</a:folHlink>"];function Hu(e,a,r){a.themeElements.clrScheme=[];var n={};(e[0].match(ur)||[]).forEach(function(t){var s=pe(t);switch(s[0]){case"<a:clrScheme":case"</a:clrScheme>":break;case"<a:srgbClr":n.rgb=s.val;break;case"<a:sysClr":n.rgb=s.lastClr;break;case"<a:dk1>":case"</a:dk1>":case"<a:lt1>":case"</a:lt1>":case"<a:dk2>":case"</a:dk2>":case"<a:lt2>":case"</a:lt2>":case"<a:accent1>":case"</a:accent1>":case"<a:accent2>":case"</a:accent2>":case"<a:accent3>":case"</a:accent3>":case"<a:accent4>":case"</a:accent4>":case"<a:accent5>":case"</a:accent5>":case"<a:accent6>":case"</a:accent6>":case"<a:hlink>":case"</a:hlink>":case"<a:folHlink>":case"</a:folHlink>":s[0].charAt(1)==="/"?(a.themeElements.clrScheme[Vu.indexOf(s[0])]=n,n={}):n.name=s[0].slice(3,s[0].length-1);break;default:if(r&&r.WTF)throw new Error("Unrecognized "+s[0]+" in clrScheme")}})}function Wu(){}function Gu(){}var $u=/<a:clrScheme([^>]*)>[\s\S]*<\/a:clrScheme>/,Xu=/<a:fontScheme([^>]*)>[\s\S]*<\/a:fontScheme>/,zu=/<a:fmtScheme([^>]*)>[\s\S]*<\/a:fmtScheme>/;function Ku(e,a,r){a.themeElements={};var n;[["clrScheme",$u,Hu],["fontScheme",Xu,Wu],["fmtScheme",zu,Gu]].forEach(function(t){if(!(n=e.match(t[1])))throw new Error(t[0]+" not found in themeElements");t[2](n,a,r)})}var Yu=/<a:themeElements([^>]*)>[\s\S]*<\/a:themeElements>/;function gi(e,a){(!e||e.length===0)&&(e=ju());var r,n={};if(!(r=e.match(Yu)))throw new Error("themeElements not found in theme");return Ku(r[0],n,a),n.raw=e,n}function ju(e,a){var r=[ys];return r[r.length]='<a:theme xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" name="Office Theme">',r[r.length]="<a:themeElements>",r[r.length]='<a:clrScheme name="Office">',r[r.length]='<a:dk1><a:sysClr val="windowText" lastClr="000000"/></a:dk1>',r[r.length]='<a:lt1><a:sysClr val="window" lastClr="FFFFFF"/></a:lt1>',r[r.length]='<a:dk2><a:srgbClr val="1F497D"/></a:dk2>',r[r.length]='<a:lt2><a:srgbClr val="EEECE1"/></a:lt2>',r[r.length]='<a:accent1><a:srgbClr val="4F81BD"/></a:accent1>',r[r.length]='<a:accent2><a:srgbClr val="C0504D"/></a:accent2>',r[r.length]='<a:accent3><a:srgbClr val="9BBB59"/></a:accent3>',r[r.length]='<a:accent4><a:srgbClr val="8064A2"/></a:accent4>',r[r.length]='<a:accent5><a:srgbClr val="4BACC6"/></a:accent5>',r[r.length]='<a:accent6><a:srgbClr val="F79646"/></a:accent6>',r[r.length]='<a:hlink><a:srgbClr val="0000FF"/></a:hlink>',r[r.length]='<a:folHlink><a:srgbClr val="800080"/></a:folHlink>',r[r.length]="</a:clrScheme>",r[r.length]='<a:fontScheme name="Office">',r[r.length]="<a:majorFont>",r[r.length]='<a:latin typeface="Cambria"/>',r[r.length]='<a:ea typeface=""/>',r[r.length]='<a:cs typeface=""/>',r[r.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>',r[r.length]='<a:font script="Hang" typeface="맑은 고딕"/>',r[r.length]='<a:font script="Hans" typeface="宋体"/>',r[r.length]='<a:font script="Hant" typeface="新細明體"/>',r[r.length]='<a:font script="Arab" typeface="Times New Roman"/>',r[r.length]='<a:font script="Hebr" typeface="Times New Roman"/>',r[r.length]='<a:font script="Thai" typeface="Tahoma"/>',r[r.length]='<a:font script="Ethi" typeface="Nyala"/>',r[r.length]='<a:font script="Beng" typeface="Vrinda"/>',r[r.length]='<a:font script="Gujr" typeface="Shruti"/>',r[r.length]='<a:font script="Khmr" typeface="MoolBoran"/>',r[r.length]='<a:font script="Knda" typeface="Tunga"/>',r[r.length]='<a:font script="Guru" typeface="Raavi"/>',r[r.length]='<a:font script="Cans" typeface="Euphemia"/>',r[r.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',r[r.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',r[r.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',r[r.length]='<a:font script="Thaa" typeface="MV Boli"/>',r[r.length]='<a:font script="Deva" typeface="Mangal"/>',r[r.length]='<a:font script="Telu" typeface="Gautami"/>',r[r.length]='<a:font script="Taml" typeface="Latha"/>',r[r.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',r[r.length]='<a:font script="Orya" typeface="Kalinga"/>',r[r.length]='<a:font script="Mlym" typeface="Kartika"/>',r[r.length]='<a:font script="Laoo" typeface="DokChampa"/>',r[r.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',r[r.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',r[r.length]='<a:font script="Viet" typeface="Times New Roman"/>',r[r.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',r[r.length]='<a:font script="Geor" typeface="Sylfaen"/>',r[r.length]="</a:majorFont>",r[r.length]="<a:minorFont>",r[r.length]='<a:latin typeface="Calibri"/>',r[r.length]='<a:ea typeface=""/>',r[r.length]='<a:cs typeface=""/>',r[r.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>',r[r.length]='<a:font script="Hang" typeface="맑은 고딕"/>',r[r.length]='<a:font script="Hans" typeface="宋体"/>',r[r.length]='<a:font script="Hant" typeface="新細明體"/>',r[r.length]='<a:font script="Arab" typeface="Arial"/>',r[r.length]='<a:font script="Hebr" typeface="Arial"/>',r[r.length]='<a:font script="Thai" typeface="Tahoma"/>',r[r.length]='<a:font script="Ethi" typeface="Nyala"/>',r[r.length]='<a:font script="Beng" typeface="Vrinda"/>',r[r.length]='<a:font script="Gujr" typeface="Shruti"/>',r[r.length]='<a:font script="Khmr" typeface="DaunPenh"/>',r[r.length]='<a:font script="Knda" typeface="Tunga"/>',r[r.length]='<a:font script="Guru" typeface="Raavi"/>',r[r.length]='<a:font script="Cans" typeface="Euphemia"/>',r[r.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',r[r.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',r[r.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',r[r.length]='<a:font script="Thaa" typeface="MV Boli"/>',r[r.length]='<a:font script="Deva" typeface="Mangal"/>',r[r.length]='<a:font script="Telu" typeface="Gautami"/>',r[r.length]='<a:font script="Taml" typeface="Latha"/>',r[r.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',r[r.length]='<a:font script="Orya" typeface="Kalinga"/>',r[r.length]='<a:font script="Mlym" typeface="Kartika"/>',r[r.length]='<a:font script="Laoo" typeface="DokChampa"/>',r[r.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',r[r.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',r[r.length]='<a:font script="Viet" typeface="Arial"/>',r[r.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',r[r.length]='<a:font script="Geor" typeface="Sylfaen"/>',r[r.length]="</a:minorFont>",r[r.length]="</a:fontScheme>",r[r.length]='<a:fmtScheme name="Office">',r[r.length]="<a:fillStyleLst>",r[r.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="50000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="35000"><a:schemeClr val="phClr"><a:tint val="37000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="15000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:lin ang="16200000" scaled="1"/>',r[r.length]="</a:gradFill>",r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="100000"/><a:shade val="100000"/><a:satMod val="130000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="50000"/><a:shade val="100000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:lin ang="16200000" scaled="0"/>',r[r.length]="</a:gradFill>",r[r.length]="</a:fillStyleLst>",r[r.length]="<a:lnStyleLst>",r[r.length]='<a:ln w="9525" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"><a:shade val="95000"/><a:satMod val="105000"/></a:schemeClr></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]='<a:ln w="25400" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]='<a:ln w="38100" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]="</a:lnStyleLst>",r[r.length]="<a:effectStyleLst>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="20000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="38000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]="</a:effectStyle>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]="</a:effectStyle>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]='<a:scene3d><a:camera prst="orthographicFront"><a:rot lat="0" lon="0" rev="0"/></a:camera><a:lightRig rig="threePt" dir="t"><a:rot lat="0" lon="0" rev="1200000"/></a:lightRig></a:scene3d>',r[r.length]='<a:sp3d><a:bevelT w="63500" h="25400"/></a:sp3d>',r[r.length]="</a:effectStyle>",r[r.length]="</a:effectStyleLst>",r[r.length]="<a:bgFillStyleLst>",r[r.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="40000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="40000"><a:schemeClr val="phClr"><a:tint val="45000"/><a:shade val="99000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="20000"/><a:satMod val="255000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:path path="circle"><a:fillToRect l="50000" t="-80000" r="50000" b="180000"/></a:path>',r[r.length]="</a:gradFill>",r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="80000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="30000"/><a:satMod val="200000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:path path="circle"><a:fillToRect l="50000" t="50000" r="50000" b="50000"/></a:path>',r[r.length]="</a:gradFill>",r[r.length]="</a:bgFillStyleLst>",r[r.length]="</a:fmtScheme>",r[r.length]="</a:themeElements>",r[r.length]="<a:objectDefaults>",r[r.length]="<a:spDef>",r[r.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="1"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="3"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="2"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="lt1"/></a:fontRef></a:style>',r[r.length]="</a:spDef>",r[r.length]="<a:lnDef>",r[r.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="2"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="0"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="1"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="tx1"/></a:fontRef></a:style>',r[r.length]="</a:lnDef>",r[r.length]="</a:objectDefaults>",r[r.length]="<a:extraClrSchemeLst/>",r[r.length]="</a:theme>",r.join("")}function Ju(e,a,r){var n=e.l+a,t=e.read_shift(4);if(t!==124226){if(!r.cellStyles){e.l=n;return}var s=e.slice(e.l);e.l=n;var i;try{i=Cs(s,{type:"array"})}catch{return}var c=Ar(i,"theme/theme/theme1.xml",!0);if(c)return gi(c,r)}}function Zu(e){return e.read_shift(4)}function qu(e){var a={};switch(a.xclrType=e.read_shift(2),a.nTintShade=e.read_shift(2),a.xclrType){case 0:e.l+=4;break;case 1:a.xclrValue=Qu(e,4);break;case 2:a.xclrValue=ti(e);break;case 3:a.xclrValue=Zu(e);break;case 4:e.l+=4;break}return e.l+=8,a}function Qu(e,a){return lr(e,a)}function eh(e,a){return lr(e,a)}function rh(e){var a=e.read_shift(2),r=e.read_shift(2)-4,n=[a];switch(a){case 4:case 5:case 7:case 8:case 9:case 10:case 11:case 13:n[1]=qu(e);break;case 6:n[1]=eh(e,r);break;case 14:case 15:n[1]=e.read_shift(r===1?1:2);break;default:throw new Error("Unrecognized ExtProp type: "+a+" "+r)}return n}function ah(e,a){var r=e.l+a;e.l+=2;var n=e.read_shift(2);e.l+=2;for(var t=e.read_shift(2),s=[];t-- >0;)s.push(rh(e,r-e.l));return{ixfe:n,ext:s}}function th(e,a){a.forEach(function(r){r[0]})}function nh(e,a){return{flags:e.read_shift(4),version:e.read_shift(4),name:or(e)}}function sh(e){for(var a=[],r=e.read_shift(4);r-- >0;)a.push([e.read_shift(4),e.read_shift(4)]);return a}function ih(e){return e.l+=4,e.read_shift(4)!=0}function ch(e,a,r){var n={Types:[],Cell:[],Value:[]},t=r||{},s=[],i=!1,c=2;return qr(e,function(f,l,o){switch(o){case 335:n.Types.push({name:f.name});break;case 51:f.forEach(function(u){c==1?n.Cell.push({type:n.Types[u[0]-1].name,index:u[1]}):c==0&&n.Value.push({type:n.Types[u[0]-1].name,index:u[1]})});break;case 337:c=f?1:0;break;case 338:c=2;break;case 35:s.push(o),i=!0;break;case 36:s.pop(),i=!1;break;default:if(!l.T){if(!i||t.WTF&&s[s.length-1]!=35)throw new Error("Unexpected record 0x"+o.toString(16))}}}),n}function fh(e,a,r){var n={Types:[],Cell:[],Value:[]};if(!e)return n;var t=!1,s=2,i;return e.replace(ur,function(c){var f=pe(c);switch($r(f[0])){case"<?xml":break;case"<metadata":case"</metadata>":break;case"<metadataTypes":case"</metadataTypes>":break;case"<metadataType":n.Types.push({name:f.name});break;case"</metadataType>":break;case"<futureMetadata":for(var l=0;l<n.Types.length;++l)n.Types[l].name==f.name&&(i=n.Types[l]);break;case"</futureMetadata>":break;case"<bk>":break;case"</bk>":break;case"<rc":s==1?n.Cell.push({type:n.Types[f.t-1].name,index:+f.v}):s==0&&n.Value.push({type:n.Types[f.t-1].name,index:+f.v});break;case"</rc>":break;case"<cellMetadata":s=1;break;case"</cellMetadata>":s=2;break;case"<valueMetadata":s=0;break;case"</valueMetadata>":s=2;break;case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<ext":t=!0;break;case"</ext>":t=!1;break;case"<rvb":if(!i)break;i.offsets||(i.offsets=[]),i.offsets.push(+f.i);break;default:if(!t&&r.WTF)throw new Error("unrecognized "+f[0]+" in metadata")}return c}),n}function oh(e){var a=[];if(!e)return a;var r=1;return(e.match(ur)||[]).forEach(function(n){var t=pe(n);switch(t[0]){case"<?xml":break;case"<calcChain":case"<calcChain>":case"</calcChain>":break;case"<c":delete t[0],t.i?r=t.i:t.i=r,a.push(t);break}}),a}function lh(e){var a={};a.i=e.read_shift(4);var r={};r.r=e.read_shift(4),r.c=e.read_shift(4),a.r=ve(r);var n=e.read_shift(1);return n&2&&(a.l="1"),n&8&&(a.a="1"),a}function uh(e,a,r){var n=[];return qr(e,function(s,i,c){switch(c){case 63:n.push(s);break;default:if(!i.T)throw new Error("Unexpected record 0x"+c.toString(16))}}),n}function hh(e,a,r,n){if(!e)return e;var t=n||{},s=!1;qr(e,function(c,f,l){switch(l){case 359:case 363:case 364:case 366:case 367:case 368:case 369:case 370:case 371:case 472:case 577:case 578:case 579:case 580:case 581:case 582:case 583:case 584:case 585:case 586:case 587:break;case 35:s=!0;break;case 36:s=!1;break;default:if(!f.T){if(!s||t.WTF)throw new Error("Unexpected record 0x"+l.toString(16))}}},t)}function xh(e,a){if(!e)return"??";var r=(e.match(/<c:chart [^>]*r:id="([^"]*)"/)||["",""])[1];return a["!id"][r].Target}function Vn(e,a,r,n){var t=Array.isArray(e),s;a.forEach(function(i){var c=vr(i.ref);if(t?(e[c.r]||(e[c.r]=[]),s=e[c.r][c.c]):s=e[i.ref],!s){s={t:"z"},t?e[c.r][c.c]=s:e[i.ref]=s;var f=He(e["!ref"]||"BDWGO1000001:A1");f.s.r>c.r&&(f.s.r=c.r),f.e.r<c.r&&(f.e.r=c.r),f.s.c>c.c&&(f.s.c=c.c),f.e.c<c.c&&(f.e.c=c.c);var l=ye(f);l!==e["!ref"]&&(e["!ref"]=l)}s.c||(s.c=[]);var o={a:i.author,t:i.t,r:i.r,T:r};i.h&&(o.h=i.h);for(var u=s.c.length-1;u>=0;--u){if(!r&&s.c[u].T)return;r&&!s.c[u].T&&s.c.splice(u,1)}if(r&&n){for(u=0;u<n.length;++u)if(o.a==n[u].id){o.a=n[u].name||o.a;break}}s.c.push(o)})}function dh(e,a){if(e.match(/<(?:\w+:)?comments *\/>/))return[];var r=[],n=[],t=e.match(/<(?:\w+:)?authors>([\s\S]*)<\/(?:\w+:)?authors>/);t&&t[1]&&t[1].split(/<\/\w*:?author>/).forEach(function(i){if(!(i===""||i.trim()==="")){var c=i.match(/<(?:\w+:)?author[^>]*>(.*)/);c&&r.push(c[1])}});var s=e.match(/<(?:\w+:)?commentList>([\s\S]*)<\/(?:\w+:)?commentList>/);return s&&s[1]&&s[1].split(/<\/\w*:?comment>/).forEach(function(i){if(!(i===""||i.trim()==="")){var c=i.match(/<(?:\w+:)?comment[^>]*>/);if(c){var f=pe(c[0]),l={author:f.authorId&&r[f.authorId]||"sheetjsghost",ref:f.ref,guid:f.guid},o=vr(f.ref);if(!(a.sheetRows&&a.sheetRows<=o.r)){var u=i.match(/<(?:\w+:)?text>([\s\S]*)<\/(?:\w+:)?text>/),p=!!u&&!!u[1]&&b0(u[1])||{r:"",t:"",h:""};l.r=p.r,p.r=="<t></t>"&&(p.t=p.h=""),l.t=(p.t||"").replace(/\r\n/g,`
`).replace(/\r/g,`
`),a.cellHTML&&(l.h=p.h),n.push(l)}}}}),n}function ph(e,a){var r=[],n=!1,t={},s=0;return e.replace(ur,function(c,f){var l=pe(c);switch($r(l[0])){case"<?xml":break;case"<ThreadedComments":break;case"</ThreadedComments>":break;case"<threadedComment":t={author:l.personId,guid:l.id,ref:l.ref,T:1};break;case"</threadedComment>":t.t!=null&&r.push(t);break;case"<text>":case"<text":s=f+c.length;break;case"</text>":t.t=e.slice(s,f).replace(/\r\n/g,`
`).replace(/\r/g,`
`);break;case"<mentions":case"<mentions>":n=!0;break;case"</mentions>":n=!1;break;case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<ext":n=!0;break;case"</ext>":n=!1;break;default:if(!n&&a.WTF)throw new Error("unrecognized "+l[0]+" in threaded comments")}return c}),r}function vh(e,a){var r=[],n=!1;return e.replace(ur,function(s){var i=pe(s);switch($r(i[0])){case"<?xml":break;case"<personList":break;case"</personList>":break;case"<person":r.push({name:i.displayname,id:i.id});break;case"</person>":break;case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<ext":n=!0;break;case"</ext>":n=!1;break;default:if(!n&&a.WTF)throw new Error("unrecognized "+i[0]+" in threaded comments")}return s}),r}function gh(e){var a={};a.iauthor=e.read_shift(4);var r=Sa(e);return a.rfx=r.s,a.ref=ve(r.s),e.l+=16,a}var mh=or;function _h(e,a){var r=[],n=[],t={},s=!1;return qr(e,function(c,f,l){switch(l){case 632:n.push(c);break;case 635:t=c;break;case 637:t.t=c.t,t.h=c.h,t.r=c.r;break;case 636:if(t.author=n[t.iauthor],delete t.iauthor,a.sheetRows&&t.rfx&&a.sheetRows<=t.rfx.r)break;t.t||(t.t=""),delete t.rfx,r.push(t);break;case 3072:break;case 35:s=!0;break;case 36:s=!1;break;case 37:break;case 38:break;default:if(!f.T){if(!s||a.WTF)throw new Error("Unexpected record 0x"+l.toString(16))}}}),r}var Eh="application/vnd.ms-office.vbaProject";function Th(e){var a=Ce.utils.cfb_new({root:"R"});return e.FullPaths.forEach(function(r,n){if(!(r.slice(-1)==="/"||!r.match(/_VBA_PROJECT_CUR/))){var t=r.replace(/^[^\/]*/,"R").replace(/\/_VBA_PROJECT_CUR\u0000*/,"");Ce.utils.cfb_add(a,t,e.FileIndex[n].content)}}),Ce.write(a)}function kh(){return{"!type":"dialog"}}function wh(){return{"!type":"dialog"}}function Ah(){return{"!type":"macro"}}function Fh(){return{"!type":"macro"}}var Na=function(){var e=/(^|[^A-Za-z_])R(\[?-?\d+\]|[1-9]\d*|)C(\[?-?\d+\]|[1-9]\d*|)(?![A-Za-z0-9_])/g,a={r:0,c:0};function r(n,t,s,i){var c=!1,f=!1;s.length==0?f=!0:s.charAt(0)=="["&&(f=!0,s=s.slice(1,-1)),i.length==0?c=!0:i.charAt(0)=="["&&(c=!0,i=i.slice(1,-1));var l=s.length>0?parseInt(s,10)|0:0,o=i.length>0?parseInt(i,10)|0:0;return c?o+=a.c:--o,f?l+=a.r:--l,t+(c?"":"$")+Je(o)+(f?"":"$")+tr(l)}return function(t,s){return a=s,t.replace(e,r)}}(),mi=/(^|[^._A-Z0-9])([$]?)([A-Z]{1,2}|[A-W][A-Z]{2}|X[A-E][A-Z]|XF[A-D])([$]?)(10[0-3]\d{4}|104[0-7]\d{3}|1048[0-4]\d{2}|10485[0-6]\d|104857[0-6]|[1-9]\d{0,5})(?![_.\(A-Za-z0-9])/g,Sh=function(){return function(a,r){return a.replace(mi,function(n,t,s,i,c,f){var l=O0(i)-(s?0:r.c),o=D0(f)-(c?0:r.r),u=o==0?"":c?o+1:"["+o+"]",p=l==0?"":s?l+1:"["+l+"]";return t+"R"+u+"C"+p})}}();function _i(e,a){return e.replace(mi,function(r,n,t,s,i,c){return n+(t=="$"?t+s:Je(O0(s)+a.c))+(i=="$"?i+c:tr(D0(c)+a.r))})}function Ch(e,a,r){var n=Ma(a),t=n.s,s=vr(r),i={r:s.r-t.r,c:s.c-t.c};return _i(e,i)}function yh(e){return e.length!=1}function Hn(e){return e.replace(/_xlfn\./g,"")}function $e(e){e.l+=1}function oa(e,a){var r=e.read_shift(2);return[r&16383,r>>14&1,r>>15&1]}function Ei(e,a,r){var n=2;if(r){if(r.biff>=2&&r.biff<=5)return Ti(e);r.biff==12&&(n=4)}var t=e.read_shift(n),s=e.read_shift(n),i=oa(e),c=oa(e);return{s:{r:t,c:i[0],cRel:i[1],rRel:i[2]},e:{r:s,c:c[0],cRel:c[1],rRel:c[2]}}}function Ti(e){var a=oa(e),r=oa(e),n=e.read_shift(1),t=e.read_shift(1);return{s:{r:a[0],c:n,cRel:a[1],rRel:a[2]},e:{r:r[0],c:t,cRel:r[1],rRel:r[2]}}}function Dh(e,a,r){if(r.biff<8)return Ti(e);var n=e.read_shift(r.biff==12?4:2),t=e.read_shift(r.biff==12?4:2),s=oa(e),i=oa(e);return{s:{r:n,c:s[0],cRel:s[1],rRel:s[2]},e:{r:t,c:i[0],cRel:i[1],rRel:i[2]}}}function ki(e,a,r){if(r&&r.biff>=2&&r.biff<=5)return Oh(e);var n=e.read_shift(r&&r.biff==12?4:2),t=oa(e);return{r:n,c:t[0],cRel:t[1],rRel:t[2]}}function Oh(e){var a=oa(e),r=e.read_shift(1);return{r:a[0],c:r,cRel:a[1],rRel:a[2]}}function Ih(e){var a=e.read_shift(2),r=e.read_shift(2);return{r:a,c:r&255,fQuoted:!!(r&16384),cRel:r>>15,rRel:r>>15}}function Rh(e,a,r){var n=r&&r.biff?r.biff:8;if(n>=2&&n<=5)return Nh(e);var t=e.read_shift(n>=12?4:2),s=e.read_shift(2),i=(s&16384)>>14,c=(s&32768)>>15;if(s&=16383,c==1)for(;t>524287;)t-=1048576;if(i==1)for(;s>8191;)s=s-16384;return{r:t,c:s,cRel:i,rRel:c}}function Nh(e){var a=e.read_shift(2),r=e.read_shift(1),n=(a&32768)>>15,t=(a&16384)>>14;return a&=16383,n==1&&a>=8192&&(a=a-16384),t==1&&r>=128&&(r=r-256),{r:a,c:r,cRel:t,rRel:n}}function Lh(e,a,r){var n=(e[e.l++]&96)>>5,t=Ei(e,r.biff>=2&&r.biff<=5?6:8,r);return[n,t]}function Ph(e,a,r){var n=(e[e.l++]&96)>>5,t=e.read_shift(2,"i"),s=8;if(r)switch(r.biff){case 5:e.l+=12,s=6;break;case 12:s=12;break}var i=Ei(e,s,r);return[n,t,i]}function bh(e,a,r){var n=(e[e.l++]&96)>>5;return e.l+=r&&r.biff>8?12:r.biff<8?6:8,[n]}function Bh(e,a,r){var n=(e[e.l++]&96)>>5,t=e.read_shift(2),s=8;if(r)switch(r.biff){case 5:e.l+=12,s=6;break;case 12:s=12;break}return e.l+=s,[n,t]}function Mh(e,a,r){var n=(e[e.l++]&96)>>5,t=Dh(e,a-1,r);return[n,t]}function Uh(e,a,r){var n=(e[e.l++]&96)>>5;return e.l+=r.biff==2?6:r.biff==12?14:7,[n]}function Wn(e){var a=e[e.l+1]&1,r=1;return e.l+=4,[a,r]}function Vh(e,a,r){e.l+=2;for(var n=e.read_shift(r&&r.biff==2?1:2),t=[],s=0;s<=n;++s)t.push(e.read_shift(r&&r.biff==2?1:2));return t}function Hh(e,a,r){var n=e[e.l+1]&255?1:0;return e.l+=2,[n,e.read_shift(r&&r.biff==2?1:2)]}function Wh(e,a,r){var n=e[e.l+1]&255?1:0;return e.l+=2,[n,e.read_shift(r&&r.biff==2?1:2)]}function Gh(e){var a=e[e.l+1]&255?1:0;return e.l+=2,[a,e.read_shift(2)]}function $h(e,a,r){var n=e[e.l+1]&255?1:0;return e.l+=r&&r.biff==2?3:4,[n]}function wi(e){var a=e.read_shift(1),r=e.read_shift(1);return[a,r]}function Xh(e){return e.read_shift(2),wi(e)}function zh(e){return e.read_shift(2),wi(e)}function Kh(e,a,r){var n=(e[e.l]&96)>>5;e.l+=1;var t=ki(e,0,r);return[n,t]}function Yh(e,a,r){var n=(e[e.l]&96)>>5;e.l+=1;var t=Rh(e,0,r);return[n,t]}function jh(e,a,r){var n=(e[e.l]&96)>>5;e.l+=1;var t=e.read_shift(2);r&&r.biff==5&&(e.l+=12);var s=ki(e,0,r);return[n,t,s]}function Jh(e,a,r){var n=(e[e.l]&96)>>5;e.l+=1;var t=e.read_shift(r&&r.biff<=3?1:2);return[Zx[t],Si[t],n]}function Zh(e,a,r){var n=e[e.l++],t=e.read_shift(1),s=r&&r.biff<=3?[n==88?-1:0,e.read_shift(1)]:qh(e);return[t,(s[0]===0?Si:Jx)[s[1]]]}function qh(e){return[e[e.l+1]>>7,e.read_shift(2)&32767]}function Qh(e,a,r){e.l+=r&&r.biff==2?3:4}function ex(e,a,r){if(e.l++,r&&r.biff==12)return[e.read_shift(4,"i"),0];var n=e.read_shift(2),t=e.read_shift(r&&r.biff==2?1:2);return[n,t]}function rx(e){return e.l++,Ca[e.read_shift(1)]}function ax(e){return e.l++,e.read_shift(2)}function tx(e){return e.l++,e.read_shift(1)!==0}function nx(e){return e.l++,cr(e)}function sx(e,a,r){return e.l++,vt(e,a-1,r)}function ix(e,a){var r=[e.read_shift(1)];if(a==12)switch(r[0]){case 2:r[0]=4;break;case 4:r[0]=16;break;case 0:r[0]=1;break;case 1:r[0]=2;break}switch(r[0]){case 4:r[1]=Ge(e,1)?"TRUE":"FALSE",a!=12&&(e.l+=7);break;case 37:case 16:r[1]=Ca[e[e.l]],e.l+=a==12?4:8;break;case 0:e.l+=8;break;case 1:r[1]=cr(e);break;case 2:r[1]=ya(e,0,{biff:a>0&&a<8?2:a});break;default:throw new Error("Bad SerAr: "+r[0])}return r}function cx(e,a,r){for(var n=e.read_shift(r.biff==12?4:2),t=[],s=0;s!=n;++s)t.push((r.biff==12?Sa:Xt)(e));return t}function fx(e,a,r){var n=0,t=0;r.biff==12?(n=e.read_shift(4),t=e.read_shift(4)):(t=1+e.read_shift(1),n=1+e.read_shift(2)),r.biff>=2&&r.biff<8&&(--n,--t==0&&(t=256));for(var s=0,i=[];s!=n&&(i[s]=[]);++s)for(var c=0;c!=t;++c)i[s][c]=ix(e,r.biff);return i}function ox(e,a,r){var n=e.read_shift(1)>>>5&3,t=!r||r.biff>=8?4:2,s=e.read_shift(t);switch(r.biff){case 2:e.l+=5;break;case 3:case 4:e.l+=8;break;case 5:e.l+=12;break}return[n,0,s]}function lx(e,a,r){if(r.biff==5)return ux(e);var n=e.read_shift(1)>>>5&3,t=e.read_shift(2),s=e.read_shift(4);return[n,t,s]}function ux(e){var a=e.read_shift(1)>>>5&3,r=e.read_shift(2,"i");e.l+=8;var n=e.read_shift(2);return e.l+=12,[a,r,n]}function hx(e,a,r){var n=e.read_shift(1)>>>5&3;e.l+=r&&r.biff==2?3:4;var t=e.read_shift(r&&r.biff==2?1:2);return[n,t]}function xx(e,a,r){var n=e.read_shift(1)>>>5&3,t=e.read_shift(r&&r.biff==2?1:2);return[n,t]}function dx(e,a,r){var n=e.read_shift(1)>>>5&3;return e.l+=4,r.biff<8&&e.l--,r.biff==12&&(e.l+=2),[n]}function px(e,a,r){var n=(e[e.l++]&96)>>5,t=e.read_shift(2),s=4;if(r)switch(r.biff){case 5:s=15;break;case 12:s=6;break}return e.l+=s,[n,t]}var vx=lr,gx=lr,mx=lr;function mt(e,a,r){return e.l+=2,[Ih(e)]}function M0(e){return e.l+=6,[]}var _x=mt,Ex=M0,Tx=M0,kx=mt;function Ai(e){return e.l+=2,[ze(e),e.read_shift(2)&1]}var wx=mt,Ax=Ai,Fx=M0,Sx=mt,Cx=mt,yx=["Data","All","Headers","??","?Data2","??","?DataHeaders","??","Totals","??","??","??","?DataTotals","??","??","??","?Current"];function Dx(e){e.l+=2;var a=e.read_shift(2),r=e.read_shift(2),n=e.read_shift(4),t=e.read_shift(2),s=e.read_shift(2),i=yx[r>>2&31];return{ixti:a,coltype:r&3,rt:i,idx:n,c:t,C:s}}function Ox(e){return e.l+=2,[e.read_shift(4)]}function Ix(e,a,r){return e.l+=5,e.l+=2,e.l+=r.biff==2?1:4,["PTGSHEET"]}function Rx(e,a,r){return e.l+=r.biff==2?4:5,["PTGENDSHEET"]}function Nx(e){var a=e.read_shift(1)>>>5&3,r=e.read_shift(2);return[a,r]}function Lx(e){var a=e.read_shift(1)>>>5&3,r=e.read_shift(2);return[a,r]}function Px(e){return e.l+=4,[0,0]}var Gn={1:{n:"PtgExp",f:ex},2:{n:"PtgTbl",f:mx},3:{n:"PtgAdd",f:$e},4:{n:"PtgSub",f:$e},5:{n:"PtgMul",f:$e},6:{n:"PtgDiv",f:$e},7:{n:"PtgPower",f:$e},8:{n:"PtgConcat",f:$e},9:{n:"PtgLt",f:$e},10:{n:"PtgLe",f:$e},11:{n:"PtgEq",f:$e},12:{n:"PtgGe",f:$e},13:{n:"PtgGt",f:$e},14:{n:"PtgNe",f:$e},15:{n:"PtgIsect",f:$e},16:{n:"PtgUnion",f:$e},17:{n:"PtgRange",f:$e},18:{n:"PtgUplus",f:$e},19:{n:"PtgUminus",f:$e},20:{n:"PtgPercent",f:$e},21:{n:"PtgParen",f:$e},22:{n:"PtgMissArg",f:$e},23:{n:"PtgStr",f:sx},26:{n:"PtgSheet",f:Ix},27:{n:"PtgEndSheet",f:Rx},28:{n:"PtgErr",f:rx},29:{n:"PtgBool",f:tx},30:{n:"PtgInt",f:ax},31:{n:"PtgNum",f:nx},32:{n:"PtgArray",f:Uh},33:{n:"PtgFunc",f:Jh},34:{n:"PtgFuncVar",f:Zh},35:{n:"PtgName",f:ox},36:{n:"PtgRef",f:Kh},37:{n:"PtgArea",f:Lh},38:{n:"PtgMemArea",f:hx},39:{n:"PtgMemErr",f:vx},40:{n:"PtgMemNoMem",f:gx},41:{n:"PtgMemFunc",f:xx},42:{n:"PtgRefErr",f:dx},43:{n:"PtgAreaErr",f:bh},44:{n:"PtgRefN",f:Yh},45:{n:"PtgAreaN",f:Mh},46:{n:"PtgMemAreaN",f:Nx},47:{n:"PtgMemNoMemN",f:Lx},57:{n:"PtgNameX",f:lx},58:{n:"PtgRef3d",f:jh},59:{n:"PtgArea3d",f:Ph},60:{n:"PtgRefErr3d",f:px},61:{n:"PtgAreaErr3d",f:Bh},255:{}},bx={64:32,96:32,65:33,97:33,66:34,98:34,67:35,99:35,68:36,100:36,69:37,101:37,70:38,102:38,71:39,103:39,72:40,104:40,73:41,105:41,74:42,106:42,75:43,107:43,76:44,108:44,77:45,109:45,78:46,110:46,79:47,111:47,88:34,120:34,89:57,121:57,90:58,122:58,91:59,123:59,92:60,124:60,93:61,125:61},Bx={1:{n:"PtgElfLel",f:Ai},2:{n:"PtgElfRw",f:Sx},3:{n:"PtgElfCol",f:_x},6:{n:"PtgElfRwV",f:Cx},7:{n:"PtgElfColV",f:kx},10:{n:"PtgElfRadical",f:wx},11:{n:"PtgElfRadicalS",f:Fx},13:{n:"PtgElfColS",f:Ex},15:{n:"PtgElfColSV",f:Tx},16:{n:"PtgElfRadicalLel",f:Ax},25:{n:"PtgList",f:Dx},29:{n:"PtgSxName",f:Ox},255:{}},Mx={0:{n:"PtgAttrNoop",f:Px},1:{n:"PtgAttrSemi",f:$h},2:{n:"PtgAttrIf",f:Wh},4:{n:"PtgAttrChoose",f:Vh},8:{n:"PtgAttrGoto",f:Hh},16:{n:"PtgAttrSum",f:Qh},32:{n:"PtgAttrBaxcel",f:Wn},33:{n:"PtgAttrBaxcel",f:Wn},64:{n:"PtgAttrSpace",f:Xh},65:{n:"PtgAttrSpaceSemi",f:zh},128:{n:"PtgAttrIfError",f:Gh},255:{}};function _t(e,a,r,n){if(n.biff<8)return lr(e,a);for(var t=e.l+a,s=[],i=0;i!==r.length;++i)switch(r[i][0]){case"PtgArray":r[i][1]=fx(e,0,n),s.push(r[i][1]);break;case"PtgMemArea":r[i][2]=cx(e,r[i][1],n),s.push(r[i][2]);break;case"PtgExp":n&&n.biff==12&&(r[i][1][1]=e.read_shift(4),s.push(r[i][1]));break;case"PtgList":case"PtgElfRadicalS":case"PtgElfColS":case"PtgElfColSV":throw"Unsupported "+r[i][0]}return a=t-e.l,a!==0&&s.push(lr(e,a)),s}function Et(e,a,r){for(var n=e.l+a,t,s,i=[];n!=e.l;)a=n-e.l,s=e[e.l],t=Gn[s]||Gn[bx[s]],(s===24||s===25)&&(t=(s===24?Bx:Mx)[e[e.l+1]]),!t||!t.f?lr(e,a):i.push([t.n,t.f(e,a,r)]);return i}function Ux(e){for(var a=[],r=0;r<e.length;++r){for(var n=e[r],t=[],s=0;s<n.length;++s){var i=n[s];if(i)switch(i[0]){case 2:t.push('"'+i[1].replace(/"/g,'""')+'"');break;default:t.push(i[1])}else t.push("")}a.push(t.join(","))}return a.join(";")}var Vx={PtgAdd:"+",PtgConcat:"&",PtgDiv:"/",PtgEq:"=",PtgGe:">=",PtgGt:">",PtgLe:"<=",PtgLt:"<",PtgMul:"*",PtgNe:"<>",PtgPower:"^",PtgSub:"-"};function Hx(e,a){if(!e&&!(a&&a.biff<=5&&a.biff>=2))throw new Error("empty sheet name");return/[^\w\u4E00-\u9FFF\u3040-\u30FF]/.test(e)?"'"+e+"'":e}function Fi(e,a,r){if(!e)return"SH33TJSERR0";if(r.biff>8&&(!e.XTI||!e.XTI[a]))return e.SheetNames[a];if(!e.XTI)return"SH33TJSERR6";var n=e.XTI[a];if(r.biff<8)return a>1e4&&(a-=65536),a<0&&(a=-a),a==0?"":e.XTI[a-1];if(!n)return"SH33TJSERR1";var t="";if(r.biff>8)switch(e[n[0]][0]){case 357:return t=n[1]==-1?"#REF":e.SheetNames[n[1]],n[1]==n[2]?t:t+":"+e.SheetNames[n[2]];case 358:return r.SID!=null?e.SheetNames[r.SID]:"SH33TJSSAME"+e[n[0]][0];case 355:default:return"SH33TJSSRC"+e[n[0]][0]}switch(e[n[0]][0][0]){case 1025:return t=n[1]==-1?"#REF":e.SheetNames[n[1]]||"SH33TJSERR3",n[1]==n[2]?t:t+":"+e.SheetNames[n[2]];case 14849:return e[n[0]].slice(1).map(function(s){return s.Name}).join(";;");default:return e[n[0]][0][3]?(t=n[1]==-1?"#REF":e[n[0]][0][3][n[1]]||"SH33TJSERR4",n[1]==n[2]?t:t+":"+e[n[0]][0][3][n[2]]):"SH33TJSERR2"}}function $n(e,a,r){var n=Fi(e,a,r);return n=="#REF"?n:Hx(n,r)}function ir(e,a,r,n,t){var s=t&&t.biff||8,i={s:{c:0,r:0}},c=[],f,l,o,u=0,p=0,h,d="";if(!e[0]||!e[0][0])return"";for(var x=-1,g="",w=0,y=e[0].length;w<y;++w){var m=e[0][w];switch(m[0]){case"PtgUminus":c.push("-"+c.pop());break;case"PtgUplus":c.push("+"+c.pop());break;case"PtgPercent":c.push(c.pop()+"%");break;case"PtgAdd":case"PtgConcat":case"PtgDiv":case"PtgEq":case"PtgGe":case"PtgGt":case"PtgLe":case"PtgLt":case"PtgMul":case"PtgNe":case"PtgPower":case"PtgSub":if(f=c.pop(),l=c.pop(),x>=0){switch(e[0][x][1][0]){case 0:g=Ve(" ",e[0][x][1][1]);break;case 1:g=Ve("\r",e[0][x][1][1]);break;default:if(g="",t.WTF)throw new Error("Unexpected PtgAttrSpaceType "+e[0][x][1][0])}l=l+g,x=-1}c.push(l+Vx[m[0]]+f);break;case"PtgIsect":f=c.pop(),l=c.pop(),c.push(l+" "+f);break;case"PtgUnion":f=c.pop(),l=c.pop(),c.push(l+","+f);break;case"PtgRange":f=c.pop(),l=c.pop(),c.push(l+":"+f);break;case"PtgAttrChoose":break;case"PtgAttrGoto":break;case"PtgAttrIf":break;case"PtgAttrIfError":break;case"PtgRef":o=ja(m[1][1],i,t),c.push(Ja(o,s));break;case"PtgRefN":o=r?ja(m[1][1],r,t):m[1][1],c.push(Ja(o,s));break;case"PtgRef3d":u=m[1][1],o=ja(m[1][2],i,t),d=$n(n,u,t),c.push(d+"!"+Ja(o,s));break;case"PtgFunc":case"PtgFuncVar":var O=m[1][0],P=m[1][1];O||(O=0),O&=127;var D=O==0?[]:c.slice(-O);c.length-=O,P==="User"&&(P=D.shift()),c.push(P+"("+D.join(",")+")");break;case"PtgBool":c.push(m[1]?"TRUE":"FALSE");break;case"PtgInt":c.push(m[1]);break;case"PtgNum":c.push(String(m[1]));break;case"PtgStr":c.push('"'+m[1].replace(/"/g,'""')+'"');break;case"PtgErr":c.push(m[1]);break;case"PtgAreaN":h=An(m[1][1],r?{s:r}:i,t),c.push(qt(h,t));break;case"PtgArea":h=An(m[1][1],i,t),c.push(qt(h,t));break;case"PtgArea3d":u=m[1][1],h=m[1][2],d=$n(n,u,t),c.push(d+"!"+qt(h,t));break;case"PtgAttrSum":c.push("SUM("+c.pop()+")");break;case"PtgAttrBaxcel":case"PtgAttrSemi":break;case"PtgName":p=m[1][2];var S=(n.names||[])[p-1]||(n[0]||[])[p],U=S?S.Name:"SH33TJSNAME"+String(p);U&&U.slice(0,6)=="_xlfn."&&!t.xlfn&&(U=U.slice(6)),c.push(U);break;case"PtgNameX":var R=m[1][1];p=m[1][2];var z;if(t.biff<=5)R<0&&(R=-R),n[R]&&(z=n[R][p]);else{var G="";if(((n[R]||[])[0]||[])[0]==14849||(((n[R]||[])[0]||[])[0]==1025?n[R][p]&&n[R][p].itab>0&&(G=n.SheetNames[n[R][p].itab-1]+"!"):G=n.SheetNames[p-1]+"!"),n[R]&&n[R][p])G+=n[R][p].Name;else if(n[0]&&n[0][p])G+=n[0][p].Name;else{var b=(Fi(n,R,t)||"").split(";;");b[p-1]?G=b[p-1]:G+="SH33TJSERRX"}c.push(G);break}z||(z={Name:"SH33TJSERRY"}),c.push(z.Name);break;case"PtgParen":var j="(",Y=")";if(x>=0){switch(g="",e[0][x][1][0]){case 2:j=Ve(" ",e[0][x][1][1])+j;break;case 3:j=Ve("\r",e[0][x][1][1])+j;break;case 4:Y=Ve(" ",e[0][x][1][1])+Y;break;case 5:Y=Ve("\r",e[0][x][1][1])+Y;break;default:if(t.WTF)throw new Error("Unexpected PtgAttrSpaceType "+e[0][x][1][0])}x=-1}c.push(j+c.pop()+Y);break;case"PtgRefErr":c.push("#REF!");break;case"PtgRefErr3d":c.push("#REF!");break;case"PtgExp":o={c:m[1][1],r:m[1][0]};var Q={c:r.c,r:r.r};if(n.sharedf[ve(o)]){var se=n.sharedf[ve(o)];c.push(ir(se,i,Q,n,t))}else{var ne=!1;for(f=0;f!=n.arrayf.length;++f)if(l=n.arrayf[f],!(o.c<l[0].s.c||o.c>l[0].e.c)&&!(o.r<l[0].s.r||o.r>l[0].e.r)){c.push(ir(l[1],i,Q,n,t)),ne=!0;break}ne||c.push(m[1])}break;case"PtgArray":c.push("{"+Ux(m[1])+"}");break;case"PtgMemArea":break;case"PtgAttrSpace":case"PtgAttrSpaceSemi":x=w;break;case"PtgTbl":break;case"PtgMemErr":break;case"PtgMissArg":c.push("");break;case"PtgAreaErr":c.push("#REF!");break;case"PtgAreaErr3d":c.push("#REF!");break;case"PtgList":c.push("Table"+m[1].idx+"[#"+m[1].rt+"]");break;case"PtgMemAreaN":case"PtgMemNoMemN":case"PtgAttrNoop":case"PtgSheet":case"PtgEndSheet":break;case"PtgMemFunc":break;case"PtgMemNoMem":break;case"PtgElfCol":case"PtgElfColS":case"PtgElfColSV":case"PtgElfColV":case"PtgElfLel":case"PtgElfRadical":case"PtgElfRadicalLel":case"PtgElfRadicalS":case"PtgElfRw":case"PtgElfRwV":throw new Error("Unsupported ELFs");case"PtgSxName":throw new Error("Unrecognized Formula Token: "+String(m));default:throw new Error("Unrecognized Formula Token: "+String(m))}var De=["PtgAttrSpace","PtgAttrSpaceSemi","PtgAttrGoto"];if(t.biff!=3&&x>=0&&De.indexOf(e[0][w][0])==-1){m=e[0][x];var L=!0;switch(m[1][0]){case 4:L=!1;case 0:g=Ve(" ",m[1][1]);break;case 5:L=!1;case 1:g=Ve("\r",m[1][1]);break;default:if(g="",t.WTF)throw new Error("Unexpected PtgAttrSpaceType "+m[1][0])}c.push((L?g:"")+c.pop()+(L?"":g)),x=-1}}if(c.length>1&&t.WTF)throw new Error("bad formula stack");return c[0]}function Wx(e,a,r){var n=e.l+a,t=r.biff==2?1:2,s,i=e.read_shift(t);if(i==65535)return[[],lr(e,a-2)];var c=Et(e,i,r);return a!==i+t&&(s=_t(e,a-i-t,c,r)),e.l=n,[c,s]}function Gx(e,a,r){var n=e.l+a,t=r.biff==2?1:2,s,i=e.read_shift(t);if(i==65535)return[[],lr(e,a-2)];var c=Et(e,i,r);return a!==i+t&&(s=_t(e,a-i-t,c,r)),e.l=n,[c,s]}function $x(e,a,r,n){var t=e.l+a,s=Et(e,n,r),i;return t!==e.l&&(i=_t(e,t-e.l,s,r)),[s,i]}function Xx(e,a,r){var n=e.l+a,t,s=e.read_shift(2),i=Et(e,s,r);return s==65535?[[],lr(e,a-2)]:(a!==s+2&&(t=_t(e,n-s-2,i,r)),[i,t])}function zx(e){var a;if(Kr(e,e.l+6)!==65535)return[cr(e),"n"];switch(e[e.l]){case 0:return e.l+=8,["String","s"];case 1:return a=e[e.l+2]===1,e.l+=8,[a,"b"];case 2:return a=e[e.l+2],e.l+=8,[a,"e"];case 3:return e.l+=8,["","s"]}return[]}function r0(e,a,r){var n=e.l+a,t=Xr(e);r.biff==2&&++e.l;var s=zx(e),i=e.read_shift(1);r.biff!=2&&(e.read_shift(1),r.biff>=5&&e.read_shift(4));var c=Gx(e,n-e.l,r);return{cell:t,val:s[0],formula:c,shared:i>>3&1,tt:s[1]}}function zt(e,a,r){var n=e.read_shift(4),t=Et(e,n,r),s=e.read_shift(4),i=s>0?_t(e,s,t,r):null;return[t,i]}var Kx=zt,Kt=zt,Yx=zt,jx=zt,Jx={0:"BEEP",1:"OPEN",2:"OPEN.LINKS",3:"CLOSE.ALL",4:"SAVE",5:"SAVE.AS",6:"FILE.DELETE",7:"PAGE.SETUP",8:"PRINT",9:"PRINTER.SETUP",10:"QUIT",11:"NEW.WINDOW",12:"ARRANGE.ALL",13:"WINDOW.SIZE",14:"WINDOW.MOVE",15:"FULL",16:"CLOSE",17:"RUN",22:"SET.PRINT.AREA",23:"SET.PRINT.TITLES",24:"SET.PAGE.BREAK",25:"REMOVE.PAGE.BREAK",26:"FONT",27:"DISPLAY",28:"PROTECT.DOCUMENT",29:"PRECISION",30:"A1.R1C1",31:"CALCULATE.NOW",32:"CALCULATION",34:"DATA.FIND",35:"EXTRACT",36:"DATA.DELETE",37:"SET.DATABASE",38:"SET.CRITERIA",39:"SORT",40:"DATA.SERIES",41:"TABLE",42:"FORMAT.NUMBER",43:"ALIGNMENT",44:"STYLE",45:"BORDER",46:"CELL.PROTECTION",47:"COLUMN.WIDTH",48:"UNDO",49:"CUT",50:"COPY",51:"PASTE",52:"CLEAR",53:"PASTE.SPECIAL",54:"EDIT.DELETE",55:"INSERT",56:"FILL.RIGHT",57:"FILL.DOWN",61:"DEFINE.NAME",62:"CREATE.NAMES",63:"FORMULA.GOTO",64:"FORMULA.FIND",65:"SELECT.LAST.CELL",66:"SHOW.ACTIVE.CELL",67:"GALLERY.AREA",68:"GALLERY.BAR",69:"GALLERY.COLUMN",70:"GALLERY.LINE",71:"GALLERY.PIE",72:"GALLERY.SCATTER",73:"COMBINATION",74:"PREFERRED",75:"ADD.OVERLAY",76:"GRIDLINES",77:"SET.PREFERRED",78:"AXES",79:"LEGEND",80:"ATTACH.TEXT",81:"ADD.ARROW",82:"SELECT.CHART",83:"SELECT.PLOT.AREA",84:"PATTERNS",85:"MAIN.CHART",86:"OVERLAY",87:"SCALE",88:"FORMAT.LEGEND",89:"FORMAT.TEXT",90:"EDIT.REPEAT",91:"PARSE",92:"JUSTIFY",93:"HIDE",94:"UNHIDE",95:"WORKSPACE",96:"FORMULA",97:"FORMULA.FILL",98:"FORMULA.ARRAY",99:"DATA.FIND.NEXT",100:"DATA.FIND.PREV",101:"FORMULA.FIND.NEXT",102:"FORMULA.FIND.PREV",103:"ACTIVATE",104:"ACTIVATE.NEXT",105:"ACTIVATE.PREV",106:"UNLOCKED.NEXT",107:"UNLOCKED.PREV",108:"COPY.PICTURE",109:"SELECT",110:"DELETE.NAME",111:"DELETE.FORMAT",112:"VLINE",113:"HLINE",114:"VPAGE",115:"HPAGE",116:"VSCROLL",117:"HSCROLL",118:"ALERT",119:"NEW",120:"CANCEL.COPY",121:"SHOW.CLIPBOARD",122:"MESSAGE",124:"PASTE.LINK",125:"APP.ACTIVATE",126:"DELETE.ARROW",127:"ROW.HEIGHT",128:"FORMAT.MOVE",129:"FORMAT.SIZE",130:"FORMULA.REPLACE",131:"SEND.KEYS",132:"SELECT.SPECIAL",133:"APPLY.NAMES",134:"REPLACE.FONT",135:"FREEZE.PANES",136:"SHOW.INFO",137:"SPLIT",138:"ON.WINDOW",139:"ON.DATA",140:"DISABLE.INPUT",142:"OUTLINE",143:"LIST.NAMES",144:"FILE.CLOSE",145:"SAVE.WORKBOOK",146:"DATA.FORM",147:"COPY.CHART",148:"ON.TIME",149:"WAIT",150:"FORMAT.FONT",151:"FILL.UP",152:"FILL.LEFT",153:"DELETE.OVERLAY",155:"SHORT.MENUS",159:"SET.UPDATE.STATUS",161:"COLOR.PALETTE",162:"DELETE.STYLE",163:"WINDOW.RESTORE",164:"WINDOW.MAXIMIZE",166:"CHANGE.LINK",167:"CALCULATE.DOCUMENT",168:"ON.KEY",169:"APP.RESTORE",170:"APP.MOVE",171:"APP.SIZE",172:"APP.MINIMIZE",173:"APP.MAXIMIZE",174:"BRING.TO.FRONT",175:"SEND.TO.BACK",185:"MAIN.CHART.TYPE",186:"OVERLAY.CHART.TYPE",187:"SELECT.END",188:"OPEN.MAIL",189:"SEND.MAIL",190:"STANDARD.FONT",191:"CONSOLIDATE",192:"SORT.SPECIAL",193:"GALLERY.3D.AREA",194:"GALLERY.3D.COLUMN",195:"GALLERY.3D.LINE",196:"GALLERY.3D.PIE",197:"VIEW.3D",198:"GOAL.SEEK",199:"WORKGROUP",200:"FILL.GROUP",201:"UPDATE.LINK",202:"PROMOTE",203:"DEMOTE",204:"SHOW.DETAIL",206:"UNGROUP",207:"OBJECT.PROPERTIES",208:"SAVE.NEW.OBJECT",209:"SHARE",210:"SHARE.NAME",211:"DUPLICATE",212:"APPLY.STYLE",213:"ASSIGN.TO.OBJECT",214:"OBJECT.PROTECTION",215:"HIDE.OBJECT",216:"SET.EXTRACT",217:"CREATE.PUBLISHER",218:"SUBSCRIBE.TO",219:"ATTRIBUTES",220:"SHOW.TOOLBAR",222:"PRINT.PREVIEW",223:"EDIT.COLOR",224:"SHOW.LEVELS",225:"FORMAT.MAIN",226:"FORMAT.OVERLAY",227:"ON.RECALC",228:"EDIT.SERIES",229:"DEFINE.STYLE",240:"LINE.PRINT",243:"ENTER.DATA",249:"GALLERY.RADAR",250:"MERGE.STYLES",251:"EDITION.OPTIONS",252:"PASTE.PICTURE",253:"PASTE.PICTURE.LINK",254:"SPELLING",256:"ZOOM",259:"INSERT.OBJECT",260:"WINDOW.MINIMIZE",265:"SOUND.NOTE",266:"SOUND.PLAY",267:"FORMAT.SHAPE",268:"EXTEND.POLYGON",269:"FORMAT.AUTO",272:"GALLERY.3D.BAR",273:"GALLERY.3D.SURFACE",274:"FILL.AUTO",276:"CUSTOMIZE.TOOLBAR",277:"ADD.TOOL",278:"EDIT.OBJECT",279:"ON.DOUBLECLICK",280:"ON.ENTRY",281:"WORKBOOK.ADD",282:"WORKBOOK.MOVE",283:"WORKBOOK.COPY",284:"WORKBOOK.OPTIONS",285:"SAVE.WORKSPACE",288:"CHART.WIZARD",289:"DELETE.TOOL",290:"MOVE.TOOL",291:"WORKBOOK.SELECT",292:"WORKBOOK.ACTIVATE",293:"ASSIGN.TO.TOOL",295:"COPY.TOOL",296:"RESET.TOOL",297:"CONSTRAIN.NUMERIC",298:"PASTE.TOOL",302:"WORKBOOK.NEW",305:"SCENARIO.CELLS",306:"SCENARIO.DELETE",307:"SCENARIO.ADD",308:"SCENARIO.EDIT",309:"SCENARIO.SHOW",310:"SCENARIO.SHOW.NEXT",311:"SCENARIO.SUMMARY",312:"PIVOT.TABLE.WIZARD",313:"PIVOT.FIELD.PROPERTIES",314:"PIVOT.FIELD",315:"PIVOT.ITEM",316:"PIVOT.ADD.FIELDS",318:"OPTIONS.CALCULATION",319:"OPTIONS.EDIT",320:"OPTIONS.VIEW",321:"ADDIN.MANAGER",322:"MENU.EDITOR",323:"ATTACH.TOOLBARS",324:"VBAActivate",325:"OPTIONS.CHART",328:"VBA.INSERT.FILE",330:"VBA.PROCEDURE.DEFINITION",336:"ROUTING.SLIP",338:"ROUTE.DOCUMENT",339:"MAIL.LOGON",342:"INSERT.PICTURE",343:"EDIT.TOOL",344:"GALLERY.DOUGHNUT",350:"CHART.TREND",352:"PIVOT.ITEM.PROPERTIES",354:"WORKBOOK.INSERT",355:"OPTIONS.TRANSITION",356:"OPTIONS.GENERAL",370:"FILTER.ADVANCED",373:"MAIL.ADD.MAILER",374:"MAIL.DELETE.MAILER",375:"MAIL.REPLY",376:"MAIL.REPLY.ALL",377:"MAIL.FORWARD",378:"MAIL.NEXT.LETTER",379:"DATA.LABEL",380:"INSERT.TITLE",381:"FONT.PROPERTIES",382:"MACRO.OPTIONS",383:"WORKBOOK.HIDE",384:"WORKBOOK.UNHIDE",385:"WORKBOOK.DELETE",386:"WORKBOOK.NAME",388:"GALLERY.CUSTOM",390:"ADD.CHART.AUTOFORMAT",391:"DELETE.CHART.AUTOFORMAT",392:"CHART.ADD.DATA",393:"AUTO.OUTLINE",394:"TAB.ORDER",395:"SHOW.DIALOG",396:"SELECT.ALL",397:"UNGROUP.SHEETS",398:"SUBTOTAL.CREATE",399:"SUBTOTAL.REMOVE",400:"RENAME.OBJECT",412:"WORKBOOK.SCROLL",413:"WORKBOOK.NEXT",414:"WORKBOOK.PREV",415:"WORKBOOK.TAB.SPLIT",416:"FULL.SCREEN",417:"WORKBOOK.PROTECT",420:"SCROLLBAR.PROPERTIES",421:"PIVOT.SHOW.PAGES",422:"TEXT.TO.COLUMNS",423:"FORMAT.CHARTTYPE",424:"LINK.FORMAT",425:"TRACER.DISPLAY",430:"TRACER.NAVIGATE",431:"TRACER.CLEAR",432:"TRACER.ERROR",433:"PIVOT.FIELD.GROUP",434:"PIVOT.FIELD.UNGROUP",435:"CHECKBOX.PROPERTIES",436:"LABEL.PROPERTIES",437:"LISTBOX.PROPERTIES",438:"EDITBOX.PROPERTIES",439:"PIVOT.REFRESH",440:"LINK.COMBO",441:"OPEN.TEXT",442:"HIDE.DIALOG",443:"SET.DIALOG.FOCUS",444:"ENABLE.OBJECT",445:"PUSHBUTTON.PROPERTIES",446:"SET.DIALOG.DEFAULT",447:"FILTER",448:"FILTER.SHOW.ALL",449:"CLEAR.OUTLINE",450:"FUNCTION.WIZARD",451:"ADD.LIST.ITEM",452:"SET.LIST.ITEM",453:"REMOVE.LIST.ITEM",454:"SELECT.LIST.ITEM",455:"SET.CONTROL.VALUE",456:"SAVE.COPY.AS",458:"OPTIONS.LISTS.ADD",459:"OPTIONS.LISTS.DELETE",460:"SERIES.AXES",461:"SERIES.X",462:"SERIES.Y",463:"ERRORBAR.X",464:"ERRORBAR.Y",465:"FORMAT.CHART",466:"SERIES.ORDER",467:"MAIL.LOGOFF",468:"CLEAR.ROUTING.SLIP",469:"APP.ACTIVATE.MICROSOFT",470:"MAIL.EDIT.MAILER",471:"ON.SHEET",472:"STANDARD.WIDTH",473:"SCENARIO.MERGE",474:"SUMMARY.INFO",475:"FIND.FILE",476:"ACTIVE.CELL.FONT",477:"ENABLE.TIPWIZARD",478:"VBA.MAKE.ADDIN",480:"INSERTDATATABLE",481:"WORKGROUP.OPTIONS",482:"MAIL.SEND.MAILER",485:"AUTOCORRECT",489:"POST.DOCUMENT",491:"PICKLIST",493:"VIEW.SHOW",494:"VIEW.DEFINE",495:"VIEW.DELETE",509:"SHEET.BACKGROUND",510:"INSERT.MAP.OBJECT",511:"OPTIONS.MENONO",517:"MSOCHECKS",518:"NORMAL",519:"LAYOUT",520:"RM.PRINT.AREA",521:"CLEAR.PRINT.AREA",522:"ADD.PRINT.AREA",523:"MOVE.BRK",545:"HIDECURR.NOTE",546:"HIDEALL.NOTES",547:"DELETE.NOTE",548:"TRAVERSE.NOTES",549:"ACTIVATE.NOTES",620:"PROTECT.REVISIONS",621:"UNPROTECT.REVISIONS",647:"OPTIONS.ME",653:"WEB.PUBLISH",667:"NEWWEBQUERY",673:"PIVOT.TABLE.CHART",753:"OPTIONS.SAVE",755:"OPTIONS.SPELL",808:"HIDEALL.INKANNOTS"},Si={0:"COUNT",1:"IF",2:"ISNA",3:"ISERROR",4:"SUM",5:"AVERAGE",6:"MIN",7:"MAX",8:"ROW",9:"COLUMN",10:"NA",11:"NPV",12:"STDEV",13:"DOLLAR",14:"FIXED",15:"SIN",16:"COS",17:"TAN",18:"ATAN",19:"PI",20:"SQRT",21:"EXP",22:"LN",23:"LOG10",24:"ABS",25:"INT",26:"SIGN",27:"ROUND",28:"LOOKUP",29:"INDEX",30:"REPT",31:"MID",32:"LEN",33:"VALUE",34:"TRUE",35:"FALSE",36:"AND",37:"OR",38:"NOT",39:"MOD",40:"DCOUNT",41:"DSUM",42:"DAVERAGE",43:"DMIN",44:"DMAX",45:"DSTDEV",46:"VAR",47:"DVAR",48:"TEXT",49:"LINEST",50:"TREND",51:"LOGEST",52:"GROWTH",53:"GOTO",54:"HALT",55:"RETURN",56:"PV",57:"FV",58:"NPER",59:"PMT",60:"RATE",61:"MIRR",62:"IRR",63:"RAND",64:"MATCH",65:"DATE",66:"TIME",67:"DAY",68:"MONTH",69:"YEAR",70:"WEEKDAY",71:"HOUR",72:"MINUTE",73:"SECOND",74:"NOW",75:"AREAS",76:"ROWS",77:"COLUMNS",78:"OFFSET",79:"ABSREF",80:"RELREF",81:"ARGUMENT",82:"SEARCH",83:"TRANSPOSE",84:"ERROR",85:"STEP",86:"TYPE",87:"ECHO",88:"SET.NAME",89:"CALLER",90:"DEREF",91:"WINDOWS",92:"SERIES",93:"DOCUMENTS",94:"ACTIVE.CELL",95:"SELECTION",96:"RESULT",97:"ATAN2",98:"ASIN",99:"ACOS",100:"CHOOSE",101:"HLOOKUP",102:"VLOOKUP",103:"LINKS",104:"INPUT",105:"ISREF",106:"GET.FORMULA",107:"GET.NAME",108:"SET.VALUE",109:"LOG",110:"EXEC",111:"CHAR",112:"LOWER",113:"UPPER",114:"PROPER",115:"LEFT",116:"RIGHT",117:"EXACT",118:"TRIM",119:"REPLACE",120:"SUBSTITUTE",121:"CODE",122:"NAMES",123:"DIRECTORY",124:"FIND",125:"CELL",126:"ISERR",127:"ISTEXT",128:"ISNUMBER",129:"ISBLANK",130:"T",131:"N",132:"FOPEN",133:"FCLOSE",134:"FSIZE",135:"FREADLN",136:"FREAD",137:"FWRITELN",138:"FWRITE",139:"FPOS",140:"DATEVALUE",141:"TIMEVALUE",142:"SLN",143:"SYD",144:"DDB",145:"GET.DEF",146:"REFTEXT",147:"TEXTREF",148:"INDIRECT",149:"REGISTER",150:"CALL",151:"ADD.BAR",152:"ADD.MENU",153:"ADD.COMMAND",154:"ENABLE.COMMAND",155:"CHECK.COMMAND",156:"RENAME.COMMAND",157:"SHOW.BAR",158:"DELETE.MENU",159:"DELETE.COMMAND",160:"GET.CHART.ITEM",161:"DIALOG.BOX",162:"CLEAN",163:"MDETERM",164:"MINVERSE",165:"MMULT",166:"FILES",167:"IPMT",168:"PPMT",169:"COUNTA",170:"CANCEL.KEY",171:"FOR",172:"WHILE",173:"BREAK",174:"NEXT",175:"INITIATE",176:"REQUEST",177:"POKE",178:"EXECUTE",179:"TERMINATE",180:"RESTART",181:"HELP",182:"GET.BAR",183:"PRODUCT",184:"FACT",185:"GET.CELL",186:"GET.WORKSPACE",187:"GET.WINDOW",188:"GET.DOCUMENT",189:"DPRODUCT",190:"ISNONTEXT",191:"GET.NOTE",192:"NOTE",193:"STDEVP",194:"VARP",195:"DSTDEVP",196:"DVARP",197:"TRUNC",198:"ISLOGICAL",199:"DCOUNTA",200:"DELETE.BAR",201:"UNREGISTER",204:"USDOLLAR",205:"FINDB",206:"SEARCHB",207:"REPLACEB",208:"LEFTB",209:"RIGHTB",210:"MIDB",211:"LENB",212:"ROUNDUP",213:"ROUNDDOWN",214:"ASC",215:"DBCS",216:"RANK",219:"ADDRESS",220:"DAYS360",221:"TODAY",222:"VDB",223:"ELSE",224:"ELSE.IF",225:"END.IF",226:"FOR.CELL",227:"MEDIAN",228:"SUMPRODUCT",229:"SINH",230:"COSH",231:"TANH",232:"ASINH",233:"ACOSH",234:"ATANH",235:"DGET",236:"CREATE.OBJECT",237:"VOLATILE",238:"LAST.ERROR",239:"CUSTOM.UNDO",240:"CUSTOM.REPEAT",241:"FORMULA.CONVERT",242:"GET.LINK.INFO",243:"TEXT.BOX",244:"INFO",245:"GROUP",246:"GET.OBJECT",247:"DB",248:"PAUSE",251:"RESUME",252:"FREQUENCY",253:"ADD.TOOLBAR",254:"DELETE.TOOLBAR",255:"User",256:"RESET.TOOLBAR",257:"EVALUATE",258:"GET.TOOLBAR",259:"GET.TOOL",260:"SPELLING.CHECK",261:"ERROR.TYPE",262:"APP.TITLE",263:"WINDOW.TITLE",264:"SAVE.TOOLBAR",265:"ENABLE.TOOL",266:"PRESS.TOOL",267:"REGISTER.ID",268:"GET.WORKBOOK",269:"AVEDEV",270:"BETADIST",271:"GAMMALN",272:"BETAINV",273:"BINOMDIST",274:"CHIDIST",275:"CHIINV",276:"COMBIN",277:"CONFIDENCE",278:"CRITBINOM",279:"EVEN",280:"EXPONDIST",281:"FDIST",282:"FINV",283:"FISHER",284:"FISHERINV",285:"FLOOR",286:"GAMMADIST",287:"GAMMAINV",288:"CEILING",289:"HYPGEOMDIST",290:"LOGNORMDIST",291:"LOGINV",292:"NEGBINOMDIST",293:"NORMDIST",294:"NORMSDIST",295:"NORMINV",296:"NORMSINV",297:"STANDARDIZE",298:"ODD",299:"PERMUT",300:"POISSON",301:"TDIST",302:"WEIBULL",303:"SUMXMY2",304:"SUMX2MY2",305:"SUMX2PY2",306:"CHITEST",307:"CORREL",308:"COVAR",309:"FORECAST",310:"FTEST",311:"INTERCEPT",312:"PEARSON",313:"RSQ",314:"STEYX",315:"SLOPE",316:"TTEST",317:"PROB",318:"DEVSQ",319:"GEOMEAN",320:"HARMEAN",321:"SUMSQ",322:"KURT",323:"SKEW",324:"ZTEST",325:"LARGE",326:"SMALL",327:"QUARTILE",328:"PERCENTILE",329:"PERCENTRANK",330:"MODE",331:"TRIMMEAN",332:"TINV",334:"MOVIE.COMMAND",335:"GET.MOVIE",336:"CONCATENATE",337:"POWER",338:"PIVOT.ADD.DATA",339:"GET.PIVOT.TABLE",340:"GET.PIVOT.FIELD",341:"GET.PIVOT.ITEM",342:"RADIANS",343:"DEGREES",344:"SUBTOTAL",345:"SUMIF",346:"COUNTIF",347:"COUNTBLANK",348:"SCENARIO.GET",349:"OPTIONS.LISTS.GET",350:"ISPMT",351:"DATEDIF",352:"DATESTRING",353:"NUMBERSTRING",354:"ROMAN",355:"OPEN.DIALOG",356:"SAVE.DIALOG",357:"VIEW.GET",358:"GETPIVOTDATA",359:"HYPERLINK",360:"PHONETIC",361:"AVERAGEA",362:"MAXA",363:"MINA",364:"STDEVPA",365:"VARPA",366:"STDEVA",367:"VARA",368:"BAHTTEXT",369:"THAIDAYOFWEEK",370:"THAIDIGIT",371:"THAIMONTHOFYEAR",372:"THAINUMSOUND",373:"THAINUMSTRING",374:"THAISTRINGLENGTH",375:"ISTHAIDIGIT",376:"ROUNDBAHTDOWN",377:"ROUNDBAHTUP",378:"THAIYEAR",379:"RTD",380:"CUBEVALUE",381:"CUBEMEMBER",382:"CUBEMEMBERPROPERTY",383:"CUBERANKEDMEMBER",384:"HEX2BIN",385:"HEX2DEC",386:"HEX2OCT",387:"DEC2BIN",388:"DEC2HEX",389:"DEC2OCT",390:"OCT2BIN",391:"OCT2HEX",392:"OCT2DEC",393:"BIN2DEC",394:"BIN2OCT",395:"BIN2HEX",396:"IMSUB",397:"IMDIV",398:"IMPOWER",399:"IMABS",400:"IMSQRT",401:"IMLN",402:"IMLOG2",403:"IMLOG10",404:"IMSIN",405:"IMCOS",406:"IMEXP",407:"IMARGUMENT",408:"IMCONJUGATE",409:"IMAGINARY",410:"IMREAL",411:"COMPLEX",412:"IMSUM",413:"IMPRODUCT",414:"SERIESSUM",415:"FACTDOUBLE",416:"SQRTPI",417:"QUOTIENT",418:"DELTA",419:"GESTEP",420:"ISEVEN",421:"ISODD",422:"MROUND",423:"ERF",424:"ERFC",425:"BESSELJ",426:"BESSELK",427:"BESSELY",428:"BESSELI",429:"XIRR",430:"XNPV",431:"PRICEMAT",432:"YIELDMAT",433:"INTRATE",434:"RECEIVED",435:"DISC",436:"PRICEDISC",437:"YIELDDISC",438:"TBILLEQ",439:"TBILLPRICE",440:"TBILLYIELD",441:"PRICE",442:"YIELD",443:"DOLLARDE",444:"DOLLARFR",445:"NOMINAL",446:"EFFECT",447:"CUMPRINC",448:"CUMIPMT",449:"EDATE",450:"EOMONTH",451:"YEARFRAC",452:"COUPDAYBS",453:"COUPDAYS",454:"COUPDAYSNC",455:"COUPNCD",456:"COUPNUM",457:"COUPPCD",458:"DURATION",459:"MDURATION",460:"ODDLPRICE",461:"ODDLYIELD",462:"ODDFPRICE",463:"ODDFYIELD",464:"RANDBETWEEN",465:"WEEKNUM",466:"AMORDEGRC",467:"AMORLINC",468:"CONVERT",724:"SHEETJS",469:"ACCRINT",470:"ACCRINTM",471:"WORKDAY",472:"NETWORKDAYS",473:"GCD",474:"MULTINOMIAL",475:"LCM",476:"FVSCHEDULE",477:"CUBEKPIMEMBER",478:"CUBESET",479:"CUBESETCOUNT",480:"IFERROR",481:"COUNTIFS",482:"SUMIFS",483:"AVERAGEIF",484:"AVERAGEIFS"},Zx={2:1,3:1,10:0,15:1,16:1,17:1,18:1,19:0,20:1,21:1,22:1,23:1,24:1,25:1,26:1,27:2,30:2,31:3,32:1,33:1,34:0,35:0,38:1,39:2,40:3,41:3,42:3,43:3,44:3,45:3,47:3,48:2,53:1,61:3,63:0,65:3,66:3,67:1,68:1,69:1,70:1,71:1,72:1,73:1,74:0,75:1,76:1,77:1,79:2,80:2,83:1,85:0,86:1,89:0,90:1,94:0,95:0,97:2,98:1,99:1,101:3,102:3,105:1,106:1,108:2,111:1,112:1,113:1,114:1,117:2,118:1,119:4,121:1,126:1,127:1,128:1,129:1,130:1,131:1,133:1,134:1,135:1,136:2,137:2,138:2,140:1,141:1,142:3,143:4,144:4,161:1,162:1,163:1,164:1,165:2,172:1,175:2,176:2,177:3,178:2,179:1,184:1,186:1,189:3,190:1,195:3,196:3,197:1,198:1,199:3,201:1,207:4,210:3,211:1,212:2,213:2,214:1,215:1,225:0,229:1,230:1,231:1,232:1,233:1,234:1,235:3,244:1,247:4,252:2,257:1,261:1,271:1,273:4,274:2,275:2,276:2,277:3,278:3,279:1,280:3,281:3,282:3,283:1,284:1,285:2,286:4,287:3,288:2,289:4,290:3,291:3,292:3,293:4,294:1,295:3,296:1,297:3,298:1,299:2,300:3,301:3,302:4,303:2,304:2,305:2,306:2,307:2,308:2,309:3,310:2,311:2,312:2,313:2,314:2,315:2,316:4,325:2,326:2,327:2,328:2,331:2,332:2,337:2,342:1,343:1,346:2,347:1,350:4,351:3,352:1,353:2,360:1,368:1,369:1,370:1,371:1,372:1,373:1,374:1,375:1,376:1,377:1,378:1,382:3,385:1,392:1,393:1,396:2,397:2,398:2,399:1,400:1,401:1,402:1,403:1,404:1,405:1,406:1,407:1,408:1,409:1,410:1,414:4,415:1,416:1,417:2,420:1,421:1,422:2,424:1,425:2,426:2,427:2,428:2,430:3,438:3,439:3,440:3,443:2,444:2,445:2,446:2,447:6,448:6,449:2,450:2,464:2,468:3,476:2,479:1,480:2,65535:0};function Xn(e){return e.slice(0,3)=="of:"&&(e=e.slice(3)),e.charCodeAt(0)==61&&(e=e.slice(1),e.charCodeAt(0)==61&&(e=e.slice(1))),e=e.replace(/COM\.MICROSOFT\./g,""),e=e.replace(/\[((?:\.[A-Z]+[0-9]+)(?::\.[A-Z]+[0-9]+)?)\]/g,function(a,r){return r.replace(/\./g,"")}),e=e.replace(/\[.(#[A-Z]*[?!])\]/g,"$1"),e.replace(/[;~]/g,",").replace(/\|/g,";")}function a0(e){var a=e.split(":"),r=a[0].split(".")[0];return[r,a[0].split(".")[1]+(a.length>1?":"+(a[1].split(".")[1]||a[1].split(".")[0]):"")]}var et={},La={};function rt(e,a){if(e){var r=[.7,.7,.75,.75,.3,.3];a=="xlml"&&(r=[1,1,1,1,.5,.5]),e.left==null&&(e.left=r[0]),e.right==null&&(e.right=r[1]),e.top==null&&(e.top=r[2]),e.bottom==null&&(e.bottom=r[3]),e.header==null&&(e.header=r[4]),e.footer==null&&(e.footer=r[5])}}function Ci(e,a,r,n,t,s){try{n.cellNF&&(e.z=_e[a])}catch(c){if(n.WTF)throw c}if(!(e.t==="z"&&!n.cellStyles)){if(e.t==="d"&&typeof e.v=="string"&&(e.v=er(e.v)),(!n||n.cellText!==!1)&&e.t!=="z")try{if(_e[a]==null&&ga(Zf[a]||"General",a),e.t==="e")e.w=e.w||Ca[e.v];else if(a===0)if(e.t==="n")(e.v|0)===e.v?e.w=e.v.toString(10):e.w=tt(e.v);else if(e.t==="d"){var i=mr(e.v);(i|0)===i?e.w=i.toString(10):e.w=tt(i)}else{if(e.v===void 0)return"";e.w=Ea(e.v,La)}else e.t==="d"?e.w=Ir(a,mr(e.v),La):e.w=Ir(a,e.v,La)}catch(c){if(n.WTF)throw c}if(n.cellStyles&&r!=null)try{e.s=s.Fills[r],e.s.fgColor&&e.s.fgColor.theme&&!e.s.fgColor.rgb&&(e.s.fgColor.rgb=Mt(t.themeElements.clrScheme[e.s.fgColor.theme].rgb,e.s.fgColor.tint||0),n.WTF&&(e.s.fgColor.raw_rgb=t.themeElements.clrScheme[e.s.fgColor.theme].rgb)),e.s.bgColor&&e.s.bgColor.theme&&(e.s.bgColor.rgb=Mt(t.themeElements.clrScheme[e.s.bgColor.theme].rgb,e.s.bgColor.tint||0),n.WTF&&(e.s.bgColor.raw_rgb=t.themeElements.clrScheme[e.s.bgColor.theme].rgb))}catch(c){if(n.WTF&&s.Fills)throw c}}}function qx(e,a){var r=He(a);r.s.r<=r.e.r&&r.s.c<=r.e.c&&r.s.r>=0&&r.s.c>=0&&(e["!ref"]=ye(r))}var Qx=/<(?:\w:)?mergeCell ref="[A-Z0-9:]+"\s*[\/]?>/g,ed=/<(?:\w+:)?sheetData[^>]*>([\s\S]*)<\/(?:\w+:)?sheetData>/,rd=/<(?:\w:)?hyperlink [^>]*>/mg,ad=/"(\w*:\w*)"/,td=/<(?:\w:)?col\b[^>]*[\/]?>/g,nd=/<(?:\w:)?autoFilter[^>]*([\/]|>([\s\S]*)<\/(?:\w:)?autoFilter)>/g,sd=/<(?:\w:)?pageMargins[^>]*\/>/g,yi=/<(?:\w:)?sheetPr\b(?:[^>a-z][^>]*)?\/>/,id=/<(?:\w:)?sheetPr[^>]*(?:[\/]|>([\s\S]*)<\/(?:\w:)?sheetPr)>/,cd=/<(?:\w:)?sheetViews[^>]*(?:[\/]|>([\s\S]*)<\/(?:\w:)?sheetViews)>/;function fd(e,a,r,n,t,s,i){if(!e)return e;n||(n={"!id":{}});var c=a.dense?[]:{},f={s:{r:2e6,c:2e6},e:{r:0,c:0}},l="",o="",u=e.match(ed);u?(l=e.slice(0,u.index),o=e.slice(u.index+u[0].length)):l=o=e;var p=l.match(yi);p?U0(p[0],c,t,r):(p=l.match(id))&&od(p[0],p[1]||"",c,t,r);var h=(l.match(/<(?:\w*:)?dimension/)||{index:-1}).index;if(h>0){var d=l.slice(h,h+50).match(ad);d&&qx(c,d[1])}var x=l.match(cd);x&&x[1]&&pd(x[1],t);var g=[];if(a.cellStyles){var w=l.match(td);w&&hd(g,w)}u&&vd(u[1],c,a,f,s,i);var y=o.match(nd);y&&(c["!autofilter"]=xd(y[0]));var m=[],O=o.match(Qx);if(O)for(h=0;h!=O.length;++h)m[h]=He(O[h].slice(O[h].indexOf('"')+1));var P=o.match(rd);P&&ld(c,P,n);var D=o.match(sd);if(D&&(c["!margins"]=ud(pe(D[0]))),!c["!ref"]&&f.e.c>=f.s.c&&f.e.r>=f.s.r&&(c["!ref"]=ye(f)),a.sheetRows>0&&c["!ref"]){var S=He(c["!ref"]);a.sheetRows<=+S.e.r&&(S.e.r=a.sheetRows-1,S.e.r>f.e.r&&(S.e.r=f.e.r),S.e.r<S.s.r&&(S.s.r=S.e.r),S.e.c>f.e.c&&(S.e.c=f.e.c),S.e.c<S.s.c&&(S.s.c=S.e.c),c["!fullref"]=c["!ref"],c["!ref"]=ye(S))}return g.length>0&&(c["!cols"]=g),m.length>0&&(c["!merges"]=m),c}function U0(e,a,r,n){var t=pe(e);r.Sheets[n]||(r.Sheets[n]={}),t.codeName&&(r.Sheets[n].CodeName=Oe(Ne(t.codeName)))}function od(e,a,r,n,t){U0(e.slice(0,e.indexOf(">")),r,n,t)}function ld(e,a,r){for(var n=Array.isArray(e),t=0;t!=a.length;++t){var s=pe(Ne(a[t]),!0);if(!s.ref)return;var i=((r||{})["!id"]||[])[s.id];i?(s.Target=i.Target,s.location&&(s.Target+="#"+Oe(s.location))):(s.Target="#"+Oe(s.location),i={Target:s.Target,TargetMode:"Internal"}),s.Rel=i,s.tooltip&&(s.Tooltip=s.tooltip,delete s.tooltip);for(var c=He(s.ref),f=c.s.r;f<=c.e.r;++f)for(var l=c.s.c;l<=c.e.c;++l){var o=ve({c:l,r:f});n?(e[f]||(e[f]=[]),e[f][l]||(e[f][l]={t:"z",v:void 0}),e[f][l].l=s):(e[o]||(e[o]={t:"z",v:void 0}),e[o].l=s)}}}function ud(e){var a={};return["left","right","top","bottom","header","footer"].forEach(function(r){e[r]&&(a[r]=parseFloat(e[r]))}),a}function hd(e,a){for(var r=!1,n=0;n!=a.length;++n){var t=pe(a[n],!0);t.hidden&&(t.hidden=be(t.hidden));var s=parseInt(t.min,10)-1,i=parseInt(t.max,10)-1;for(t.outlineLevel&&(t.level=+t.outlineLevel||0),delete t.min,delete t.max,t.width=+t.width,!r&&t.width&&(r=!0,B0(t.width)),ba(t);s<=i;)e[s++]=ar(t)}}function xd(e){var a={ref:(e.match(/ref="([^"]*)"/)||[])[1]};return a}var dd=/<(?:\w:)?sheetView(?:[^>a-z][^>]*)?\/?>/;function pd(e,a){a.Views||(a.Views=[{}]),(e.match(dd)||[]).forEach(function(r,n){var t=pe(r);a.Views[n]||(a.Views[n]={}),+t.zoomScale&&(a.Views[n].zoom=+t.zoomScale),be(t.rightToLeft)&&(a.Views[n].RTL=!0)})}var vd=function(){var e=/<(?:\w+:)?c[ \/>]/,a=/<\/(?:\w+:)?row>/,r=/r=["']([^"']*)["']/,n=/<(?:\w+:)?is>([\S\s]*?)<\/(?:\w+:)?is>/,t=/ref=["']([^"']*)["']/,s=nt("v"),i=nt("f");return function(f,l,o,u,p,h){for(var d=0,x="",g=[],w=[],y=0,m=0,O=0,P="",D,S,U=0,R=0,z,G,b=0,j=0,Y=Array.isArray(h.CellXf),Q,se=[],ne=[],De=Array.isArray(l),L=[],X={},ue=!1,A=!!o.sheetStubs,B=f.split(a),N=0,I=B.length;N!=I;++N){x=B[N].trim();var J=x.length;if(J!==0){var Z=0;e:for(d=0;d<J;++d)switch(x[d]){case">":if(x[d-1]!="/"){++d;break e}if(o&&o.cellStyles){if(S=pe(x.slice(Z,d),!0),U=S.r!=null?parseInt(S.r,10):U+1,R=-1,o.sheetRows&&o.sheetRows<U)continue;X={},ue=!1,S.ht&&(ue=!0,X.hpt=parseFloat(S.ht),X.hpx=ft(X.hpt)),S.hidden=="1"&&(ue=!0,X.hidden=!0),S.outlineLevel!=null&&(ue=!0,X.level=+S.outlineLevel),ue&&(L[U-1]=X)}break;case"<":Z=d;break}if(Z>=d)break;if(S=pe(x.slice(Z,d),!0),U=S.r!=null?parseInt(S.r,10):U+1,R=-1,!(o.sheetRows&&o.sheetRows<U)){u.s.r>U-1&&(u.s.r=U-1),u.e.r<U-1&&(u.e.r=U-1),o&&o.cellStyles&&(X={},ue=!1,S.ht&&(ue=!0,X.hpt=parseFloat(S.ht),X.hpx=ft(X.hpt)),S.hidden=="1"&&(ue=!0,X.hidden=!0),S.outlineLevel!=null&&(ue=!0,X.level=+S.outlineLevel),ue&&(L[U-1]=X)),g=x.slice(d).split(e);for(var ie=0;ie!=g.length&&g[ie].trim().charAt(0)=="<";++ie);for(g=g.slice(ie),d=0;d!=g.length;++d)if(x=g[d].trim(),x.length!==0){if(w=x.match(r),y=d,m=0,O=0,x="<c "+(x.slice(0,1)=="<"?">":"")+x,w!=null&&w.length===2){for(y=0,P=w[1],m=0;m!=P.length&&!((O=P.charCodeAt(m)-64)<1||O>26);++m)y=26*y+O;--y,R=y}else++R;for(m=0;m!=x.length&&x.charCodeAt(m)!==62;++m);if(++m,S=pe(x.slice(0,m),!0),S.r||(S.r=ve({r:U-1,c:R})),P=x.slice(m),D={t:""},(w=P.match(s))!=null&&w[1]!==""&&(D.v=Oe(w[1])),o.cellFormula){if((w=P.match(i))!=null&&w[1]!==""){if(D.f=Oe(Ne(w[1])).replace(/\r\n/g,`
`),o.xlfn||(D.f=Hn(D.f)),w[0].indexOf('t="array"')>-1)D.F=(P.match(t)||[])[1],D.F.indexOf(":")>-1&&se.push([He(D.F),D.F]);else if(w[0].indexOf('t="shared"')>-1){G=pe(w[0]);var te=Oe(Ne(w[1]));o.xlfn||(te=Hn(te)),ne[parseInt(G.si,10)]=[G,te,S.r]}}else(w=P.match(/<f[^>]*\/>/))&&(G=pe(w[0]),ne[G.si]&&(D.f=Ch(ne[G.si][1],ne[G.si][2],S.r)));var ee=vr(S.r);for(m=0;m<se.length;++m)ee.r>=se[m][0].s.r&&ee.r<=se[m][0].e.r&&ee.c>=se[m][0].s.c&&ee.c<=se[m][0].e.c&&(D.F=se[m][1])}if(S.t==null&&D.v===void 0)if(D.f||D.F)D.v=0,D.t="n";else if(A)D.t="z";else continue;else D.t=S.t||"n";switch(u.s.c>R&&(u.s.c=R),u.e.c<R&&(u.e.c=R),D.t){case"n":if(D.v==""||D.v==null){if(!A)continue;D.t="z"}else D.v=parseFloat(D.v);break;case"s":if(typeof D.v>"u"){if(!A)continue;D.t="z"}else z=et[parseInt(D.v,10)],D.v=z.t,D.r=z.r,o.cellHTML&&(D.h=z.h);break;case"str":D.t="s",D.v=D.v!=null?Ne(D.v):"",o.cellHTML&&(D.h=S0(D.v));break;case"inlineStr":w=P.match(n),D.t="s",w!=null&&(z=b0(w[1]))?(D.v=z.t,o.cellHTML&&(D.h=z.h)):D.v="";break;case"b":D.v=be(D.v);break;case"d":o.cellDates?D.v=er(D.v,1):(D.v=mr(er(D.v,1)),D.t="n");break;case"e":(!o||o.cellText!==!1)&&(D.w=D.v),D.v=js[D.v];break}if(b=j=0,Q=null,Y&&S.s!==void 0&&(Q=h.CellXf[S.s],Q!=null&&(Q.numFmtId!=null&&(b=Q.numFmtId),o.cellStyles&&Q.fillId!=null&&(j=Q.fillId))),Ci(D,b,j,o,p,h),o.cellDates&&Y&&D.t=="n"&&Ba(_e[b])&&(D.t="d",D.v=$t(D.v)),S.cm&&o.xlmeta){var ke=(o.xlmeta.Cell||[])[+S.cm-1];ke&&ke.type=="XLDAPR"&&(D.D=!0)}if(De){var C=vr(S.r);l[C.r]||(l[C.r]=[]),l[C.r][C.c]=D}else l[S.r]=D}}}}L.length>0&&(l["!rows"]=L)}}();function gd(e,a){var r={},n=e.l+a;r.r=e.read_shift(4),e.l+=4;var t=e.read_shift(2);e.l+=1;var s=e.read_shift(1);return e.l=n,s&7&&(r.level=s&7),s&16&&(r.hidden=!0),s&32&&(r.hpt=t/20),r}var md=Sa;function _d(){}function Ed(e,a){var r={},n=e[e.l];return++e.l,r.above=!(n&64),r.left=!(n&128),e.l+=18,r.name=Wo(e),r}function Td(e){var a=Rr(e);return[a]}function kd(e){var a=Fa(e);return[a]}function wd(e){var a=Rr(e),r=e.read_shift(1);return[a,r,"b"]}function Ad(e){var a=Fa(e),r=e.read_shift(1);return[a,r,"b"]}function Fd(e){var a=Rr(e),r=e.read_shift(1);return[a,r,"e"]}function Sd(e){var a=Fa(e),r=e.read_shift(1);return[a,r,"e"]}function Cd(e){var a=Rr(e),r=e.read_shift(4);return[a,r,"s"]}function yd(e){var a=Fa(e),r=e.read_shift(4);return[a,r,"s"]}function Dd(e){var a=Rr(e),r=cr(e);return[a,r,"n"]}function Di(e){var a=Fa(e),r=cr(e);return[a,r,"n"]}function Od(e){var a=Rr(e),r=N0(e);return[a,r,"n"]}function Id(e){var a=Fa(e),r=N0(e);return[a,r,"n"]}function Rd(e){var a=Rr(e),r=I0(e);return[a,r,"is"]}function Nd(e){var a=Rr(e),r=or(e);return[a,r,"str"]}function Ld(e){var a=Fa(e),r=or(e);return[a,r,"str"]}function Pd(e,a,r){var n=e.l+a,t=Rr(e);t.r=r["!row"];var s=e.read_shift(1),i=[t,s,"b"];if(r.cellFormula){e.l+=2;var c=Kt(e,n-e.l,r);i[3]=ir(c,null,t,r.supbooks,r)}else e.l=n;return i}function bd(e,a,r){var n=e.l+a,t=Rr(e);t.r=r["!row"];var s=e.read_shift(1),i=[t,s,"e"];if(r.cellFormula){e.l+=2;var c=Kt(e,n-e.l,r);i[3]=ir(c,null,t,r.supbooks,r)}else e.l=n;return i}function Bd(e,a,r){var n=e.l+a,t=Rr(e);t.r=r["!row"];var s=cr(e),i=[t,s,"n"];if(r.cellFormula){e.l+=2;var c=Kt(e,n-e.l,r);i[3]=ir(c,null,t,r.supbooks,r)}else e.l=n;return i}function Md(e,a,r){var n=e.l+a,t=Rr(e);t.r=r["!row"];var s=or(e),i=[t,s,"str"];if(r.cellFormula){e.l+=2;var c=Kt(e,n-e.l,r);i[3]=ir(c,null,t,r.supbooks,r)}else e.l=n;return i}var Ud=Sa;function Vd(e,a){var r=e.l+a,n=Sa(e),t=R0(e),s=or(e),i=or(e),c=or(e);e.l=r;var f={rfx:n,relId:t,loc:s,display:c};return i&&(f.Tooltip=i),f}function Hd(){}function Wd(e,a,r){var n=e.l+a,t=zs(e),s=e.read_shift(1),i=[t];if(i[2]=s,r.cellFormula){var c=Kx(e,n-e.l,r);i[1]=c}else e.l=n;return i}function Gd(e,a,r){var n=e.l+a,t=Sa(e),s=[t];if(r.cellFormula){var i=jx(e,n-e.l,r);s[1]=i,e.l=n}else e.l=n;return s}var $d=["left","right","top","bottom","header","footer"];function Xd(e){var a={};return $d.forEach(function(r){a[r]=cr(e)}),a}function zd(e){var a=e.read_shift(2);return e.l+=28,{RTL:a&32}}function Kd(){}function Yd(){}function jd(e,a,r,n,t,s,i){if(!e)return e;var c=a||{};n||(n={"!id":{}});var f=c.dense?[]:{},l,o={s:{r:2e6,c:2e6},e:{r:0,c:0}},u=!1,p=!1,h,d,x,g,w,y,m,O,P,D=[];c.biff=12,c["!row"]=0;var S=0,U=!1,R=[],z={},G=c.supbooks||t.supbooks||[[]];if(G.sharedf=z,G.arrayf=R,G.SheetNames=t.SheetNames||t.Sheets.map(function(De){return De.name}),!c.supbooks&&(c.supbooks=G,t.Names))for(var b=0;b<t.Names.length;++b)G[0][b+1]=t.Names[b];var j=[],Y=[],Q=!1;Ht[16]={n:"BrtShortReal",f:Di};var se;if(qr(e,function(L,X,ue){if(!p)switch(ue){case 148:l=L;break;case 0:h=L,c.sheetRows&&c.sheetRows<=h.r&&(p=!0),O=tr(g=h.r),c["!row"]=h.r,(L.hidden||L.hpt||L.level!=null)&&(L.hpt&&(L.hpx=ft(L.hpt)),Y[L.r]=L);break;case 2:case 3:case 4:case 5:case 6:case 7:case 8:case 9:case 10:case 11:case 13:case 14:case 15:case 16:case 17:case 18:case 62:switch(d={t:L[2]},L[2]){case"n":d.v=L[1];break;case"s":m=et[L[1]],d.v=m.t,d.r=m.r;break;case"b":d.v=!!L[1];break;case"e":d.v=L[1],c.cellText!==!1&&(d.w=Ca[d.v]);break;case"str":d.t="s",d.v=L[1];break;case"is":d.t="s",d.v=L[1].t;break}if((x=i.CellXf[L[0].iStyleRef])&&Ci(d,x.numFmtId,null,c,s,i),w=L[0].c==-1?w+1:L[0].c,c.dense?(f[g]||(f[g]=[]),f[g][w]=d):f[Je(w)+O]=d,c.cellFormula){for(U=!1,S=0;S<R.length;++S){var A=R[S];h.r>=A[0].s.r&&h.r<=A[0].e.r&&w>=A[0].s.c&&w<=A[0].e.c&&(d.F=ye(A[0]),U=!0)}!U&&L.length>3&&(d.f=L[3])}if(o.s.r>h.r&&(o.s.r=h.r),o.s.c>w&&(o.s.c=w),o.e.r<h.r&&(o.e.r=h.r),o.e.c<w&&(o.e.c=w),c.cellDates&&x&&d.t=="n"&&Ba(_e[x.numFmtId])){var B=da(d.v);B&&(d.t="d",d.v=new Date(B.y,B.m-1,B.d,B.H,B.M,B.S,B.u))}se&&(se.type=="XLDAPR"&&(d.D=!0),se=void 0);break;case 1:case 12:if(!c.sheetStubs||u)break;d={t:"z",v:void 0},w=L[0].c==-1?w+1:L[0].c,c.dense?(f[g]||(f[g]=[]),f[g][w]=d):f[Je(w)+O]=d,o.s.r>h.r&&(o.s.r=h.r),o.s.c>w&&(o.s.c=w),o.e.r<h.r&&(o.e.r=h.r),o.e.c<w&&(o.e.c=w),se&&(se.type=="XLDAPR"&&(d.D=!0),se=void 0);break;case 176:D.push(L);break;case 49:se=((c.xlmeta||{}).Cell||[])[L-1];break;case 494:var N=n["!id"][L.relId];for(N?(L.Target=N.Target,L.loc&&(L.Target+="#"+L.loc),L.Rel=N):L.relId==""&&(L.Target="#"+L.loc),g=L.rfx.s.r;g<=L.rfx.e.r;++g)for(w=L.rfx.s.c;w<=L.rfx.e.c;++w)c.dense?(f[g]||(f[g]=[]),f[g][w]||(f[g][w]={t:"z",v:void 0}),f[g][w].l=L):(y=ve({c:w,r:g}),f[y]||(f[y]={t:"z",v:void 0}),f[y].l=L);break;case 426:if(!c.cellFormula)break;R.push(L),P=c.dense?f[g][w]:f[Je(w)+O],P.f=ir(L[1],o,{r:h.r,c:w},G,c),P.F=ye(L[0]);break;case 427:if(!c.cellFormula)break;z[ve(L[0].s)]=L[1],P=c.dense?f[g][w]:f[Je(w)+O],P.f=ir(L[1],o,{r:h.r,c:w},G,c);break;case 60:if(!c.cellStyles)break;for(;L.e>=L.s;)j[L.e--]={width:L.w/256,hidden:!!(L.flags&1),level:L.level},Q||(Q=!0,B0(L.w/256)),ba(j[L.e+1]);break;case 161:f["!autofilter"]={ref:ye(L)};break;case 476:f["!margins"]=L;break;case 147:t.Sheets[r]||(t.Sheets[r]={}),L.name&&(t.Sheets[r].CodeName=L.name),(L.above||L.left)&&(f["!outline"]={above:L.above,left:L.left});break;case 137:t.Views||(t.Views=[{}]),t.Views[0]||(t.Views[0]={}),L.RTL&&(t.Views[0].RTL=!0);break;case 485:break;case 64:case 1053:break;case 151:break;case 152:case 175:case 644:case 625:case 562:case 396:case 1112:case 1146:case 471:case 1050:case 649:case 1105:case 589:case 607:case 564:case 1055:case 168:case 174:case 1180:case 499:case 507:case 550:case 171:case 167:case 1177:case 169:case 1181:case 551:case 552:case 661:case 639:case 478:case 537:case 477:case 536:case 1103:case 680:case 1104:case 1024:case 663:case 535:case 678:case 504:case 1043:case 428:case 170:case 3072:case 50:case 2070:case 1045:break;case 35:u=!0;break;case 36:u=!1;break;case 37:u=!0;break;case 38:u=!1;break;default:if(!X.T){if(!u||c.WTF)throw new Error("Unexpected record 0x"+ue.toString(16))}}},c),delete c.supbooks,delete c["!row"],!f["!ref"]&&(o.s.r<2e6||l&&(l.e.r>0||l.e.c>0||l.s.r>0||l.s.c>0))&&(f["!ref"]=ye(l||o)),c.sheetRows&&f["!ref"]){var ne=He(f["!ref"]);c.sheetRows<=+ne.e.r&&(ne.e.r=c.sheetRows-1,ne.e.r>o.e.r&&(ne.e.r=o.e.r),ne.e.r<ne.s.r&&(ne.s.r=ne.e.r),ne.e.c>o.e.c&&(ne.e.c=o.e.c),ne.e.c<ne.s.c&&(ne.s.c=ne.e.c),f["!fullref"]=f["!ref"],f["!ref"]=ye(ne))}return D.length>0&&(f["!merges"]=D),j.length>0&&(f["!cols"]=j),Y.length>0&&(f["!rows"]=Y),f}function Jd(e){var a=[],r=e.match(/^<c:numCache>/),n;(e.match(/<c:pt idx="(\d*)">(.*?)<\/c:pt>/mg)||[]).forEach(function(s){var i=s.match(/<c:pt idx="(\d*?)"><c:v>(.*)<\/c:v><\/c:pt>/);i&&(a[+i[1]]=r?+i[2]:i[2])});var t=Oe((e.match(/<c:formatCode>([\s\S]*?)<\/c:formatCode>/)||["","General"])[1]);return(e.match(/<c:f>(.*?)<\/c:f>/mg)||[]).forEach(function(s){n=s.replace(/<.*?>/g,"")}),[a,t,n]}function Zd(e,a,r,n,t,s){var i=s||{"!type":"chart"};if(!e)return s;var c=0,f=0,l="A",o={s:{r:2e6,c:2e6},e:{r:0,c:0}};return(e.match(/<c:numCache>[\s\S]*?<\/c:numCache>/gm)||[]).forEach(function(u){var p=Jd(u);o.s.r=o.s.c=0,o.e.c=c,l=Je(c),p[0].forEach(function(h,d){i[l+tr(d)]={t:"n",v:h,z:p[1]},f=d}),o.e.r<f&&(o.e.r=f),++c}),c>0&&(i["!ref"]=ye(o)),i}function qd(e,a,r,n,t){if(!e)return e;n||(n={"!id":{}});var s={"!type":"chart","!drawel":null,"!rel":""},i,c=e.match(yi);return c&&U0(c[0],s,t,r),(i=e.match(/drawing r:id="(.*?)"/))&&(s["!rel"]=i[1]),n["!id"][s["!rel"]]&&(s["!drawel"]=n["!id"][s["!rel"]]),s}function Qd(e,a){e.l+=10;var r=or(e);return{name:r}}function e2(e,a,r,n,t){if(!e)return e;n||(n={"!id":{}});var s={"!type":"chart","!drawel":null,"!rel":""},i=!1;return qr(e,function(f,l,o){switch(o){case 550:s["!rel"]=f;break;case 651:t.Sheets[r]||(t.Sheets[r]={}),f.name&&(t.Sheets[r].CodeName=f.name);break;case 562:case 652:case 669:case 679:case 551:case 552:case 476:case 3072:break;case 35:i=!0;break;case 36:i=!1;break;case 37:break;case 38:break;default:if(!(l.T>0)){if(!(l.T<0)){if(!i||a.WTF)throw new Error("Unexpected record 0x"+o.toString(16))}}}},a),n["!id"][s["!rel"]]&&(s["!drawel"]=n["!id"][s["!rel"]]),s}var Oi=[["allowRefreshQuery",!1,"bool"],["autoCompressPictures",!0,"bool"],["backupFile",!1,"bool"],["checkCompatibility",!1,"bool"],["CodeName",""],["date1904",!1,"bool"],["defaultThemeVersion",0,"int"],["filterPrivacy",!1,"bool"],["hidePivotFieldList",!1,"bool"],["promptedSolutions",!1,"bool"],["publishItems",!1,"bool"],["refreshAllConnections",!1,"bool"],["saveExternalLinkValues",!0,"bool"],["showBorderUnselectedTables",!0,"bool"],["showInkAnnotation",!0,"bool"],["showObjects","all"],["showPivotChartFilter",!1,"bool"],["updateLinks","userSet"]],r2=[["activeTab",0,"int"],["autoFilterDateGrouping",!0,"bool"],["firstSheet",0,"int"],["minimized",!1,"bool"],["showHorizontalScroll",!0,"bool"],["showSheetTabs",!0,"bool"],["showVerticalScroll",!0,"bool"],["tabRatio",600,"int"],["visibility","visible"]],a2=[],t2=[["calcCompleted","true"],["calcMode","auto"],["calcOnSave","true"],["concurrentCalc","true"],["fullCalcOnLoad","false"],["fullPrecision","true"],["iterate","false"],["iterateCount","100"],["iterateDelta","0.001"],["refMode","A1"]];function zn(e,a){for(var r=0;r!=e.length;++r)for(var n=e[r],t=0;t!=a.length;++t){var s=a[t];if(n[s[0]]==null)n[s[0]]=s[1];else switch(s[2]){case"bool":typeof n[s[0]]=="string"&&(n[s[0]]=be(n[s[0]]));break;case"int":typeof n[s[0]]=="string"&&(n[s[0]]=parseInt(n[s[0]],10));break}}}function Kn(e,a){for(var r=0;r!=a.length;++r){var n=a[r];if(e[n[0]]==null)e[n[0]]=n[1];else switch(n[2]){case"bool":typeof e[n[0]]=="string"&&(e[n[0]]=be(e[n[0]]));break;case"int":typeof e[n[0]]=="string"&&(e[n[0]]=parseInt(e[n[0]],10));break}}}function Ii(e){Kn(e.WBProps,Oi),Kn(e.CalcPr,t2),zn(e.WBView,r2),zn(e.Sheets,a2),La.date1904=be(e.WBProps.date1904)}var n2="][*?/\\".split("");function s2(e,a){if(e.length>31)throw new Error("Sheet names cannot exceed 31 chars");var r=!0;return n2.forEach(function(n){if(e.indexOf(n)!=-1)throw new Error("Sheet name cannot contain : \\ / ? * [ ]")}),r}var i2=/<\w+:workbook/;function c2(e,a){if(!e)throw new Error("Could not find file");var r={AppVersion:{},WBProps:{},WBView:[],Sheets:[],CalcPr:{},Names:[],xmlns:""},n=!1,t="xmlns",s={},i=0;if(e.replace(ur,function(f,l){var o=pe(f);switch($r(o[0])){case"<?xml":break;case"<workbook":f.match(i2)&&(t="xmlns"+f.match(/<(\w+):/)[1]),r.xmlns=o[t];break;case"</workbook>":break;case"<fileVersion":delete o[0],r.AppVersion=o;break;case"<fileVersion/>":case"</fileVersion>":break;case"<fileSharing":break;case"<fileSharing/>":break;case"<workbookPr":case"<workbookPr/>":Oi.forEach(function(u){if(o[u[0]]!=null)switch(u[2]){case"bool":r.WBProps[u[0]]=be(o[u[0]]);break;case"int":r.WBProps[u[0]]=parseInt(o[u[0]],10);break;default:r.WBProps[u[0]]=o[u[0]]}}),o.codeName&&(r.WBProps.CodeName=Ne(o.codeName));break;case"</workbookPr>":break;case"<workbookProtection":break;case"<workbookProtection/>":break;case"<bookViews":case"<bookViews>":case"</bookViews>":break;case"<workbookView":case"<workbookView/>":delete o[0],r.WBView.push(o);break;case"</workbookView>":break;case"<sheets":case"<sheets>":case"</sheets>":break;case"<sheet":switch(o.state){case"hidden":o.Hidden=1;break;case"veryHidden":o.Hidden=2;break;default:o.Hidden=0}delete o.state,o.name=Oe(Ne(o.name)),delete o[0],r.Sheets.push(o);break;case"</sheet>":break;case"<functionGroups":case"<functionGroups/>":break;case"<functionGroup":break;case"<externalReferences":case"</externalReferences>":case"<externalReferences>":break;case"<externalReference":break;case"<definedNames/>":break;case"<definedNames>":case"<definedNames":n=!0;break;case"</definedNames>":n=!1;break;case"<definedName":s={},s.Name=Ne(o.name),o.comment&&(s.Comment=o.comment),o.localSheetId&&(s.Sheet=+o.localSheetId),be(o.hidden||"0")&&(s.Hidden=!0),i=l+f.length;break;case"</definedName>":s.Ref=Oe(Ne(e.slice(i,l))),r.Names.push(s);break;case"<definedName/>":break;case"<calcPr":delete o[0],r.CalcPr=o;break;case"<calcPr/>":delete o[0],r.CalcPr=o;break;case"</calcPr>":break;case"<oleSize":break;case"<customWorkbookViews>":case"</customWorkbookViews>":case"<customWorkbookViews":break;case"<customWorkbookView":case"</customWorkbookView>":break;case"<pivotCaches>":case"</pivotCaches>":case"<pivotCaches":break;case"<pivotCache":break;case"<smartTagPr":case"<smartTagPr/>":break;case"<smartTagTypes":case"<smartTagTypes>":case"</smartTagTypes>":break;case"<smartTagType":break;case"<webPublishing":case"<webPublishing/>":break;case"<fileRecoveryPr":case"<fileRecoveryPr/>":break;case"<webPublishObjects>":case"<webPublishObjects":case"</webPublishObjects>":break;case"<webPublishObject":break;case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<ext":n=!0;break;case"</ext>":n=!1;break;case"<ArchID":break;case"<AlternateContent":case"<AlternateContent>":n=!0;break;case"</AlternateContent>":n=!1;break;case"<revisionPtr":break;default:if(!n&&a.WTF)throw new Error("unrecognized "+o[0]+" in workbook")}return f}),Fo.indexOf(r.xmlns)===-1)throw new Error("Unknown Namespace: "+r.xmlns);return Ii(r),r}function f2(e,a){var r={};return r.Hidden=e.read_shift(4),r.iTabID=e.read_shift(4),r.strRelID=o0(e),r.name=or(e),r}function o2(e,a){var r={},n=e.read_shift(4);r.defaultThemeVersion=e.read_shift(4);var t=a>8?or(e):"";return t.length>0&&(r.CodeName=t),r.autoCompressPictures=!!(n&65536),r.backupFile=!!(n&64),r.checkCompatibility=!!(n&4096),r.date1904=!!(n&1),r.filterPrivacy=!!(n&8),r.hidePivotFieldList=!!(n&1024),r.promptedSolutions=!!(n&16),r.publishItems=!!(n&2048),r.refreshAllConnections=!!(n&262144),r.saveExternalLinkValues=!!(n&128),r.showBorderUnselectedTables=!!(n&4),r.showInkAnnotation=!!(n&32),r.showObjects=["all","placeholders","none"][n>>13&3],r.showPivotChartFilter=!!(n&32768),r.updateLinks=["userSet","never","always"][n>>8&3],r}function l2(e,a){var r={};return e.read_shift(4),r.ArchID=e.read_shift(4),e.l+=a-8,r}function u2(e,a,r){var n=e.l+a;e.l+=4,e.l+=1;var t=e.read_shift(4),s=Go(e),i=Yx(e,0,r),c=R0(e);e.l=n;var f={Name:s,Ptg:i};return t<268435455&&(f.Sheet=t),c&&(f.Comment=c),f}function h2(e,a){var r={AppVersion:{},WBProps:{},WBView:[],Sheets:[],CalcPr:{},xmlns:""},n=[],t=!1;a||(a={}),a.biff=12;var s=[],i=[[]];return i.SheetNames=[],i.XTI=[],Ht[16]={n:"BrtFRTArchID$",f:l2},qr(e,function(f,l,o){switch(o){case 156:i.SheetNames.push(f.name),r.Sheets.push(f);break;case 153:r.WBProps=f;break;case 39:f.Sheet!=null&&(a.SID=f.Sheet),f.Ref=ir(f.Ptg,null,null,i,a),delete a.SID,delete f.Ptg,s.push(f);break;case 1036:break;case 357:case 358:case 355:case 667:i[0].length?i.push([o,f]):i[0]=[o,f],i[i.length-1].XTI=[];break;case 362:i.length===0&&(i[0]=[],i[0].XTI=[]),i[i.length-1].XTI=i[i.length-1].XTI.concat(f),i.XTI=i.XTI.concat(f);break;case 361:break;case 2071:case 158:case 143:case 664:case 353:break;case 3072:case 3073:case 534:case 677:case 157:case 610:case 2050:case 155:case 548:case 676:case 128:case 665:case 2128:case 2125:case 549:case 2053:case 596:case 2076:case 2075:case 2082:case 397:case 154:case 1117:case 553:case 2091:break;case 35:n.push(o),t=!0;break;case 36:n.pop(),t=!1;break;case 37:n.push(o),t=!0;break;case 38:n.pop(),t=!1;break;case 16:break;default:if(!l.T){if(!t||a.WTF&&n[n.length-1]!=37&&n[n.length-1]!=35)throw new Error("Unexpected record 0x"+o.toString(16))}}},a),Ii(r),r.Names=s,r.supbooks=i,r}function x2(e,a,r){return a.slice(-4)===".bin"?h2(e,r):c2(e,r)}function d2(e,a,r,n,t,s,i,c){return a.slice(-4)===".bin"?jd(e,n,r,t,s,i,c):fd(e,n,r,t,s,i,c)}function p2(e,a,r,n,t,s,i,c){return a.slice(-4)===".bin"?e2(e,n,r,t,s):qd(e,n,r,t,s)}function v2(e,a,r,n,t,s,i,c){return a.slice(-4)===".bin"?Ah():Fh()}function g2(e,a,r,n,t,s,i,c){return a.slice(-4)===".bin"?kh():wh()}function m2(e,a,r,n){return a.slice(-4)===".bin"?Uu(e,r,n):Nu(e,r,n)}function _2(e,a,r){return gi(e,r)}function E2(e,a,r){return a.slice(-4)===".bin"?ru(e,r):Q1(e,r)}function T2(e,a,r){return a.slice(-4)===".bin"?_h(e,r):dh(e,r)}function k2(e,a,r){return a.slice(-4)===".bin"?uh(e):oh(e)}function w2(e,a,r,n){return r.slice(-4)===".bin"?hh(e,a,r,n):void 0}function A2(e,a,r){return a.slice(-4)===".bin"?ch(e,a,r):fh(e,a,r)}var Ri=/([\w:]+)=((?:")([^"]*)(?:")|(?:')([^']*)(?:'))/g,Ni=/([\w:]+)=((?:")(?:[^"]*)(?:")|(?:')(?:[^']*)(?:'))/;function Lr(e,a){var r=e.split(/\s+/),n=[];if(n[0]=r[0],r.length===1)return n;var t=e.match(Ri),s,i,c,f;if(t)for(f=0;f!=t.length;++f)s=t[f].match(Ni),(i=s[1].indexOf(":"))===-1?n[s[1]]=s[2].slice(1,s[2].length-1):(s[1].slice(0,6)==="xmlns:"?c="xmlns"+s[1].slice(6):c=s[1].slice(i+1),n[c]=s[2].slice(1,s[2].length-1));return n}function F2(e){var a=e.split(/\s+/),r={};if(a.length===1)return r;var n=e.match(Ri),t,s,i,c;if(n)for(c=0;c!=n.length;++c)t=n[c].match(Ni),(s=t[1].indexOf(":"))===-1?r[t[1]]=t[2].slice(1,t[2].length-1):(t[1].slice(0,6)==="xmlns:"?i="xmlns"+t[1].slice(6):i=t[1].slice(s+1),r[i]=t[2].slice(1,t[2].length-1));return r}var at;function S2(e,a){var r=at[e]||Oe(e);return r==="General"?Ea(a):Ir(r,a)}function C2(e,a,r,n){var t=n;switch((r[0].match(/dt:dt="([\w.]+)"/)||["",""])[1]){case"boolean":t=be(n);break;case"i2":case"int":t=parseInt(n,10);break;case"r4":case"float":t=parseFloat(n);break;case"date":case"dateTime.tz":t=er(n);break;case"i8":case"string":case"fixed":case"uuid":case"bin.base64":break;default:throw new Error("bad custprop:"+r[0])}e[Oe(a)]=t}function y2(e,a,r){if(e.t!=="z"){if(!r||r.cellText!==!1)try{e.t==="e"?e.w=e.w||Ca[e.v]:a==="General"?e.t==="n"?(e.v|0)===e.v?e.w=e.v.toString(10):e.w=tt(e.v):e.w=Ea(e.v):e.w=S2(a||"General",e.v)}catch(s){if(r.WTF)throw s}try{var n=at[a]||a||"General";if(r.cellNF&&(e.z=n),r.cellDates&&e.t=="n"&&Ba(n)){var t=da(e.v);t&&(e.t="d",e.v=new Date(t.y,t.m-1,t.d,t.H,t.M,t.S,t.u))}}catch(s){if(r.WTF)throw s}}}function D2(e,a,r){if(r.cellStyles&&a.Interior){var n=a.Interior;n.Pattern&&(n.patternType=Cu[n.Pattern]||n.Pattern)}e[a.ID]=a}function O2(e,a,r,n,t,s,i,c,f,l){var o="General",u=n.StyleID,p={};l=l||{};var h=[],d=0;for(u===void 0&&c&&(u=c.StyleID),u===void 0&&i&&(u=i.StyleID);s[u]!==void 0&&(s[u].nf&&(o=s[u].nf),s[u].Interior&&h.push(s[u].Interior),!!s[u].Parent);)u=s[u].Parent;switch(r.Type){case"Boolean":n.t="b",n.v=be(e);break;case"String":n.t="s",n.r=pn(Oe(e)),n.v=e.indexOf("<")>-1?Oe(a||e).replace(/<.*?>/g,""):n.r;break;case"DateTime":e.slice(-1)!="Z"&&(e+="Z"),n.v=(er(e)-new Date(Date.UTC(1899,11,30)))/(24*60*60*1e3),n.v!==n.v?n.v=Oe(e):n.v<60&&(n.v=n.v-1),(!o||o=="General")&&(o="yyyy-mm-dd");case"Number":n.v===void 0&&(n.v=+e),n.t||(n.t="n");break;case"Error":n.t="e",n.v=js[e],l.cellText!==!1&&(n.w=e);break;default:e==""&&a==""?n.t="z":(n.t="s",n.v=pn(a||e));break}if(y2(n,o,l),l.cellFormula!==!1)if(n.Formula){var x=Oe(n.Formula);x.charCodeAt(0)==61&&(x=x.slice(1)),n.f=Na(x,t),delete n.Formula,n.ArrayRange=="RC"?n.F=Na("RC:RC",t):n.ArrayRange&&(n.F=Na(n.ArrayRange,t),f.push([He(n.F),n.F]))}else for(d=0;d<f.length;++d)t.r>=f[d][0].s.r&&t.r<=f[d][0].e.r&&t.c>=f[d][0].s.c&&t.c<=f[d][0].e.c&&(n.F=f[d][1]);l.cellStyles&&(h.forEach(function(g){!p.patternType&&g.patternType&&(p.patternType=g.patternType)}),n.s=p),n.StyleID!==void 0&&(n.ixfe=n.StyleID)}function I2(e){e.t=e.v||"",e.t=e.t.replace(/\r\n/g,`
`).replace(/\r/g,`
`),e.v=e.w=e.ixfe=void 0}function t0(e,a){var r=a||{};Ts();var n=$a(C0(e));(r.type=="binary"||r.type=="array"||r.type=="base64")&&(n=Ne(n));var t=n.slice(0,1024).toLowerCase(),s=!1;if(t=t.replace(/".*?"/g,""),(t.indexOf(">")&1023)>Math.min(t.indexOf(",")&1023,t.indexOf(";")&1023)){var i=ar(r);return i.type="string",it.to_workbook(n,i)}if(t.indexOf("<?xml")==-1&&["html","table","head","meta","script","style","div"].forEach(function(Pe){t.indexOf("<"+Pe)>=0&&(s=!0)}),s)return V2(n,r);at={"General Number":"General","General Date":_e[22],"Long Date":"dddd, mmmm dd, yyyy","Medium Date":_e[15],"Short Date":_e[14],"Long Time":_e[19],"Medium Time":_e[18],"Short Time":_e[20],Currency:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',Fixed:_e[2],Standard:_e[4],Percent:_e[10],Scientific:_e[11],"Yes/No":'"Yes";"Yes";"No";@',"True/False":'"True";"True";"False";@',"On/Off":'"Yes";"Yes";"No";@'};var c,f=[],l,o={},u=[],p=r.dense?[]:{},h="",d={},x={},g=Lr('<Data ss:Type="String">'),w=0,y=0,m=0,O={s:{r:2e6,c:2e6},e:{r:0,c:0}},P={},D={},S="",U=0,R=[],z={},G={},b=0,j=[],Y=[],Q={},se=[],ne,De=!1,L=[],X=[],ue={},A=0,B=0,N={Sheets:[],WBProps:{date1904:!1}},I={};st.lastIndex=0,n=n.replace(/<!--([\s\S]*?)-->/mg,"");for(var J="";c=st.exec(n);)switch(c[3]=(J=c[3]).toLowerCase()){case"data":if(J=="data"){if(c[1]==="/"){if((l=f.pop())[0]!==c[3])throw new Error("Bad state: "+l.join("|"))}else c[0].charAt(c[0].length-2)!=="/"&&f.push([c[3],!0]);break}if(f[f.length-1][1])break;c[1]==="/"?O2(n.slice(w,c.index),S,g,f[f.length-1][0]=="comment"?Q:d,{c:y,r:m},P,se[y],x,L,r):(S="",g=Lr(c[0]),w=c.index+c[0].length);break;case"cell":if(c[1]==="/")if(Y.length>0&&(d.c=Y),(!r.sheetRows||r.sheetRows>m)&&d.v!==void 0&&(r.dense?(p[m]||(p[m]=[]),p[m][y]=d):p[Je(y)+tr(m)]=d),d.HRef&&(d.l={Target:Oe(d.HRef)},d.HRefScreenTip&&(d.l.Tooltip=d.HRefScreenTip),delete d.HRef,delete d.HRefScreenTip),(d.MergeAcross||d.MergeDown)&&(A=y+(parseInt(d.MergeAcross,10)|0),B=m+(parseInt(d.MergeDown,10)|0),R.push({s:{c:y,r:m},e:{c:A,r:B}})),!r.sheetStubs)d.MergeAcross?y=A+1:++y;else if(d.MergeAcross||d.MergeDown){for(var Z=y;Z<=A;++Z)for(var ie=m;ie<=B;++ie)(Z>y||ie>m)&&(r.dense?(p[ie]||(p[ie]=[]),p[ie][Z]={t:"z"}):p[Je(Z)+tr(ie)]={t:"z"});y=A+1}else++y;else d=F2(c[0]),d.Index&&(y=+d.Index-1),y<O.s.c&&(O.s.c=y),y>O.e.c&&(O.e.c=y),c[0].slice(-2)==="/>"&&++y,Y=[];break;case"row":c[1]==="/"||c[0].slice(-2)==="/>"?(m<O.s.r&&(O.s.r=m),m>O.e.r&&(O.e.r=m),c[0].slice(-2)==="/>"&&(x=Lr(c[0]),x.Index&&(m=+x.Index-1)),y=0,++m):(x=Lr(c[0]),x.Index&&(m=+x.Index-1),ue={},(x.AutoFitHeight=="0"||x.Height)&&(ue.hpx=parseInt(x.Height,10),ue.hpt=vi(ue.hpx),X[m]=ue),x.Hidden=="1"&&(ue.hidden=!0,X[m]=ue));break;case"worksheet":if(c[1]==="/"){if((l=f.pop())[0]!==c[3])throw new Error("Bad state: "+l.join("|"));u.push(h),O.s.r<=O.e.r&&O.s.c<=O.e.c&&(p["!ref"]=ye(O),r.sheetRows&&r.sheetRows<=O.e.r&&(p["!fullref"]=p["!ref"],O.e.r=r.sheetRows-1,p["!ref"]=ye(O))),R.length&&(p["!merges"]=R),se.length>0&&(p["!cols"]=se),X.length>0&&(p["!rows"]=X),o[h]=p}else O={s:{r:2e6,c:2e6},e:{r:0,c:0}},m=y=0,f.push([c[3],!1]),l=Lr(c[0]),h=Oe(l.Name),p=r.dense?[]:{},R=[],L=[],X=[],I={name:h,Hidden:0},N.Sheets.push(I);break;case"table":if(c[1]==="/"){if((l=f.pop())[0]!==c[3])throw new Error("Bad state: "+l.join("|"))}else{if(c[0].slice(-2)=="/>")break;f.push([c[3],!1]),se=[],De=!1}break;case"style":c[1]==="/"?D2(P,D,r):D=Lr(c[0]);break;case"numberformat":D.nf=Oe(Lr(c[0]).Format||"General"),at[D.nf]&&(D.nf=at[D.nf]);for(var te=0;te!=392&&_e[te]!=D.nf;++te);if(te==392){for(te=57;te!=392;++te)if(_e[te]==null){ga(D.nf,te);break}}break;case"column":if(f[f.length-1][0]!=="table")break;if(ne=Lr(c[0]),ne.Hidden&&(ne.hidden=!0,delete ne.Hidden),ne.Width&&(ne.wpx=parseInt(ne.Width,10)),!De&&ne.wpx>10){De=!0,pr=di;for(var ee=0;ee<se.length;++ee)se[ee]&&ba(se[ee])}De&&ba(ne),se[ne.Index-1||se.length]=ne;for(var ke=0;ke<+ne.Span;++ke)se[se.length]=ar(ne);break;case"namedrange":if(c[1]==="/")break;N.Names||(N.Names=[]);var C=pe(c[0]),Le={Name:C.Name,Ref:Na(C.RefersTo.slice(1),{r:0,c:0})};N.Sheets.length>0&&(Le.Sheet=N.Sheets.length-1),N.Names.push(Le);break;case"namedcell":break;case"b":break;case"i":break;case"u":break;case"s":break;case"em":break;case"h2":break;case"h3":break;case"sub":break;case"sup":break;case"span":break;case"alignment":break;case"borders":break;case"border":break;case"font":if(c[0].slice(-2)==="/>")break;c[1]==="/"?S+=n.slice(U,c.index):U=c.index+c[0].length;break;case"interior":if(!r.cellStyles)break;D.Interior=Lr(c[0]);break;case"protection":break;case"author":case"title":case"description":case"created":case"keywords":case"subject":case"category":case"company":case"lastauthor":case"lastsaved":case"lastprinted":case"version":case"revision":case"totaltime":case"hyperlinkbase":case"manager":case"contentstatus":case"identifier":case"language":case"appname":if(c[0].slice(-2)==="/>")break;c[1]==="/"?dl(z,J,n.slice(b,c.index)):b=c.index+c[0].length;break;case"paragraphs":break;case"styles":case"workbook":if(c[1]==="/"){if((l=f.pop())[0]!==c[3])throw new Error("Bad state: "+l.join("|"))}else f.push([c[3],!1]);break;case"comment":if(c[1]==="/"){if((l=f.pop())[0]!==c[3])throw new Error("Bad state: "+l.join("|"));I2(Q),Y.push(Q)}else f.push([c[3],!1]),l=Lr(c[0]),Q={a:l.Author};break;case"autofilter":if(c[1]==="/"){if((l=f.pop())[0]!==c[3])throw new Error("Bad state: "+l.join("|"))}else if(c[0].charAt(c[0].length-2)!=="/"){var Fe=Lr(c[0]);p["!autofilter"]={ref:Na(Fe.Range).replace(/\$/g,"")},f.push([c[3],!0])}break;case"name":break;case"datavalidation":if(c[1]==="/"){if((l=f.pop())[0]!==c[3])throw new Error("Bad state: "+l.join("|"))}else c[0].charAt(c[0].length-2)!=="/"&&f.push([c[3],!0]);break;case"pixelsperinch":break;case"componentoptions":case"documentproperties":case"customdocumentproperties":case"officedocumentsettings":case"pivottable":case"pivotcache":case"names":case"mapinfo":case"pagebreaks":case"querytable":case"sorting":case"schema":case"conditionalformatting":case"smarttagtype":case"smarttags":case"excelworkbook":case"workbookoptions":case"worksheetoptions":if(c[1]==="/"){if((l=f.pop())[0]!==c[3])throw new Error("Bad state: "+l.join("|"))}else c[0].charAt(c[0].length-2)!=="/"&&f.push([c[3],!0]);break;case"null":break;default:if(f.length==0&&c[3]=="document"||f.length==0&&c[3]=="uof")return Qn(n,r);var Ie=!0;switch(f[f.length-1][0]){case"officedocumentsettings":switch(c[3]){case"allowpng":break;case"removepersonalinformation":break;case"downloadcomponents":break;case"locationofcomponents":break;case"colors":break;case"color":break;case"index":break;case"rgb":break;case"targetscreensize":break;case"readonlyrecommended":break;default:Ie=!1}break;case"componentoptions":switch(c[3]){case"toolbar":break;case"hideofficelogo":break;case"spreadsheetautofit":break;case"label":break;case"caption":break;case"maxheight":break;case"maxwidth":break;case"nextsheetnumber":break;default:Ie=!1}break;case"excelworkbook":switch(c[3]){case"date1904":N.WBProps.date1904=!0;break;case"windowheight":break;case"windowwidth":break;case"windowtopx":break;case"windowtopy":break;case"tabratio":break;case"protectstructure":break;case"protectwindow":break;case"protectwindows":break;case"activesheet":break;case"displayinknotes":break;case"firstvisiblesheet":break;case"supbook":break;case"sheetname":break;case"sheetindex":break;case"sheetindexfirst":break;case"sheetindexlast":break;case"dll":break;case"acceptlabelsinformulas":break;case"donotsavelinkvalues":break;case"iteration":break;case"maxiterations":break;case"maxchange":break;case"path":break;case"xct":break;case"count":break;case"selectedsheets":break;case"calculation":break;case"uncalced":break;case"startupprompt":break;case"crn":break;case"externname":break;case"formula":break;case"colfirst":break;case"collast":break;case"wantadvise":break;case"boolean":break;case"error":break;case"text":break;case"ole":break;case"noautorecover":break;case"publishobjects":break;case"donotcalculatebeforesave":break;case"number":break;case"refmoder1c1":break;case"embedsavesmarttags":break;default:Ie=!1}break;case"workbookoptions":switch(c[3]){case"owcversion":break;case"height":break;case"width":break;default:Ie=!1}break;case"worksheetoptions":switch(c[3]){case"visible":if(c[0].slice(-2)!=="/>")if(c[1]==="/")switch(n.slice(b,c.index)){case"SheetHidden":I.Hidden=1;break;case"SheetVeryHidden":I.Hidden=2;break}else b=c.index+c[0].length;break;case"header":p["!margins"]||rt(p["!margins"]={},"xlml"),isNaN(+pe(c[0]).Margin)||(p["!margins"].header=+pe(c[0]).Margin);break;case"footer":p["!margins"]||rt(p["!margins"]={},"xlml"),isNaN(+pe(c[0]).Margin)||(p["!margins"].footer=+pe(c[0]).Margin);break;case"pagemargins":var Ee=pe(c[0]);p["!margins"]||rt(p["!margins"]={},"xlml"),isNaN(+Ee.Top)||(p["!margins"].top=+Ee.Top),isNaN(+Ee.Left)||(p["!margins"].left=+Ee.Left),isNaN(+Ee.Right)||(p["!margins"].right=+Ee.Right),isNaN(+Ee.Bottom)||(p["!margins"].bottom=+Ee.Bottom);break;case"displayrighttoleft":N.Views||(N.Views=[]),N.Views[0]||(N.Views[0]={}),N.Views[0].RTL=!0;break;case"freezepanes":break;case"frozennosplit":break;case"splithorizontal":case"splitvertical":break;case"donotdisplaygridlines":break;case"activerow":break;case"activecol":break;case"toprowbottompane":break;case"leftcolumnrightpane":break;case"unsynced":break;case"print":break;case"printerrors":break;case"panes":break;case"scale":break;case"pane":break;case"number":break;case"layout":break;case"pagesetup":break;case"selected":break;case"protectobjects":break;case"enableselection":break;case"protectscenarios":break;case"validprinterinfo":break;case"horizontalresolution":break;case"verticalresolution":break;case"numberofcopies":break;case"activepane":break;case"toprowvisible":break;case"leftcolumnvisible":break;case"fittopage":break;case"rangeselection":break;case"papersizeindex":break;case"pagelayoutzoom":break;case"pagebreakzoom":break;case"filteron":break;case"fitwidth":break;case"fitheight":break;case"commentslayout":break;case"zoom":break;case"lefttoright":break;case"gridlines":break;case"allowsort":break;case"allowfilter":break;case"allowinsertrows":break;case"allowdeleterows":break;case"allowinsertcols":break;case"allowdeletecols":break;case"allowinserthyperlinks":break;case"allowformatcells":break;case"allowsizecols":break;case"allowsizerows":break;case"nosummaryrowsbelowdetail":p["!outline"]||(p["!outline"]={}),p["!outline"].above=!0;break;case"tabcolorindex":break;case"donotdisplayheadings":break;case"showpagelayoutzoom":break;case"nosummarycolumnsrightdetail":p["!outline"]||(p["!outline"]={}),p["!outline"].left=!0;break;case"blackandwhite":break;case"donotdisplayzeros":break;case"displaypagebreak":break;case"rowcolheadings":break;case"donotdisplayoutline":break;case"noorientation":break;case"allowusepivottables":break;case"zeroheight":break;case"viewablerange":break;case"selection":break;case"protectcontents":break;default:Ie=!1}break;case"pivottable":case"pivotcache":switch(c[3]){case"immediateitemsondrop":break;case"showpagemultipleitemlabel":break;case"compactrowindent":break;case"location":break;case"pivotfield":break;case"orientation":break;case"layoutform":break;case"layoutsubtotallocation":break;case"layoutcompactrow":break;case"position":break;case"pivotitem":break;case"datatype":break;case"datafield":break;case"sourcename":break;case"parentfield":break;case"ptlineitems":break;case"ptlineitem":break;case"countofsameitems":break;case"item":break;case"itemtype":break;case"ptsource":break;case"cacheindex":break;case"consolidationreference":break;case"filename":break;case"reference":break;case"nocolumngrand":break;case"norowgrand":break;case"blanklineafteritems":break;case"hidden":break;case"subtotal":break;case"basefield":break;case"mapchilditems":break;case"function":break;case"refreshonfileopen":break;case"printsettitles":break;case"mergelabels":break;case"defaultversion":break;case"refreshname":break;case"refreshdate":break;case"refreshdatecopy":break;case"versionlastrefresh":break;case"versionlastupdate":break;case"versionupdateablemin":break;case"versionrefreshablemin":break;case"calculation":break;default:Ie=!1}break;case"pagebreaks":switch(c[3]){case"colbreaks":break;case"colbreak":break;case"rowbreaks":break;case"rowbreak":break;case"colstart":break;case"colend":break;case"rowend":break;default:Ie=!1}break;case"autofilter":switch(c[3]){case"autofiltercolumn":break;case"autofiltercondition":break;case"autofilterand":break;case"autofilteror":break;default:Ie=!1}break;case"querytable":switch(c[3]){case"id":break;case"autoformatfont":break;case"autoformatpattern":break;case"querysource":break;case"querytype":break;case"enableredirections":break;case"refreshedinxl9":break;case"urlstring":break;case"htmltables":break;case"connection":break;case"commandtext":break;case"refreshinfo":break;case"notitles":break;case"nextid":break;case"columninfo":break;case"overwritecells":break;case"donotpromptforfile":break;case"textwizardsettings":break;case"source":break;case"number":break;case"decimal":break;case"thousandseparator":break;case"trailingminusnumbers":break;case"formatsettings":break;case"fieldtype":break;case"delimiters":break;case"tab":break;case"comma":break;case"autoformatname":break;case"versionlastedit":break;case"versionlastrefresh":break;default:Ie=!1}break;case"datavalidation":switch(c[3]){case"range":break;case"type":break;case"min":break;case"max":break;case"sort":break;case"descending":break;case"order":break;case"casesensitive":break;case"value":break;case"errorstyle":break;case"errormessage":break;case"errortitle":break;case"inputmessage":break;case"inputtitle":break;case"combohide":break;case"inputhide":break;case"condition":break;case"qualifier":break;case"useblank":break;case"value1":break;case"value2":break;case"format":break;case"cellrangelist":break;default:Ie=!1}break;case"sorting":case"conditionalformatting":switch(c[3]){case"range":break;case"type":break;case"min":break;case"max":break;case"sort":break;case"descending":break;case"order":break;case"casesensitive":break;case"value":break;case"errorstyle":break;case"errormessage":break;case"errortitle":break;case"cellrangelist":break;case"inputmessage":break;case"inputtitle":break;case"combohide":break;case"inputhide":break;case"condition":break;case"qualifier":break;case"useblank":break;case"value1":break;case"value2":break;case"format":break;default:Ie=!1}break;case"mapinfo":case"schema":case"data":switch(c[3]){case"map":break;case"entry":break;case"range":break;case"xpath":break;case"field":break;case"xsdtype":break;case"filteron":break;case"aggregate":break;case"elementtype":break;case"attributetype":break;case"schema":case"element":case"complextype":case"datatype":case"all":case"attribute":case"extends":break;case"row":break;default:Ie=!1}break;case"smarttags":break;default:Ie=!1;break}if(Ie||c[3].match(/!\[CDATA/))break;if(!f[f.length-1][1])throw"Unrecognized tag: "+c[3]+"|"+f.join("|");if(f[f.length-1][0]==="customdocumentproperties"){if(c[0].slice(-2)==="/>")break;c[1]==="/"?C2(G,J,j,n.slice(b,c.index)):(j=c,b=c.index+c[0].length);break}if(r.WTF)throw"Unrecognized tag: "+c[3]+"|"+f.join("|")}var fe={};return!r.bookSheets&&!r.bookProps&&(fe.Sheets=o),fe.SheetNames=u,fe.Workbook=N,fe.SSF=ar(_e),fe.Props=z,fe.Custprops=G,fe}function x0(e,a){switch(W0(a=a||{}),a.type||"base64"){case"base64":return t0(Fr(e),a);case"binary":case"buffer":case"file":return t0(e,a);case"array":return t0(Aa(e),a)}}function R2(e){var a={},r=e.content;if(r.l=28,a.AnsiUserType=r.read_shift(0,"lpstr-ansi"),a.AnsiClipboardFormat=zo(r),r.length-r.l<=4)return a;var n=r.read_shift(4);if(n==0||n>40||(r.l-=4,a.Reserved1=r.read_shift(0,"lpstr-ansi"),r.length-r.l<=4)||(n=r.read_shift(4),n!==1907505652)||(a.UnicodeClipboardFormat=Ko(r),n=r.read_shift(4),n==0||n>40))return a;r.l-=4,a.Reserved2=r.read_shift(0,"lpwstr")}var N2=[60,1084,2066,2165,2175];function L2(e,a,r,n,t){var s=n,i=[],c=r.slice(r.l,r.l+s);if(t&&t.enc&&t.enc.insitu&&c.length>0)switch(e){case 9:case 521:case 1033:case 2057:case 47:case 405:case 225:case 406:case 312:case 404:case 10:break;case 133:break;default:t.enc.insitu(c)}i.push(c),r.l+=s;for(var f=Kr(r,r.l),l=d0[f],o=0;l!=null&&N2.indexOf(f)>-1;)s=Kr(r,r.l+2),o=r.l+4,f==2066?o+=4:(f==2165||f==2175)&&(o+=12),c=r.slice(o,r.l+4+s),i.push(c),r.l+=4+s,l=d0[f=Kr(r,r.l)];var u=aa(i);rr(u,0);var p=0;u.lens=[];for(var h=0;h<i.length;++h)u.lens.push(p),p+=i[h].length;if(u.length<n)throw"XLS Record 0x"+e.toString(16)+" Truncated: "+u.length+" < "+n;return a.f(u,u.length,t)}function Hr(e,a,r){if(e.t!=="z"&&e.XF){var n=0;try{n=e.z||e.XF.numFmtId||0,a.cellNF&&(e.z=_e[n])}catch(s){if(a.WTF)throw s}if(!a||a.cellText!==!1)try{e.t==="e"?e.w=e.w||Ca[e.v]:n===0||n=="General"?e.t==="n"?(e.v|0)===e.v?e.w=e.v.toString(10):e.w=tt(e.v):e.w=Ea(e.v):e.w=Ir(n,e.v,{date1904:!!r,dateNF:a&&a.dateNF})}catch(s){if(a.WTF)throw s}if(a.cellDates&&n&&e.t=="n"&&Ba(_e[n]||String(n))){var t=da(e.v);t&&(e.t="d",e.v=new Date(t.y,t.m-1,t.d,t.H,t.M,t.S,t.u))}}}function Ot(e,a,r){return{v:e,ixfe:a,t:r}}function P2(e,a){var r={opts:{}},n={},t=a.dense?[]:{},s={},i={},c=null,f=[],l="",o={},u,p="",h,d,x,g,w={},y=[],m,O,P=[],D=[],S={Sheets:[],WBProps:{date1904:!1},Views:[{}]},U={},R=function(Te){return Te<8?ma[Te]:Te<64&&D[Te-8]||ma[Te]},z=function(Te,We,Cr){var Ke=We.XF.data;if(!(!Ke||!Ke.patternType||!Cr||!Cr.cellStyles)){We.s={},We.s.patternType=Ke.patternType;var ua;(ua=ct(R(Ke.icvFore)))&&(We.s.fgColor={rgb:ua}),(ua=ct(R(Ke.icvBack)))&&(We.s.bgColor={rgb:ua})}},G=function(Te,We,Cr){if(!(ue>1)&&!(Cr.sheetRows&&Te.r>=Cr.sheetRows)){if(Cr.cellStyles&&We.XF&&We.XF.data&&z(Te,We,Cr),delete We.ixfe,delete We.XF,u=Te,p=ve(Te),(!i||!i.s||!i.e)&&(i={s:{r:0,c:0},e:{r:0,c:0}}),Te.r<i.s.r&&(i.s.r=Te.r),Te.c<i.s.c&&(i.s.c=Te.c),Te.r+1>i.e.r&&(i.e.r=Te.r+1),Te.c+1>i.e.c&&(i.e.c=Te.c+1),Cr.cellFormula&&We.f){for(var Ke=0;Ke<y.length;++Ke)if(!(y[Ke][0].s.c>Te.c||y[Ke][0].s.r>Te.r)&&!(y[Ke][0].e.c<Te.c||y[Ke][0].e.r<Te.r)){We.F=ye(y[Ke][0]),(y[Ke][0].s.c!=Te.c||y[Ke][0].s.r!=Te.r)&&delete We.f,We.f&&(We.f=""+ir(y[Ke][1],i,Te,L,b));break}}Cr.dense?(t[Te.r]||(t[Te.r]=[]),t[Te.r][Te.c]=We):t[p]=We}},b={enc:!1,sbcch:0,snames:[],sharedf:w,arrayf:y,rrtabid:[],lastuser:"",biff:8,codepage:0,winlocked:0,cellStyles:!!a&&!!a.cellStyles,WTF:!!a&&!!a.wtf};a.password&&(b.password=a.password);var j,Y=[],Q=[],se=[],ne=[],De=!1,L=[];L.SheetNames=b.snames,L.sharedf=b.sharedf,L.arrayf=b.arrayf,L.names=[],L.XTI=[];var X=0,ue=0,A=0,B=[],N=[],I;b.codepage=1200,Br(1200);for(var J=!1;e.l<e.length-1;){var Z=e.l,ie=e.read_shift(2);if(ie===0&&X===10)break;var te=e.l===e.length?0:e.read_shift(2),ee=d0[ie];if(ee&&ee.f){if(a.bookSheets&&X===133&&ie!==133)break;if(X=ie,ee.r===2||ee.r==12){var ke=e.read_shift(2);if(te-=2,!b.enc&&ke!==ie&&((ke&255)<<8|ke>>8)!==ie)throw new Error("rt mismatch: "+ke+"!="+ie);ee.r==12&&(e.l+=10,te-=10)}var C={};if(ie===10?C=ee.f(e,te,b):C=L2(ie,ee,e,te,b),ue==0&&[9,521,1033,2057].indexOf(X)===-1)continue;switch(ie){case 34:r.opts.Date1904=S.WBProps.date1904=C;break;case 134:r.opts.WriteProtect=!0;break;case 47:if(b.enc||(e.l=0),b.enc=C,!a.password)throw new Error("File is password-protected");if(C.valid==null)throw new Error("Encryption scheme unsupported");if(!C.valid)throw new Error("Password is incorrect");break;case 92:b.lastuser=C;break;case 66:var Le=Number(C);switch(Le){case 21010:Le=1200;break;case 32768:Le=1e4;break;case 32769:Le=1252;break}Br(b.codepage=Le),J=!0;break;case 317:b.rrtabid=C;break;case 25:b.winlocked=C;break;case 439:r.opts.RefreshAll=C;break;case 12:r.opts.CalcCount=C;break;case 16:r.opts.CalcDelta=C;break;case 17:r.opts.CalcIter=C;break;case 13:r.opts.CalcMode=C;break;case 14:r.opts.CalcPrecision=C;break;case 95:r.opts.CalcSaveRecalc=C;break;case 15:b.CalcRefMode=C;break;case 2211:r.opts.FullCalc=C;break;case 129:C.fDialog&&(t["!type"]="dialog"),C.fBelow||((t["!outline"]||(t["!outline"]={})).above=!0),C.fRight||((t["!outline"]||(t["!outline"]={})).left=!0);break;case 224:P.push(C);break;case 430:L.push([C]),L[L.length-1].XTI=[];break;case 35:case 547:L[L.length-1].push(C);break;case 24:case 536:I={Name:C.Name,Ref:ir(C.rgce,i,null,L,b)},C.itab>0&&(I.Sheet=C.itab-1),L.names.push(I),L[0]||(L[0]=[],L[0].XTI=[]),L[L.length-1].push(C),C.Name=="_xlnm._FilterDatabase"&&C.itab>0&&C.rgce&&C.rgce[0]&&C.rgce[0][0]&&C.rgce[0][0][0]=="PtgArea3d"&&(N[C.itab-1]={ref:ye(C.rgce[0][0][1][2])});break;case 22:b.ExternCount=C;break;case 23:L.length==0&&(L[0]=[],L[0].XTI=[]),L[L.length-1].XTI=L[L.length-1].XTI.concat(C),L.XTI=L.XTI.concat(C);break;case 2196:if(b.biff<8)break;I!=null&&(I.Comment=C[1]);break;case 18:t["!protect"]=C;break;case 19:C!==0&&b.WTF&&console.error("Password verifier: "+C);break;case 133:s[C.pos]=C,b.snames.push(C.name);break;case 10:{if(--ue)break;if(i.e){if(i.e.r>0&&i.e.c>0){if(i.e.r--,i.e.c--,t["!ref"]=ye(i),a.sheetRows&&a.sheetRows<=i.e.r){var Fe=i.e.r;i.e.r=a.sheetRows-1,t["!fullref"]=t["!ref"],t["!ref"]=ye(i),i.e.r=Fe}i.e.r++,i.e.c++}Y.length>0&&(t["!merges"]=Y),Q.length>0&&(t["!objects"]=Q),se.length>0&&(t["!cols"]=se),ne.length>0&&(t["!rows"]=ne),S.Sheets.push(U)}l===""?o=t:n[l]=t,t=a.dense?[]:{}}break;case 9:case 521:case 1033:case 2057:{if(b.biff===8&&(b.biff={9:2,521:3,1033:4}[ie]||{512:2,768:3,1024:4,1280:5,1536:8,2:2,7:2}[C.BIFFVer]||8),b.biffguess=C.BIFFVer==0,C.BIFFVer==0&&C.dt==4096&&(b.biff=5,J=!0,Br(b.codepage=28591)),b.biff==8&&C.BIFFVer==0&&C.dt==16&&(b.biff=2),ue++)break;if(t=a.dense?[]:{},b.biff<8&&!J&&(J=!0,Br(b.codepage=a.codepage||1252)),b.biff<5||C.BIFFVer==0&&C.dt==4096){l===""&&(l="Sheet1"),i={s:{r:0,c:0},e:{r:0,c:0}};var Ie={pos:e.l-te,name:l};s[Ie.pos]=Ie,b.snames.push(l)}else l=(s[Z]||{name:""}).name;C.dt==32&&(t["!type"]="chart"),C.dt==64&&(t["!type"]="macro"),Y=[],Q=[],b.arrayf=y=[],se=[],ne=[],De=!1,U={Hidden:(s[Z]||{hs:0}).hs,name:l}}break;case 515:case 3:case 2:t["!type"]=="chart"&&(a.dense?(t[C.r]||[])[C.c]:t[ve({c:C.c,r:C.r})])&&++C.c,m={ixfe:C.ixfe,XF:P[C.ixfe]||{},v:C.val,t:"n"},A>0&&(m.z=B[m.ixfe>>8&63]),Hr(m,a,r.opts.Date1904),G({c:C.c,r:C.r},m,a);break;case 5:case 517:m={ixfe:C.ixfe,XF:P[C.ixfe],v:C.val,t:C.t},A>0&&(m.z=B[m.ixfe>>8&63]),Hr(m,a,r.opts.Date1904),G({c:C.c,r:C.r},m,a);break;case 638:m={ixfe:C.ixfe,XF:P[C.ixfe],v:C.rknum,t:"n"},A>0&&(m.z=B[m.ixfe>>8&63]),Hr(m,a,r.opts.Date1904),G({c:C.c,r:C.r},m,a);break;case 189:for(var Ee=C.c;Ee<=C.C;++Ee){var fe=C.rkrec[Ee-C.c][0];m={ixfe:fe,XF:P[fe],v:C.rkrec[Ee-C.c][1],t:"n"},A>0&&(m.z=B[m.ixfe>>8&63]),Hr(m,a,r.opts.Date1904),G({c:Ee,r:C.r},m,a)}break;case 6:case 518:case 1030:{if(C.val=="String"){c=C;break}if(m=Ot(C.val,C.cell.ixfe,C.tt),m.XF=P[m.ixfe],a.cellFormula){var Pe=C.formula;if(Pe&&Pe[0]&&Pe[0][0]&&Pe[0][0][0]=="PtgExp"){var hr=Pe[0][0][1][0],kr=Pe[0][0][1][1],Sr=ve({r:hr,c:kr});w[Sr]?m.f=""+ir(C.formula,i,C.cell,L,b):m.F=((a.dense?(t[hr]||[])[kr]:t[Sr])||{}).F}else m.f=""+ir(C.formula,i,C.cell,L,b)}A>0&&(m.z=B[m.ixfe>>8&63]),Hr(m,a,r.opts.Date1904),G(C.cell,m,a),c=C}break;case 7:case 519:if(c)c.val=C,m=Ot(C,c.cell.ixfe,"s"),m.XF=P[m.ixfe],a.cellFormula&&(m.f=""+ir(c.formula,i,c.cell,L,b)),A>0&&(m.z=B[m.ixfe>>8&63]),Hr(m,a,r.opts.Date1904),G(c.cell,m,a),c=null;else throw new Error("String record expects Formula");break;case 33:case 545:{y.push(C);var Qr=ve(C[0].s);if(h=a.dense?(t[C[0].s.r]||[])[C[0].s.c]:t[Qr],a.cellFormula&&h){if(!c||!Qr||!h)break;h.f=""+ir(C[1],i,C[0],L,b),h.F=ye(C[0])}}break;case 1212:{if(!a.cellFormula)break;if(p){if(!c)break;w[ve(c.cell)]=C[0],h=a.dense?(t[c.cell.r]||[])[c.cell.c]:t[ve(c.cell)],(h||{}).f=""+ir(C[0],i,u,L,b)}}break;case 253:m=Ot(f[C.isst].t,C.ixfe,"s"),f[C.isst].h&&(m.h=f[C.isst].h),m.XF=P[m.ixfe],A>0&&(m.z=B[m.ixfe>>8&63]),Hr(m,a,r.opts.Date1904),G({c:C.c,r:C.r},m,a);break;case 513:a.sheetStubs&&(m={ixfe:C.ixfe,XF:P[C.ixfe],t:"z"},A>0&&(m.z=B[m.ixfe>>8&63]),Hr(m,a,r.opts.Date1904),G({c:C.c,r:C.r},m,a));break;case 190:if(a.sheetStubs)for(var Nr=C.c;Nr<=C.C;++Nr){var we=C.ixfe[Nr-C.c];m={ixfe:we,XF:P[we],t:"z"},A>0&&(m.z=B[m.ixfe>>8&63]),Hr(m,a,r.opts.Date1904),G({c:Nr,r:C.r},m,a)}break;case 214:case 516:case 4:m=Ot(C.val,C.ixfe,"s"),m.XF=P[m.ixfe],A>0&&(m.z=B[m.ixfe>>8&63]),Hr(m,a,r.opts.Date1904),G({c:C.c,r:C.r},m,a);break;case 0:case 512:ue===1&&(i=C);break;case 252:f=C;break;case 1054:if(b.biff==4){B[A++]=C[1];for(var _r=0;_r<A+163&&_e[_r]!=C[1];++_r);_r>=163&&ga(C[1],A+163)}else ga(C[1],C[0]);break;case 30:{B[A++]=C;for(var ea=0;ea<A+163&&_e[ea]!=C;++ea);ea>=163&&ga(C,A+163)}break;case 229:Y=Y.concat(C);break;case 93:Q[C.cmo[0]]=b.lastobj=C;break;case 438:b.lastobj.TxO=C;break;case 127:b.lastobj.ImData=C;break;case 440:for(g=C[0].s.r;g<=C[0].e.r;++g)for(x=C[0].s.c;x<=C[0].e.c;++x)h=a.dense?(t[g]||[])[x]:t[ve({c:x,r:g})],h&&(h.l=C[1]);break;case 2048:for(g=C[0].s.r;g<=C[0].e.r;++g)for(x=C[0].s.c;x<=C[0].e.c;++x)h=a.dense?(t[g]||[])[x]:t[ve({c:x,r:g})],h&&h.l&&(h.l.Tooltip=C[1]);break;case 28:{if(b.biff<=5&&b.biff>=2)break;h=a.dense?(t[C[0].r]||[])[C[0].c]:t[ve(C[0])];var Va=Q[C[2]];h||(a.dense?(t[C[0].r]||(t[C[0].r]=[]),h=t[C[0].r][C[0].c]={t:"z"}):h=t[ve(C[0])]={t:"z"},i.e.r=Math.max(i.e.r,C[0].r),i.s.r=Math.min(i.s.r,C[0].r),i.e.c=Math.max(i.e.c,C[0].c),i.s.c=Math.min(i.s.c,C[0].c)),h.c||(h.c=[]),d={a:C[1],t:Va.TxO.t},h.c.push(d)}break;case 2173:th(P[C.ixfe],C.ext);break;case 125:{if(!b.cellStyles)break;for(;C.e>=C.s;)se[C.e--]={width:C.w/256,level:C.level||0,hidden:!!(C.flags&1)},De||(De=!0,B0(C.w/256)),ba(se[C.e+1])}break;case 520:{var xr={};C.level!=null&&(ne[C.r]=xr,xr.level=C.level),C.hidden&&(ne[C.r]=xr,xr.hidden=!0),C.hpt&&(ne[C.r]=xr,xr.hpt=C.hpt,xr.hpx=ft(C.hpt))}break;case 38:case 39:case 40:case 41:t["!margins"]||rt(t["!margins"]={}),t["!margins"][{38:"left",39:"right",40:"top",41:"bottom"}[ie]]=C;break;case 161:t["!margins"]||rt(t["!margins"]={}),t["!margins"].header=C.header,t["!margins"].footer=C.footer;break;case 574:C.RTL&&(S.Views[0].RTL=!0);break;case 146:D=C;break;case 2198:j=C;break;case 140:O=C;break;case 442:l?U.CodeName=C||U.name:S.WBProps.CodeName=C||"ThisWorkbook";break}}else ee||console.error("Missing Info for XLS Record 0x"+ie.toString(16)),e.l+=te}return r.SheetNames=Gr(s).sort(function(Vr,Te){return Number(Vr)-Number(Te)}).map(function(Vr){return s[Vr].name}),a.bookSheets||(r.Sheets=n),!r.SheetNames.length&&o["!ref"]?(r.SheetNames.push("Sheet1"),r.Sheets&&(r.Sheets.Sheet1=o)):r.Preamble=o,r.Sheets&&N.forEach(function(Vr,Te){r.Sheets[r.SheetNames[Te]]["!autofilter"]=Vr}),r.Strings=f,r.SSF=ar(_e),b.enc&&(r.Encryption=b.enc),j&&(r.Themes=j),r.Metadata={},O!==void 0&&(r.Metadata.Country=O),L.names.length>0&&(S.Names=L.names),r.Workbook=S,r}var Yn={SI:"e0859ff2f94f6810ab9108002b27b3d9",DSI:"02d5cdd59c2e1b10939708002b2cf9ae",UDI:"05d5cdd59c2e1b10939708002b2cf9ae"};function b2(e,a,r){var n=Ce.find(e,"/!DocumentSummaryInformation");if(n&&n.size>0)try{var t=In(n,Qo,Yn.DSI);for(var s in t)a[s]=t[s]}catch(l){if(r.WTF)throw l}var i=Ce.find(e,"/!SummaryInformation");if(i&&i.size>0)try{var c=In(i,el,Yn.SI);for(var f in c)a[f]==null&&(a[f]=c[f])}catch(l){if(r.WTF)throw l}a.HeadingPairs&&a.TitlesOfParts&&(Zs(a.HeadingPairs,a.TitlesOfParts,a,r),delete a.HeadingPairs,delete a.TitlesOfParts)}function Li(e,a){a||(a={}),W0(a),os(),a.codepage&&E0(a.codepage);var r,n;if(e.FullPaths){if(Ce.find(e,"/encryption"))throw new Error("File is password-protected");r=Ce.find(e,"!CompObj"),n=Ce.find(e,"/Workbook")||Ce.find(e,"/Book")}else{switch(a.type){case"base64":e=br(Fr(e));break;case"binary":e=br(e);break;case"buffer":break;case"array":Array.isArray(e)||(e=Array.prototype.slice.call(e));break}rr(e,0),n={content:e}}var t,s;if(r&&R2(r),a.bookProps&&!a.bookSheets)t={};else{var i=Se?"buffer":"array";if(n&&n.content)t=P2(n.content,a);else if((s=Ce.find(e,"PerfectOffice_MAIN"))&&s.content)t=Qa.to_workbook(s.content,(a.type=i,a));else if((s=Ce.find(e,"NativeContent_MAIN"))&&s.content)t=Qa.to_workbook(s.content,(a.type=i,a));else throw(s=Ce.find(e,"MN0"))&&s.content?new Error("Unsupported Works 4 for Mac file"):new Error("Cannot find Workbook stream");a.bookVBA&&e.FullPaths&&Ce.find(e,"/_VBA_PROJECT_CUR/VBA/dir")&&(t.vbaraw=Th(e))}var c={};return e.FullPaths&&b2(e,c,a),t.Props=t.Custprops=c,a.bookFiles&&(t.cfb=e),t}var Ht={0:{f:gd},1:{f:Td},2:{f:Od},3:{f:Fd},4:{f:wd},5:{f:Dd},6:{f:Nd},7:{f:Cd},8:{f:Md},9:{f:Bd},10:{f:Pd},11:{f:bd},12:{f:kd},13:{f:Id},14:{f:Sd},15:{f:Ad},16:{f:Di},17:{f:Ld},18:{f:yd},19:{f:I0},20:{},21:{},22:{},23:{},24:{},25:{},26:{},27:{},28:{},29:{},30:{},31:{},32:{},33:{},34:{},35:{T:1},36:{T:-1},37:{T:1},38:{T:-1},39:{f:u2},40:{},42:{},43:{f:Pu},44:{f:Lu},45:{f:bu},46:{f:Mu},47:{f:Bu},48:{},49:{f:Uo},50:{},51:{f:sh},52:{T:1},53:{T:-1},54:{T:1},55:{T:-1},56:{T:1},57:{T:-1},58:{},59:{},60:{f:oi},62:{f:Rd},63:{f:lh},64:{f:Kd},65:{},66:{},67:{},68:{},69:{},70:{},128:{},129:{T:1},130:{T:-1},131:{T:1,f:lr,p:0},132:{T:-1},133:{T:1},134:{T:-1},135:{T:1},136:{T:-1},137:{T:1,f:zd},138:{T:-1},139:{T:1},140:{T:-1},141:{T:1},142:{T:-1},143:{T:1},144:{T:-1},145:{T:1},146:{T:-1},147:{f:Ed},148:{f:md,p:16},151:{f:Hd},152:{},153:{f:o2},154:{},155:{},156:{f:f2},157:{},158:{},159:{T:1,f:eu},160:{T:-1},161:{T:1,f:Sa},162:{T:-1},163:{T:1},164:{T:-1},165:{T:1},166:{T:-1},167:{},168:{},169:{},170:{},171:{},172:{T:1},173:{T:-1},174:{},175:{},176:{f:Ud},177:{T:1},178:{T:-1},179:{T:1},180:{T:-1},181:{T:1},182:{T:-1},183:{T:1},184:{T:-1},185:{T:1},186:{T:-1},187:{T:1},188:{T:-1},189:{T:1},190:{T:-1},191:{T:1},192:{T:-1},193:{T:1},194:{T:-1},195:{T:1},196:{T:-1},197:{T:1},198:{T:-1},199:{T:1},200:{T:-1},201:{T:1},202:{T:-1},203:{T:1},204:{T:-1},205:{T:1},206:{T:-1},207:{T:1},208:{T:-1},209:{T:1},210:{T:-1},211:{T:1},212:{T:-1},213:{T:1},214:{T:-1},215:{T:1},216:{T:-1},217:{T:1},218:{T:-1},219:{T:1},220:{T:-1},221:{T:1},222:{T:-1},223:{T:1},224:{T:-1},225:{T:1},226:{T:-1},227:{T:1},228:{T:-1},229:{T:1},230:{T:-1},231:{T:1},232:{T:-1},233:{T:1},234:{T:-1},235:{T:1},236:{T:-1},237:{T:1},238:{T:-1},239:{T:1},240:{T:-1},241:{T:1},242:{T:-1},243:{T:1},244:{T:-1},245:{T:1},246:{T:-1},247:{T:1},248:{T:-1},249:{T:1},250:{T:-1},251:{T:1},252:{T:-1},253:{T:1},254:{T:-1},255:{T:1},256:{T:-1},257:{T:1},258:{T:-1},259:{T:1},260:{T:-1},261:{T:1},262:{T:-1},263:{T:1},264:{T:-1},265:{T:1},266:{T:-1},267:{T:1},268:{T:-1},269:{T:1},270:{T:-1},271:{T:1},272:{T:-1},273:{T:1},274:{T:-1},275:{T:1},276:{T:-1},277:{},278:{T:1},279:{T:-1},280:{T:1},281:{T:-1},282:{T:1},283:{T:1},284:{T:-1},285:{T:1},286:{T:-1},287:{T:1},288:{T:-1},289:{T:1},290:{T:-1},291:{T:1},292:{T:-1},293:{T:1},294:{T:-1},295:{T:1},296:{T:-1},297:{T:1},298:{T:-1},299:{T:1},300:{T:-1},301:{T:1},302:{T:-1},303:{T:1},304:{T:-1},305:{T:1},306:{T:-1},307:{T:1},308:{T:-1},309:{T:1},310:{T:-1},311:{T:1},312:{T:-1},313:{T:-1},314:{T:1},315:{T:-1},316:{T:1},317:{T:-1},318:{T:1},319:{T:-1},320:{T:1},321:{T:-1},322:{T:1},323:{T:-1},324:{T:1},325:{T:-1},326:{T:1},327:{T:-1},328:{T:1},329:{T:-1},330:{T:1},331:{T:-1},332:{T:1},333:{T:-1},334:{T:1},335:{f:nh},336:{T:-1},337:{f:ih,T:1},338:{T:-1},339:{T:1},340:{T:-1},341:{T:1},342:{T:-1},343:{T:1},344:{T:-1},345:{T:1},346:{T:-1},347:{T:1},348:{T:-1},349:{T:1},350:{T:-1},351:{},352:{},353:{T:1},354:{T:-1},355:{f:o0},357:{},358:{},359:{},360:{T:1},361:{},362:{f:fi},363:{},364:{},366:{},367:{},368:{},369:{},370:{},371:{},372:{T:1},373:{T:-1},374:{T:1},375:{T:-1},376:{T:1},377:{T:-1},378:{T:1},379:{T:-1},380:{T:1},381:{T:-1},382:{T:1},383:{T:-1},384:{T:1},385:{T:-1},386:{T:1},387:{T:-1},388:{T:1},389:{T:-1},390:{T:1},391:{T:-1},392:{T:1},393:{T:-1},394:{T:1},395:{T:-1},396:{},397:{},398:{},399:{},400:{},401:{T:1},403:{},404:{},405:{},406:{},407:{},408:{},409:{},410:{},411:{},412:{},413:{},414:{},415:{},416:{},417:{},418:{},419:{},420:{},421:{},422:{T:1},423:{T:1},424:{T:-1},425:{T:-1},426:{f:Wd},427:{f:Gd},428:{},429:{T:1},430:{T:-1},431:{T:1},432:{T:-1},433:{T:1},434:{T:-1},435:{T:1},436:{T:-1},437:{T:1},438:{T:-1},439:{T:1},440:{T:-1},441:{T:1},442:{T:-1},443:{T:1},444:{T:-1},445:{T:1},446:{T:-1},447:{T:1},448:{T:-1},449:{T:1},450:{T:-1},451:{T:1},452:{T:-1},453:{T:1},454:{T:-1},455:{T:1},456:{T:-1},457:{T:1},458:{T:-1},459:{T:1},460:{T:-1},461:{T:1},462:{T:-1},463:{T:1},464:{T:-1},465:{T:1},466:{T:-1},467:{T:1},468:{T:-1},469:{T:1},470:{T:-1},471:{},472:{},473:{T:1},474:{T:-1},475:{},476:{f:Xd},477:{},478:{},479:{T:1},480:{T:-1},481:{T:1},482:{T:-1},483:{T:1},484:{T:-1},485:{f:_d},486:{T:1},487:{T:-1},488:{T:1},489:{T:-1},490:{T:1},491:{T:-1},492:{T:1},493:{T:-1},494:{f:Vd},495:{T:1},496:{T:-1},497:{T:1},498:{T:-1},499:{},500:{T:1},501:{T:-1},502:{T:1},503:{T:-1},504:{},505:{T:1},506:{T:-1},507:{},508:{T:1},509:{T:-1},510:{T:1},511:{T:-1},512:{},513:{},514:{T:1},515:{T:-1},516:{T:1},517:{T:-1},518:{T:1},519:{T:-1},520:{T:1},521:{T:-1},522:{},523:{},524:{},525:{},526:{},527:{},528:{T:1},529:{T:-1},530:{T:1},531:{T:-1},532:{T:1},533:{T:-1},534:{},535:{},536:{},537:{},538:{T:1},539:{T:-1},540:{T:1},541:{T:-1},542:{T:1},548:{},549:{},550:{f:o0},551:{},552:{},553:{},554:{T:1},555:{T:-1},556:{T:1},557:{T:-1},558:{T:1},559:{T:-1},560:{T:1},561:{T:-1},562:{},564:{},565:{T:1},566:{T:-1},569:{T:1},570:{T:-1},572:{},573:{T:1},574:{T:-1},577:{},578:{},579:{},580:{},581:{},582:{},583:{},584:{},585:{},586:{},587:{},588:{T:-1},589:{},590:{T:1},591:{T:-1},592:{T:1},593:{T:-1},594:{T:1},595:{T:-1},596:{},597:{T:1},598:{T:-1},599:{T:1},600:{T:-1},601:{T:1},602:{T:-1},603:{T:1},604:{T:-1},605:{T:1},606:{T:-1},607:{},608:{T:1},609:{T:-1},610:{},611:{T:1},612:{T:-1},613:{T:1},614:{T:-1},615:{T:1},616:{T:-1},617:{T:1},618:{T:-1},619:{T:1},620:{T:-1},625:{},626:{T:1},627:{T:-1},628:{T:1},629:{T:-1},630:{T:1},631:{T:-1},632:{f:mh},633:{T:1},634:{T:-1},635:{T:1,f:gh},636:{T:-1},637:{f:Ho},638:{T:1},639:{},640:{T:-1},641:{T:1},642:{T:-1},643:{T:1},644:{},645:{T:-1},646:{T:1},648:{T:1},649:{},650:{T:-1},651:{f:Qd},652:{},653:{T:1},654:{T:-1},655:{T:1},656:{T:-1},657:{T:1},658:{T:-1},659:{},660:{T:1},661:{},662:{T:-1},663:{},664:{T:1},665:{},666:{T:-1},667:{},668:{},669:{},671:{T:1},672:{T:-1},673:{T:1},674:{T:-1},675:{},676:{},677:{},678:{},679:{},680:{},681:{},1024:{},1025:{},1026:{T:1},1027:{T:-1},1028:{T:1},1029:{T:-1},1030:{},1031:{T:1},1032:{T:-1},1033:{T:1},1034:{T:-1},1035:{},1036:{},1037:{},1038:{T:1},1039:{T:-1},1040:{},1041:{T:1},1042:{T:-1},1043:{},1044:{},1045:{},1046:{T:1},1047:{T:-1},1048:{T:1},1049:{T:-1},1050:{},1051:{T:1},1052:{T:1},1053:{f:Yd},1054:{T:1},1055:{},1056:{T:1},1057:{T:-1},1058:{T:1},1059:{T:-1},1061:{},1062:{T:1},1063:{T:-1},1064:{T:1},1065:{T:-1},1066:{T:1},1067:{T:-1},1068:{T:1},1069:{T:-1},1070:{T:1},1071:{T:-1},1072:{T:1},1073:{T:-1},1075:{T:1},1076:{T:-1},1077:{T:1},1078:{T:-1},1079:{T:1},1080:{T:-1},1081:{T:1},1082:{T:-1},1083:{T:1},1084:{T:-1},1085:{},1086:{T:1},1087:{T:-1},1088:{T:1},1089:{T:-1},1090:{T:1},1091:{T:-1},1092:{T:1},1093:{T:-1},1094:{T:1},1095:{T:-1},1096:{},1097:{T:1},1098:{},1099:{T:-1},1100:{T:1},1101:{T:-1},1102:{},1103:{},1104:{},1105:{},1111:{},1112:{},1113:{T:1},1114:{T:-1},1115:{T:1},1116:{T:-1},1117:{},1118:{T:1},1119:{T:-1},1120:{T:1},1121:{T:-1},1122:{T:1},1123:{T:-1},1124:{T:1},1125:{T:-1},1126:{},1128:{T:1},1129:{T:-1},1130:{},1131:{T:1},1132:{T:-1},1133:{T:1},1134:{T:-1},1135:{T:1},1136:{T:-1},1137:{T:1},1138:{T:-1},1139:{T:1},1140:{T:-1},1141:{},1142:{T:1},1143:{T:-1},1144:{T:1},1145:{T:-1},1146:{},1147:{T:1},1148:{T:-1},1149:{T:1},1150:{T:-1},1152:{T:1},1153:{T:-1},1154:{T:-1},1155:{T:-1},1156:{T:-1},1157:{T:1},1158:{T:-1},1159:{T:1},1160:{T:-1},1161:{T:1},1162:{T:-1},1163:{T:1},1164:{T:-1},1165:{T:1},1166:{T:-1},1167:{T:1},1168:{T:-1},1169:{T:1},1170:{T:-1},1171:{},1172:{T:1},1173:{T:-1},1177:{},1178:{T:1},1180:{},1181:{},1182:{},2048:{T:1},2049:{T:-1},2050:{},2051:{T:1},2052:{T:-1},2053:{},2054:{},2055:{T:1},2056:{T:-1},2057:{T:1},2058:{T:-1},2060:{},2067:{},2068:{T:1},2069:{T:-1},2070:{},2071:{},2072:{T:1},2073:{T:-1},2075:{},2076:{},2077:{T:1},2078:{T:-1},2079:{},2080:{T:1},2081:{T:-1},2082:{},2083:{T:1},2084:{T:-1},2085:{T:1},2086:{T:-1},2087:{T:1},2088:{T:-1},2089:{T:1},2090:{T:-1},2091:{},2092:{},2093:{T:1},2094:{T:-1},2095:{},2096:{T:1},2097:{T:-1},2098:{T:1},2099:{T:-1},2100:{T:1},2101:{T:-1},2102:{},2103:{T:1},2104:{T:-1},2105:{},2106:{T:1},2107:{T:-1},2108:{},2109:{T:1},2110:{T:-1},2111:{T:1},2112:{T:-1},2113:{T:1},2114:{T:-1},2115:{},2116:{},2117:{},2118:{T:1},2119:{T:-1},2120:{},2121:{T:1},2122:{T:-1},2123:{T:1},2124:{T:-1},2125:{},2126:{T:1},2127:{T:-1},2128:{},2129:{T:1},2130:{T:-1},2131:{T:1},2132:{T:-1},2133:{T:1},2134:{},2135:{},2136:{},2137:{T:1},2138:{T:-1},2139:{T:1},2140:{T:-1},2141:{},3072:{},3073:{},4096:{T:1},4097:{T:-1},5002:{T:1},5003:{T:-1},5081:{T:1},5082:{T:-1},5083:{},5084:{T:1},5085:{T:-1},5086:{T:1},5087:{T:-1},5088:{},5089:{},5090:{},5092:{T:1},5093:{T:-1},5094:{},5095:{T:1},5096:{T:-1},5097:{},5099:{},65535:{n:""}},d0={6:{f:r0},10:{f:ra},12:{f:ze},13:{f:ze},14:{f:Ge},15:{f:Ge},16:{f:cr},17:{f:Ge},18:{f:Ge},19:{f:ze},20:{f:Pn},21:{f:Pn},23:{f:fi},24:{f:Bn},25:{f:Ge},26:{},27:{},28:{f:p1},29:{},34:{f:Ge},35:{f:bn},38:{f:cr},39:{f:cr},40:{f:cr},41:{f:cr},42:{f:Ge},43:{f:Ge},47:{f:_u},49:{f:Jl},51:{f:ze},60:{},61:{f:Kl},64:{f:Ge},65:{f:jl},66:{f:ze},77:{},80:{},81:{},82:{},85:{f:ze},89:{},90:{},91:{},92:{f:Ul},93:{f:g1},94:{},95:{f:Ge},96:{},97:{},99:{f:Ge},125:{f:oi},128:{f:i1},129:{f:Vl},130:{f:ze},131:{f:Ge},132:{f:Ge},133:{f:Hl},134:{},140:{f:w1},141:{f:ze},144:{},146:{f:F1},151:{},152:{},153:{},154:{},155:{},156:{f:ze},157:{},158:{},160:{f:O1},161:{f:C1},174:{},175:{},176:{},177:{},178:{},180:{},181:{},182:{},184:{},185:{},189:{f:a1},190:{f:t1},193:{f:ra},197:{},198:{},199:{},200:{},201:{},202:{f:Ge},203:{},204:{},205:{},206:{},207:{},208:{},209:{},210:{},211:{},213:{},215:{},216:{},217:{},218:{f:ze},220:{},221:{f:Ge},222:{},224:{f:s1},225:{f:Ml},226:{f:ra},227:{},229:{f:v1},233:{},235:{},236:{},237:{},239:{},240:{},241:{},242:{},244:{},245:{},246:{},247:{},248:{},249:{},251:{},252:{f:Wl},253:{f:Zl},255:{f:Gl},256:{},259:{},290:{},311:{},312:{},315:{},317:{f:ai},318:{},319:{},320:{},330:{},331:{},333:{},334:{},335:{},336:{},337:{},338:{},339:{},340:{},351:{},352:{f:Ge},353:{f:ra},401:{},402:{},403:{},404:{},405:{},406:{},407:{},408:{},425:{},426:{},427:{},428:{},429:{},430:{f:f1},431:{f:Ge},432:{},433:{},434:{},437:{},438:{f:E1},439:{f:Ge},440:{f:T1},441:{},442:{f:gt},443:{},444:{f:ze},445:{},446:{},448:{f:ra},449:{f:zl,r:2},450:{f:ra},512:{f:Nn},513:{f:D1},515:{f:c1},516:{f:ql},517:{f:Ln},519:{f:I1},520:{f:$l},523:{},545:{f:Mn},549:{f:Rn},566:{},574:{f:Yl},638:{f:r1},659:{},1048:{},1054:{f:Ql},1084:{},1212:{f:h1},2048:{f:k1},2049:{},2050:{},2051:{},2052:{},2053:{},2054:{},2055:{},2056:{},2057:{f:Ct},2058:{},2059:{},2060:{},2061:{},2062:{},2063:{},2064:{},2066:{},2067:{},2128:{},2129:{},2130:{},2131:{},2132:{},2133:{},2134:{},2135:{},2136:{},2137:{},2138:{},2146:{},2147:{r:12},2148:{},2149:{},2150:{},2151:{f:ra},2152:{},2154:{},2155:{},2156:{},2161:{},2162:{},2164:{},2165:{},2166:{},2167:{},2168:{},2169:{},2170:{},2171:{},2172:{f:S1,r:12},2173:{f:ah,r:12},2174:{},2175:{},2180:{},2181:{},2182:{},2183:{},2184:{},2185:{},2186:{},2187:{},2188:{f:Ge,r:12},2189:{},2190:{r:12},2191:{},2192:{},2194:{},2195:{},2196:{f:u1,r:12},2197:{},2198:{f:Ju,r:12},2199:{},2200:{},2201:{},2202:{f:x1,r:12},2203:{f:ra},2204:{},2205:{},2206:{},2207:{},2211:{f:Xl},2212:{},2213:{},2214:{},2215:{},4097:{},4098:{},4099:{},4102:{},4103:{},4105:{},4106:{},4107:{},4108:{},4109:{},4116:{},4117:{},4118:{},4119:{},4120:{},4121:{},4122:{},4123:{},4124:{},4125:{},4126:{},4127:{},4128:{},4129:{},4130:{},4132:{},4133:{},4134:{f:ze},4135:{},4146:{},4147:{},4148:{},4149:{},4154:{},4156:{},4157:{},4158:{},4159:{},4160:{},4161:{},4163:{},4164:{f:y1},4165:{},4166:{},4168:{},4170:{},4171:{},4174:{},4175:{},4176:{},4177:{},4187:{},4188:{f:A1},4189:{},4191:{},4192:{},4193:{},4194:{},4195:{},4196:{},4197:{},4198:{},4199:{},4200:{},0:{f:Nn},1:{},2:{f:P1},3:{f:L1},4:{f:N1},5:{f:Ln},7:{f:b1},8:{},9:{f:Ct},11:{},22:{f:ze},30:{f:e1},31:{},32:{},33:{f:Mn},36:{},37:{f:Rn},50:{f:B1},62:{},52:{},67:{},68:{f:ze},69:{},86:{},126:{},127:{f:R1},135:{},136:{},137:{},145:{},148:{},149:{},150:{},169:{},171:{},188:{},191:{},192:{},194:{},195:{},214:{f:M1},223:{},234:{},354:{},421:{},518:{f:r0},521:{f:Ct},536:{f:Bn},547:{f:bn},561:{},579:{},1030:{f:r0},1033:{f:Ct},1091:{},2157:{},2163:{},2177:{},2240:{},2241:{},2242:{},2243:{},2244:{},2245:{},2246:{},2247:{},2248:{},2249:{},2250:{},2251:{},2262:{r:12},29282:{}};function Pr(e,a,r,n){var t=a;if(!isNaN(t)){var s=(r||[]).length||0,i=e.next(4);i.write_shift(2,t),i.write_shift(2,s),s>0&&Gs(r)&&e.push(r)}}function jn(e,a){var r=a||{},n=r.dense?[]:{};e=e.replace(/<!--.*?-->/g,"");var t=e.match(/<table/i);if(!t)throw new Error("Invalid HTML: could not find <table>");var s=e.match(/<\/table/i),i=t.index,c=s&&s.index||e.length,f=io(e.slice(i,c),/(:?<tr[^>]*>)/i,"<tr>"),l=-1,o=0,u=0,p=0,h={s:{r:1e7,c:1e7},e:{r:0,c:0}},d=[];for(i=0;i<f.length;++i){var x=f[i].trim(),g=x.slice(0,3).toLowerCase();if(g=="<tr"){if(++l,r.sheetRows&&r.sheetRows<=l){--l;break}o=0;continue}if(!(g!="<td"&&g!="<th")){var w=x.split(/<\/t[dh]>/i);for(c=0;c<w.length;++c){var y=w[c].trim();if(y.match(/<t[dh]/i)){for(var m=y,O=0;m.charAt(0)=="<"&&(O=m.indexOf(">"))>-1;)m=m.slice(O+1);for(var P=0;P<d.length;++P){var D=d[P];D.s.c==o&&D.s.r<l&&l<=D.e.r&&(o=D.e.c+1,P=-1)}var S=pe(y.slice(0,y.indexOf(">")));p=S.colspan?+S.colspan:1,((u=+S.rowspan)>1||p>1)&&d.push({s:{r:l,c:o},e:{r:l+(u||1)-1,c:o+p-1}});var U=S.t||S["data-t"]||"";if(!m.length){o+=p;continue}if(m=Os(m),h.s.r>l&&(h.s.r=l),h.e.r<l&&(h.e.r=l),h.s.c>o&&(h.s.c=o),h.e.c<o&&(h.e.c=o),!m.length){o+=p;continue}var R={t:"s",v:m};r.raw||!m.trim().length||U=="s"||(m==="TRUE"?R={t:"b",v:!0}:m==="FALSE"?R={t:"b",v:!1}:isNaN(Ur(m))?isNaN(Pa(m).getDate())||(R={t:"d",v:er(m)},r.cellDates||(R={t:"n",v:mr(R.v)}),R.z=r.dateNF||_e[14]):R={t:"n",v:Ur(m)}),r.dense?(n[l]||(n[l]=[]),n[l][o]=R):n[ve({r:l,c:o})]=R,o+=p}}}}return n["!ref"]=ye(h),d.length&&(n["!merges"]=d),n}function B2(e,a,r,n){for(var t=e["!merges"]||[],s=[],i=a.s.c;i<=a.e.c;++i){for(var c=0,f=0,l=0;l<t.length;++l)if(!(t[l].s.r>r||t[l].s.c>i)&&!(t[l].e.r<r||t[l].e.c<i)){if(t[l].s.r<r||t[l].s.c<i){c=-1;break}c=t[l].e.r-t[l].s.r+1,f=t[l].e.c-t[l].s.c+1;break}if(!(c<0)){var o=ve({r,c:i}),u=n.dense?(e[r]||[])[i]:e[o],p=u&&u.v!=null&&(u.h||S0(u.w||(Zr(u),u.w)||""))||"",h={};c>1&&(h.rowspan=c),f>1&&(h.colspan=f),n.editable?p='<span contenteditable="true">'+p+"</span>":u&&(h["data-t"]=u&&u.t||"z",u.v!=null&&(h["data-v"]=u.v),u.z!=null&&(h["data-z"]=u.z),u.l&&(u.l.Target||"#").charAt(0)!="#"&&(p='<a href="'+u.l.Target+'">'+p+"</a>")),h.id=(n.id||"sjs")+"-"+o,s.push(wo("td",p,h))}}var d="<tr>";return d+s.join("")+"</tr>"}var M2='<html><head><meta charset="utf-8"/><title>SheetJS Table Export</title></head><body>',U2="</body></html>";function V2(e,a){var r=e.match(/<table[\s\S]*?>[\s\S]*?<\/table>/gi);if(!r||r.length==0)throw new Error("Invalid HTML: could not find <table>");if(r.length==1)return la(jn(r[0],a),a);var n=$0();return r.forEach(function(t,s){X0(n,jn(t,a),"Sheet"+(s+1))}),n}function H2(e,a,r){var n=[];return n.join("")+"<table"+(r&&r.id?' id="'+r.id+'"':"")+">"}function W2(e,a){var r=a||{},n=r.header!=null?r.header:M2,t=r.footer!=null?r.footer:U2,s=[n],i=Ma(e["!ref"]);r.dense=Array.isArray(e),s.push(H2(e,i,r));for(var c=i.s.r;c<=i.e.r;++c)s.push(B2(e,i,c,r));return s.push("</table>"+t),s.join("")}function Pi(e,a,r){var n=r||{},t=0,s=0;if(n.origin!=null)if(typeof n.origin=="number")t=n.origin;else{var i=typeof n.origin=="string"?vr(n.origin):n.origin;t=i.r,s=i.c}var c=a.getElementsByTagName("tr"),f=Math.min(n.sheetRows||1e7,c.length),l={s:{r:0,c:0},e:{r:t,c:s}};if(e["!ref"]){var o=Ma(e["!ref"]);l.s.r=Math.min(l.s.r,o.s.r),l.s.c=Math.min(l.s.c,o.s.c),l.e.r=Math.max(l.e.r,o.e.r),l.e.c=Math.max(l.e.c,o.e.c),t==-1&&(l.e.r=t=o.e.r+1)}var u=[],p=0,h=e["!rows"]||(e["!rows"]=[]),d=0,x=0,g=0,w=0,y=0,m=0;for(e["!cols"]||(e["!cols"]=[]);d<c.length&&x<f;++d){var O=c[d];if(Jn(O)){if(n.display)continue;h[x]={hidden:!0}}var P=O.children;for(g=w=0;g<P.length;++g){var D=P[g];if(!(n.display&&Jn(D))){var S=D.hasAttribute("data-v")?D.getAttribute("data-v"):D.hasAttribute("v")?D.getAttribute("v"):Os(D.innerHTML),U=D.getAttribute("data-z")||D.getAttribute("z");for(p=0;p<u.length;++p){var R=u[p];R.s.c==w+s&&R.s.r<x+t&&x+t<=R.e.r&&(w=R.e.c+1-s,p=-1)}m=+D.getAttribute("colspan")||1,((y=+D.getAttribute("rowspan")||1)>1||m>1)&&u.push({s:{r:x+t,c:w+s},e:{r:x+t+(y||1)-1,c:w+s+(m||1)-1}});var z={t:"s",v:S},G=D.getAttribute("data-t")||D.getAttribute("t")||"";S!=null&&(S.length==0?z.t=G||"z":n.raw||S.trim().length==0||G=="s"||(S==="TRUE"?z={t:"b",v:!0}:S==="FALSE"?z={t:"b",v:!1}:isNaN(Ur(S))?isNaN(Pa(S).getDate())||(z={t:"d",v:er(S)},n.cellDates||(z={t:"n",v:mr(z.v)}),z.z=n.dateNF||_e[14]):z={t:"n",v:Ur(S)})),z.z===void 0&&U!=null&&(z.z=U);var b="",j=D.getElementsByTagName("A");if(j&&j.length)for(var Y=0;Y<j.length&&!(j[Y].hasAttribute("href")&&(b=j[Y].getAttribute("href"),b.charAt(0)!="#"));++Y);b&&b.charAt(0)!="#"&&(z.l={Target:b}),n.dense?(e[x+t]||(e[x+t]=[]),e[x+t][w+s]=z):e[ve({c:w+s,r:x+t})]=z,l.e.c<w+s&&(l.e.c=w+s),w+=m}}++x}return u.length&&(e["!merges"]=(e["!merges"]||[]).concat(u)),l.e.r=Math.max(l.e.r,x-1+t),e["!ref"]=ye(l),x>=f&&(e["!fullref"]=ye((l.e.r=c.length-d+x-1+t,l))),e}function bi(e,a){var r=a||{},n=r.dense?[]:{};return Pi(n,e,a)}function G2(e,a){return la(bi(e,a),a)}function Jn(e){var a="",r=$2(e);return r&&(a=r(e).getPropertyValue("display")),a||(a=e.style&&e.style.display),a==="none"}function $2(e){return e.ownerDocument.defaultView&&typeof e.ownerDocument.defaultView.getComputedStyle=="function"?e.ownerDocument.defaultView.getComputedStyle:typeof getComputedStyle=="function"?getComputedStyle:null}function X2(e){var a=e.replace(/[\t\r\n]/g," ").trim().replace(/ +/g," ").replace(/<text:s\/>/g," ").replace(/<text:s text:c="(\d+)"\/>/g,function(n,t){return Array(parseInt(t,10)+1).join(" ")}).replace(/<text:tab[^>]*\/>/g,"	").replace(/<text:line-break\/>/g,`
`),r=Oe(a.replace(/<[^>]*>/g,""));return[r]}var Zn={day:["d","dd"],month:["m","mm"],year:["y","yy"],hours:["h","hh"],minutes:["m","mm"],seconds:["s","ss"],"am-pm":["A/P","AM/PM"],"day-of-week":["ddd","dddd"],era:["e","ee"],quarter:["\\Qm",'m\\"th quarter"']};function Bi(e,a){var r=a||{},n=C0(e),t=[],s,i,c={name:""},f="",l=0,o,u,p={},h=[],d=r.dense?[]:{},x,g,w={value:""},y="",m=0,O=[],P=-1,D=-1,S={s:{r:1e6,c:1e7},e:{r:0,c:0}},U=0,R={},z=[],G={},b=0,j=0,Y=[],Q=1,se=1,ne=[],De={Names:[]},L={},X=["",""],ue=[],A={},B="",N=0,I=!1,J=!1,Z=0;for(st.lastIndex=0,n=n.replace(/<!--([\s\S]*?)-->/mg,"").replace(/<!DOCTYPE[^\[]*\[[^\]]*\]>/gm,"");x=st.exec(n);)switch(x[3]=x[3].replace(/_.*$/,"")){case"table":case"工作表":x[1]==="/"?(S.e.c>=S.s.c&&S.e.r>=S.s.r?d["!ref"]=ye(S):d["!ref"]="A1:A1",r.sheetRows>0&&r.sheetRows<=S.e.r&&(d["!fullref"]=d["!ref"],S.e.r=r.sheetRows-1,d["!ref"]=ye(S)),z.length&&(d["!merges"]=z),Y.length&&(d["!rows"]=Y),o.name=o.名称||o.name,typeof JSON<"u"&&JSON.stringify(o),h.push(o.name),p[o.name]=d,J=!1):x[0].charAt(x[0].length-2)!=="/"&&(o=pe(x[0],!1),P=D=-1,S.s.r=S.s.c=1e7,S.e.r=S.e.c=0,d=r.dense?[]:{},z=[],Y=[],J=!0);break;case"table-row-group":x[1]==="/"?--U:++U;break;case"table-row":case"行":if(x[1]==="/"){P+=Q,Q=1;break}if(u=pe(x[0],!1),u.行号?P=u.行号-1:P==-1&&(P=0),Q=+u["number-rows-repeated"]||1,Q<10)for(Z=0;Z<Q;++Z)U>0&&(Y[P+Z]={level:U});D=-1;break;case"covered-table-cell":x[1]!=="/"&&++D,r.sheetStubs&&(r.dense?(d[P]||(d[P]=[]),d[P][D]={t:"z"}):d[ve({r:P,c:D})]={t:"z"}),y="",O=[];break;case"table-cell":case"数据":if(x[0].charAt(x[0].length-2)==="/")++D,w=pe(x[0],!1),se=parseInt(w["number-columns-repeated"]||"1",10),g={t:"z",v:null},w.formula&&r.cellFormula!=!1&&(g.f=Xn(Oe(w.formula))),(w.数据类型||w["value-type"])=="string"&&(g.t="s",g.v=Oe(w["string-value"]||""),r.dense?(d[P]||(d[P]=[]),d[P][D]=g):d[ve({r:P,c:D})]=g),D+=se-1;else if(x[1]!=="/"){++D,y="",m=0,O=[],se=1;var ie=Q?P+Q-1:P;if(D>S.e.c&&(S.e.c=D),D<S.s.c&&(S.s.c=D),P<S.s.r&&(S.s.r=P),ie>S.e.r&&(S.e.r=ie),w=pe(x[0],!1),ue=[],A={},g={t:w.数据类型||w["value-type"],v:null},r.cellFormula)if(w.formula&&(w.formula=Oe(w.formula)),w["number-matrix-columns-spanned"]&&w["number-matrix-rows-spanned"]&&(b=parseInt(w["number-matrix-rows-spanned"],10)||0,j=parseInt(w["number-matrix-columns-spanned"],10)||0,G={s:{r:P,c:D},e:{r:P+b-1,c:D+j-1}},g.F=ye(G),ne.push([G,g.F])),w.formula)g.f=Xn(w.formula);else for(Z=0;Z<ne.length;++Z)P>=ne[Z][0].s.r&&P<=ne[Z][0].e.r&&D>=ne[Z][0].s.c&&D<=ne[Z][0].e.c&&(g.F=ne[Z][1]);switch((w["number-columns-spanned"]||w["number-rows-spanned"])&&(b=parseInt(w["number-rows-spanned"],10)||0,j=parseInt(w["number-columns-spanned"],10)||0,G={s:{r:P,c:D},e:{r:P+b-1,c:D+j-1}},z.push(G)),w["number-columns-repeated"]&&(se=parseInt(w["number-columns-repeated"],10)),g.t){case"boolean":g.t="b",g.v=be(w["boolean-value"]);break;case"float":g.t="n",g.v=parseFloat(w.value);break;case"percentage":g.t="n",g.v=parseFloat(w.value);break;case"currency":g.t="n",g.v=parseFloat(w.value);break;case"date":g.t="d",g.v=er(w["date-value"]),r.cellDates||(g.t="n",g.v=mr(g.v)),g.z="m/d/yy";break;case"time":g.t="n",g.v=to(w["time-value"])/86400,r.cellDates&&(g.t="d",g.v=$t(g.v)),g.z="HH:MM:SS";break;case"number":g.t="n",g.v=parseFloat(w.数据数值);break;default:if(g.t==="string"||g.t==="text"||!g.t)g.t="s",w["string-value"]!=null&&(y=Oe(w["string-value"]),O=[]);else throw new Error("Unsupported value type "+g.t)}}else{if(I=!1,g.t==="s"&&(g.v=y||"",O.length&&(g.R=O),I=m==0),L.Target&&(g.l=L),ue.length>0&&(g.c=ue,ue=[]),y&&r.cellText!==!1&&(g.w=y),I&&(g.t="z",delete g.v),(!I||r.sheetStubs)&&!(r.sheetRows&&r.sheetRows<=P))for(var te=0;te<Q;++te){if(se=parseInt(w["number-columns-repeated"]||"1",10),r.dense)for(d[P+te]||(d[P+te]=[]),d[P+te][D]=te==0?g:ar(g);--se>0;)d[P+te][D+se]=ar(g);else for(d[ve({r:P+te,c:D})]=g;--se>0;)d[ve({r:P+te,c:D+se})]=ar(g);S.e.c<=D&&(S.e.c=D)}se=parseInt(w["number-columns-repeated"]||"1",10),D+=se-1,se=0,g={},y="",O=[]}L={};break;case"document":case"document-content":case"电子表格文档":case"spreadsheet":case"主体":case"scripts":case"styles":case"font-face-decls":case"master-styles":if(x[1]==="/"){if((s=t.pop())[0]!==x[3])throw"Bad state: "+s}else x[0].charAt(x[0].length-2)!=="/"&&t.push([x[3],!0]);break;case"annotation":if(x[1]==="/"){if((s=t.pop())[0]!==x[3])throw"Bad state: "+s;A.t=y,O.length&&(A.R=O),A.a=B,ue.push(A)}else x[0].charAt(x[0].length-2)!=="/"&&t.push([x[3],!1]);B="",N=0,y="",m=0,O=[];break;case"creator":x[1]==="/"?B=n.slice(N,x.index):N=x.index+x[0].length;break;case"meta":case"元数据":case"settings":case"config-item-set":case"config-item-map-indexed":case"config-item-map-entry":case"config-item-map-named":case"shapes":case"frame":case"text-box":case"image":case"data-pilot-tables":case"list-style":case"form":case"dde-links":case"event-listeners":case"chart":if(x[1]==="/"){if((s=t.pop())[0]!==x[3])throw"Bad state: "+s}else x[0].charAt(x[0].length-2)!=="/"&&t.push([x[3],!1]);y="",m=0,O=[];break;case"scientific-number":break;case"currency-symbol":break;case"currency-style":break;case"number-style":case"percentage-style":case"date-style":case"time-style":if(x[1]==="/"){if(R[c.name]=f,(s=t.pop())[0]!==x[3])throw"Bad state: "+s}else x[0].charAt(x[0].length-2)!=="/"&&(f="",c=pe(x[0],!1),t.push([x[3],!0]));break;case"script":break;case"libraries":break;case"automatic-styles":break;case"default-style":case"page-layout":break;case"style":break;case"map":break;case"font-face":break;case"paragraph-properties":break;case"table-properties":break;case"table-column-properties":break;case"table-row-properties":break;case"table-cell-properties":break;case"number":switch(t[t.length-1][0]){case"time-style":case"date-style":i=pe(x[0],!1),f+=Zn[x[3]][i.style==="long"?1:0];break}break;case"fraction":break;case"day":case"month":case"year":case"era":case"day-of-week":case"week-of-year":case"quarter":case"hours":case"minutes":case"seconds":case"am-pm":switch(t[t.length-1][0]){case"time-style":case"date-style":i=pe(x[0],!1),f+=Zn[x[3]][i.style==="long"?1:0];break}break;case"boolean-style":break;case"boolean":break;case"text-style":break;case"text":if(x[0].slice(-2)==="/>")break;if(x[1]==="/")switch(t[t.length-1][0]){case"number-style":case"date-style":case"time-style":f+=n.slice(l,x.index);break}else l=x.index+x[0].length;break;case"named-range":i=pe(x[0],!1),X=a0(i["cell-range-address"]);var ee={Name:i.name,Ref:X[0]+"!"+X[1]};J&&(ee.Sheet=h.length),De.Names.push(ee);break;case"text-content":break;case"text-properties":break;case"embedded-text":break;case"body":case"电子表格":break;case"forms":break;case"table-column":break;case"table-header-rows":break;case"table-rows":break;case"table-column-group":break;case"table-header-columns":break;case"table-columns":break;case"null-date":break;case"graphic-properties":break;case"calculation-settings":break;case"named-expressions":break;case"label-range":break;case"label-ranges":break;case"named-expression":break;case"sort":break;case"sort-by":break;case"sort-groups":break;case"tab":break;case"line-break":break;case"span":break;case"p":case"文本串":if(["master-styles"].indexOf(t[t.length-1][0])>-1)break;if(x[1]==="/"&&(!w||!w["string-value"])){var ke=X2(n.slice(m,x.index));y=(y.length>0?y+`
`:"")+ke[0]}else pe(x[0],!1),m=x.index+x[0].length;break;case"s":break;case"database-range":if(x[1]==="/")break;try{X=a0(pe(x[0])["target-range-address"]),p[X[0]]["!autofilter"]={ref:X[1]}}catch{}break;case"date":break;case"object":break;case"title":case"标题":break;case"desc":break;case"binary-data":break;case"table-source":break;case"scenario":break;case"iteration":break;case"content-validations":break;case"content-validation":break;case"help-message":break;case"error-message":break;case"database-ranges":break;case"filter":break;case"filter-and":break;case"filter-or":break;case"filter-condition":break;case"list-level-style-bullet":break;case"list-level-style-number":break;case"list-level-properties":break;case"sender-firstname":case"sender-lastname":case"sender-initials":case"sender-title":case"sender-position":case"sender-email":case"sender-phone-private":case"sender-fax":case"sender-company":case"sender-phone-work":case"sender-street":case"sender-city":case"sender-postal-code":case"sender-country":case"sender-state-or-province":case"author-name":case"author-initials":case"chapter":case"file-name":case"template-name":case"sheet-name":break;case"event-listener":break;case"initial-creator":case"creation-date":case"print-date":case"generator":case"document-statistic":case"user-defined":case"editing-duration":case"editing-cycles":break;case"config-item":break;case"page-number":break;case"page-count":break;case"time":break;case"cell-range-source":break;case"detective":break;case"operation":break;case"highlighted-range":break;case"data-pilot-table":case"source-cell-range":case"source-service":case"data-pilot-field":case"data-pilot-level":case"data-pilot-subtotals":case"data-pilot-subtotal":case"data-pilot-members":case"data-pilot-member":case"data-pilot-display-info":case"data-pilot-sort-info":case"data-pilot-layout-info":case"data-pilot-field-reference":case"data-pilot-groups":case"data-pilot-group":case"data-pilot-group-member":break;case"rect":break;case"dde-connection-decls":case"dde-connection-decl":case"dde-link":case"dde-source":break;case"properties":break;case"property":break;case"a":if(x[1]!=="/"){if(L=pe(x[0],!1),!L.href)break;L.Target=Oe(L.href),delete L.href,L.Target.charAt(0)=="#"&&L.Target.indexOf(".")>-1?(X=a0(L.Target.slice(1)),L.Target="#"+X[0]+"!"+X[1]):L.Target.match(/^\.\.[\\\/]/)&&(L.Target=L.Target.slice(3))}break;case"table-protection":break;case"data-pilot-grand-total":break;case"office-document-common-attrs":break;default:switch(x[2]){case"dc:":case"calcext:":case"loext:":case"ooo:":case"chartooo:":case"draw:":case"style:":case"chart:":case"form:":case"uof:":case"表:":case"字:":break;default:if(r.WTF)throw new Error(x)}}var C={Sheets:p,SheetNames:h,Workbook:De};return r.bookSheets&&delete C.Sheets,C}function qn(e,a){a=a||{},Or(e,"META-INF/manifest.xml")&&cl(Xe(e,"META-INF/manifest.xml"),a);var r=Ar(e,"content.xml");if(!r)throw new Error("Missing content.xml in ODS / UOF file");var n=Bi(Ne(r),a);return Or(e,"meta.xml")&&(n.Props=Js(Xe(e,"meta.xml"))),n}function Qn(e,a){return Bi(e,a)}/*! sheetjs (C) 2013-present SheetJS -- http://sheetjs.com */function V0(e){return new DataView(e.buffer,e.byteOffset,e.byteLength)}function p0(e){return typeof TextDecoder<"u"?new TextDecoder().decode(e):Ne(Aa(e))}function v0(e){var a=e.reduce(function(t,s){return t+s.length},0),r=new Uint8Array(a),n=0;return e.forEach(function(t){r.set(t,n),n+=t.length}),r}function es(e){return e-=e>>1&1431655765,e=(e&858993459)+(e>>2&858993459),(e+(e>>4)&252645135)*16843009>>>24}function z2(e,a){for(var r=(e[a+15]&127)<<7|e[a+14]>>1,n=e[a+14]&1,t=a+13;t>=a;--t)n=n*256+e[t];return(e[a+15]&128?-n:n)*Math.pow(10,r-6176)}function ot(e,a){var r=a?a[0]:0,n=e[r]&127;e:if(e[r++]>=128&&(n|=(e[r]&127)<<7,e[r++]<128||(n|=(e[r]&127)<<14,e[r++]<128)||(n|=(e[r]&127)<<21,e[r++]<128)||(n+=(e[r]&127)*Math.pow(2,28),++r,e[r++]<128)||(n+=(e[r]&127)*Math.pow(2,35),++r,e[r++]<128)||(n+=(e[r]&127)*Math.pow(2,42),++r,e[r++]<128)))break e;return a&&(a[0]=r),n}function Ze(e){var a=0,r=e[a]&127;e:if(e[a++]>=128){if(r|=(e[a]&127)<<7,e[a++]<128||(r|=(e[a]&127)<<14,e[a++]<128)||(r|=(e[a]&127)<<21,e[a++]<128))break e;r|=(e[a]&127)<<28}return r}function fr(e){for(var a=[],r=[0];r[0]<e.length;){var n=r[0],t=ot(e,r),s=t&7;t=Math.floor(t/8);var i=0,c;if(t==0)break;switch(s){case 0:{for(var f=r[0];e[r[0]++]>=128;);c=e.slice(f,r[0])}break;case 5:i=4,c=e.slice(r[0],r[0]+i),r[0]+=i;break;case 1:i=8,c=e.slice(r[0],r[0]+i),r[0]+=i;break;case 2:i=ot(e,r),c=e.slice(r[0],r[0]+i),r[0]+=i;break;case 3:case 4:default:throw new Error("PB Type ".concat(s," for Field ").concat(t," at offset ").concat(n))}var l={data:c,type:s};a[t]==null?a[t]=[l]:a[t].push(l)}return a}function H0(e,a){return(e==null?void 0:e.map(function(r){return a(r.data)}))||[]}function K2(e){for(var a,r=[],n=[0];n[0]<e.length;){var t=ot(e,n),s=fr(e.slice(n[0],n[0]+t));n[0]+=t;var i={id:Ze(s[1][0].data),messages:[]};s[2].forEach(function(c){var f=fr(c.data),l=Ze(f[3][0].data);i.messages.push({meta:f,data:e.slice(n[0],n[0]+l)}),n[0]+=l}),(a=s[3])!=null&&a[0]&&(i.merge=Ze(s[3][0].data)>>>0>0),r.push(i)}return r}function Y2(e,a){if(e!=0)throw new Error("Unexpected Snappy chunk type ".concat(e));for(var r=[0],n=ot(a,r),t=[];r[0]<a.length;){var s=a[r[0]]&3;if(s==0){var i=a[r[0]++]>>2;if(i<60)++i;else{var c=i-59;i=a[r[0]],c>1&&(i|=a[r[0]+1]<<8),c>2&&(i|=a[r[0]+2]<<16),c>3&&(i|=a[r[0]+3]<<24),i>>>=0,i++,r[0]+=c}t.push(a.slice(r[0],r[0]+i)),r[0]+=i;continue}else{var f=0,l=0;if(s==1?(l=(a[r[0]]>>2&7)+4,f=(a[r[0]++]&224)<<3,f|=a[r[0]++]):(l=(a[r[0]++]>>2)+1,s==2?(f=a[r[0]]|a[r[0]+1]<<8,r[0]+=2):(f=(a[r[0]]|a[r[0]+1]<<8|a[r[0]+2]<<16|a[r[0]+3]<<24)>>>0,r[0]+=4)),t=[v0(t)],f==0)throw new Error("Invalid offset 0");if(f>t[0].length)throw new Error("Invalid offset beyond length");if(l>=f)for(t.push(t[0].slice(-f)),l-=f;l>=t[t.length-1].length;)t.push(t[t.length-1]),l-=t[t.length-1].length;t.push(t[0].slice(-f,-f+l))}}var o=v0(t);if(o.length!=n)throw new Error("Unexpected length: ".concat(o.length," != ").concat(n));return o}function j2(e){for(var a=[],r=0;r<e.length;){var n=e[r++],t=e[r]|e[r+1]<<8|e[r+2]<<16;r+=3,a.push(Y2(n,e.slice(r,r+t))),r+=t}if(r!==e.length)throw new Error("data is not a valid framed stream!");return v0(a)}function J2(e,a,r,n){var t=V0(e),s=t.getUint32(4,!0),i=(n>1?12:8)+es(s&(n>1?3470:398))*4,c=-1,f=-1,l=NaN,o=new Date(2001,0,1);s&512&&(c=t.getUint32(i,!0),i+=4),i+=es(s&(n>1?12288:4096))*4,s&16&&(f=t.getUint32(i,!0),i+=4),s&32&&(l=t.getFloat64(i,!0),i+=8),s&64&&(o.setTime(o.getTime()+t.getFloat64(i,!0)*1e3),i+=8);var u;switch(e[2]){case 0:break;case 2:u={t:"n",v:l};break;case 3:u={t:"s",v:a[f]};break;case 5:u={t:"d",v:o};break;case 6:u={t:"b",v:l>0};break;case 7:u={t:"n",v:l/86400};break;case 8:u={t:"e",v:0};break;case 9:if(c>-1)u={t:"s",v:r[c]};else if(f>-1)u={t:"s",v:a[f]};else if(!isNaN(l))u={t:"n",v:l};else throw new Error("Unsupported cell type ".concat(e.slice(0,4)));break;default:throw new Error("Unsupported cell type ".concat(e.slice(0,4)))}return u}function Z2(e,a,r){var n=V0(e),t=n.getUint32(8,!0),s=12,i=-1,c=-1,f=NaN,l=NaN,o=new Date(2001,0,1);t&1&&(f=z2(e,s),s+=16),t&2&&(l=n.getFloat64(s,!0),s+=8),t&4&&(o.setTime(o.getTime()+n.getFloat64(s,!0)*1e3),s+=8),t&8&&(c=n.getUint32(s,!0),s+=4),t&16&&(i=n.getUint32(s,!0),s+=4);var u;switch(e[1]){case 0:break;case 2:u={t:"n",v:f};break;case 3:u={t:"s",v:a[c]};break;case 5:u={t:"d",v:o};break;case 6:u={t:"b",v:l>0};break;case 7:u={t:"n",v:l/86400};break;case 8:u={t:"e",v:0};break;case 9:if(i>-1)u={t:"s",v:r[i]};else throw new Error("Unsupported cell type ".concat(e[1]," : ").concat(t&31," : ").concat(e.slice(0,4)));break;case 10:u={t:"n",v:f};break;default:throw new Error("Unsupported cell type ".concat(e[1]," : ").concat(t&31," : ").concat(e.slice(0,4)))}return u}function q2(e,a,r){switch(e[0]){case 0:case 1:case 2:case 3:return J2(e,a,r,e[0]);case 5:return Z2(e,a,r);default:throw new Error("Unsupported payload version ".concat(e[0]))}}function ca(e){var a=fr(e);return ot(a[1][0].data)}function rs(e,a){var r=fr(a.data),n=Ze(r[1][0].data),t=r[3],s=[];return(t||[]).forEach(function(i){var c=fr(i.data),f=Ze(c[1][0].data)>>>0;switch(n){case 1:s[f]=p0(c[3][0].data);break;case 8:{var l=e[ca(c[9][0].data)][0],o=fr(l.data),u=e[ca(o[1][0].data)][0],p=Ze(u.meta[1][0].data);if(p!=2001)throw new Error("2000 unexpected reference to ".concat(p));var h=fr(u.data);s[f]=h[3].map(function(d){return p0(d.data)}).join("")}break}}),s}function Q2(e,a){var r,n,t,s,i,c,f,l,o,u,p,h,d,x,g=fr(e),w=Ze(g[1][0].data)>>>0,y=Ze(g[2][0].data)>>>0,m=((n=(r=g[8])==null?void 0:r[0])==null?void 0:n.data)&&Ze(g[8][0].data)>0||!1,O,P;if((s=(t=g[7])==null?void 0:t[0])!=null&&s.data&&a!=0)O=(c=(i=g[7])==null?void 0:i[0])==null?void 0:c.data,P=(l=(f=g[6])==null?void 0:f[0])==null?void 0:l.data;else if((u=(o=g[4])==null?void 0:o[0])!=null&&u.data&&a!=1)O=(h=(p=g[4])==null?void 0:p[0])==null?void 0:h.data,P=(x=(d=g[3])==null?void 0:d[0])==null?void 0:x.data;else throw"NUMBERS Tile missing ".concat(a," cell storage");for(var D=m?4:1,S=V0(O),U=[],R=0;R<O.length/2;++R){var z=S.getUint16(R*2,!0);z<65535&&U.push([R,z])}if(U.length!=y)throw"Expected ".concat(y," cells, found ").concat(U.length);var G=[];for(R=0;R<U.length-1;++R)G[U[R][0]]=P.subarray(U[R][1]*D,U[R+1][1]*D);return U.length>=1&&(G[U[U.length-1][0]]=P.subarray(U[U.length-1][1]*D)),{R:w,cells:G}}function ep(e,a){var r,n=fr(a.data),t=(r=n==null?void 0:n[7])!=null&&r[0]?Ze(n[7][0].data)>>>0>0?1:0:-1,s=H0(n[5],function(i){return Q2(i,t)});return{nrows:Ze(n[4][0].data)>>>0,data:s.reduce(function(i,c){return i[c.R]||(i[c.R]=[]),c.cells.forEach(function(f,l){if(i[c.R][l])throw new Error("Duplicate cell r=".concat(c.R," c=").concat(l));i[c.R][l]=f}),i},[])}}function rp(e,a,r){var n,t=fr(a.data),s={s:{r:0,c:0},e:{r:0,c:0}};if(s.e.r=(Ze(t[6][0].data)>>>0)-1,s.e.r<0)throw new Error("Invalid row varint ".concat(t[6][0].data));if(s.e.c=(Ze(t[7][0].data)>>>0)-1,s.e.c<0)throw new Error("Invalid col varint ".concat(t[7][0].data));r["!ref"]=ye(s);var i=fr(t[4][0].data),c=rs(e,e[ca(i[4][0].data)][0]),f=(n=i[17])!=null&&n[0]?rs(e,e[ca(i[17][0].data)][0]):[],l=fr(i[3][0].data),o=0;l[1].forEach(function(u){var p=fr(u.data),h=e[ca(p[2][0].data)][0],d=Ze(h.meta[1][0].data);if(d!=6002)throw new Error("6001 unexpected reference to ".concat(d));var x=ep(e,h);x.data.forEach(function(g,w){g.forEach(function(y,m){var O=ve({r:o+w,c:m}),P=q2(y,c,f);P&&(r[O]=P)})}),o+=x.nrows})}function ap(e,a){var r=fr(a.data),n={"!ref":"A1"},t=e[ca(r[2][0].data)],s=Ze(t[0].meta[1][0].data);if(s!=6001)throw new Error("6000 unexpected reference to ".concat(s));return rp(e,t[0],n),n}function tp(e,a){var r,n=fr(a.data),t={name:(r=n[1])!=null&&r[0]?p0(n[1][0].data):"",sheets:[]},s=H0(n[2],ca);return s.forEach(function(i){e[i].forEach(function(c){var f=Ze(c.meta[1][0].data);f==6e3&&t.sheets.push(ap(e,c))})}),t}function np(e,a){var r=$0(),n=fr(a.data),t=H0(n[1],ca);if(t.forEach(function(s){e[s].forEach(function(i){var c=Ze(i.meta[1][0].data);if(c==2){var f=tp(e,i);f.sheets.forEach(function(l,o){X0(r,l,o==0?f.name:f.name+"_"+o,!0)})}})}),r.SheetNames.length==0)throw new Error("Empty NUMBERS file");return r}function n0(e){var a,r,n,t,s={},i=[];if(e.FullPaths.forEach(function(f){if(f.match(/\.iwpv2/))throw new Error("Unsupported password protection")}),e.FileIndex.forEach(function(f){if(f.name.match(/\.iwa$/)){var l;try{l=j2(f.content)}catch(u){return console.log("?? "+f.content.length+" "+(u.message||u))}var o;try{o=K2(l)}catch(u){return console.log("## "+(u.message||u))}o.forEach(function(u){s[u.id]=u.messages,i.push(u.id)})}}),!i.length)throw new Error("File has no messages");var c=((t=(n=(r=(a=s==null?void 0:s[1])==null?void 0:a[0])==null?void 0:r.meta)==null?void 0:n[1])==null?void 0:t[0].data)&&Ze(s[1][0].meta[1][0].data)==1&&s[1][0];if(c||i.forEach(function(f){s[f].forEach(function(l){var o=Ze(l.meta[1][0].data)>>>0;if(o==1)if(!c)c=l;else throw new Error("Document has multiple roots")})}),!c)throw new Error("Cannot find Document root");return np(s,c)}function sp(e){return function(r){for(var n=0;n!=e.length;++n){var t=e[n];r[t[0]]===void 0&&(r[t[0]]=t[1]),t[2]==="n"&&(r[t[0]]=Number(r[t[0]]))}}}function W0(e){sp([["cellNF",!1],["cellHTML",!0],["cellFormula",!0],["cellStyles",!1],["cellText",!0],["cellDates",!1],["sheetStubs",!1],["sheetRows",0,"n"],["bookDeps",!1],["bookSheets",!1],["bookProps",!1],["bookFiles",!1],["bookVBA",!1],["password",""],["WTF",!1]])(e)}function ip(e){return Ia.WS.indexOf(e)>-1?"sheet":e==Ia.CS?"chart":e==Ia.DS?"dialog":e==Ia.MS?"macro":e&&e.length?e:"sheet"}function cp(e,a){if(!e)return 0;try{e=a.map(function(n){return n.id||(n.id=n.strRelID),[n.name,e["!id"][n.id].Target,ip(e["!id"][n.id].Type)]})}catch{return null}return!e||e.length===0?null:e}function fp(e,a,r,n,t,s,i,c,f,l,o,u){try{s[n]=Za(Ar(e,r,!0),a);var p=Xe(e,a),h;switch(c){case"sheet":h=d2(p,a,t,f,s[n],l,o,u);break;case"chart":if(h=p2(p,a,t,f,s[n],l,o,u),!h||!h["!drawel"])break;var d=za(h["!drawel"].Target,a),x=l0(d),g=xh(Ar(e,d,!0),Za(Ar(e,x,!0),d)),w=za(g,d),y=l0(w);h=Zd(Ar(e,w,!0),w,f,Za(Ar(e,y,!0),w),l,h);break;case"macro":h=v2(p,a,t,f,s[n],l,o,u);break;case"dialog":h=g2(p,a,t,f,s[n],l,o,u);break;default:throw new Error("Unrecognized sheet type "+c)}i[n]=h;var m=[];s&&s[n]&&Gr(s[n]).forEach(function(O){var P="";if(s[n][O].Type==Ia.CMNT){P=za(s[n][O].Target,a);var D=T2(Xe(e,P,!0),P,f);if(!D||!D.length)return;Vn(h,D,!1)}s[n][O].Type==Ia.TCMNT&&(P=za(s[n][O].Target,a),m=m.concat(ph(Xe(e,P,!0),f)))}),m&&m.length&&Vn(h,m,!0,f.people||[])}catch(O){if(f.WTF)throw O}}function yr(e){return e.charAt(0)=="/"?e.slice(1):e}function op(e,a){if(Ts(),a=a||{},W0(a),Or(e,"META-INF/manifest.xml")||Or(e,"objectdata.xml"))return qn(e,a);if(Or(e,"Index/Document.iwa")){if(typeof Uint8Array>"u")throw new Error("NUMBERS file parsing requires Uint8Array support");if(typeof n0<"u"){if(e.FileIndex)return n0(e);var r=Ce.utils.cfb_new();return xn(e).forEach(function(Y){oo(r,Y,fo(e,Y))}),n0(r)}throw new Error("Unsupported NUMBERS file")}if(!Or(e,"[Content_Types].xml"))throw Or(e,"index.xml.gz")?new Error("Unsupported NUMBERS 08 file"):Or(e,"index.xml")?new Error("Unsupported NUMBERS 09 file"):new Error("Unsupported ZIP file");var n=xn(e),t=sl(Ar(e,"[Content_Types].xml")),s=!1,i,c;if(t.workbooks.length===0&&(c="xl/workbook.xml",Xe(e,c,!0)&&t.workbooks.push(c)),t.workbooks.length===0){if(c="xl/workbook.bin",!Xe(e,c,!0))throw new Error("Could not find workbook");t.workbooks.push(c),s=!0}t.workbooks[0].slice(-3)=="bin"&&(s=!0);var f={},l={};if(!a.bookSheets&&!a.bookProps){if(et=[],t.sst)try{et=E2(Xe(e,yr(t.sst)),t.sst,a)}catch(Y){if(a.WTF)throw Y}a.cellStyles&&t.themes.length&&(f=_2(Ar(e,t.themes[0].replace(/^\//,""),!0)||"",t.themes[0],a)),t.style&&(l=m2(Xe(e,yr(t.style)),t.style,f,a))}t.links.map(function(Y){try{var Q=Za(Ar(e,l0(yr(Y))),Y);return w2(Xe(e,yr(Y)),Q,Y,a)}catch{}});var o=x2(Xe(e,yr(t.workbooks[0])),t.workbooks[0],a),u={},p="";t.coreprops.length&&(p=Xe(e,yr(t.coreprops[0]),!0),p&&(u=Js(p)),t.extprops.length!==0&&(p=Xe(e,yr(t.extprops[0]),!0),p&&ll(p,u,a)));var h={};(!a.bookSheets||a.bookProps)&&t.custprops.length!==0&&(p=Ar(e,yr(t.custprops[0]),!0),p&&(h=hl(p,a)));var d={};if((a.bookSheets||a.bookProps)&&(o.Sheets?i=o.Sheets.map(function(Q){return Q.name}):u.Worksheets&&u.SheetNames.length>0&&(i=u.SheetNames),a.bookProps&&(d.Props=u,d.Custprops=h),a.bookSheets&&typeof i<"u"&&(d.SheetNames=i),a.bookSheets?d.SheetNames:a.bookProps))return d;i={};var x={};a.bookDeps&&t.calcchain&&(x=k2(Xe(e,yr(t.calcchain)),t.calcchain));var g=0,w={},y,m;{var O=o.Sheets;u.Worksheets=O.length,u.SheetNames=[];for(var P=0;P!=O.length;++P)u.SheetNames[P]=O[P].name}var D=s?"bin":"xml",S=t.workbooks[0].lastIndexOf("/"),U=(t.workbooks[0].slice(0,S+1)+"_rels/"+t.workbooks[0].slice(S+1)+".rels").replace(/^\//,"");Or(e,U)||(U="xl/_rels/workbook."+D+".rels");var R=Za(Ar(e,U,!0),U.replace(/_rels.*/,"s5s"));(t.metadata||[]).length>=1&&(a.xlmeta=A2(Xe(e,yr(t.metadata[0])),t.metadata[0],a)),(t.people||[]).length>=1&&(a.people=vh(Xe(e,yr(t.people[0])),a)),R&&(R=cp(R,o.Sheets));var z=Xe(e,"xl/worksheets/sheet.xml",!0)?1:0;e:for(g=0;g!=u.Worksheets;++g){var G="sheet";if(R&&R[g]?(y="xl/"+R[g][1].replace(/[\/]?xl\//,""),Or(e,y)||(y=R[g][1]),Or(e,y)||(y=U.replace(/_rels\/.*$/,"")+R[g][1]),G=R[g][2]):(y="xl/worksheets/sheet"+(g+1-z)+"."+D,y=y.replace(/sheet0\./,"sheet.")),m=y.replace(/^(.*)(\/)([^\/]*)$/,"$1/_rels/$3.rels"),a&&a.sheets!=null)switch(typeof a.sheets){case"number":if(g!=a.sheets)continue e;break;case"string":if(u.SheetNames[g].toLowerCase()!=a.sheets.toLowerCase())continue e;break;default:if(Array.isArray&&Array.isArray(a.sheets)){for(var b=!1,j=0;j!=a.sheets.length;++j)typeof a.sheets[j]=="number"&&a.sheets[j]==g&&(b=1),typeof a.sheets[j]=="string"&&a.sheets[j].toLowerCase()==u.SheetNames[g].toLowerCase()&&(b=1);if(!b)continue e}}fp(e,y,m,u.SheetNames[g],g,w,i,G,a,o,f,l)}return d={Directory:t,Workbook:o,Props:u,Custprops:h,Deps:x,Sheets:i,SheetNames:u.SheetNames,Strings:et,Styles:l,Themes:f,SSF:ar(_e)},a&&a.bookFiles&&(e.files?(d.keys=n,d.files=e.files):(d.keys=[],d.files={},e.FullPaths.forEach(function(Y,Q){Y=Y.replace(/^Root Entry[\/]/,""),d.keys.push(Y),d.files[Y]=e.FileIndex[Q]}))),a&&a.bookVBA&&(t.vba.length>0?d.vbaraw=Xe(e,yr(t.vba[0]),!0):t.defaults&&t.defaults.bin===Eh&&(d.vbaraw=Xe(e,"xl/vbaProject.bin",!0))),d}function lp(e,a){var r=a||{},n="Workbook",t=Ce.find(e,n);try{if(n="/!DataSpaces/Version",t=Ce.find(e,n),!t||!t.content)throw new Error("ECMA-376 Encrypted file missing "+n);if(au(t.content),n="/!DataSpaces/DataSpaceMap",t=Ce.find(e,n),!t||!t.content)throw new Error("ECMA-376 Encrypted file missing "+n);var s=nu(t.content);if(s.length!==1||s[0].comps.length!==1||s[0].comps[0].t!==0||s[0].name!=="StrongEncryptionDataSpace"||s[0].comps[0].v!=="EncryptedPackage")throw new Error("ECMA-376 Encrypted file bad "+n);if(n="/!DataSpaces/DataSpaceInfo/StrongEncryptionDataSpace",t=Ce.find(e,n),!t||!t.content)throw new Error("ECMA-376 Encrypted file missing "+n);var i=su(t.content);if(i.length!=1||i[0]!="StrongEncryptionTransform")throw new Error("ECMA-376 Encrypted file bad "+n);if(n="/!DataSpaces/TransformInfo/StrongEncryptionTransform/!Primary",t=Ce.find(e,n),!t||!t.content)throw new Error("ECMA-376 Encrypted file missing "+n);cu(t.content)}catch{}if(n="/EncryptionInfo",t=Ce.find(e,n),!t||!t.content)throw new Error("ECMA-376 Encrypted file missing "+n);var c=fu(t.content);if(n="/EncryptedPackage",t=Ce.find(e,n),!t||!t.content)throw new Error("ECMA-376 Encrypted file missing "+n);if(c[0]==4&&typeof decrypt_agile<"u")return decrypt_agile(c[1],t.content,r.password||"",r);if(c[0]==2&&typeof decrypt_std76<"u")return decrypt_std76(c[1],t.content,r.password||"",r);throw new Error("File is password-protected")}function G0(e,a){var r="";switch((a||{}).type||"base64"){case"buffer":return[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7]];case"base64":r=Fr(e.slice(0,12));break;case"binary":r=e;break;case"array":return[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7]];default:throw new Error("Unrecognized type "+(a&&a.type||"undefined"))}return[r.charCodeAt(0),r.charCodeAt(1),r.charCodeAt(2),r.charCodeAt(3),r.charCodeAt(4),r.charCodeAt(5),r.charCodeAt(6),r.charCodeAt(7)]}function up(e,a){return Ce.find(e,"EncryptedPackage")?lp(e,a):Li(e,a)}function hp(e,a){var r,n=e,t=a||{};return t.type||(t.type=Se&&Buffer.isBuffer(e)?"buffer":"base64"),r=Cs(n,t),op(r,t)}function Mi(e,a){var r=0;e:for(;r<e.length;)switch(e.charCodeAt(r)){case 10:case 13:case 32:++r;break;case 60:return x0(e.slice(r),a);default:break e}return it.to_workbook(e,a)}function xp(e,a){var r="",n=G0(e,a);switch(a.type){case"base64":r=Fr(e);break;case"binary":r=e;break;case"buffer":r=e.toString("binary");break;case"array":r=Ta(e);break;default:throw new Error("Unrecognized type "+a.type)}return n[0]==239&&n[1]==187&&n[2]==191&&(r=Ne(r)),a.type="binary",Mi(r,a)}function dp(e,a){var r=e;return a.type=="base64"&&(r=Fr(r)),r=c0.utils.decode(1200,r.slice(2),"str"),a.type="binary",Mi(r,a)}function pp(e){return e.match(/[^\x00-\x7F]/)?Ka(e):e}function s0(e,a,r,n){return n?(r.type="string",it.to_workbook(e,r)):it.to_workbook(a,r)}function g0(e,a){os();var r=a||{};if(typeof ArrayBuffer<"u"&&e instanceof ArrayBuffer)return g0(new Uint8Array(e),(r=ar(r),r.type="array",r));typeof Uint8Array<"u"&&e instanceof Uint8Array&&!r.type&&(r.type=typeof Deno<"u"?"buffer":"array");var n=e,t=[0,0,0,0],s=!1;if(r.cellStyles&&(r.cellNF=!0,r.sheetStubs=!0),La={},r.dateNF&&(La.dateNF=r.dateNF),r.type||(r.type=Se&&Buffer.isBuffer(e)?"buffer":"base64"),r.type=="file"&&(r.type=Se?"buffer":"binary",n=ro(e),typeof Uint8Array<"u"&&!Se&&(r.type="array")),r.type=="string"&&(s=!0,r.type="binary",r.codepage=65001,n=pp(e)),r.type=="array"&&typeof Uint8Array<"u"&&e instanceof Uint8Array&&typeof ArrayBuffer<"u"){var i=new ArrayBuffer(3),c=new Uint8Array(i);if(c.foo="bar",!c.foo)return r=ar(r),r.type="array",g0(T0(n),r)}switch((t=G0(n,r))[0]){case 208:if(t[1]===207&&t[2]===17&&t[3]===224&&t[4]===161&&t[5]===177&&t[6]===26&&t[7]===225)return up(Ce.read(n,r),r);break;case 9:if(t[1]<=8)return Li(n,r);break;case 60:return x0(n,r);case 73:if(t[1]===73&&t[2]===42&&t[3]===0)throw new Error("TIFF Image File is not a spreadsheet");if(t[1]===68)return G1(n,r);break;case 84:if(t[1]===65&&t[2]===66&&t[3]===76)return H1.to_workbook(n,r);break;case 80:return t[1]===75&&t[2]<9&&t[3]<9?hp(n,r):s0(e,n,r,s);case 239:return t[3]===60?x0(n,r):s0(e,n,r,s);case 255:if(t[1]===254)return dp(n,r);if(t[1]===0&&t[2]===2&&t[3]===0)return Qa.to_workbook(n,r);break;case 0:if(t[1]===0&&(t[2]>=2&&t[3]===0||t[2]===0&&(t[3]===8||t[3]===9)))return Qa.to_workbook(n,r);break;case 3:case 131:case 139:case 140:return Un.to_workbook(n,r);case 123:if(t[1]===92&&t[2]===114&&t[3]===116)return Eu.to_workbook(n,r);break;case 10:case 13:case 32:return xp(n,r);case 137:if(t[1]===80&&t[2]===78&&t[3]===71)throw new Error("PNG Image File is not a spreadsheet");break}return U1.indexOf(t[0])>-1&&t[2]<=12&&t[3]<=31?Un.to_workbook(n,r):s0(e,n,r,s)}function vp(e,a,r,n,t,s,i,c){var f=tr(r),l=c.defval,o=c.raw||!Object.prototype.hasOwnProperty.call(c,"raw"),u=!0,p=t===1?[]:{};if(t!==1)if(Object.defineProperty)try{Object.defineProperty(p,"__rowNum__",{value:r,enumerable:!1})}catch{p.__rowNum__=r}else p.__rowNum__=r;if(!i||e[r])for(var h=a.s.c;h<=a.e.c;++h){var d=i?e[r][h]:e[n[h]+f];if(d===void 0||d.t===void 0){if(l===void 0)continue;s[h]!=null&&(p[s[h]]=l);continue}var x=d.v;switch(d.t){case"z":if(x==null)break;continue;case"e":x=x==0?null:void 0;break;case"s":case"d":case"b":case"n":break;default:throw new Error("unrecognized type "+d.t)}if(s[h]!=null){if(x==null)if(d.t=="e"&&x===null)p[s[h]]=null;else if(l!==void 0)p[s[h]]=l;else if(o&&x===null)p[s[h]]=null;else continue;else p[s[h]]=o&&(d.t!=="n"||d.t==="n"&&c.rawNumbers!==!1)?x:Zr(d,x,c);x!=null&&(u=!1)}}return{row:p,isempty:u}}function m0(e,a){if(e==null||e["!ref"]==null)return[];var r={t:"n",v:0},n=0,t=1,s=[],i=0,c="",f={s:{r:0,c:0},e:{r:0,c:0}},l=a||{},o=l.range!=null?l.range:e["!ref"];switch(l.header===1?n=1:l.header==="A"?n=2:Array.isArray(l.header)?n=3:l.header==null&&(n=0),typeof o){case"string":f=He(o);break;case"number":f=He(e["!ref"]),f.s.r=o;break;default:f=o}n>0&&(t=0);var u=tr(f.s.r),p=[],h=[],d=0,x=0,g=Array.isArray(e),w=f.s.r,y=0,m={};g&&!e[w]&&(e[w]=[]);var O=l.skipHidden&&e["!cols"]||[],P=l.skipHidden&&e["!rows"]||[];for(y=f.s.c;y<=f.e.c;++y)if(!(O[y]||{}).hidden)switch(p[y]=Je(y),r=g?e[w][y]:e[p[y]+u],n){case 1:s[y]=y-f.s.c;break;case 2:s[y]=p[y];break;case 3:s[y]=l.header[y-f.s.c];break;default:if(r==null&&(r={w:"__EMPTY",t:"s"}),c=i=Zr(r,null,l),x=m[i]||0,!x)m[i]=1;else{do c=i+"_"+x++;while(m[c]);m[i]=x,m[c]=1}s[y]=c}for(w=f.s.r+t;w<=f.e.r;++w)if(!(P[w]||{}).hidden){var D=vp(e,f,w,p,n,s,g,l);(D.isempty===!1||(n===1?l.blankrows!==!1:l.blankrows))&&(h[d++]=D.row)}return h.length=d,h}var as=/"/g;function gp(e,a,r,n,t,s,i,c){for(var f=!0,l=[],o="",u=tr(r),p=a.s.c;p<=a.e.c;++p)if(n[p]){var h=c.dense?(e[r]||[])[p]:e[n[p]+u];if(h==null)o="";else if(h.v!=null){f=!1,o=""+(c.rawNumbers&&h.t=="n"?h.v:Zr(h,null,c));for(var d=0,x=0;d!==o.length;++d)if((x=o.charCodeAt(d))===t||x===s||x===34||c.forceQuotes){o='"'+o.replace(as,'""')+'"';break}o=="ID"&&(o='"ID"')}else h.f!=null&&!h.F?(f=!1,o="="+h.f,o.indexOf(",")>=0&&(o='"'+o.replace(as,'""')+'"')):o="";l.push(o)}return c.blankrows===!1&&f?null:l.join(i)}function Ui(e,a){var r=[],n=a??{};if(e==null||e["!ref"]==null)return"";var t=He(e["!ref"]),s=n.FS!==void 0?n.FS:",",i=s.charCodeAt(0),c=n.RS!==void 0?n.RS:`
`,f=c.charCodeAt(0),l=new RegExp((s=="|"?"\\|":s)+"+$"),o="",u=[];n.dense=Array.isArray(e);for(var p=n.skipHidden&&e["!cols"]||[],h=n.skipHidden&&e["!rows"]||[],d=t.s.c;d<=t.e.c;++d)(p[d]||{}).hidden||(u[d]=Je(d));for(var x=0,g=t.s.r;g<=t.e.r;++g)(h[g]||{}).hidden||(o=gp(e,t,g,u,i,f,s,n),o!=null&&(n.strip&&(o=o.replace(l,"")),(o||n.blankrows!==!1)&&r.push((x++?c:"")+o)));return delete n.dense,r.join("")}function mp(e,a){a||(a={}),a.FS="	",a.RS=`
`;var r=Ui(e,a);return r}function _p(e){var a="",r,n="";if(e==null||e["!ref"]==null)return[];var t=He(e["!ref"]),s="",i=[],c,f=[],l=Array.isArray(e);for(c=t.s.c;c<=t.e.c;++c)i[c]=Je(c);for(var o=t.s.r;o<=t.e.r;++o)for(s=tr(o),c=t.s.c;c<=t.e.c;++c)if(a=i[c]+s,r=l?(e[o]||[])[c]:e[a],n="",r!==void 0){if(r.F!=null){if(a=r.F,!r.f)continue;n=r.f,a.indexOf(":")==-1&&(a=a+":"+a)}if(r.f!=null)n=r.f;else{if(r.t=="z")continue;if(r.t=="n"&&r.v!=null)n=""+r.v;else if(r.t=="b")n=r.v?"TRUE":"FALSE";else if(r.w!==void 0)n="'"+r.w;else{if(r.v===void 0)continue;r.t=="s"?n="'"+r.v:n=""+r.v}}f[f.length]=a+"="+n}return f}function Vi(e,a,r){var n=r||{},t=+!n.skipHeader,s=e||{},i=0,c=0;if(s&&n.origin!=null)if(typeof n.origin=="number")i=n.origin;else{var f=typeof n.origin=="string"?vr(n.origin):n.origin;i=f.r,c=f.c}var l,o={s:{c:0,r:0},e:{c,r:i+a.length-1+t}};if(s["!ref"]){var u=He(s["!ref"]);o.e.c=Math.max(o.e.c,u.e.c),o.e.r=Math.max(o.e.r,u.e.r),i==-1&&(i=u.e.r+1,o.e.r=i+a.length-1+t)}else i==-1&&(i=0,o.e.r=a.length-1+t);var p=n.header||[],h=0;a.forEach(function(x,g){Gr(x).forEach(function(w){(h=p.indexOf(w))==-1&&(p[h=p.length]=w);var y=x[w],m="z",O="",P=ve({c:c+h,r:i+g+t});l=lt(s,P),y&&typeof y=="object"&&!(y instanceof Date)?s[P]=y:(typeof y=="number"?m="n":typeof y=="boolean"?m="b":typeof y=="string"?m="s":y instanceof Date?(m="d",n.cellDates||(m="n",y=mr(y)),O=n.dateNF||_e[14]):y===null&&n.nullError&&(m="e",y=0),l?(l.t=m,l.v=y,delete l.w,delete l.R,O&&(l.z=O)):s[P]=l={t:m,v:y},O&&(l.z=O))})}),o.e.c=Math.max(o.e.c,c+p.length-1);var d=tr(i);if(t)for(h=0;h<p.length;++h)s[Je(h+c)+d]={t:"s",v:p[h]};return s["!ref"]=ye(o),s}function Ep(e,a){return Vi(null,e,a)}function lt(e,a,r){if(typeof a=="string"){if(Array.isArray(e)){var n=vr(a);return e[n.r]||(e[n.r]=[]),e[n.r][n.c]||(e[n.r][n.c]={t:"z"})}return e[a]||(e[a]={t:"z"})}return typeof a!="number"?lt(e,ve(a)):lt(e,ve({r:a,c:r||0}))}function Tp(e,a){if(typeof a=="number"){if(a>=0&&e.SheetNames.length>a)return a;throw new Error("Cannot find sheet # "+a)}else if(typeof a=="string"){var r=e.SheetNames.indexOf(a);if(r>-1)return r;throw new Error("Cannot find sheet name |"+a+"|")}else throw new Error("Cannot find sheet |"+a+"|")}function $0(){return{SheetNames:[],Sheets:{}}}function X0(e,a,r,n){var t=1;if(!r)for(;t<=65535&&e.SheetNames.indexOf(r="Sheet"+t)!=-1;++t,r=void 0);if(!r||e.SheetNames.length>=65535)throw new Error("Too many worksheets");if(n&&e.SheetNames.indexOf(r)>=0){var s=r.match(/(^.*?)(\d+)$/);t=s&&+s[2]||0;var i=s&&s[1]||r;for(++t;t<=65535&&e.SheetNames.indexOf(r=i+t)!=-1;++t);}if(s2(r),e.SheetNames.indexOf(r)>=0)throw new Error("Worksheet with name |"+r+"| already exists!");return e.SheetNames.push(r),e.Sheets[r]=a,r}function kp(e,a,r){e.Workbook||(e.Workbook={}),e.Workbook.Sheets||(e.Workbook.Sheets=[]);var n=Tp(e,a);switch(e.Workbook.Sheets[n]||(e.Workbook.Sheets[n]={}),r){case 0:case 1:case 2:break;default:throw new Error("Bad sheet visibility setting "+r)}e.Workbook.Sheets[n].Hidden=r}function wp(e,a){return e.z=a,e}function Hi(e,a,r){return a?(e.l={Target:a},r&&(e.l.Tooltip=r)):delete e.l,e}function Ap(e,a,r){return Hi(e,"#"+a,r)}function Fp(e,a,r){e.c||(e.c=[]),e.c.push({t:a,a:r||"SheetJS"})}function Sp(e,a,r,n){for(var t=typeof a!="string"?a:He(a),s=typeof a=="string"?a:ye(a),i=t.s.r;i<=t.e.r;++i)for(var c=t.s.c;c<=t.e.c;++c){var f=lt(e,i,c);f.t="n",f.F=s,delete f.v,i==t.s.r&&c==t.s.c&&(f.f=r,n&&(f.D=!0))}return e}var Cp={encode_col:Je,encode_row:tr,encode_cell:ve,encode_range:ye,decode_col:O0,decode_row:D0,split_cell:Mo,decode_cell:vr,decode_range:Ma,format_cell:Zr,sheet_add_aoa:Xs,sheet_add_json:Vi,sheet_add_dom:Pi,aoa_to_sheet:Ua,json_to_sheet:Ep,table_to_sheet:bi,table_to_book:G2,sheet_to_csv:Ui,sheet_to_txt:mp,sheet_to_json:m0,sheet_to_html:W2,sheet_to_formulae:_p,sheet_to_row_object_array:m0,sheet_get_cell:lt,book_new:$0,book_append_sheet:X0,book_set_sheet_visibility:kp,cell_set_number_format:wp,cell_set_hyperlink:Hi,cell_set_internal_link:Ap,cell_add_comment:Fp,sheet_set_array_formula:Sp,consts:{SHEET_VISIBLE:0,SHEET_HIDDEN:1,SHEET_VERY_HIDDEN:2}};const yp={class:"pmm2_flex_between"},Dp={class:"upload-demo-box"},Op={class:"tig-box"},Ip={class:"tig-com huiA"},Rp={key:0,class:"t1"},Np={key:1},Lp={class:"bottom-box"},Pp=ut({__name:"upXlsx",emits:["setParsedData"],setup(e,{expose:a,emit:r}){const n=me(!1),t=me(""),s=me([]),i=me({});a({opens:p=>{t.value="",i.value=p,s.value=[],n.value=!0}});const f=r,l=p=>{n.value=!1;let h=s.value.length;Tr({message:`已添加${h}个单词`,type:"success",plain:!0}),h>0&&f("setParsedData",s.value,p)},o=["xls","xlsx","txt"],u=p=>{let d=p.name.toLowerCase().split(".").pop();if(!o.includes(d))return Tr.error("上传格式不正确，仅支持.xls、.xlsx、.txt格式");t.value=p.name;const x=new FileReader;let g=[];d=="txt"?(x.onload=w=>{var P;const m=(((P=w==null?void 0:w.target)==null?void 0:P.result)||"").split(/[,\uff0c.\u3002\u3001\n]/).filter(D=>D.trim());let O=m.length;if(O>i.value.pageCountSum){Tr({message:`最多支持${i.value.pageCountSum}个单词`,type:"warning"}),t.value="";return}for(let D=0;D<O;D++)g.push({word:String(m[D]||"").trim()});s.value=g},x.readAsText(p)):(x.onload=w=>{const y=new Uint8Array(w.target.result),m=g0(y,{type:"array"}),O=m.SheetNames[0],P=m.Sheets[O],D=Cp.sheet_to_json(P);let S=D.length;if(S>i.value.pageCountSum){Tr({message:`最多支持${i.value.pageCountSum}个单词`,type:"warning"}),t.value="";return}for(let U=0;U<S;U++)if(D[U]){let R=D[U];for(let z in R)g.push({word:String(R[z]||"").trim()})}s.value=g},x.readAsArrayBuffer(p))};return(p,h)=>{const d=va("CloseBold"),x=Wt,g=Gt,w=va("Bell"),y=is,m=xt,O=cc,P=ht;return Ae(),_a(P,{"append-to-body":"",class:"hhypt-dialog-boxs hhypt-dialog-boxs-upXlsx","show-close":!1,modelValue:oe(n),"onUpdate:modelValue":h[2]||(h[2]=D=>Yr(n)?n.value=D:null),title:"上传单词",width:"500","align-center":""},{header:de(({close:D,titleClass:S})=>[q("div",yp,[q("span",{class:pa(S)},"上传单词("+Ye(oe(i).label)+"最多支持"+Ye(oe(i).pageCountSum)+"个单词)",3),xe(g,{onClick:D,underline:!1},{default:de(()=>[xe(x,{size:"20"},{default:de(()=>[xe(d)]),_:1})]),_:2},1032,["onClick"])])]),default:de(()=>[q("div",Dp,[q("div",Op,[xe(y,{width:400,"popper-style":"box-shadow: rgb(14 18 22 / 35%) 0px 10px 38px -10px, rgb(14 18 22 / 20%) 0px 10px 20px -15px; padding: 20px;"},{reference:de(()=>[q("div",Ip,[xe(x,null,{default:de(()=>[xe(w)]),_:1}),h[3]||(h[3]=q("span",{class:"c1"},"上传规则",-1)),h[4]||(h[4]=q("span",{class:"c2"},"查看",-1))])]),default:de(()=>h[5]||(h[5]=[q("div",{class:"demo-rich-conent"},[q("p",null,"1. txt格式文件每个单词需要使用中文或英文逗号隔开。"),q("p",null,"2. xls和xlsx格式文件内容如下"),q("img",{style:{width:"100%"},src:Af,alt:""})],-1)])),_:1})]),xe(O,{class:"upload-demo",drag:"",action:"#","http-request":()=>{},"before-upload":u,"show-file-list":!1,accept:".xls,.xlsx,.txt"},{default:de(()=>[oe(t)?(Ae(),Re("div",Rp,Ye(oe(t)),1)):(Ae(),Re("div",Np,h[6]||(h[6]=[q("div",{class:"t1"},"点击选择文件或将文件拖入框中",-1),q("div",{class:"t2"},[Qe("导入仅支持.xls、.xlsx、.txt格式，大小"),q("span",{style:{color:"red"}},"不超过10M")],-1)]))),xe(m,null,{default:de(()=>h[7]||(h[7]=[Qe("选择文件")])),_:1,__:[7]})]),_:1})]),q("div",Lp,[xe(m,{onClick:h[0]||(h[0]=D=>n.value=!1)},{default:de(()=>h[8]||(h[8]=[Qe("取消")])),_:1,__:[8]}),xe(m,{type:"primary",onClick:h[1]||(h[1]=D=>l("cover"))},{default:de(()=>h[9]||(h[9]=[Qe("确认")])),_:1,__:[9]})])]),_:1},8,["modelValue"])}}}),bp=dt(Pp,[["__scopeId","data-v-b155a0b1"]]),Bp={key:0,class:"pmm2_flex_center",style:{"flex-direction":"column"}},Mp={style:{color:"#999"}},Up={key:1,class:"cards-box"},Vp={class:"header-box"},Hp={class:"containers pmm2_flex_between"},Wp={class:"left-box"},Gp={href:"/"},$p=["src"],Xp={class:"right-box"},zp={class:"main-box scrollBar"},Kp={key:0,class:"containers"},Yp={class:"setup-com",style:{"margin-bottom":"20px"}},jp={class:"com"},Jp={class:"com"},Zp={style:{"margin-top":"15px"}},qp={class:"pmm2_flex_between"},Qp={class:"preview-box scrollBar","align-center":""},ev={class:"previewList",style:{width:"100%"}},rv=ut({__name:"index",setup(e){function a(L){return new URLSearchParams(window.location.search).get(L)}const r=me("");let n=a("uuid");const t=me([]);let s=me({});const i=me([]),c=fc(()=>i.value.length),f=me(!0),l=me(n),o=L=>{l.value=wc()};let u={};const p=me(null),h=me(null),d=me(!1),x=me(!1),g=async()=>{var B,N,I;if(!n){r.value="非法请求";return}const L=await mc({uuid:n});if(L.code!=0)return f.value=!1,r.value=L.msg,Tr.error(L.msg);let X=L.data||{};u=X.cardInfo;let ue=u.setting,A=((N=(B=ue.pageLayout)==null?void 0:B.printLayout)==null?void 0:N.column)||2;s.value={courseName:u.courseName,title:u.title,id:u.id,showCreatorInfoVal:u.showCreatorInfoVal||"",created_by_name:u.created_by_name||"-",createdAt:u.createdAt,headNum:A*2,column:A,layoutOpen:!0},t.value=u.metadata,Ue().setting=ue,i.value=X.wordList||[],x.value=!1,((I=X.coordinateInfo)==null?void 0:I.length)>0?(Ue().pageStyles=X.cardInfo.pageStyle,y()):(d.value=!0,lc(()=>{h.value.opens()})),f.value=!1},w=me(0),y=()=>{let{headHeight:L,pageNumberHeight:X,handWriteHeadHeight:ue}=Ue().pageStyles,A=L;Ue().setting.examNoTypeId!="FILL"&&(A=ue||L);let B=Ue().setting.pageLayout.columnWidth.height-(Ue().pageStyles.pagePadding[0]*2+X),N=i.value.length;s.value.pageCount1=Math.floor((B-A)/50)*2-2,s.value.pageCount2=Math.floor(B/50)*2-2,w.value=0;for(let I=0;I<s.value.headNum;I++)I==0?w.value+=s.value.pageCount1:w.value+=s.value.pageCount2;N>w.value&&(i.value=i.value.slice(0,w.value),Tr({message:"超出部分已经截取！",type:"warning"}))};g();const m=me(null),O=()=>{var L;m.value.opens({label:(L=Ue().setting.pageLayout)==null?void 0:L.label,pageCountSum:w.value,stageId:u.stageId,stageLabel:u.stageLabel,courseCode:u.courseCode,courseName:u.courseName})},P=me(null),D=()=>{var L;P.value.opens({label:(L=Ue().setting.pageLayout)==null?void 0:L.label,pageCountSum:w.value})},S=(L,X)=>{L.length>0&&(X=="add"?i.value.push(...L):i.value=L,x.value=!0,o())},U=L=>{var ue,A;let X=((A=(ue=Ue().setting.pageLayout)==null?void 0:ue.printLayout)==null?void 0:A.column)||2;s.value.headNum=X*2,d.value&&(d.value=!1),y(),o()},R=()=>{for(let L=c.value-1;L>0;L--){const X=Math.floor(Math.random()*(L+1));[i.value[L],i.value[X]]=[i.value[X],i.value[L]]}o()},z=()=>{const L=p.value.getZB();let X={uuid:n,coordinateInfo:L,wordList:i.value,cardInfo:{...u,showCreatorInfoVal:s.value.showCreatorInfoVal,title:s.value.title,setting:Ue().setting,pageStyle:Ue().pageStyles}};return o(),X},G=async L=>{let X="保存";const ue=q0.service({lock:!0,text:X+"中..",background:"rgba(0, 0, 0, 0.7)"});let A=z(),B={};if(B=await _c({uuid:n,data:A}),ue.close(),B.code==0)x.value=!1,Tr({message:X+"成功",type:"success",plain:!0});else return Tr.error(X+"失败")},b=me(null),j=async()=>{var X;let L=s.value.title+"-"+s.value.courseName+"（音频）.mp3";(X=b==null?void 0:b.value)==null||X.opens(n,L)},Y=async L=>{const X=q0.service({lock:!0,text:"下载中..",background:"rgba(0, 0, 0, 0.7)"});let ue=z(),A=await Ec({uuid:n,data:ue});try{let B=s.value.title+"-"+s.value.courseName+".pdf";hc(A,B),setTimeout(()=>{X.close()},1e3)}catch{if(X.close(),A.msg)return Tr.error(A.msg)}},Q=me(!1),se=me(!0),ne=me([]),De=async()=>{ne.value=[],Q.value=!0,se.value=!0;let L=z(),X=await Tc({uuid:n,data:L});if(X.code==0)x.value=!1,ne.value=X.data||[],se.value=!1;else return Tr.error(X.msg)};return(L,X)=>{var ke,C,Le,Fe,Ie,Ee,fe,Pe,hr,kr,Sr,Qr,Nr;const ue=xt,A=ns,B=ss,N=cs,I=va("CloseBold"),J=Wt,Z=Gt,ie=ts,te=ht,ee=oc;return oe(r)?(Ae(),Re("div",Bp,[X[12]||(X[12]=q("img",{style:{width:"200px"},src:"https://oss.hhypt.com/resources/mini-parent-helper/err.png"},null,-1)),q("span",Mp,Ye(oe(r)),1)])):(Ae(),Re("div",Up,[q("header",Vp,[q("div",Hp,[q("div",Wp,[q("a",Gp,[q("img",{src:oe(kc)(),alt:"Logo",width:"218",height:"38"},null,8,$p)])]),X[20]||(X[20]=q("div",{class:"title",style:{flex:"1","text-align":"center"}},"听力默写",-1)),q("div",Xp,[xe(ue,{onClick:X[0]||(X[0]=we=>De())},{default:de(()=>X[13]||(X[13]=[Qe("预览")])),_:1,__:[13]}),xe(ue,{onClick:X[1]||(X[1]=we=>R()),type:"danger",plain:"",disabled:oe(c)==0},{default:de(()=>X[14]||(X[14]=[Qe("打乱顺序")])),_:1,__:[14]},8,["disabled"]),xe(ue,{onClick:X[2]||(X[2]=we=>O())},{default:de(()=>X[15]||(X[15]=[Qe("选择单词")])),_:1,__:[15]}),xe(ue,{onClick:X[3]||(X[3]=we=>D())},{default:de(()=>X[16]||(X[16]=[Qe("上传单词")])),_:1,__:[16]}),xe(ue,{onClick:X[4]||(X[4]=we=>G()),type:"primary",disabled:oe(c)==0},{default:de(()=>X[17]||(X[17]=[Qe("保存")])),_:1,__:[17]},8,["disabled"]),xe(ue,{onClick:X[5]||(X[5]=we=>Y()),type:"primary",disabled:oe(c)==0||oe(x)},{default:de(()=>X[18]||(X[18]=[Qe("下载题卡")])),_:1,__:[18]},8,["disabled"]),xe(ue,{onClick:X[6]||(X[6]=we=>j()),type:"primary",disabled:oe(c)==0||oe(x)},{default:de(()=>X[19]||(X[19]=[Qe("下载音频")])),_:1,__:[19]},8,["disabled"])])])]),J0((Ae(),Re("main",zp,[!oe(f)&&!oe(d)?(Ae(),Re("div",Kp,[(Ae(),Re("div",{class:"topic-box scrollBar",key:oe(l)},[q("div",{ref:"topicBoxs",style:Z0(`transform-origin: top left;transition: transform 0.2s; transform: translate(0, 0);margin: 0 auto;
					 width: ${(Le=(C=(ke=oe(Ue)().setting)==null?void 0:ke.pageLayout)==null?void 0:C.columnWidth)==null?void 0:Le.width}px;
					 min-width: ${(Ee=(Ie=(Fe=oe(Ue)().setting)==null?void 0:Fe.pageLayout)==null?void 0:Ie.columnWidth)==null?void 0:Ee.width}px;
					 max-width: ${(hr=(Pe=(fe=oe(Ue)().setting)==null?void 0:fe.pageLayout)==null?void 0:Pe.columnWidth)==null?void 0:hr.width}px;
					`)},[xe(gc,{ref_key:"subjectRef",ref:p,questionsArr:oe(i),layout:oe(s)},null,8,["questionsArr","layout"])],4)])),q("div",{class:"setup-box scrollBar",style:Z0(`width: calc(100% - ${((Qr=(Sr=(kr=oe(Ue)().setting)==null?void 0:kr.pageLayout)==null?void 0:Sr.columnWidth)==null?void 0:Qr.width)+4}px)`)},[q("div",Yp,[X[23]||(X[23]=q("div",{class:"title-box pmm2_flex_between"},[q("div",{class:"title"},[q("span",{class:"bt"},"版面设计")])],-1)),q("ul",null,[q("li",null,[X[21]||(X[21]=q("div",{class:"name"},"考号识别",-1)),q("div",jp,[xe(B,{onChange:X[7]||(X[7]=we=>o()),modelValue:oe(Ue)().setting.examNoTypeId,"onUpdate:modelValue":X[8]||(X[8]=we=>oe(Ue)().setting.examNoTypeId=we)},{default:de(()=>[(Ae(!0),Re(na,null,ta(oe(t).examNoTypes,we=>(Ae(),_a(A,{value:we.value,size:"large"},{default:de(()=>[q("div",null,Ye(we.label),1)]),_:2},1032,["value"]))),256))]),_:1},8,["modelValue"])])]),q("li",null,[X[22]||(X[22]=q("div",{class:"name"},"页面布局",-1)),q("div",Jp,[q("span",null,Ye((Nr=oe(Ue)().setting.pageLayout)==null?void 0:Nr.label),1),q("span",{class:"huiA",style:{"padding-left":"5px"},onClick:X[9]||(X[9]=(...we)=>oe(h).opens&&oe(h).opens(...we))},"修改")])])]),q("div",Zp,[xe(N,{modelValue:oe(Ue)().pageStyles.showCreatorInfo,"onUpdate:modelValue":X[10]||(X[10]=we=>oe(Ue)().pageStyles.showCreatorInfo=we),label:"显示制卡信息"},null,8,["modelValue"])])])],4)])):i0("",!0)])),[[ee,oe(f)]]),xe(wf,{ref_key:"chooseWordsRef",ref:m,onSetParsedData:S},null,512),xe(bp,{ref_key:"upXlsxRef",ref:P,onSetParsedData:S},null,512),xe(Uc,{ref_key:"downAudioRef",ref:b},null,512),xe(Rc,{paperCardPageLayouts:oe(t).paperCardPageLayouts,answerSheetPageLayouts:oe(t).answerSheetPageLayouts,ref_key:"papeSizeRef",ref:h,onSetCardSetting:U,cardPageStyles:oe(t).cardPageStyles},null,8,["paperCardPageLayouts","answerSheetPageLayouts","cardPageStyles"]),xe(te,{"append-to-body":"",class:"hhypt-dialog-boxs hhypt-dialog-boxs2","show-close":!1,modelValue:oe(Q),"onUpdate:modelValue":X[11]||(X[11]=we=>Yr(Q)?Q.value=we:null),title:"预览",width:"1400","align-center":""},{header:de(({close:we,titleClass:_r})=>[q("div",qp,[q("span",{class:pa(_r)},"预览",2),xe(Z,{onClick:we,underline:!1},{default:de(()=>[xe(J,{size:"20"},{default:de(()=>[xe(I)]),_:1})]),_:2},1032,["onClick"])])]),default:de(()=>[J0((Ae(),Re("div",Qp,[q("div",ev,[(Ae(!0),Re(na,null,ta(oe(ne),(we,_r)=>(Ae(),_a(ie,{class:"demo-image-box",key:_r,src:we,"preview-src-list":oe(ne),"initial-index":_r},null,8,["src","preview-src-list","initial-index"]))),128))])])),[[ee,oe(se)]])]),_:1},8,["modelValue"])]))}}}),Sv=dt(rv,[["__scopeId","data-v-64d5d759"]]);export{Sv as default};

import{d as le,c as u,o as h,e as M,a as l,K as oe,Z as de,au as j,n as Z,t as _,A as be,B as Ce,r as A,F as W,f as C,E as $e,w as O,u as d,i as ge,L as ve,q as T,h as z,U as Ie,b as Q,T as ke,v as Oe,s as Me,az as He,g as Xe,R as he,G as Se,k as me,l as ie,C as _e,j as we,m as We,p as ee,aL as Ye,aO as Le,O as Ee}from"./index.ZZ6UQeaF.js";/* empty css                        *//* empty css                  *//* empty css                   *//* empty css                  */import{s as re}from"./request.CsKOOJzG.js";/* empty css                *//* empty css                    *//* empty css                 *//* empty css                        *//* empty css                     *//* empty css                  *//* empty css                       *//* empty css                   */import{_ as xe}from"./_plugin-vue_export-helper.DlAUqK2U.js";/* empty css                       */import"./index.xsH4HHeE.js";const ae="api/";function ze(e){return re({url:ae+"v1/writing-demo/save",method:"post",data:e,headers:{"Content-Type":"multipart/form-data"}})}function Te(e){return re({url:ae+"v1/writing-demo/list",method:"post",data:e})}function Be(e){return re({url:ae+"v1/writing-demo/delete",method:"post",data:e})}function Ne(e){return re({url:ae+"v1/writing-demo/status",method:"post",data:e})}function Ae(e){return re({url:ae+"v1/writing-demo/detail",method:"post",data:e})}const ye={};ye.getData=e=>new Promise((o,t)=>{let i={};Ve(e).then(s=>{i.arrayBuffer=s;try{i.orientation=De(s)}catch{i.orientation=-1}o(i)}).catch(s=>{t(s)})});function Ve(e){let o=null;return new Promise((t,i)=>{if(e.src)if(/^data\:/i.test(e.src))o=Re(e.src),t(o);else if(/^blob\:/i.test(e.src)){var s=new FileReader;s.onload=function(a){o=a.target.result,t(o)},Ue(e.src,function(a){s.readAsArrayBuffer(a)})}else{var r=new XMLHttpRequest;r.onload=function(){if(this.status==200||this.status===0)o=r.response,t(o);else throw"Could not load image";r=null},r.open("GET",e.src,!0),r.responseType="arraybuffer",r.send(null)}else i("img error")})}function Ue(e,o){var t=new XMLHttpRequest;t.open("GET",e,!0),t.responseType="blob",t.onload=function(i){(this.status==200||this.status===0)&&o(this.response)},t.send()}function Re(e,o){o=o||e.match(/^data\:([^\;]+)\;base64,/mi)[1]||"",e=e.replace(/^data\:([^\;]+)\;base64,/gmi,"");for(var t=atob(e),i=t.length%2==0?t.length:t.length+1,s=new ArrayBuffer(i),r=new Uint16Array(s),a=0;a<i;a++)r[a]=t.charCodeAt(a);return s}function Pe(e,o,t){var i="",s;for(s=o,t+=o;s<t;s++)i+=String.fromCharCode(e.getUint8(s));return i}function De(e){var o=new DataView(e),t=o.byteLength,i,s,r,a,n,p,g,y,b,x;if(o.getUint8(0)===255&&o.getUint8(1)===216)for(b=2;b<t;){if(o.getUint8(b)===255&&o.getUint8(b+1)===225){g=b;break}b++}if(g&&(s=g+4,r=g+10,Pe(o,s,4)==="Exif"&&(p=o.getUint16(r),n=p===18761,(n||p===19789)&&o.getUint16(r+2,n)===42&&(a=o.getUint32(r+4,n),a>=8&&(y=r+a)))),y){for(t=o.getUint16(y,n),x=0;x<t;x++)if(b=y+x*12+2,o.getUint16(b,n)===274){b+=8,i=o.getUint16(b,n);break}}return i}const je=(e,o)=>{const t=e.__vccOpts||e;for(const[i,s]of o)t[i]=s;return t},Fe=le({data:function(){return{w:0,h:0,scale:1,x:0,y:0,loading:!0,trueWidth:0,trueHeight:0,move:!0,moveX:0,moveY:0,crop:!1,cropping:!1,cropW:0,cropH:0,cropOldW:0,cropOldH:0,canChangeX:!1,canChangeY:!1,changeCropTypeX:1,changeCropTypeY:1,cropX:0,cropY:0,cropChangeX:0,cropChangeY:0,cropOffsertX:0,cropOffsertY:0,support:"",touches:[],touchNow:!1,rotate:0,isIos:!1,orientation:0,imgs:"",coe:.2,scaling:!1,scalingSet:"",coeStatus:"",isCanShow:!0,imgIsQqualCrop:!1}},props:{img:{type:[String,Blob,null,File],default:""},outputSize:{type:Number,default:1},outputType:{type:String,default:"jpeg"},info:{type:Boolean,default:!0},canScale:{type:Boolean,default:!0},autoCrop:{type:Boolean,default:!1},autoCropWidth:{type:[Number,String],default:0},autoCropHeight:{type:[Number,String],default:0},fixed:{type:Boolean,default:!1},fixedNumber:{type:Array,default:()=>[1,1]},fixedBox:{type:Boolean,default:!1},full:{type:Boolean,default:!1},canMove:{type:Boolean,default:!0},canMoveBox:{type:Boolean,default:!0},original:{type:Boolean,default:!1},centerBox:{type:Boolean,default:!1},high:{type:Boolean,default:!0},infoTrue:{type:Boolean,default:!1},maxImgSize:{type:[Number,String],default:2e3},enlarge:{type:[Number,String],default:1},preW:{type:[Number,String],default:0},mode:{type:String,default:"contain"},limitMinSize:{type:[Number,Array,String],default:()=>10,validator:function(e){return Array.isArray(e)?Number(e[0])>=0&&Number(e[1])>=0:Number(e)>=0}},fillColor:{type:String,default:""}},computed:{cropInfo(){let e={};if(e.top=this.cropOffsertY>21?"-21px":"0px",e.width=this.cropW>0?this.cropW:0,e.height=this.cropH>0?this.cropH:0,this.infoTrue){let o=1;this.high&&!this.full&&(o=window.devicePixelRatio),this.enlarge!==1&!this.full&&(o=Math.abs(Number(this.enlarge))),e.width=e.width*o,e.height=e.height*o,this.full&&(e.width=e.width/this.scale,e.height=e.height/this.scale)}return e.width=e.width.toFixed(0),e.height=e.height.toFixed(0),e},isIE(){return!!window.ActiveXObject||"ActiveXObject"in window},passive(){return this.isIE?null:{passive:!1}},isRotateRightOrLeft(){return[1,-1,3,-3].includes(this.rotate)}},watch:{img(){this.checkedImg()},imgs(e){e!==""&&this.reload()},cropW(){this.showPreview()},cropH(){this.showPreview()},cropOffsertX(){this.showPreview()},cropOffsertY(){this.showPreview()},scale(e,o){this.showPreview()},x(){this.showPreview()},y(){this.showPreview()},autoCrop(e){e&&this.goAutoCrop()},autoCropWidth(){this.autoCrop&&this.goAutoCrop()},autoCropHeight(){this.autoCrop&&this.goAutoCrop()},mode(){this.checkedImg()},rotate(){this.showPreview(),this.autoCrop?this.goAutoCrop(this.cropW,this.cropH):(this.cropW>0||this.cropH>0)&&this.goAutoCrop(this.cropW,this.cropH)}},methods:{getVersion(e){var o=navigator.userAgent.split(" "),t="";let i=0;const s=new RegExp(e,"i");for(var r=0;r<o.length;r++)s.test(o[r])&&(t=o[r]);return t?i=t.split("/")[1].split("."):i=["0","0","0"],i},checkOrientationImage(e,o,t,i){if(this.getVersion("chrome")[0]>=81)o=-1;else if(this.getVersion("safari")[0]>=605){const a=this.getVersion("version");a[0]>13&&a[1]>1&&(o=-1)}else{const a=navigator.userAgent.toLowerCase().match(/cpu iphone os (.*?) like mac os/);if(a){let n=a[1];n=n.split("_"),(n[0]>13||n[0]>=13&&n[1]>=4)&&(o=-1)}}let s=document.createElement("canvas"),r=s.getContext("2d");switch(r.save(),o){case 2:s.width=t,s.height=i,r.translate(t,0),r.scale(-1,1);break;case 3:s.width=t,s.height=i,r.translate(t/2,i/2),r.rotate(180*Math.PI/180),r.translate(-t/2,-i/2);break;case 4:s.width=t,s.height=i,r.translate(0,i),r.scale(1,-1);break;case 5:s.height=t,s.width=i,r.rotate(.5*Math.PI),r.scale(1,-1);break;case 6:s.width=i,s.height=t,r.translate(i/2,t/2),r.rotate(90*Math.PI/180),r.translate(-t/2,-i/2);break;case 7:s.height=t,s.width=i,r.rotate(.5*Math.PI),r.translate(t,-i),r.scale(-1,1);break;case 8:s.height=t,s.width=i,r.translate(i/2,t/2),r.rotate(-90*Math.PI/180),r.translate(-t/2,-i/2);break;default:s.width=t,s.height=i}r.drawImage(e,0,0,t,i),r.restore(),s.toBlob(a=>{let n=URL.createObjectURL(a);URL.revokeObjectURL(this.imgs),this.imgs=n},"image/"+this.outputType,1)},checkedImg(){if(this.img===null||this.img===""){this.imgs="",this.clearCrop();return}this.loading=!0,this.scale=1,this.rotate=0,this.imgIsQqualCrop=!1,this.clearCrop();let e=new Image;if(e.onload=()=>{if(this.img==="")return this.$emit("img-load",new Error("图片不能为空")),!1;let t=e.width,i=e.height;ye.getData(e).then(s=>{this.orientation=s.orientation||1;let r=Number(this.maxImgSize);if(!this.orientation&&t<r&i<r){this.imgs=this.img;return}t>r&&(i=i/t*r,t=r),i>r&&(t=t/i*r,i=r),this.checkOrientationImage(e,this.orientation,t,i)}).catch(s=>{this.$emit("img-load","error"),this.$emit("img-load-error",s)})},e.onerror=t=>{this.$emit("img-load","error"),this.$emit("img-load-error",t)},this.img.substr(0,4)!=="data"&&(e.crossOrigin=""),this.isIE){var o=new XMLHttpRequest;o.onload=function(){var t=URL.createObjectURL(this.response);e.src=t},o.open("GET",this.img,!0),o.responseType="blob",o.send()}else e.src=this.img},startMove(e){if(e.preventDefault(),this.move&&!this.crop){if(!this.canMove)return!1;this.moveX=("clientX"in e?e.clientX:e.touches[0].clientX)-this.x,this.moveY=("clientY"in e?e.clientY:e.touches[0].clientY)-this.y,e.touches?(window.addEventListener("touchmove",this.moveImg),window.addEventListener("touchend",this.leaveImg),e.touches.length==2&&(this.touches=e.touches,window.addEventListener("touchmove",this.touchScale),window.addEventListener("touchend",this.cancelTouchScale))):(window.addEventListener("mousemove",this.moveImg),window.addEventListener("mouseup",this.leaveImg)),this.$emit("img-moving",{moving:!0,axis:this.getImgAxis()})}else this.cropping=!0,window.addEventListener("mousemove",this.createCrop),window.addEventListener("mouseup",this.endCrop),window.addEventListener("touchmove",this.createCrop),window.addEventListener("touchend",this.endCrop),this.cropOffsertX=e.offsetX?e.offsetX:e.touches[0].pageX-this.$refs.cropper.offsetLeft,this.cropOffsertY=e.offsetY?e.offsetY:e.touches[0].pageY-this.$refs.cropper.offsetTop,this.cropX="clientX"in e?e.clientX:e.touches[0].clientX,this.cropY="clientY"in e?e.clientY:e.touches[0].clientY,this.cropChangeX=this.cropOffsertX,this.cropChangeY=this.cropOffsertY,this.cropW=0,this.cropH=0},touchScale(e){e.preventDefault();let o=this.scale;var t={x:this.touches[0].clientX,y:this.touches[0].clientY},i={x:e.touches[0].clientX,y:e.touches[0].clientY},s={x:this.touches[1].clientX,y:this.touches[1].clientY},r={x:e.touches[1].clientX,y:e.touches[1].clientY},a=Math.sqrt(Math.pow(t.x-s.x,2)+Math.pow(t.y-s.y,2)),n=Math.sqrt(Math.pow(i.x-r.x,2)+Math.pow(i.y-r.y,2)),p=n-a,g=1;g=g/this.trueWidth>g/this.trueHeight?g/this.trueHeight:g/this.trueWidth,g=g>.1?.1:g;var y=g*p;if(!this.touchNow){if(this.touchNow=!0,p>0?o+=Math.abs(y):p<0&&o>Math.abs(y)&&(o-=Math.abs(y)),this.touches=e.touches,setTimeout(()=>{this.touchNow=!1},8),!this.checkoutImgAxis(this.x,this.y,o))return!1;this.scale=o}},cancelTouchScale(e){window.removeEventListener("touchmove",this.touchScale)},moveImg(e){if(e.preventDefault(),e.touches&&e.touches.length===2)return this.touches=e.touches,window.addEventListener("touchmove",this.touchScale),window.addEventListener("touchend",this.cancelTouchScale),window.removeEventListener("touchmove",this.moveImg),!1;let o="clientX"in e?e.clientX:e.touches[0].clientX,t="clientY"in e?e.clientY:e.touches[0].clientY,i,s;i=o-this.moveX,s=t-this.moveY,this.$nextTick(()=>{if(this.centerBox){let r=this.getImgAxis(i,s,this.scale),a=this.getCropAxis(),n=this.trueHeight*this.scale,p=this.trueWidth*this.scale,g,y,b,x;switch(this.rotate){case 1:case-1:case 3:case-3:g=this.cropOffsertX-this.trueWidth*(1-this.scale)/2+(n-p)/2,y=this.cropOffsertY-this.trueHeight*(1-this.scale)/2+(p-n)/2,b=g-n+this.cropW,x=y-p+this.cropH;break;default:g=this.cropOffsertX-this.trueWidth*(1-this.scale)/2,y=this.cropOffsertY-this.trueHeight*(1-this.scale)/2,b=g-p+this.cropW,x=y-n+this.cropH;break}r.x1>=a.x1&&(i=g),r.y1>=a.y1&&(s=y),r.x2<=a.x2&&(i=b),r.y2<=a.y2&&(s=x)}this.x=i,this.y=s,this.$emit("img-moving",{moving:!0,axis:this.getImgAxis()})})},leaveImg(e){window.removeEventListener("mousemove",this.moveImg),window.removeEventListener("touchmove",this.moveImg),window.removeEventListener("mouseup",this.leaveImg),window.removeEventListener("touchend",this.leaveImg),this.$emit("img-moving",{moving:!1,axis:this.getImgAxis()})},scaleImg(){this.canScale&&window.addEventListener(this.support,this.changeSize,this.passive)},cancelScale(){this.canScale&&window.removeEventListener(this.support,this.changeSize)},changeSize(e){e.preventDefault();let o=this.scale;var t=e.deltaY||e.wheelDelta,i=navigator.userAgent.indexOf("Firefox");t=i>0?t*30:t,this.isIE&&(t=-t);var s=this.coe;s=s/this.trueWidth>s/this.trueHeight?s/this.trueHeight:s/this.trueWidth;var r=s*t;r<0?o+=Math.abs(r):o>Math.abs(r)&&(o-=Math.abs(r));let a=r<0?"add":"reduce";if(a!==this.coeStatus&&(this.coeStatus=a,this.coe=.2),this.scaling||(this.scalingSet=setTimeout(()=>{this.scaling=!1,this.coe=this.coe+=.01},50)),this.scaling=!0,!this.checkoutImgAxis(this.x,this.y,o))return!1;this.scale=o},changeScale(e){let o=this.scale;e=e||1;var t=20;if(t=t/this.trueWidth>t/this.trueHeight?t/this.trueHeight:t/this.trueWidth,e=e*t,e>0?o+=Math.abs(e):o>Math.abs(e)&&(o-=Math.abs(e)),!this.checkoutImgAxis(this.x,this.y,o))return!1;this.scale=o},createCrop(e){e.preventDefault();var o="clientX"in e?e.clientX:e.touches?e.touches[0].clientX:0,t="clientY"in e?e.clientY:e.touches?e.touches[0].clientY:0;this.$nextTick(()=>{var i=o-this.cropX,s=t-this.cropY;if(i>0?(this.cropW=i+this.cropChangeX>this.w?this.w-this.cropChangeX:i,this.cropOffsertX=this.cropChangeX):(this.cropW=this.w-this.cropChangeX+Math.abs(i)>this.w?this.cropChangeX:Math.abs(i),this.cropOffsertX=this.cropChangeX+i>0?this.cropChangeX+i:0),!this.fixed)s>0?(this.cropH=s+this.cropChangeY>this.h?this.h-this.cropChangeY:s,this.cropOffsertY=this.cropChangeY):(this.cropH=this.h-this.cropChangeY+Math.abs(s)>this.h?this.cropChangeY:Math.abs(s),this.cropOffsertY=this.cropChangeY+s>0?this.cropChangeY+s:0);else{var r=this.cropW/this.fixedNumber[0]*this.fixedNumber[1];r+this.cropOffsertY>this.h?(this.cropH=this.h-this.cropOffsertY,this.cropW=this.cropH/this.fixedNumber[1]*this.fixedNumber[0],i>0?this.cropOffsertX=this.cropChangeX:this.cropOffsertX=this.cropChangeX-this.cropW):this.cropH=r,this.cropOffsertY=this.cropOffsertY}})},changeCropSize(e,o,t,i,s){e.preventDefault(),window.addEventListener("mousemove",this.changeCropNow),window.addEventListener("mouseup",this.changeCropEnd),window.addEventListener("touchmove",this.changeCropNow),window.addEventListener("touchend",this.changeCropEnd),this.canChangeX=o,this.canChangeY=t,this.changeCropTypeX=i,this.changeCropTypeY=s,this.cropX="clientX"in e?e.clientX:e.touches[0].clientX,this.cropY="clientY"in e?e.clientY:e.touches[0].clientY,this.cropOldW=this.cropW,this.cropOldH=this.cropH,this.cropChangeX=this.cropOffsertX,this.cropChangeY=this.cropOffsertY,this.fixed&&this.canChangeX&&this.canChangeY&&(this.canChangeY=0),this.$emit("change-crop-size",{width:this.cropW,height:this.cropH})},changeCropNow(e){e.preventDefault();var o="clientX"in e?e.clientX:e.touches?e.touches[0].clientX:0,t="clientY"in e?e.clientY:e.touches?e.touches[0].clientY:0;let i=this.w,s=this.h,r=0,a=0;if(this.centerBox){let g=this.getImgAxis(),y=g.x2,b=g.y2;r=g.x1>0?g.x1:0,a=g.y1>0?g.y1:0,i>y&&(i=y),s>b&&(s=b)}const[n,p]=this.checkCropLimitSize();this.$nextTick(()=>{var g=o-this.cropX,y=t-this.cropY;if(this.canChangeX&&(this.changeCropTypeX===1?this.cropOldW-g<n?(this.cropW=n,this.cropOffsertX=this.cropOldW+this.cropChangeX-r-n):this.cropOldW-g>0?(this.cropW=i-this.cropChangeX-g<=i-r?this.cropOldW-g:this.cropOldW+this.cropChangeX-r,this.cropOffsertX=i-this.cropChangeX-g<=i-r?this.cropChangeX+g:r):(this.cropW=Math.abs(g)+this.cropChangeX<=i?Math.abs(g)-this.cropOldW:i-this.cropOldW-this.cropChangeX,this.cropOffsertX=this.cropChangeX+this.cropOldW):this.changeCropTypeX===2&&(this.cropOldW+g<n?this.cropW=n:this.cropOldW+g>0?(this.cropW=this.cropOldW+g+this.cropOffsertX<=i?this.cropOldW+g:i-this.cropOffsertX,this.cropOffsertX=this.cropChangeX):(this.cropW=i-this.cropChangeX+Math.abs(g+this.cropOldW)<=i-r?Math.abs(g+this.cropOldW):this.cropChangeX-r,this.cropOffsertX=i-this.cropChangeX+Math.abs(g+this.cropOldW)<=i-r?this.cropChangeX-Math.abs(g+this.cropOldW):r))),this.canChangeY&&(this.changeCropTypeY===1?this.cropOldH-y<p?(this.cropH=p,this.cropOffsertY=this.cropOldH+this.cropChangeY-a-p):this.cropOldH-y>0?(this.cropH=s-this.cropChangeY-y<=s-a?this.cropOldH-y:this.cropOldH+this.cropChangeY-a,this.cropOffsertY=s-this.cropChangeY-y<=s-a?this.cropChangeY+y:a):(this.cropH=Math.abs(y)+this.cropChangeY<=s?Math.abs(y)-this.cropOldH:s-this.cropOldH-this.cropChangeY,this.cropOffsertY=this.cropChangeY+this.cropOldH):this.changeCropTypeY===2&&(this.cropOldH+y<p?this.cropH=p:this.cropOldH+y>0?(this.cropH=this.cropOldH+y+this.cropOffsertY<=s?this.cropOldH+y:s-this.cropOffsertY,this.cropOffsertY=this.cropChangeY):(this.cropH=s-this.cropChangeY+Math.abs(y+this.cropOldH)<=s-a?Math.abs(y+this.cropOldH):this.cropChangeY-a,this.cropOffsertY=s-this.cropChangeY+Math.abs(y+this.cropOldH)<=s-a?this.cropChangeY-Math.abs(y+this.cropOldH):a))),this.canChangeX&&this.fixed){var b=this.cropW/this.fixedNumber[0]*this.fixedNumber[1];b<p?(this.cropH=p,this.cropW=this.fixedNumber[0]*p/this.fixedNumber[1],this.changeCropTypeX===1&&(this.cropOffsertX=this.cropChangeX+(this.cropOldW-this.cropW))):b+this.cropOffsertY>s?(this.cropH=s-this.cropOffsertY,this.cropW=this.cropH/this.fixedNumber[1]*this.fixedNumber[0],this.changeCropTypeX===1&&(this.cropOffsertX=this.cropChangeX+(this.cropOldW-this.cropW))):this.cropH=b}if(this.canChangeY&&this.fixed){var x=this.cropH/this.fixedNumber[1]*this.fixedNumber[0];x<n?(this.cropW=n,this.cropH=this.fixedNumber[1]*n/this.fixedNumber[0],this.cropOffsertY=this.cropOldH+this.cropChangeY-this.cropH):x+this.cropOffsertX>i?(this.cropW=i-this.cropOffsertX,this.cropH=this.cropW/this.fixedNumber[0]*this.fixedNumber[1]):this.cropW=x}})},checkCropLimitSize(){let{cropW:e,cropH:o,limitMinSize:t}=this,i=new Array;return Array.isArray(t)?i=t:i=[t,t],e=parseFloat(i[0]),o=parseFloat(i[1]),[e,o]},changeCropEnd(e){window.removeEventListener("mousemove",this.changeCropNow),window.removeEventListener("mouseup",this.changeCropEnd),window.removeEventListener("touchmove",this.changeCropNow),window.removeEventListener("touchend",this.changeCropEnd)},calculateSize(e,o,t,i,s,r){const a=e/o;let n=s,p=r;return n<t&&(n=t,p=Math.ceil(n/a)),p<i&&(p=i,n=Math.ceil(p*a),n<t&&(n=t,p=Math.ceil(n/a))),n<s&&(n=s,p=Math.ceil(n/a)),p<r&&(p=r,n=Math.ceil(p*a)),{width:n,height:p}},endCrop(){this.cropW===0&&this.cropH===0&&(this.cropping=!1);let[e,o]=this.checkCropLimitSize();const{width:t,height:i}=this.fixed?this.calculateSize(this.fixedNumber[0],this.fixedNumber[1],e,o,this.cropW,this.cropH):{width:e,height:o};t>this.cropW&&(this.cropW=t,this.cropOffsertX+t>this.w&&(this.cropOffsertX=this.w-t)),i>this.cropH&&(this.cropH=i,this.cropOffsertY+i>this.h&&(this.cropOffsertY=this.h-i)),window.removeEventListener("mousemove",this.createCrop),window.removeEventListener("mouseup",this.endCrop),window.removeEventListener("touchmove",this.createCrop),window.removeEventListener("touchend",this.endCrop)},startCrop(){this.crop=!0},stopCrop(){this.crop=!1},clearCrop(){this.cropping=!1,this.cropW=0,this.cropH=0},cropMove(e){if(e.preventDefault(),!this.canMoveBox)return this.crop=!1,this.startMove(e),!1;if(e.touches&&e.touches.length===2)return this.crop=!1,this.startMove(e),this.leaveCrop(),!1;window.addEventListener("mousemove",this.moveCrop),window.addEventListener("mouseup",this.leaveCrop),window.addEventListener("touchmove",this.moveCrop),window.addEventListener("touchend",this.leaveCrop);let o="clientX"in e?e.clientX:e.touches[0].clientX,t="clientY"in e?e.clientY:e.touches[0].clientY,i,s;i=o-this.cropOffsertX,s=t-this.cropOffsertY,this.cropX=i,this.cropY=s,this.$emit("crop-moving",{moving:!0,axis:this.getCropAxis()})},moveCrop(e,o){let t=0,i=0;e&&(e.preventDefault(),t="clientX"in e?e.clientX:e.touches[0].clientX,i="clientY"in e?e.clientY:e.touches[0].clientY),this.$nextTick(()=>{let s,r,a=t-this.cropX,n=i-this.cropY;if(o&&(a=this.cropOffsertX,n=this.cropOffsertY),a<=0?s=0:a+this.cropW>this.w?s=this.w-this.cropW:s=a,n<=0?r=0:n+this.cropH>this.h?r=this.h-this.cropH:r=n,this.centerBox){let p=this.getImgAxis();s<=p.x1&&(s=p.x1),s+this.cropW>p.x2&&(s=p.x2-this.cropW),r<=p.y1&&(r=p.y1),r+this.cropH>p.y2&&(r=p.y2-this.cropH)}this.cropOffsertX=s,this.cropOffsertY=r,this.$emit("crop-moving",{moving:!0,axis:this.getCropAxis()})})},getImgAxis(e,o,t){e=e||this.x,o=o||this.y,t=t||this.scale;let i={x1:0,x2:0,y1:0,y2:0},s=this.trueWidth*t,r=this.trueHeight*t;switch(this.rotate){case 0:i.x1=e+this.trueWidth*(1-t)/2,i.x2=i.x1+this.trueWidth*t,i.y1=o+this.trueHeight*(1-t)/2,i.y2=i.y1+this.trueHeight*t;break;case 1:case-1:case 3:case-3:i.x1=e+this.trueWidth*(1-t)/2+(s-r)/2,i.x2=i.x1+this.trueHeight*t,i.y1=o+this.trueHeight*(1-t)/2+(r-s)/2,i.y2=i.y1+this.trueWidth*t;break;default:i.x1=e+this.trueWidth*(1-t)/2,i.x2=i.x1+this.trueWidth*t,i.y1=o+this.trueHeight*(1-t)/2,i.y2=i.y1+this.trueHeight*t;break}return i},getCropAxis(){let e={x1:0,x2:0,y1:0,y2:0};return e.x1=this.cropOffsertX,e.x2=e.x1+this.cropW,e.y1=this.cropOffsertY,e.y2=e.y1+this.cropH,e},leaveCrop(e){window.removeEventListener("mousemove",this.moveCrop),window.removeEventListener("mouseup",this.leaveCrop),window.removeEventListener("touchmove",this.moveCrop),window.removeEventListener("touchend",this.leaveCrop),this.$emit("crop-moving",{moving:!1,axis:this.getCropAxis()})},getCropChecked(e){let o=document.createElement("canvas"),t=o.getContext("2d"),i=new Image,s=this.rotate,r=this.trueWidth,a=this.trueHeight,n=this.cropOffsertX,p=this.cropOffsertY;i.onload=()=>{if(this.cropW!==0){let x=1;this.high&!this.full&&(x=window.devicePixelRatio),this.enlarge!==1&!this.full&&(x=Math.abs(Number(this.enlarge)));let H=this.cropW*x,B=this.cropH*x,$=r*this.scale*x,I=a*this.scale*x,m=(this.x-n+this.trueWidth*(1-this.scale)/2)*x,c=(this.y-p+this.trueHeight*(1-this.scale)/2)*x;switch(b(H,B),t.save(),s){case 0:this.full?(b(H/this.scale,B/this.scale),t.drawImage(i,m/this.scale,c/this.scale,$/this.scale,I/this.scale)):t.drawImage(i,m,c,$,I);break;case 1:case-3:this.full?(b(H/this.scale,B/this.scale),m=m/this.scale+($/this.scale-I/this.scale)/2,c=c/this.scale+(I/this.scale-$/this.scale)/2,t.rotate(s*90*Math.PI/180),t.drawImage(i,c,-m-I/this.scale,$/this.scale,I/this.scale)):(m=m+($-I)/2,c=c+(I-$)/2,t.rotate(s*90*Math.PI/180),t.drawImage(i,c,-m-I,$,I));break;case 2:case-2:this.full?(b(H/this.scale,B/this.scale),t.rotate(s*90*Math.PI/180),m=m/this.scale,c=c/this.scale,t.drawImage(i,-m-$/this.scale,-c-I/this.scale,$/this.scale,I/this.scale)):(t.rotate(s*90*Math.PI/180),t.drawImage(i,-m-$,-c-I,$,I));break;case 3:case-1:this.full?(b(H/this.scale,B/this.scale),m=m/this.scale+($/this.scale-I/this.scale)/2,c=c/this.scale+(I/this.scale-$/this.scale)/2,t.rotate(s*90*Math.PI/180),t.drawImage(i,-c-$/this.scale,m,$/this.scale,I/this.scale)):(m=m+($-I)/2,c=c+(I-$)/2,t.rotate(s*90*Math.PI/180),t.drawImage(i,-c-$,m,$,I));break;default:this.full?(b(H/this.scale,B/this.scale),t.drawImage(i,m/this.scale,c/this.scale,$/this.scale,I/this.scale)):t.drawImage(i,m,c,$,I)}t.restore()}else{let x=r*this.scale,H=a*this.scale;switch(t.save(),s){case 0:b(x,H),t.drawImage(i,0,0,x,H);break;case 1:case-3:b(H,x),t.rotate(s*90*Math.PI/180),t.drawImage(i,0,-H,x,H);break;case 2:case-2:b(x,H),t.rotate(s*90*Math.PI/180),t.drawImage(i,-x,-H,x,H);break;case 3:case-1:b(H,x),t.rotate(s*90*Math.PI/180),t.drawImage(i,-x,0,x,H);break;default:b(x,H),t.drawImage(i,0,0,x,H)}t.restore()}e(o)};var g=this.img.substr(0,4);g!=="data"&&(i.crossOrigin="Anonymous"),i.src=this.imgs;const y=this.fillColor;function b(x,H){o.width=Math.round(x),o.height=Math.round(H),y&&(t.fillStyle=y,t.fillRect(0,0,o.width,o.height))}},getCropData(e){this.getCropChecked(o=>{e(o.toDataURL("image/"+this.outputType,this.outputSize))})},getCropBlob(e){this.getCropChecked(o=>{o.toBlob(t=>e(t),"image/"+this.outputType,this.outputSize)})},showPreview(){if(this.isCanShow)this.isCanShow=!1,setTimeout(()=>{this.isCanShow=!0},16);else return!1;let e=this.cropW,o=this.cropH,t=this.scale;var i={};i.div={width:`${e}px`,height:`${o}px`};let s=(this.x-this.cropOffsertX)/t,r=(this.y-this.cropOffsertY)/t,a=0;i.w=e,i.h=o,i.url=this.imgs,i.img={width:`${this.trueWidth}px`,height:`${this.trueHeight}px`,transform:`scale(${t})translate3d(${s}px, ${r}px, ${a}px)rotateZ(${this.rotate*90}deg)`},i.html=`
      <div class="show-preview" style="width: ${i.w}px; height: ${i.h}px,; overflow: hidden">
        <div style="width: ${e}px; height: ${o}px">
          <img src=${i.url} style="width: ${this.trueWidth}px; height: ${this.trueHeight}px; transform:
          scale(${t})translate3d(${s}px, ${r}px, ${a}px)rotateZ(${this.rotate*90}deg)">
        </div>
      </div>`,this.$emit("real-time",i)},reload(){let e=new Image;e.onload=()=>{this.w=parseFloat(window.getComputedStyle(this.$refs.cropper).width),this.h=parseFloat(window.getComputedStyle(this.$refs.cropper).height),this.trueWidth=e.width,this.trueHeight=e.height,this.original?this.scale=1:this.scale=this.checkedMode(),this.$nextTick(()=>{this.x=-(this.trueWidth-this.trueWidth*this.scale)/2+(this.w-this.trueWidth*this.scale)/2,this.y=-(this.trueHeight-this.trueHeight*this.scale)/2+(this.h-this.trueHeight*this.scale)/2,this.loading=!1,this.autoCrop&&this.goAutoCrop(),this.$emit("img-load","success"),setTimeout(()=>{this.showPreview()},20)})},e.onerror=()=>{this.$emit("img-load","error")},e.src=this.imgs},checkedMode(){let e=1,o=this.trueWidth,t=this.trueHeight;const i=this.mode.split(" ");switch(i[0]){case"contain":this.trueWidth>this.w&&(e=this.w/this.trueWidth),this.trueHeight*e>this.h&&(e=this.h/this.trueHeight);break;case"cover":o=this.w,e=o/this.trueWidth,t=t*e,t<this.h&&(t=this.h,e=t/this.trueHeight);break;default:try{let s=i[0];if(s.search("px")!==-1){s=s.replace("px",""),o=parseFloat(s);const r=o/this.trueWidth;let a=1,n=i[1];n.search("px")!==-1&&(n=n.replace("px",""),t=parseFloat(n),a=t/this.trueHeight),e=Math.min(r,a)}if(s.search("%")!==-1&&(s=s.replace("%",""),o=parseFloat(s)/100*this.w,e=o/this.trueWidth),i.length===2&&s==="auto"){let r=i[1];r.search("px")!==-1&&(r=r.replace("px",""),t=parseFloat(r),e=t/this.trueHeight),r.search("%")!==-1&&(r=r.replace("%",""),t=parseFloat(r)/100*this.h,e=t/this.trueHeight)}}catch{e=1}}return e},goAutoCrop(e,o){if(this.imgs===""||this.imgs===null)return;this.clearCrop(),this.cropping=!0;let t=this.w,i=this.h;if(this.centerBox){const a=Math.abs(this.rotate)%2>0;let n=(a?this.trueHeight:this.trueWidth)*this.scale,p=(a?this.trueWidth:this.trueHeight)*this.scale;t=n<t?n:t,i=p<i?p:i}var s=e||parseFloat(this.autoCropWidth),r=o||parseFloat(this.autoCropHeight);(s===0||r===0)&&(s=t*.8,r=i*.8),s=s>t?t:s,r=r>i?i:r,this.fixed&&(r=s/this.fixedNumber[0]*this.fixedNumber[1]),r>this.h&&(r=this.h,s=r/this.fixedNumber[1]*this.fixedNumber[0]),this.changeCrop(s,r)},changeCrop(e,o){if(this.centerBox){let t=this.getImgAxis();e>t.x2-t.x1&&(e=t.x2-t.x1,o=e/this.fixedNumber[0]*this.fixedNumber[1]),o>t.y2-t.y1&&(o=t.y2-t.y1,e=o/this.fixedNumber[1]*this.fixedNumber[0])}this.cropW=e,this.cropH=o,this.checkCropLimitSize(),this.$nextTick(()=>{this.cropOffsertX=(this.w-this.cropW)/2,this.cropOffsertY=(this.h-this.cropH)/2,this.centerBox&&this.moveCrop(null,!0)})},refresh(){this.img,this.imgs="",this.scale=1,this.crop=!1,this.rotate=0,this.w=0,this.h=0,this.trueWidth=0,this.trueHeight=0,this.imgIsQqualCrop=!1,this.clearCrop(),this.$nextTick(()=>{this.checkedImg()})},rotateLeft(){this.rotate=this.rotate<=-3?0:this.rotate-1},rotateRight(){this.rotate=this.rotate>=3?0:this.rotate+1},rotateClear(){this.rotate=0},checkoutImgAxis(e,o,t){e=e||this.x,o=o||this.y,t=t||this.scale;let i=!0;if(this.centerBox){let s=this.getImgAxis(e,o,t),r=this.getCropAxis();s.x1>=r.x1&&(i=!1),s.x2<=r.x2&&(i=!1),s.y1>=r.y1&&(i=!1),s.y2<=r.y2&&(i=!1),i||this.changeImgScale(s,r,t)}return i},changeImgScale(e,o,t){let i=this.trueWidth,s=this.trueHeight,r=i*t,a=s*t;if(r>=this.cropW&&a>=this.cropH)this.scale=t;else{const n=this.cropW/i,p=this.cropH/s,g=this.cropH<=s*n?n:p;this.scale=g,r=i*g,a=s*g}this.imgIsQqualCrop||(e.x1>=o.x1&&(this.isRotateRightOrLeft?this.x=o.x1-(i-r)/2-(r-a)/2:this.x=o.x1-(i-r)/2),e.x2<=o.x2&&(this.isRotateRightOrLeft?this.x=o.x1-(i-r)/2-(r-a)/2-a+this.cropW:this.x=o.x2-(i-r)/2-r),e.y1>=o.y1&&(this.isRotateRightOrLeft?this.y=o.y1-(s-a)/2-(a-r)/2:this.y=o.y1-(s-a)/2),e.y2<=o.y2&&(this.isRotateRightOrLeft?this.y=o.y2-(s-a)/2-(a-r)/2-r:this.y=o.y2-(s-a)/2-a)),(r<this.cropW||a<this.cropH)&&(this.imgIsQqualCrop=!0)}},mounted(){this.support="onwheel"in document.createElement("div")?"wheel":document.onmousewheel!==void 0?"mousewheel":"DOMMouseScroll";let e=this;var o=navigator.userAgent;this.isIOS=!!o.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/),HTMLCanvasElement.prototype.toBlob||Object.defineProperty(HTMLCanvasElement.prototype,"toBlob",{value:function(t,i,s){for(var r=atob(this.toDataURL(i,s).split(",")[1]),a=r.length,n=new Uint8Array(a),p=0;p<a;p++)n[p]=r.charCodeAt(p);t(new Blob([n],{type:e.type||"image/png"}))}}),this.showPreview(),this.checkedImg()},unmounted(){window.removeEventListener("mousemove",this.moveCrop),window.removeEventListener("mouseup",this.leaveCrop),window.removeEventListener("touchmove",this.moveCrop),window.removeEventListener("touchend",this.leaveCrop),this.cancelScale()}}),qe={key:0,class:"cropper-box"},Ge=["src"],Qe={class:"cropper-view-box"},Ze=["src"],Ke={key:1};function Je(e,o,t,i,s,r){return h(),u("div",{class:"vue-cropper",ref:"cropper",onMouseover:o[28]||(o[28]=(...a)=>e.scaleImg&&e.scaleImg(...a)),onMouseout:o[29]||(o[29]=(...a)=>e.cancelScale&&e.cancelScale(...a))},[e.imgs?(h(),u("div",qe,[oe(l("div",{class:"cropper-box-canvas",style:j({width:e.trueWidth+"px",height:e.trueHeight+"px",transform:"scale("+e.scale+","+e.scale+") translate3d("+e.x/e.scale+"px,"+e.y/e.scale+"px,0)rotateZ("+e.rotate*90+"deg)"})},[l("img",{src:e.imgs,alt:"cropper-img",ref:"cropperImg"},null,8,Ge)],4),[[de,!e.loading]])])):M("",!0),l("div",{class:Z(["cropper-drag-box",{"cropper-move":e.move&&!e.crop,"cropper-crop":e.crop,"cropper-modal":e.cropping}]),onMousedown:o[0]||(o[0]=(...a)=>e.startMove&&e.startMove(...a)),onTouchstart:o[1]||(o[1]=(...a)=>e.startMove&&e.startMove(...a))},null,34),oe(l("div",{class:"cropper-crop-box",style:j({width:e.cropW+"px",height:e.cropH+"px",transform:"translate3d("+e.cropOffsertX+"px,"+e.cropOffsertY+"px,0)"})},[l("span",Qe,[l("img",{style:j({width:e.trueWidth+"px",height:e.trueHeight+"px",transform:"scale("+e.scale+","+e.scale+") translate3d("+(e.x-e.cropOffsertX)/e.scale+"px,"+(e.y-e.cropOffsertY)/e.scale+"px,0)rotateZ("+e.rotate*90+"deg)"}),src:e.imgs,alt:"cropper-img"},null,12,Ze)]),l("span",{class:"cropper-face cropper-move",onMousedown:o[2]||(o[2]=(...a)=>e.cropMove&&e.cropMove(...a)),onTouchstart:o[3]||(o[3]=(...a)=>e.cropMove&&e.cropMove(...a))},null,32),e.info?(h(),u("span",{key:0,class:"crop-info",style:j({top:e.cropInfo.top})},_(e.cropInfo.width)+" × "+_(e.cropInfo.height),5)):M("",!0),e.fixedBox?M("",!0):(h(),u("span",Ke,[l("span",{class:"crop-line line-w",onMousedown:o[4]||(o[4]=a=>e.changeCropSize(a,!1,!0,0,1)),onTouchstart:o[5]||(o[5]=a=>e.changeCropSize(a,!1,!0,0,1))},null,32),l("span",{class:"crop-line line-a",onMousedown:o[6]||(o[6]=a=>e.changeCropSize(a,!0,!1,1,0)),onTouchstart:o[7]||(o[7]=a=>e.changeCropSize(a,!0,!1,1,0))},null,32),l("span",{class:"crop-line line-s",onMousedown:o[8]||(o[8]=a=>e.changeCropSize(a,!1,!0,0,2)),onTouchstart:o[9]||(o[9]=a=>e.changeCropSize(a,!1,!0,0,2))},null,32),l("span",{class:"crop-line line-d",onMousedown:o[10]||(o[10]=a=>e.changeCropSize(a,!0,!1,2,0)),onTouchstart:o[11]||(o[11]=a=>e.changeCropSize(a,!0,!1,2,0))},null,32),l("span",{class:"crop-point point1",onMousedown:o[12]||(o[12]=a=>e.changeCropSize(a,!0,!0,1,1)),onTouchstart:o[13]||(o[13]=a=>e.changeCropSize(a,!0,!0,1,1))},null,32),l("span",{class:"crop-point point2",onMousedown:o[14]||(o[14]=a=>e.changeCropSize(a,!1,!0,0,1)),onTouchstart:o[15]||(o[15]=a=>e.changeCropSize(a,!1,!0,0,1))},null,32),l("span",{class:"crop-point point3",onMousedown:o[16]||(o[16]=a=>e.changeCropSize(a,!0,!0,2,1)),onTouchstart:o[17]||(o[17]=a=>e.changeCropSize(a,!0,!0,2,1))},null,32),l("span",{class:"crop-point point4",onMousedown:o[18]||(o[18]=a=>e.changeCropSize(a,!0,!1,1,0)),onTouchstart:o[19]||(o[19]=a=>e.changeCropSize(a,!0,!1,1,0))},null,32),l("span",{class:"crop-point point5",onMousedown:o[20]||(o[20]=a=>e.changeCropSize(a,!0,!1,2,0)),onTouchstart:o[21]||(o[21]=a=>e.changeCropSize(a,!0,!1,2,0))},null,32),l("span",{class:"crop-point point6",onMousedown:o[22]||(o[22]=a=>e.changeCropSize(a,!0,!0,1,2)),onTouchstart:o[23]||(o[23]=a=>e.changeCropSize(a,!0,!0,1,2))},null,32),l("span",{class:"crop-point point7",onMousedown:o[24]||(o[24]=a=>e.changeCropSize(a,!1,!0,0,2)),onTouchstart:o[25]||(o[25]=a=>e.changeCropSize(a,!1,!0,0,2))},null,32),l("span",{class:"crop-point point8",onMousedown:o[26]||(o[26]=a=>e.changeCropSize(a,!0,!0,2,2)),onTouchstart:o[27]||(o[27]=a=>e.changeCropSize(a,!0,!0,2,2))},null,32)]))],4),[[de,e.cropping]])],544)}const et=je(Fe,[["render",Je],["__scopeId","data-v-a742df44"]]),tt=[{label:"语文",value:"ch"},{label:"英语",value:"en"}],st=[{label:"初中",value:"junior"},{label:"高中",value:"high"}],it=[{label:"体裁不限",value:"体裁不限"},{label:"记叙文",value:"记叙文"},{label:"议论文",value:"议论文"},{label:"说明文",value:"说明文"},{label:"应用文",value:"应用文"}],ue={ch:[{label:"50分",value:"x",cont:50},{label:"100分",value:"y",cont:100},{label:"其他",value:"z"}],en:[{label:"15分",value:"x",cont:15},{label:"20分",value:"y",cont:20},{label:"其他",value:"z"}]},pe={ch:[{label:"600字",value:"x",cont:600},{label:"800字",value:"y",cont:800},{label:"其他",value:"z",min:100}],en:[{label:"60词",value:"x",cont:60},{label:"80词",value:"y",cont:80},{label:"其他",value:"z",min:10}]},K={outputSize:1,outputType:"png",canScale:!0,autoCrop:!0,fixed:!1,full:!0,centerBox:!1,original:!0,canMove:!0,canMoveBox:!0,fixedBox:!1},ot={class:"pmm2_flex_between"},lt={class:"add-box"},rt={class:"tab-box pmm2_flex_acenter"},at=["onClick"],nt={class:"from-box"},ht={class:"item-box pmm2_flex_acenter"},ct={class:"nr"},dt={key:0,class:"item-box pmm2_flex_acenter"},ut={class:"nr"},pt={class:"item-box"},ft={class:"item-com pmm2_flex_acenter"},gt={class:"nr"},vt={key:0,class:"tig-box"},mt={class:"item-box"},_t={class:"item-com pmm2_flex_acenter"},wt={class:"nr"},xt={key:0,class:"tig-box"},yt={class:"item-box"},bt={class:"item-com pmm2_flex",style:{"align-items":"start"}},Ct={class:"nr"},$t={key:0,class:"tig-box"},It={class:"item-com pmm2_flex",style:{"align-items":"start"}},kt={class:"nr"},Ot={class:"up-list pmm2_flex_acenter"},Mt={class:"up-item-box"},Ht={class:"up-item-com huiA"},Xt={class:"upload-demo pmm2_flex_center"},St={class:"up-item-com huiA"},Wt=["onClick"],Yt={class:"bs"},Lt={key:0,class:"tig-box"},Et={class:"pmm2_flex_between"},zt={class:"dialog-footer"},Tt={class:"pmm2_flex_between"},Bt={class:"cropper-box"},Nt={class:"pmm2_flex_between"},fe=6,At=le({__name:"newUpload",props:{modelValue:!1,modelModifiers:{}},emits:be(["getwritingList"],["update:modelValue"]),setup(e,{emit:o}){const t=Ce(e,"modelValue",!1),i=A(!1),s=A({}),r=A([]),a=()=>{r.value=[],s.value={course_code:"ch",stage:"junior",write_type:"体裁不限",word_countVal:"x",word_countErr:!1,full_scoreVal:"x",full_scoreErr:!1,topic:"",topicErr:!1,images:[],imagesErr:!1}},n=A(null),p=A("");let g=["jpeg","jpg","png"];const y=async m=>{if(!(m.size/1024/1024<fe))return ee.error("上传文件大小不能超过 "+fe+"MB!"),!1;let Y=m.type||"",L=m.name.lastIndexOf(".");if(L!==-1?Y=m.name.substring(L+1):Y=m.type,g.indexOf(Y)==-1)return ee.error("请上传正确的图片格式"),!1;p.value=URL.createObjectURL(m),i.value=!0,s.value.imagesErr=!1},b=()=>{n.value.getCropBlob(m=>{i.value=!1,r.value.push(URL.createObjectURL(m)),s.value.images.push(m)})},x=m=>{r.value.splice(m,1),s.value.images.splice(m,1)},H=m=>{let c=m.clipboardData.items,Y=null;for(let L of c)L.kind==="file"&&(Y=L.getAsFile());Y&&Y.type.indexOf("image")!=-1&&(r.value.push(URL.createObjectURL(Y)),s.value.images.push(Y))},B=A(!1),$=o,I=()=>{let m=!0;if(s.value.word_countVal=="z"&&!s.value.word_count&&(s.value.word_countErr=!0,m=!1),s.value.full_scoreVal=="z"&&!s.value.full_score&&(s.value.full_scoreErr=!0,m=!1),s.value.topic||(s.value.topicErr=!0,m=!1),s.value.images.length==0&&(s.value.imagesErr=!0,m=!1),!m)return;B.value=!0;const c=new FormData;for(let U=0;U<s.value.images.length;U++)c.append("images[]",s.value.images[U]);c.append("course_code",s.value.course_code),c.append("stage",s.value.stage),s.value.course_code=="ch"&&c.append("write_type",s.value.write_type),c.append("topic",s.value.topic);let Y=s.value.word_count;s.value.word_countVal!="z"&&(Y=pe[s.value.course_code].find(k=>k.value==s.value.word_countVal).cont),c.append("word_count",Y);let L=s.value.full_score;s.value.full_scoreVal!="z"&&(L=ue[s.value.course_code].find(k=>k.value==s.value.full_scoreVal).cont),c.append("full_score",L),ze(c).then(U=>{U.code===0?(B.value=!1,t.value=!1,$("getwritingList")):U.msg&&(B.value=!1,ee.error(U.msg))})};return(m,c)=>{const Y=ie("CloseBold"),L=me,U=We,k=ke,v=Ie,E=Me,N=Oe,R=He,F=Xe,P=ie("Upload"),q=Se,V=_e,G=we,w=$e,D=ve;return h(),u(W,null,[C(w,{"append-to-body":"",class:"hhypt-dialog-boxs","show-close":!1,modelValue:t.value,"onUpdate:modelValue":c[11]||(c[11]=f=>t.value=f),title:"新建上传",width:"800",onOpen:a,top:"5vh"},{header:O(({close:f,titleClass:X})=>[l("div",ot,[l("span",{class:Z(X)},"新建上传",2),d(B)?M("",!0):(h(),Q(U,{key:0,onClick:f,underline:!1},{default:O(()=>[C(L,{size:"20"},{default:O(()=>[C(Y)]),_:1})]),_:2},1032,["onClick"]))])]),footer:O(()=>[l("div",Et,[c[23]||(c[23]=l("div",null,null,-1)),l("div",zt,[C(G,{style:{width:"80px"},onClick:c[10]||(c[10]=f=>t.value=!1),loading:d(B)},{default:O(()=>c[21]||(c[21]=[z("取消")])),_:1,__:[21]},8,["loading"]),C(G,{style:{width:"80px"},type:"primary",onClick:I,loading:d(B)},{default:O(()=>c[22]||(c[22]=[z(" 提交批阅 ")])),_:1,__:[22]},8,["loading"])])])]),default:O(()=>[oe((h(),u("div",lt,[l("div",rt,[(h(!0),u(W,null,T(d(tt),f=>(h(),u("div",{class:Z(["tab-com huiA",{on:f.value==d(s).course_code}]),onClick:X=>d(s).course_code=f.value,key:f.value},_(f.label),11,at))),128))]),l("div",nt,[l("div",ht,[c[13]||(c[13]=l("div",{class:"bt"},[l("span",null,"*"),z("学段")],-1)),l("div",ct,[C(v,{modelValue:d(s).stage,"onUpdate:modelValue":c[0]||(c[0]=f=>d(s).stage=f)},{default:O(()=>[(h(!0),u(W,null,T(d(st),f=>(h(),Q(k,{key:f.value,value:f.value},{default:O(()=>[z(_(f.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])])]),d(s).course_code=="ch"?(h(),u("div",dt,[c[14]||(c[14]=l("div",{class:"bt"},[l("span",null,"*"),z("作文体裁")],-1)),l("div",ut,[C(N,{modelValue:d(s).write_type,"onUpdate:modelValue":c[1]||(c[1]=f=>d(s).write_type=f),style:{width:"240px"}},{default:O(()=>[(h(!0),u(W,null,T(d(it),f=>(h(),Q(E,{key:f.value,value:f.value,label:f.label},null,8,["value","label"]))),128))]),_:1},8,["modelValue"])])])):M("",!0),l("div",pt,[l("div",ft,[c[15]||(c[15]=l("div",{class:"bt"},[l("span",null,"*"),z("满分")],-1)),l("div",gt,[C(v,{modelValue:d(s).full_scoreVal,"onUpdate:modelValue":c[3]||(c[3]=f=>d(s).full_scoreVal=f),onChange:c[4]||(c[4]=f=>d(s).full_scoreErr=!1)},{default:O(()=>[(h(!0),u(W,null,T(d(ue)[d(s).course_code],f=>(h(),Q(k,{key:f.value,value:f.value},{default:O(()=>[l("span",null,_(f.label),1),f.value=="z"?(h(),Q(R,{key:0,size:"small",step:1,"step-strictly":"",modelValue:d(s).full_score,"onUpdate:modelValue":c[2]||(c[2]=X=>d(s).full_score=X),min:1,"controls-position":"right",style:{width:"90px","margin-left":"15px"},onChange:X=>(d(s).full_scoreErr=!1,d(s).full_scoreVal=f.value)},null,8,["modelValue","onChange"])):M("",!0)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])])]),d(s).full_scoreErr?(h(),u("div",vt,"请输入分值")):M("",!0)]),l("div",mt,[l("div",_t,[c[16]||(c[16]=l("div",{class:"bt"},[l("span",null,"*"),z("字数要求")],-1)),l("div",wt,[C(v,{modelValue:d(s).word_countVal,"onUpdate:modelValue":c[6]||(c[6]=f=>d(s).word_countVal=f),onChange:c[7]||(c[7]=f=>d(s).word_countErr=!1)},{default:O(()=>[(h(!0),u(W,null,T(d(pe)[d(s).course_code],f=>(h(),Q(k,{key:f.value,value:f.value},{default:O(()=>[l("span",null,_(f.label),1),f.value=="z"?(h(),Q(R,{key:0,size:"small",step:1,"step-strictly":"",modelValue:d(s).word_count,"onUpdate:modelValue":c[5]||(c[5]=X=>d(s).word_count=X),min:f.min,"controls-position":"right",style:{width:"90px","margin-left":"15px"},onChange:X=>(d(s).word_countErr=!1,d(s).word_countVal=f.value)},null,8,["modelValue","min","onChange"])):M("",!0)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])])]),d(s).word_countErr?(h(),u("div",xt,"请输入字数要求")):M("",!0)]),l("div",yt,[l("div",bt,[c[17]||(c[17]=l("div",{class:"bt"},[l("span",null,"*"),z("题目要求")],-1)),l("div",Ct,[C(F,{onBlur:c[8]||(c[8]=f=>d(s).topicErr=!1),modelValue:d(s).topic,"onUpdate:modelValue":c[9]||(c[9]=f=>d(s).topic=f),style:{width:"500px"},autosize:{minRows:4,maxRows:10},type:"textarea",placeholder:"请输入题目要求"},null,8,["modelValue"])])]),d(s).topicErr?(h(),u("div",$t,"请输入题目要求")):M("",!0)]),l("div",{class:"item-box",onPasteCapture:he(H,["prevent"])},[l("div",It,[c[19]||(c[19]=l("div",{class:"bt"},[l("span",null,"*"),z("上传图片")],-1)),l("div",kt,[l("div",Ot,[l("div",Mt,[l("div",Ht,[C(q,{class:"upload-demo",action:"#",multiple:"","http-request":()=>{},"before-upload":y,accept:".jpg, .jpeg, .png","show-file-list":!1,drag:""},{default:O(()=>[l("div",Xt,[C(L,null,{default:O(()=>[C(P)]),_:1}),c[18]||(c[18]=l("span",null,"上传图片",-1))])]),_:1})])]),(h(!0),u(W,null,T(d(r),(f,X)=>(h(),u("div",{class:"up-item-box",key:X},[l("div",St,[C(V,{"preview-src-list":d(r),"initial-index":X,class:"img",fit:"none",src:f},null,8,["preview-src-list","initial-index","src"]),l("div",{class:"del",onClick:he(S=>x(X),["stop"])},[C(L,null,{default:O(()=>[C(Y)]),_:1})],8,Wt),l("div",Yt,"第"+_(X+1)+"张",1)])]))),128))])])]),d(s).imagesErr?(h(),u("div",Lt,"请上传图片")):M("",!0),c[20]||(c[20]=l("div",{class:"tig-img-box pmm2_flex",style:{width:"100%","padding-top":"15px"}},[l("div",{class:"bt"}),l("div",{class:"nr",style:{"font-size":"12px",color:"#7A7A7A","line-height":"24px"}},[l("p",null,"1、支持 jpg、jpeg、png格式的图片。"),l("p",null,"2、最多可上传3张，每张不超过6M。"),l("p",null,"3、上传仅包含作文内容的图片，且不要倾斜。"),l("p",null,"4、如一篇作文多张图片，请按顺序上传。")])],-1))],32)])])),[[D,d(B)]])]),_:1},8,["modelValue"]),C(w,{"append-to-body":"",class:"hhypt-dialog-boxs","show-close":!1,modelValue:d(i),"onUpdate:modelValue":c[12]||(c[12]=f=>ge(i)?i.value=f:null),title:"裁切图片",width:"1200",top:"1vh"},{header:O(({close:f,titleClass:X})=>[l("div",Tt,[l("span",{class:Z(X)},"裁切图片",2),C(U,{onClick:f,underline:!1},{default:O(()=>[C(L,{size:"20"},{default:O(()=>[C(Y)]),_:1})]),_:2},1032,["onClick"])])]),default:O(()=>[l("div",Bt,[l("div",Nt,[c[25]||(c[25]=l("p",null,"提示:请裁剪出图像中包含手写作文的区域，以便更好地批改",-1)),C(G,{style:{width:"70px"},type:"primary",onClick:b},{default:O(()=>c[24]||(c[24]=[z(" 确定裁剪 ")])),_:1,__:[24]})]),C(d(et),{ref_key:"cropperRef",ref:n,class:"cropper-com",img:d(p),original:d(K).original,outputSize:d(K).outputSize,outputType:d(K).outputType,canScale:d(K).canScale,autoCrop:d(K).autoCrop,centerBox:d(K).centerBox,canMove:d(K).canMove,canMoveBox:d(K).canMoveBox,fixedBox:d(K).fixedBox},null,8,["img","original","outputSize","outputType","canScale","autoCrop","centerBox","canMove","canMoveBox","fixedBox"])])]),_:1},8,["modelValue"])],64)}}}),Vt=xe(At,[["__scopeId","data-v-42dbb244"]]),Ut={class:"fenxi-box"},Rt={class:"zdf-box pmm2_flex_acenter"},Pt={class:"fen"},Dt={class:"box"},jt={class:"box"},Ft={class:"box"},qt={key:0,class:"fenxi-com1"},Gt={class:"ul",style:{display:"flex"}},Qt={class:"li"},Zt={class:"li-com"},Kt={class:"bt-box"},Jt={class:"bt-com"},es={class:"bt"},ts={class:"t1"},ss={class:"t2"},is={class:"nr-com"},os={key:1,class:"fenxi-com2"},ls={class:"ul"},rs={class:"bt"},as={class:"nr"},ns={class:"level"},hs={key:2,class:"fenxi-com3"},cs={class:"ul pmm2_flex_acenter"},ds={key:3,class:"fenxi-com1 fenxi-com4"},us={class:"ul",style:{display:"flex"}},ps={class:"li-com"},fs={class:"bt-box"},gs={class:"bt-com"},vs={key:0,class:"bt"},ms={key:1,class:"bt"},_s={key:2,class:"bt"},ws={key:3,class:"bt"},xs={class:"nr-com"},ys={key:4,class:"fenxi-com5"},bs={class:"fenxi-com5-box"},Cs={class:"box box1"},$s={class:"nr"},Is={class:"box box2"},ks={class:"nr"},Os={key:0,class:"t1"},Ms={key:1,class:"t1"},Hs={key:2,class:"t1"},Xs={class:"t2"},Ss=le({__name:"ch",props:["detailsInfo"],setup(e){return(o,t)=>{var i,s,r,a;return h(),u("div",Ut,[l("div",Rt,[t[0]||(t[0]=l("div",{class:"bt"},"总得分：",-1)),l("div",Pt,_(e.detailsInfo.paperScore||0)+"分",1),l("div",Dt,_(e.detailsInfo.type),1),l("div",jt,_(e.detailsInfo.wordCount)+"字",1),l("div",Ft,_(e.detailsInfo.levelLabel),1)]),e.detailsInfo.dimensions?(h(),u("div",qt,[t[1]||(t[1]=l("div",{class:"title"},"维度分析",-1)),l("div",Gt,[(h(!0),u(W,null,T(e.detailsInfo.dimensions,(n,p)=>(h(),u("div",Qt,[l("div",Zt,[l("div",Kt,[l("div",Jt,[l("span",es,_(n.dimenName||""),1),l("span",ts,_(n.dimenScore||0)+"分",1),l("span",ss,"/"+_(n.dimenFullScore||0)+"分",1)])]),l("div",is,_(n.comment),1)])]))),256))])])):M("",!0),(i=e.detailsInfo.report)!=null&&i.write_elements?(h(),u("div",os,[t[2]||(t[2]=l("div",{class:"title"},"写作要求检测",-1)),l("div",ls,[(h(!0),u(W,null,T(e.detailsInfo.report.write_elements,(n,p)=>(h(),u("div",{class:"li pmm2_flex_between",key:p},[l("div",rs,_(n.name)+"：",1),l("div",as,_(n.text),1),l("div",ns,[l("span",{class:Z({level3:n.level==3})},"一般",2),l("span",{class:Z({level2:n.level==2})},"良好",2),l("span",{class:Z({level1:n.level==1})},"优秀",2)])]))),128))])])):M("",!0),((s=e.detailsInfo.errors)==null?void 0:s.length)>0?(h(),u("div",hs,[t[3]||(t[3]=l("div",{class:"title"},"严重问题诊断",-1)),l("div",cs,[(h(!0),u(W,null,T(e.detailsInfo.errors,(n,p)=>(h(),u("div",{class:"li",key:p},_(n),1))),128))])])):M("",!0),e.detailsInfo.evaluate?(h(),u("div",ds,[t[4]||(t[4]=l("div",{class:"title"},"教师评语",-1)),l("div",us,[(h(!0),u(W,null,T(e.detailsInfo.evaluate,(n,p)=>(h(),u("div",{class:"li",key:p},[l("div",ps,[l("div",fs,[l("div",gs,[p=="strengths"?(h(),u("span",vs,"作文亮点")):p=="weaknesses"?(h(),u("span",ms,"作文不足")):p=="comment"?(h(),u("span",_s,"综合评价")):p=="suggestions"?(h(),u("span",ws,"改进和建议")):M("",!0)])]),l("div",xs,_(n),1)])]))),128))])])):M("",!0),(r=e.detailsInfo.report)!=null&&r.plan_layout?(h(),u("div",ys,[t[7]||(t[7]=l("div",{class:"title"},"谋篇布局",-1)),l("div",bs,[l("div",Cs,[t[5]||(t[5]=l("div",{class:"bt"},"思路点评",-1)),l("div",$s,_(((a=e.detailsInfo.report.plan_layout)==null?void 0:a.layout_thoughts)||""),1)]),l("div",Is,[t[6]||(t[6]=l("div",{class:"bt"},"思路建议",-1)),l("div",ks,[(h(!0),u(W,null,T(e.detailsInfo.report.plan_layout.layout_suggestion,(n,p)=>(h(),u("div",{class:"li",key:p},[p=="begin"?(h(),u("div",Os,"开头：")):p=="middle"?(h(),u("div",Ms,"中间：")):p=="ending"?(h(),u("div",Hs,"结尾：")):M("",!0),l("div",Xs,_(n),1)]))),128))])])])])):M("",!0)])}}}),Ws={class:"fenxi-box"},Ys={class:"zdf-box pmm2_flex_acenter"},Ls={class:"fen"},Es={class:"box"},zs={class:"box"},Ts={key:0,class:"fenxi-com1"},Bs={class:"ul",style:{display:"flex"}},Ns={class:"li-com"},As={class:"bt-box"},Vs={class:"bt-com"},Us={class:"bt"},Rs={class:"nr-com"},Ps={style:{"padding-bottom":"8px"}},Ds={key:1,class:"fenxi-com1 eh-fenxi-com5"},js={class:"eh-ul"},Fs={class:"bt"},qs={class:"nr"},Gs={key:2,class:"fenxi-com1 fenxi-com4"},Qs={class:"ul",style:{display:"flex"}},Zs={class:"li",style:{width:"100%"}},Ks={class:"li-com"},Js={class:"nr-com",style:{"font-size":"16px","line-height":"38px",color:"#000"}},ei=le({__name:"eh",props:["detailsInfo"],setup(e){return(o,t)=>{var i,s,r,a;return h(),u("div",Ws,[l("div",Ys,[t[0]||(t[0]=l("div",{class:"bt"},"总得分：",-1)),l("div",Ls,_(e.detailsInfo.paperScore||0)+"分",1),l("div",Es,_(e.detailsInfo.wordCount)+"字",1),l("div",zs,_(e.detailsInfo.levelLabel),1)]),e.detailsInfo.detailedAnalysis?(h(),u("div",Ts,[t[3]||(t[3]=l("div",{class:"title"},"维度分析",-1)),l("div",Bs,[(h(!0),u(W,null,T(e.detailsInfo.detailedAnalysis,(n,p)=>(h(),u("div",{class:"li",key:p},[l("div",Ns,[l("div",As,[l("div",Vs,[l("span",Us,_(n.name||""),1)])]),l("div",Rs,[l("p",Ps,[t[1]||(t[1]=l("b",null,"优点：",-1)),z(_(n.advantage||""),1)]),l("p",null,[t[2]||(t[2]=l("b",null,"不足：",-1)),z(_(n.comment||"无"),1)])])])]))),128))])])):M("",!0),(i=e.detailsInfo.inlineCorrections)!=null&&i.length?(h(),u("div",Ds,[t[7]||(t[7]=l("div",{class:"title"},"逐句批改",-1)),l("div",js,[(h(!0),u(W,null,T(e.detailsInfo.inlineCorrections,(n,p)=>(h(),u("div",{class:"eh-li",key:p},[l("div",Fs,"原句："+_(n.originalSen),1),l("div",qs,[l("p",null,[t[4]||(t[4]=l("b",null,"问题：",-1)),z(_(n.issue),1)]),l("p",null,[t[5]||(t[5]=l("b",null,"改正：",-1)),z(_(n.correction),1)]),l("p",null,[t[6]||(t[6]=l("b",null,"解释：",-1)),z(_(n.explanation),1)])])]))),128))])])):M("",!0),(r=(s=e.detailsInfo)==null?void 0:s.teacherComment)!=null&&r.content?(h(),u("div",Gs,[t[8]||(t[8]=l("div",{class:"title"},"教师评语",-1)),l("div",Qs,[l("div",Zs,[l("div",Ks,[l("div",Js,_((a=e.detailsInfo.teacherComment)==null?void 0:a.content),1)])])])])):M("",!0)])}}}),ti=xe(ei,[["__scopeId","data-v-52b1c4b1"]]),si={class:"writing-correction"},ii={class:"writing-correction-com pmm2_flex_between"},oi={class:"left-box"},li={class:"left-com scrollBar-C"},ri={class:"title pmm2_flex_between"},ai={class:"ul"},ni=["onClick"],hi={class:"t1"},ci={class:"t2"},di={class:"t3"},ui={class:"right-box"},pi={key:0,class:"right-top pmm2_flex_between"},fi={key:1,class:"right-top pmm2_flex_between"},gi={key:2,class:"right-top pmm2_flex_between"},vi={class:"tig-box"},mi={class:"top-box pmm2_flex_between"},_i={class:"left-boxs"},wi={class:"title-box pmm2_flex_between"},xi={class:"bt"},yi={class:"pmm2_flex_acenter huiA",style:{width:"70px"}},bi=["innerHTML"],Ci={key:0,class:"tishi pmm2_flex_acenter"},$i={key:1,class:"tishi pmm2_flex_acenter"},Ii={class:"left-coms"},ki={class:"delNum"},Oi={key:1,class:"li-box"},Mi={class:"num wrongNum"},Hi={class:"num wrongNum"},Xi={class:"num errorNum"},Si={class:"num goodNum"},Wi={class:"bt"},Yi={key:0},Li={key:1},Ei={key:2},zi={key:3},Ti={class:"nr"},Bi={key:0},Ni={key:1,class:"fenxi-box"},Ai={class:"zdf-box pmm2_flex_acenter"},Vi={class:"fen"},Ui={class:"box"},Ri={class:"box"},Pi={key:4,class:"right-com scrollBar-C",style:{width:"100%"}},ho=le({__name:"index",setup(e){const o=A(!1),t=A([]),i=A({}),s=A({});let r=null;const a=()=>{Te({}).then(k=>{let v=k.data||[],E=v.filter(N=>N.status==3);!i.value.id&&E[0]&&n(E[0]),i.value.len=E.length,t.value=v,clearInterval(r),r=null,r=setInterval(()=>{let N=t.value.filter(R=>R.status!=3).map(R=>R.id);N.length>0?y(N,1):(clearInterval(r),r=null)},3e4)})};a();const n=k=>{k.id!=i.value.id&&(i.value.id=k.id,i.value.status=k.status,i.value.course_code=k.course_code,g())},p=A(!1),g=async()=>{p.value=!0;let k=await Ae({demoId:i.value.id});if($.value=[],k.code==0){if(s.value=k.data||{},s.value.isDone==1&&i.value.status!=3){let v=t.value.findIndex(E=>E.id==i.value.id);t.value[v].status==3,t.value[v].status_label="已批改"}}else ee.error(k.msg),s.value={};p.value=!1},y=(k,v)=>{Ne({demoIds:k}).then(E=>{if(E.code!=0){v==1&&(clearInterval(r),r=null),x.value=!1,ee.error(E.msg);return}let N=E.data||[],R=N.length;if(R>0)for(let F=0;F<R;F++){let P=N[F];if(P.status==3){let q=t.value.findIndex(V=>V.id==P.id);t.value[q].status=P.status,t.value[q].status_label=P.statusLabel,i.value.len++,i.value.id==t.value[q].id&&g()}}v!=1&&(x.value=!1)})},b=(k,v)=>{Ee.confirm("删除后不可恢复，是否删除该条记录?","提示",{confirmButtonText:"确认删除",cancelButtonText:"取消",type:"warning"}).then(()=>{Be({demoId:k.id}).then(E=>{E.code==0?(ee.success("删除成功"),k.status==3&&i.value.len--,t.value.splice(v,1),k.id==i.value.id&&n(t.value[0])):ee.error(dres.msg)})}).catch(()=>{})},x=A(!1),H=()=>{x.value=!0,setTimeout(()=>{y([i.value.id],2)},3e3)},B=A({}),$=A([]),I=A(0),m=document.createElement("div"),c={border:"1px solid #fff",width:"220px",position:"fixed",visibility:"hidden",fontSize:"14px",padding:"10px"};Object.assign(m.style,c),document.body.appendChild(m);const Y=k=>{m.innerHTML=k;let v=m.getBoundingClientRect().height;return m.innerHTML="",v+15},L=A(!1),U=async k=>{try{L.value=!0;const v=s.value.img_width,E=s.value.img_height,N=B.value.$el.querySelector("img");if(N){const R=N.getBoundingClientRect(),F=Math.ceil(R.width*100)/100,P=Math.ceil(R.height*100)/100;let q={dwidth:F,dheight:P,width:v,height:E,wDig:v/F,hDig:E/P};I.value=P;const V=(X=0,S)=>S=="w"?Math.ceil(X/q.wDig):Math.ceil(X/q.hDig);let G={del:1,error:1,wrong:1,good:1,usage:1},w=0,D=s.value.imageSigns.length,f=0;for(let X=0;X<D;X++){let S=s.value.imageSigns[X];if(S.type=="wrong"?f=7:f=i.value.course_code=="en"?-5:7,S.show){if(S.type=="del")$.value.push({left:V(S.left+S.width,"w"),top:V(S.top+S.height,"h"),type:S.type,num:G[S.type]});else{let ne=[],ce=S.bboxs.length;if(ce>0){for(let se=0;se<ce;se++){let J=S.bboxs[se];ne.push({left:V(J.left,"w"),top:V(J.top,"h")+f,top2:V(J.top,"h"),bottom:V(J.bottom,"h")+f,width:V(J.right-J.left,"w"),height:V(J.bottom-J.top,"h")})}let te=ne[0].top2;if(w>te){let se=w-te;te+=se}w=Y(S.analysis)+te,I.value<w&&(I.value=w+20),$.value.push({type:S.type,analysis:S.analysis,top:te,bboxs:ne,num:G[S.type]})}}G[S.type]++}}}L.value=!1}catch(v){L.value=!1,console.log(v)}};return(k,v)=>{const E=ie("Plus"),N=me,R=ie("Delete"),F=we,P=ie("QuestionFilled"),q=Ye,V=_e,G=ve;return h(),u("div",si,[v[13]||(v[13]=l("header",{class:"header-box"},[l("div",{class:"title"},"园丁邦作文智能批改系统")],-1)),l("div",ii,[l("div",oi,[l("div",{class:"btn huiA pmm2_flex_center",onClick:v[0]||(v[0]=w=>o.value=!0)},[C(N,null,{default:O(()=>[C(E)]),_:1}),v[3]||(v[3]=l("span",null,"新建上传",-1))]),l("div",li,[l("div",ri,[v[4]||(v[4]=l("span",null,"批改历史记录",-1)),l("span",null,"（"+_(d(i).len)+"/"+_(d(t).length)+"）",1)]),l("div",ai,[(h(!0),u(W,null,T(d(t),(w,D)=>(h(),u("li",{key:D,class:Z(["li pmm2_flex_between huiA",{li1:w.status==2,on:d(i).id==w.id}]),onClick:f=>n(w)},[l("div",hi,_(w.course_name),1),l("div",ci,_(w.date),1),l("div",di,_(w.status_label),1),w.hasDel==1?(h(),Q(N,{key:0,class:"t2 huiA t5",onClick:he(f=>b(w,D),["stop"]),title:"删除"},{default:O(()=>[C(R)]),_:2},1032,["onClick"])):M("",!0)],10,ni))),128))])])]),oe((h(),u("div",ui,[d(s).isDone==0?(h(),u("div",pi,[v[6]||(v[6]=l("div",{class:"tig-box"}," 您提交的图片正在智能批改中... ",-1)),l("div",{class:"btn-box",onClick:v[1]||(v[1]=w=>H())},[C(F,{loading:d(x)},{default:O(()=>v[5]||(v[5]=[z("刷新查询结果")])),_:1,__:[5]},8,["loading"])])])):M("",!0),d(i).id?M("",!0):(h(),u("div",fi,v[7]||(v[7]=[l("div",{class:"tig-box"}," 以下是批阅效果演示。您还没有上传过图像， ",-1)]))),d(s).error_user?(h(),u("div",gi,[l("div",vi,_(d(s).error_user),1)])):M("",!0),d(s).isDone==1&&!d(p)?oe((h(),u("div",{key:3,class:Z(["right-com scrollBar-C",{on:d(i).status==3&&!d(s).error_user}])},[l("div",mi,[l("div",_i,[l("div",wi,[l("div",xi,[C(q,{placement:"right-start",title:"题目要求",width:500,trigger:"hover"},{reference:O(()=>[l("div",yi,[v[8]||(v[8]=z("题目要求 ")),C(N,null,{default:O(()=>[C(P)]),_:1})])]),default:O(()=>[l("div",{innerHTML:d(s).topic},null,8,bi)]),_:1})]),d(i).course_code=="ch"?(h(),u("div",Ci,v[9]||(v[9]=[Le('<div class="li hj">好句</div><div class="li hj">引用</div><div class="li wtj">问题句</div><div class="li cz"><span class="yuan">错</span>字</div><div class="li tm">涂抹</div>',5)]))):(h(),u("div",$i,v[10]||(v[10]=[l("div",{class:"li cz"},[l("span",{class:"yuan"},"错"),z("字")],-1),l("div",{class:"li tm"},"涂抹",-1)])))]),l("div",Ii,[C(V,{src:d(s).image_url,class:"img",ref_key:"boxImgRef",ref:B,onLoad:U},null,8,["src"]),(h(!0),u(W,null,T(d($),(w,D)=>(h(),u("div",{class:"imageSigns-boxs",key:D},[w.type=="del"?(h(),u("div",{key:0,class:"li-com del",style:j(`top: ${w.top}px;left: ${w.left}px;`)},[v[11]||(v[11]=l("span",{class:"sign"},"☰",-1)),l("span",ki,_(w.num),1)],4)):(h(),u("div",Oi,[(h(!0),u(W,null,T(w.bboxs,(f,X)=>(h(),u("div",{class:"li",key:X},[w.type=="wrong"?(h(),u("div",{key:0,class:"li-com wrong",style:j(`top: ${f.top}px;height:${f.height}px;
												left: ${f.left}px;width: ${f.width}px;`)},[l("span",Mi,_(w.num),1)],4)):w.type=="wrong"?(h(),u("div",{key:1,class:"li-com wrong",style:j(`top: ${f.top}px;height:${f.height}px;
												left: ${f.left}px;width: ${f.width}px;`)},[l("span",Hi,_(w.num),1)],4)):w.type=="error"?(h(),u("div",{key:2,class:"li-com error",style:j(`top: ${f.bottom}px;
												left: ${f.left}px;width: ${f.width}px;`)},[l("span",Xi,_(w.num),1)],4)):(h(),u("div",{key:3,class:"li-com good",style:j(`top: ${f.bottom}px;
												left: ${f.left}px;width: ${f.width}px;`)},[l("span",Si,_(w.num),1)],4))]))),128))]))]))),128))])]),(h(),u("div",{class:"right-boxs",style:j(`height: ${d(I)}px;`),key:d(I)},[(h(!0),u(W,null,T(d($),(w,D)=>(h(),u("div",{class:"li",key:D},[w.analysis?(h(),u("div",{key:0,class:Z(["li-box",{on:w.type=="error",hjNum:w.type=="good",wrongNum:w.type=="wrong"}]),style:j(`top: ${w.top}px;`)},[l("div",Wi,[w.type=="error"?(h(),u("span",Yi,"问题句")):w.type=="good"?(h(),u("span",Li,"好句")):w.type=="wrong"?(h(),u("span",Ei,"错字")):(h(),u("span",zi,"引用")),l("span",null,_(w.num),1)]),l("div",Ti,_(w.analysis),1)],6)):M("",!0)]))),128))],4))]),d(s).error_user?(h(),u("div",Ni,[l("div",Ai,[v[12]||(v[12]=l("div",{class:"bt"},"总得分：",-1)),l("div",Vi,_(d(s).paperScore||0)+"分",1),l("div",Ui,_(d(s).wordCount)+"字",1),l("div",Ri,_(d(s).levelLabel),1)])])):(h(),u("div",Bi,[d(i).course_code=="ch"?(h(),Q(Ss,{key:0,detailsInfo:d(s)},null,8,["detailsInfo"])):(h(),Q(ti,{key:1,detailsInfo:d(s)},null,8,["detailsInfo"]))]))],2)),[[G,d(L)]]):(h(),u("div",Pi,[(h(!0),u(W,null,T(d(s).images,(w,D)=>(h(),u("div",{style:{width:"100%"},class:"pmm2_flex_acenter",key:D},[C(V,{src:w,style:{margin:"0 auto"}},null,8,["src"])]))),128))]))])),[[G,d(p)]])]),C(Vt,{modelValue:d(o),"onUpdate:modelValue":v[2]||(v[2]=w=>ge(o)?o.value=w:null),onGetwritingList:a},null,8,["modelValue"])])}}});export{ho as default};

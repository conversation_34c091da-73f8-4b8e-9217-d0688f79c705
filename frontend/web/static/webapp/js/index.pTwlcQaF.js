import{aq as It,d as At,aK as Q,W as Et,r as fe,at as Lt,as as Mt,c as F,o as _,au as he,u as z,a as b,e as pe,F as se,q as ae,t as ue,n as $e,f as we,X as Rt,g as bt,K as Ke,Z as Je}from"./index.ZZ6UQeaF.js";/* empty css                 */import{_ as vt}from"./_plugin-vue_export-helper.DlAUqK2U.js";import{c as Ht}from"./index.6W4rOsHv.js";import{q as St}from"./question.IyuOoK5G.js";const Pn=/<strong class="answerVal">[^<]*(\([^)]*\))[^<]*<\/strong>/,qn='<strong class="answerVal">(&ensp;&ensp;&ensp;&ensp;&ensp;&ensp;)</strong>',kn={isQuestion:!1,itemTypeId:St.H1headline,itemTypeLabel:"标题1",content:"",children:[]},Dn=(n={},o=0)=>{let r=0;return n.type==1?r=n.value:r=o*(n.value/100),parseFloat(r)},Pt=(n,o)=>{let r=document.createElement("div");r.innerHTML=n;let t=r.querySelectorAll('u[type-id="num1"]'),e=r.querySelectorAll('u[type-id="num2"]');return t?t.forEach((s,l)=>{var a,m,y,C;(m=(a=o[l])==null?void 0:a.detailData)!=null&&m.topicSortNum&&(s.innerHTML=`　　${(C=(y=o[l])==null?void 0:y.detailData)==null?void 0:C.topicSortNum}　　`)}):e&&e.forEach((s,l)=>{var a,m;(m=(a=o[l])==null?void 0:a.detailData)!=null&&m.topicSortNum&&(s.innerHTML=o[l].detailData.topicSortNum+".　　　　")}),r.innerHTML},Fn=(n,o,r)=>{var C,u;if(!((u=(C=n.setting)==null?void 0:C.pageLayout)!=null&&u.columnWidth))return;const{columnWidth:t,value:e,printLayout:i}=n.setting.pageLayout;let{pagePadding:s,contentFontSize:l,contentLineHeightMMH:a}=n.pageStyles,m=t.width-s[1]*2,y=i.gap/2;if(o&&e!="A4_2"){if(r==2)m=(t.width-s[1]*2)/2-y;else if(r==3){let c=i.gap*2;m=(t.width-c-s[1]*2)/3}}if(e=="A4_2"&&(m=(m-i.gap)/2),!o){const c=document.createElement("span");c.innerHTML="&ensp;",c.style.fontSize=l+"px",c.style.lineHeight=a,document.body.appendChild(c);let N=c.offsetWidth;const x=Math.floor((m-6)/N);n.storeEnspNum=x,document.body.removeChild(c)}n.pageWidth=m};var le={},Ce,Oe;function qt(){return Oe||(Oe=1,Ce=function(){return typeof Promise=="function"&&Promise.prototype&&Promise.prototype.then}),Ce}var Ee={},ie={},We;function oe(){if(We)return ie;We=1;let n;const o=[0,26,44,70,100,134,172,196,242,292,346,404,466,532,581,655,733,815,901,991,1085,1156,1258,1364,1474,1588,1706,1828,1921,2051,2185,2323,2465,2611,2761,2876,3034,3196,3362,3532,3706];return ie.getSymbolSize=function(t){if(!t)throw new Error('"version" cannot be null or undefined');if(t<1||t>40)throw new Error('"version" should be in range from 1 to 40');return t*4+17},ie.getSymbolTotalCodewords=function(t){return o[t]},ie.getBCHDigit=function(r){let t=0;for(;r!==0;)t++,r>>>=1;return t},ie.setToSJISFunction=function(t){if(typeof t!="function")throw new Error('"toSJISFunc" is not a valid function.');n=t},ie.isKanjiModeEnabled=function(){return typeof n<"u"},ie.toSJIS=function(t){return n(t)},ie}var Se={},Ye;function Ve(){return Ye||(Ye=1,function(n){n.L={bit:1},n.M={bit:0},n.Q={bit:3},n.H={bit:2};function o(r){if(typeof r!="string")throw new Error("Param is not a string");switch(r.toLowerCase()){case"l":case"low":return n.L;case"m":case"medium":return n.M;case"q":case"quartile":return n.Q;case"h":case"high":return n.H;default:throw new Error("Unknown EC Level: "+r)}}n.isValid=function(t){return t&&typeof t.bit<"u"&&t.bit>=0&&t.bit<4},n.from=function(t,e){if(n.isValid(t))return t;try{return o(t)}catch{return e}}}(Se)),Se}var xe,Qe;function kt(){if(Qe)return xe;Qe=1;function n(){this.buffer=[],this.length=0}return n.prototype={get:function(o){const r=Math.floor(o/8);return(this.buffer[r]>>>7-o%8&1)===1},put:function(o,r){for(let t=0;t<r;t++)this.putBit((o>>>r-t-1&1)===1)},getLengthInBits:function(){return this.length},putBit:function(o){const r=Math.floor(this.length/8);this.buffer.length<=r&&this.buffer.push(0),o&&(this.buffer[r]|=128>>>this.length%8),this.length++}},xe=n,xe}var Ne,Ge;function Dt(){if(Ge)return Ne;Ge=1;function n(o){if(!o||o<1)throw new Error("BitMatrix size must be defined and greater than 0");this.size=o,this.data=new Uint8Array(o*o),this.reservedBit=new Uint8Array(o*o)}return n.prototype.set=function(o,r,t,e){const i=o*this.size+r;this.data[i]=t,e&&(this.reservedBit[i]=!0)},n.prototype.get=function(o,r){return this.data[o*this.size+r]},n.prototype.xor=function(o,r,t){this.data[o*this.size+r]^=t},n.prototype.isReserved=function(o,r){return this.reservedBit[o*this.size+r]},Ne=n,Ne}var Te={},Ze;function Ft(){return Ze||(Ze=1,function(n){const o=oe().getSymbolSize;n.getRowColCoords=function(t){if(t===1)return[];const e=Math.floor(t/7)+2,i=o(t),s=i===145?26:Math.ceil((i-13)/(2*e-2))*2,l=[i-7];for(let a=1;a<e-1;a++)l[a]=l[a-1]-s;return l.push(6),l.reverse()},n.getPositions=function(t){const e=[],i=n.getRowColCoords(t),s=i.length;for(let l=0;l<s;l++)for(let a=0;a<s;a++)l===0&&a===0||l===0&&a===s-1||l===s-1&&a===0||e.push([i[l],i[a]]);return e}}(Te)),Te}var Be={},Xe;function _t(){if(Xe)return Be;Xe=1;const n=oe().getSymbolSize,o=7;return Be.getPositions=function(t){const e=n(t);return[[0,0],[e-o,0],[0,e-o]]},Be}var Ie={},je;function zt(){return je||(je=1,function(n){n.Patterns={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};const o={N1:3,N2:3,N3:40,N4:10};n.isValid=function(e){return e!=null&&e!==""&&!isNaN(e)&&e>=0&&e<=7},n.from=function(e){return n.isValid(e)?parseInt(e,10):void 0},n.getPenaltyN1=function(e){const i=e.size;let s=0,l=0,a=0,m=null,y=null;for(let C=0;C<i;C++){l=a=0,m=y=null;for(let u=0;u<i;u++){let c=e.get(C,u);c===m?l++:(l>=5&&(s+=o.N1+(l-5)),m=c,l=1),c=e.get(u,C),c===y?a++:(a>=5&&(s+=o.N1+(a-5)),y=c,a=1)}l>=5&&(s+=o.N1+(l-5)),a>=5&&(s+=o.N1+(a-5))}return s},n.getPenaltyN2=function(e){const i=e.size;let s=0;for(let l=0;l<i-1;l++)for(let a=0;a<i-1;a++){const m=e.get(l,a)+e.get(l,a+1)+e.get(l+1,a)+e.get(l+1,a+1);(m===4||m===0)&&s++}return s*o.N2},n.getPenaltyN3=function(e){const i=e.size;let s=0,l=0,a=0;for(let m=0;m<i;m++){l=a=0;for(let y=0;y<i;y++)l=l<<1&2047|e.get(m,y),y>=10&&(l===1488||l===93)&&s++,a=a<<1&2047|e.get(y,m),y>=10&&(a===1488||a===93)&&s++}return s*o.N3},n.getPenaltyN4=function(e){let i=0;const s=e.data.length;for(let a=0;a<s;a++)i+=e.data[a];return Math.abs(Math.ceil(i*100/s/5)-10)*o.N4};function r(t,e,i){switch(t){case n.Patterns.PATTERN000:return(e+i)%2===0;case n.Patterns.PATTERN001:return e%2===0;case n.Patterns.PATTERN010:return i%3===0;case n.Patterns.PATTERN011:return(e+i)%3===0;case n.Patterns.PATTERN100:return(Math.floor(e/2)+Math.floor(i/3))%2===0;case n.Patterns.PATTERN101:return e*i%2+e*i%3===0;case n.Patterns.PATTERN110:return(e*i%2+e*i%3)%2===0;case n.Patterns.PATTERN111:return(e*i%3+(e+i)%2)%2===0;default:throw new Error("bad maskPattern:"+t)}}n.applyMask=function(e,i){const s=i.size;for(let l=0;l<s;l++)for(let a=0;a<s;a++)i.isReserved(a,l)||i.xor(a,l,r(e,a,l))},n.getBestMask=function(e,i){const s=Object.keys(n.Patterns).length;let l=0,a=1/0;for(let m=0;m<s;m++){i(m),n.applyMask(m,e);const y=n.getPenaltyN1(e)+n.getPenaltyN2(e)+n.getPenaltyN3(e)+n.getPenaltyN4(e);n.applyMask(m,e),y<a&&(a=y,l=m)}return l}}(Ie)),Ie}var ge={},et;function xt(){if(et)return ge;et=1;const n=Ve(),o=[1,1,1,1,1,1,1,1,1,1,2,2,1,2,2,4,1,2,4,4,2,4,4,4,2,4,6,5,2,4,6,6,2,5,8,8,4,5,8,8,4,5,8,11,4,8,10,11,4,9,12,16,4,9,16,16,6,10,12,18,6,10,17,16,6,11,16,19,6,13,18,21,7,14,21,25,8,16,20,25,8,17,23,25,9,17,23,34,9,18,25,30,10,20,27,32,12,21,29,35,12,23,34,37,12,25,34,40,13,26,35,42,14,28,38,45,15,29,40,48,16,31,43,51,17,33,45,54,18,35,48,57,19,37,51,60,19,38,53,63,20,40,56,66,21,43,59,70,22,45,62,74,24,47,65,77,25,49,68,81],r=[7,10,13,17,10,16,22,28,15,26,36,44,20,36,52,64,26,48,72,88,36,64,96,112,40,72,108,130,48,88,132,156,60,110,160,192,72,130,192,224,80,150,224,264,96,176,260,308,104,198,288,352,120,216,320,384,132,240,360,432,144,280,408,480,168,308,448,532,180,338,504,588,196,364,546,650,224,416,600,700,224,442,644,750,252,476,690,816,270,504,750,900,300,560,810,960,312,588,870,1050,336,644,952,1110,360,700,1020,1200,390,728,1050,1260,420,784,1140,1350,450,812,1200,1440,480,868,1290,1530,510,924,1350,1620,540,980,1440,1710,570,1036,1530,1800,570,1064,1590,1890,600,1120,1680,1980,630,1204,1770,2100,660,1260,1860,2220,720,1316,1950,2310,750,1372,2040,2430];return ge.getBlocksCount=function(e,i){switch(i){case n.L:return o[(e-1)*4+0];case n.M:return o[(e-1)*4+1];case n.Q:return o[(e-1)*4+2];case n.H:return o[(e-1)*4+3];default:return}},ge.getTotalCodewordsCount=function(e,i){switch(i){case n.L:return r[(e-1)*4+0];case n.M:return r[(e-1)*4+1];case n.Q:return r[(e-1)*4+2];case n.H:return r[(e-1)*4+3];default:return}},ge}var Ae={},ce={},tt;function Ut(){if(tt)return ce;tt=1;const n=new Uint8Array(512),o=new Uint8Array(256);return function(){let t=1;for(let e=0;e<255;e++)n[e]=t,o[t]=e,t<<=1,t&256&&(t^=285);for(let e=255;e<512;e++)n[e]=n[e-255]}(),ce.log=function(t){if(t<1)throw new Error("log("+t+")");return o[t]},ce.exp=function(t){return n[t]},ce.mul=function(t,e){return t===0||e===0?0:n[o[t]+o[e]]},ce}var nt;function Vt(){return nt||(nt=1,function(n){const o=Ut();n.mul=function(t,e){const i=new Uint8Array(t.length+e.length-1);for(let s=0;s<t.length;s++)for(let l=0;l<e.length;l++)i[s+l]^=o.mul(t[s],e[l]);return i},n.mod=function(t,e){let i=new Uint8Array(t);for(;i.length-e.length>=0;){const s=i[0];for(let a=0;a<e.length;a++)i[a]^=o.mul(e[a],s);let l=0;for(;l<i.length&&i[l]===0;)l++;i=i.slice(l)}return i},n.generateECPolynomial=function(t){let e=new Uint8Array([1]);for(let i=0;i<t;i++)e=n.mul(e,new Uint8Array([1,o.exp(i)]));return e}}(Ae)),Ae}var Le,it;function $t(){if(it)return Le;it=1;const n=Vt();function o(r){this.genPoly=void 0,this.degree=r,this.degree&&this.initialize(this.degree)}return o.prototype.initialize=function(t){this.degree=t,this.genPoly=n.generateECPolynomial(this.degree)},o.prototype.encode=function(t){if(!this.genPoly)throw new Error("Encoder not initialized");const e=new Uint8Array(t.length+this.degree);e.set(t);const i=n.mod(e,this.genPoly),s=this.degree-i.length;if(s>0){const l=new Uint8Array(this.degree);return l.set(i,s),l}return i},Le=o,Le}var Me={},Re={},be={},ot;function Nt(){return ot||(ot=1,be.isValid=function(o){return!isNaN(o)&&o>=1&&o<=40}),be}var ee={},rt;function Tt(){if(rt)return ee;rt=1;const n="[0-9]+",o="[A-Z $%*+\\-./:]+";let r="(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+";r=r.replace(/u/g,"\\u");const t="(?:(?![A-Z0-9 $%*+\\-./:]|"+r+`)(?:.|[\r
]))+`;ee.KANJI=new RegExp(r,"g"),ee.BYTE_KANJI=new RegExp("[^A-Z0-9 $%*+\\-./:]+","g"),ee.BYTE=new RegExp(t,"g"),ee.NUMERIC=new RegExp(n,"g"),ee.ALPHANUMERIC=new RegExp(o,"g");const e=new RegExp("^"+r+"$"),i=new RegExp("^"+n+"$"),s=new RegExp("^[A-Z0-9 $%*+\\-./:]+$");return ee.testKanji=function(a){return e.test(a)},ee.testNumeric=function(a){return i.test(a)},ee.testAlphanumeric=function(a){return s.test(a)},ee}var st;function re(){return st||(st=1,function(n){const o=Nt(),r=Tt();n.NUMERIC={id:"Numeric",bit:1,ccBits:[10,12,14]},n.ALPHANUMERIC={id:"Alphanumeric",bit:2,ccBits:[9,11,13]},n.BYTE={id:"Byte",bit:4,ccBits:[8,16,16]},n.KANJI={id:"Kanji",bit:8,ccBits:[8,10,12]},n.MIXED={bit:-1},n.getCharCountIndicator=function(i,s){if(!i.ccBits)throw new Error("Invalid mode: "+i);if(!o.isValid(s))throw new Error("Invalid version: "+s);return s>=1&&s<10?i.ccBits[0]:s<27?i.ccBits[1]:i.ccBits[2]},n.getBestModeForData=function(i){return r.testNumeric(i)?n.NUMERIC:r.testAlphanumeric(i)?n.ALPHANUMERIC:r.testKanji(i)?n.KANJI:n.BYTE},n.toString=function(i){if(i&&i.id)return i.id;throw new Error("Invalid mode")},n.isValid=function(i){return i&&i.bit&&i.ccBits};function t(e){if(typeof e!="string")throw new Error("Param is not a string");switch(e.toLowerCase()){case"numeric":return n.NUMERIC;case"alphanumeric":return n.ALPHANUMERIC;case"kanji":return n.KANJI;case"byte":return n.BYTE;default:throw new Error("Unknown mode: "+e)}}n.from=function(i,s){if(n.isValid(i))return i;try{return t(i)}catch{return s}}}(Re)),Re}var at;function Kt(){return at||(at=1,function(n){const o=oe(),r=xt(),t=Ve(),e=re(),i=Nt(),s=7973,l=o.getBCHDigit(s);function a(u,c,N){for(let x=1;x<=40;x++)if(c<=n.getCapacity(x,N,u))return x}function m(u,c){return e.getCharCountIndicator(u,c)+4}function y(u,c){let N=0;return u.forEach(function(x){const v=m(x.mode,c);N+=v+x.getBitsLength()}),N}function C(u,c){for(let N=1;N<=40;N++)if(y(u,N)<=n.getCapacity(N,c,e.MIXED))return N}n.from=function(c,N){return i.isValid(c)?parseInt(c,10):N},n.getCapacity=function(c,N,x){if(!i.isValid(c))throw new Error("Invalid QR Code version");typeof x>"u"&&(x=e.BYTE);const v=o.getSymbolTotalCodewords(c),T=r.getTotalCodewordsCount(c,N),R=(v-T)*8;if(x===e.MIXED)return R;const B=R-m(x,c);switch(x){case e.NUMERIC:return Math.floor(B/10*3);case e.ALPHANUMERIC:return Math.floor(B/11*2);case e.KANJI:return Math.floor(B/13);case e.BYTE:default:return Math.floor(B/8)}},n.getBestVersionForData=function(c,N){let x;const v=t.from(N,t.M);if(Array.isArray(c)){if(c.length>1)return C(c,v);if(c.length===0)return 1;x=c[0]}else x=c;return a(x.mode,x.getLength(),v)},n.getEncodedBits=function(c){if(!i.isValid(c)||c<7)throw new Error("Invalid QR Code version");let N=c<<12;for(;o.getBCHDigit(N)-l>=0;)N^=s<<o.getBCHDigit(N)-l;return c<<12|N}}(Me)),Me}var ve={},lt;function Jt(){if(lt)return ve;lt=1;const n=oe(),o=1335,r=21522,t=n.getBCHDigit(o);return ve.getEncodedBits=function(i,s){const l=i.bit<<3|s;let a=l<<10;for(;n.getBCHDigit(a)-t>=0;)a^=o<<n.getBCHDigit(a)-t;return(l<<10|a)^r},ve}var He={},Pe,ut;function Ot(){if(ut)return Pe;ut=1;const n=re();function o(r){this.mode=n.NUMERIC,this.data=r.toString()}return o.getBitsLength=function(t){return 10*Math.floor(t/3)+(t%3?t%3*3+1:0)},o.prototype.getLength=function(){return this.data.length},o.prototype.getBitsLength=function(){return o.getBitsLength(this.data.length)},o.prototype.write=function(t){let e,i,s;for(e=0;e+3<=this.data.length;e+=3)i=this.data.substr(e,3),s=parseInt(i,10),t.put(s,10);const l=this.data.length-e;l>0&&(i=this.data.substr(e),s=parseInt(i,10),t.put(s,l*3+1))},Pe=o,Pe}var qe,ct;function Wt(){if(ct)return qe;ct=1;const n=re(),o=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"," ","$","%","*","+","-",".","/",":"];function r(t){this.mode=n.ALPHANUMERIC,this.data=t}return r.getBitsLength=function(e){return 11*Math.floor(e/2)+6*(e%2)},r.prototype.getLength=function(){return this.data.length},r.prototype.getBitsLength=function(){return r.getBitsLength(this.data.length)},r.prototype.write=function(e){let i;for(i=0;i+2<=this.data.length;i+=2){let s=o.indexOf(this.data[i])*45;s+=o.indexOf(this.data[i+1]),e.put(s,11)}this.data.length%2&&e.put(o.indexOf(this.data[i]),6)},qe=r,qe}var ke,dt;function Yt(){if(dt)return ke;dt=1;const n=re();function o(r){this.mode=n.BYTE,typeof r=="string"?this.data=new TextEncoder().encode(r):this.data=new Uint8Array(r)}return o.getBitsLength=function(t){return t*8},o.prototype.getLength=function(){return this.data.length},o.prototype.getBitsLength=function(){return o.getBitsLength(this.data.length)},o.prototype.write=function(r){for(let t=0,e=this.data.length;t<e;t++)r.put(this.data[t],8)},ke=o,ke}var De,ft;function Qt(){if(ft)return De;ft=1;const n=re(),o=oe();function r(t){this.mode=n.KANJI,this.data=t}return r.getBitsLength=function(e){return e*13},r.prototype.getLength=function(){return this.data.length},r.prototype.getBitsLength=function(){return r.getBitsLength(this.data.length)},r.prototype.write=function(t){let e;for(e=0;e<this.data.length;e++){let i=o.toSJIS(this.data[e]);if(i>=33088&&i<=40956)i-=33088;else if(i>=57408&&i<=60351)i-=49472;else throw new Error("Invalid SJIS character: "+this.data[e]+`
Make sure your charset is UTF-8`);i=(i>>>8&255)*192+(i&255),t.put(i,13)}},De=r,De}var Fe={exports:{}},ht;function Gt(){return ht||(ht=1,function(n){var o={single_source_shortest_paths:function(r,t,e){var i={},s={};s[t]=0;var l=o.PriorityQueue.make();l.push(t,0);for(var a,m,y,C,u,c,N,x,v;!l.empty();){a=l.pop(),m=a.value,C=a.cost,u=r[m]||{};for(y in u)u.hasOwnProperty(y)&&(c=u[y],N=C+c,x=s[y],v=typeof s[y]>"u",(v||x>N)&&(s[y]=N,l.push(y,N),i[y]=m))}if(typeof e<"u"&&typeof s[e]>"u"){var T=["Could not find a path from ",t," to ",e,"."].join("");throw new Error(T)}return i},extract_shortest_path_from_predecessor_list:function(r,t){for(var e=[],i=t;i;)e.push(i),r[i],i=r[i];return e.reverse(),e},find_path:function(r,t,e){var i=o.single_source_shortest_paths(r,t,e);return o.extract_shortest_path_from_predecessor_list(i,e)},PriorityQueue:{make:function(r){var t=o.PriorityQueue,e={},i;r=r||{};for(i in t)t.hasOwnProperty(i)&&(e[i]=t[i]);return e.queue=[],e.sorter=r.sorter||t.default_sorter,e},default_sorter:function(r,t){return r.cost-t.cost},push:function(r,t){var e={value:r,cost:t};this.queue.push(e),this.queue.sort(this.sorter)},pop:function(){return this.queue.shift()},empty:function(){return this.queue.length===0}}};n.exports=o}(Fe)),Fe.exports}var gt;function Zt(){return gt||(gt=1,function(n){const o=re(),r=Ot(),t=Wt(),e=Yt(),i=Qt(),s=Tt(),l=oe(),a=Gt();function m(T){return unescape(encodeURIComponent(T)).length}function y(T,R,B){const f=[];let U;for(;(U=T.exec(B))!==null;)f.push({data:U[0],index:U.index,mode:R,length:U[0].length});return f}function C(T){const R=y(s.NUMERIC,o.NUMERIC,T),B=y(s.ALPHANUMERIC,o.ALPHANUMERIC,T);let f,U;return l.isKanjiModeEnabled()?(f=y(s.BYTE,o.BYTE,T),U=y(s.KANJI,o.KANJI,T)):(f=y(s.BYTE_KANJI,o.BYTE,T),U=[]),R.concat(B,f,U).sort(function(L,A){return L.index-A.index}).map(function(L){return{data:L.data,mode:L.mode,length:L.length}})}function u(T,R){switch(R){case o.NUMERIC:return r.getBitsLength(T);case o.ALPHANUMERIC:return t.getBitsLength(T);case o.KANJI:return i.getBitsLength(T);case o.BYTE:return e.getBitsLength(T)}}function c(T){return T.reduce(function(R,B){const f=R.length-1>=0?R[R.length-1]:null;return f&&f.mode===B.mode?(R[R.length-1].data+=B.data,R):(R.push(B),R)},[])}function N(T){const R=[];for(let B=0;B<T.length;B++){const f=T[B];switch(f.mode){case o.NUMERIC:R.push([f,{data:f.data,mode:o.ALPHANUMERIC,length:f.length},{data:f.data,mode:o.BYTE,length:f.length}]);break;case o.ALPHANUMERIC:R.push([f,{data:f.data,mode:o.BYTE,length:f.length}]);break;case o.KANJI:R.push([f,{data:f.data,mode:o.BYTE,length:m(f.data)}]);break;case o.BYTE:R.push([{data:f.data,mode:o.BYTE,length:m(f.data)}])}}return R}function x(T,R){const B={},f={start:{}};let U=["start"];for(let E=0;E<T.length;E++){const L=T[E],A=[];for(let S=0;S<L.length;S++){const M=L[S],g=""+E+S;A.push(g),B[g]={node:M,lastCount:0},f[g]={};for(let d=0;d<U.length;d++){const h=U[d];B[h]&&B[h].node.mode===M.mode?(f[h][g]=u(B[h].lastCount+M.length,M.mode)-u(B[h].lastCount,M.mode),B[h].lastCount+=M.length):(B[h]&&(B[h].lastCount=M.length),f[h][g]=u(M.length,M.mode)+4+o.getCharCountIndicator(M.mode,R))}}U=A}for(let E=0;E<U.length;E++)f[U[E]].end=0;return{map:f,table:B}}function v(T,R){let B;const f=o.getBestModeForData(T);if(B=o.from(R,f),B!==o.BYTE&&B.bit<f.bit)throw new Error('"'+T+'" cannot be encoded with mode '+o.toString(B)+`.
 Suggested mode is: `+o.toString(f));switch(B===o.KANJI&&!l.isKanjiModeEnabled()&&(B=o.BYTE),B){case o.NUMERIC:return new r(T);case o.ALPHANUMERIC:return new t(T);case o.KANJI:return new i(T);case o.BYTE:return new e(T)}}n.fromArray=function(R){return R.reduce(function(B,f){return typeof f=="string"?B.push(v(f,null)):f.data&&B.push(v(f.data,f.mode)),B},[])},n.fromString=function(R,B){const f=C(R,l.isKanjiModeEnabled()),U=N(f),E=x(U,B),L=a.find_path(E.map,"start","end"),A=[];for(let S=1;S<L.length-1;S++)A.push(E.table[L[S]].node);return n.fromArray(c(A))},n.rawSplit=function(R){return n.fromArray(C(R,l.isKanjiModeEnabled()))}}(He)),He}var mt;function Xt(){if(mt)return Ee;mt=1;const n=oe(),o=Ve(),r=kt(),t=Dt(),e=Ft(),i=_t(),s=zt(),l=xt(),a=$t(),m=Kt(),y=Jt(),C=re(),u=Zt();function c(E,L){const A=E.size,S=i.getPositions(L);for(let M=0;M<S.length;M++){const g=S[M][0],d=S[M][1];for(let h=-1;h<=7;h++)if(!(g+h<=-1||A<=g+h))for(let p=-1;p<=7;p++)d+p<=-1||A<=d+p||(h>=0&&h<=6&&(p===0||p===6)||p>=0&&p<=6&&(h===0||h===6)||h>=2&&h<=4&&p>=2&&p<=4?E.set(g+h,d+p,!0,!0):E.set(g+h,d+p,!1,!0))}}function N(E){const L=E.size;for(let A=8;A<L-8;A++){const S=A%2===0;E.set(A,6,S,!0),E.set(6,A,S,!0)}}function x(E,L){const A=e.getPositions(L);for(let S=0;S<A.length;S++){const M=A[S][0],g=A[S][1];for(let d=-2;d<=2;d++)for(let h=-2;h<=2;h++)d===-2||d===2||h===-2||h===2||d===0&&h===0?E.set(M+d,g+h,!0,!0):E.set(M+d,g+h,!1,!0)}}function v(E,L){const A=E.size,S=m.getEncodedBits(L);let M,g,d;for(let h=0;h<18;h++)M=Math.floor(h/3),g=h%3+A-8-3,d=(S>>h&1)===1,E.set(M,g,d,!0),E.set(g,M,d,!0)}function T(E,L,A){const S=E.size,M=y.getEncodedBits(L,A);let g,d;for(g=0;g<15;g++)d=(M>>g&1)===1,g<6?E.set(g,8,d,!0):g<8?E.set(g+1,8,d,!0):E.set(S-15+g,8,d,!0),g<8?E.set(8,S-g-1,d,!0):g<9?E.set(8,15-g-1+1,d,!0):E.set(8,15-g-1,d,!0);E.set(S-8,8,1,!0)}function R(E,L){const A=E.size;let S=-1,M=A-1,g=7,d=0;for(let h=A-1;h>0;h-=2)for(h===6&&h--;;){for(let p=0;p<2;p++)if(!E.isReserved(M,h-p)){let H=!1;d<L.length&&(H=(L[d]>>>g&1)===1),E.set(M,h-p,H),g--,g===-1&&(d++,g=7)}if(M+=S,M<0||A<=M){M-=S,S=-S;break}}}function B(E,L,A){const S=new r;A.forEach(function(p){S.put(p.mode.bit,4),S.put(p.getLength(),C.getCharCountIndicator(p.mode,E)),p.write(S)});const M=n.getSymbolTotalCodewords(E),g=l.getTotalCodewordsCount(E,L),d=(M-g)*8;for(S.getLengthInBits()+4<=d&&S.put(0,4);S.getLengthInBits()%8!==0;)S.putBit(0);const h=(d-S.getLengthInBits())/8;for(let p=0;p<h;p++)S.put(p%2?17:236,8);return f(S,E,L)}function f(E,L,A){const S=n.getSymbolTotalCodewords(L),M=l.getTotalCodewordsCount(L,A),g=S-M,d=l.getBlocksCount(L,A),h=S%d,p=d-h,H=Math.floor(S/d),I=Math.floor(g/d),w=I+1,V=H-I,k=new a(V);let O=0;const J=new Array(d),W=new Array(d);let Y=0;const ne=new Uint8Array(E.buffer);for(let P=0;P<d;P++){const K=P<p?I:w;J[P]=ne.slice(O,O+K),W[P]=k.encode(J[P]),O+=K,Y=Math.max(Y,K)}const G=new Uint8Array(S);let $=0,q,D;for(q=0;q<Y;q++)for(D=0;D<d;D++)q<J[D].length&&(G[$++]=J[D][q]);for(q=0;q<V;q++)for(D=0;D<d;D++)G[$++]=W[D][q];return G}function U(E,L,A,S){let M;if(Array.isArray(E))M=u.fromArray(E);else if(typeof E=="string"){let H=L;if(!H){const I=u.rawSplit(E);H=m.getBestVersionForData(I,A)}M=u.fromString(E,H||40)}else throw new Error("Invalid data");const g=m.getBestVersionForData(M,A);if(!g)throw new Error("The amount of data is too big to be stored in a QR Code");if(!L)L=g;else if(L<g)throw new Error(`
The chosen QR Code version cannot contain this amount of data.
Minimum version required to store current data is: `+g+`.
`);const d=B(L,A,M),h=n.getSymbolSize(L),p=new t(h);return c(p,L),N(p),x(p,L),T(p,A,0),L>=7&&v(p,L),R(p,d),isNaN(S)&&(S=s.getBestMask(p,T.bind(null,p,A))),s.applyMask(S,p),T(p,A,S),{modules:p,version:L,errorCorrectionLevel:A,maskPattern:S,segments:M}}return Ee.create=function(L,A){if(typeof L>"u"||L==="")throw new Error("No input text");let S=o.M,M,g;return typeof A<"u"&&(S=o.from(A.errorCorrectionLevel,o.M),M=m.from(A.version),g=s.from(A.maskPattern),A.toSJISFunc&&n.setToSJISFunction(A.toSJISFunc)),U(L,M,S,g)},Ee}var _e={},ze={},yt;function Bt(){return yt||(yt=1,function(n){function o(r){if(typeof r=="number"&&(r=r.toString()),typeof r!="string")throw new Error("Color should be defined as hex string");let t=r.slice().replace("#","").split("");if(t.length<3||t.length===5||t.length>8)throw new Error("Invalid hex color: "+r);(t.length===3||t.length===4)&&(t=Array.prototype.concat.apply([],t.map(function(i){return[i,i]}))),t.length===6&&t.push("F","F");const e=parseInt(t.join(""),16);return{r:e>>24&255,g:e>>16&255,b:e>>8&255,a:e&255,hex:"#"+t.slice(0,6).join("")}}n.getOptions=function(t){t||(t={}),t.color||(t.color={});const e=typeof t.margin>"u"||t.margin===null||t.margin<0?4:t.margin,i=t.width&&t.width>=21?t.width:void 0,s=t.scale||4;return{width:i,scale:i?4:s,margin:e,color:{dark:o(t.color.dark||"#000000ff"),light:o(t.color.light||"#ffffffff")},type:t.type,rendererOpts:t.rendererOpts||{}}},n.getScale=function(t,e){return e.width&&e.width>=t+e.margin*2?e.width/(t+e.margin*2):e.scale},n.getImageWidth=function(t,e){const i=n.getScale(t,e);return Math.floor((t+e.margin*2)*i)},n.qrToImageData=function(t,e,i){const s=e.modules.size,l=e.modules.data,a=n.getScale(s,i),m=Math.floor((s+i.margin*2)*a),y=i.margin*a,C=[i.color.light,i.color.dark];for(let u=0;u<m;u++)for(let c=0;c<m;c++){let N=(u*m+c)*4,x=i.color.light;if(u>=y&&c>=y&&u<m-y&&c<m-y){const v=Math.floor((u-y)/a),T=Math.floor((c-y)/a);x=C[l[v*s+T]?1:0]}t[N++]=x.r,t[N++]=x.g,t[N++]=x.b,t[N]=x.a}}}(ze)),ze}var pt;function jt(){return pt||(pt=1,function(n){const o=Bt();function r(e,i,s){e.clearRect(0,0,i.width,i.height),i.style||(i.style={}),i.height=s,i.width=s,i.style.height=s+"px",i.style.width=s+"px"}function t(){try{return document.createElement("canvas")}catch{throw new Error("You need to specify a canvas element")}}n.render=function(i,s,l){let a=l,m=s;typeof a>"u"&&(!s||!s.getContext)&&(a=s,s=void 0),s||(m=t()),a=o.getOptions(a);const y=o.getImageWidth(i.modules.size,a),C=m.getContext("2d"),u=C.createImageData(y,y);return o.qrToImageData(u.data,i,a),r(C,m,y),C.putImageData(u,0,0),m},n.renderToDataURL=function(i,s,l){let a=l;typeof a>"u"&&(!s||!s.getContext)&&(a=s,s=void 0),a||(a={});const m=n.render(i,s,a),y=a.type||"image/png",C=a.rendererOpts||{};return m.toDataURL(y,C.quality)}}(_e)),_e}var Ue={},wt;function en(){if(wt)return Ue;wt=1;const n=Bt();function o(e,i){const s=e.a/255,l=i+'="'+e.hex+'"';return s<1?l+" "+i+'-opacity="'+s.toFixed(2).slice(1)+'"':l}function r(e,i,s){let l=e+i;return typeof s<"u"&&(l+=" "+s),l}function t(e,i,s){let l="",a=0,m=!1,y=0;for(let C=0;C<e.length;C++){const u=Math.floor(C%i),c=Math.floor(C/i);!u&&!m&&(m=!0),e[C]?(y++,C>0&&u>0&&e[C-1]||(l+=m?r("M",u+s,.5+c+s):r("m",a,0),a=0,m=!1),u+1<i&&e[C+1]||(l+=r("h",y),y=0)):a++}return l}return Ue.render=function(i,s,l){const a=n.getOptions(s),m=i.modules.size,y=i.modules.data,C=m+a.margin*2,u=a.color.light.a?"<path "+o(a.color.light,"fill")+' d="M0 0h'+C+"v"+C+'H0z"/>':"",c="<path "+o(a.color.dark,"stroke")+' d="'+t(y,m,a.margin)+'"/>',N='viewBox="0 0 '+C+" "+C+'"',v='<svg xmlns="http://www.w3.org/2000/svg" '+(a.width?'width="'+a.width+'" height="'+a.width+'" ':"")+N+' shape-rendering="crispEdges">'+u+c+`</svg>
`;return typeof l=="function"&&l(null,v),v},Ue}var Ct;function tn(){if(Ct)return le;Ct=1;const n=qt(),o=Xt(),r=jt(),t=en();function e(i,s,l,a,m){const y=[].slice.call(arguments,1),C=y.length,u=typeof y[C-1]=="function";if(!u&&!n())throw new Error("Callback required as last argument");if(u){if(C<2)throw new Error("Too few arguments provided");C===2?(m=l,l=s,s=a=void 0):C===3&&(s.getContext&&typeof m>"u"?(m=a,a=void 0):(m=a,a=l,l=s,s=void 0))}else{if(C<1)throw new Error("Too few arguments provided");return C===1?(l=s,s=a=void 0):C===2&&!s.getContext&&(a=l,l=s,s=void 0),new Promise(function(c,N){try{const x=o.create(l,a);c(i(x,s,a))}catch(x){N(x)}})}try{const c=o.create(l,a);m(null,i(c,s,a))}catch(c){m(c)}}return le.create=o.create,le.toCanvas=e.bind(null,r.render),le.toDataURL=e.bind(null,r.renderToDataURL),le.toString=e.bind(null,function(i,s,l){return t.render(i,l)}),le}var nn=tn();const on=It(nn),rn={key:0,style:{width:"100%"}},sn={class:"student-info-inside student-info-insideA3 pmm2_flex_between"},an={key:0,class:"info-item"},ln={class:"student-content student-content2"},un={class:"exam-number",style:{"border-left":"1px solid #000"}},cn={key:0,class:"smear",style:{"padding-top":"5px"}},dn={key:1,class:"shou-box pmm2_flex_acenter"},fn=["data-qrcodeText"],hn=["src"],gn={style:{"font-size":"12px",color:"#666",position:"absolute",bottom:"-3px"}},mn={key:1,class:"student-content"},yn=["data-qrcodeText"],pn=["src"],wn={style:{"font-size":"12px",color:"#666",position:"absolute",bottom:"-3px"}},Cn={class:"exam-number"},En={key:0,class:"smear"},Sn={key:1,class:"shou-box pmm2_flex_acenter"},xn={class:"student-info-inside"},Nn={key:0,class:"info-item"},Tn={class:"title"},Bn={key:2,class:"total-score pmm2_flex_center"},In={class:"total-score-input-box total-score-input-box2"},An={key:3,class:"total-score pmm2_flex_center"},Ln=At({__name:"index",props:{examNumberLength:Number,pageLayoutName:String,types:String,index:Number,layout:{type:Object}},setup(n){let o=Q().pageStyles;const r=n,t=Et(()=>Q().setting.examNoTypeId!="FILL"&&o.handWriteHeadHeight||o.headHeight),e=fe(""),i=fe(200);r.layout.showCreatorInfoVal||(r.layout.showCreatorInfoVal=`日期：${r.layout.createdAt}  制卡人：${r.layout.created_by_name||""} `),Lt(()=>{s()});const s=()=>{e.value.textContent=r.layout.showCreatorInfoVal;const u=e.value.offsetWidth;i.value=u>200?u+20:200};Mt(()=>o.showCreatorInfo,u=>{u&&(r.layout.showCreatorInfoVal=`日期：${r.layout.createdAt}  制卡人：${r.layout.created_by_name||""}`,s())});const l=fe(""),a=fe(1);(async()=>{try{let{id:u,headNum:c}=r.layout;r.pageLayoutName=="A4_2"&&(c=c/2),r.index==0?a.value=1:a.value=r.index/c+1,l.value=await on.toDataURL(u+"-"+a.value,{width:100,height:100,margin:0})}catch(u){console.error(u)}})();const y=[];for(let u=0;u<(r==null?void 0:r.examNumberLength);u++)y[u]=[0,1,2,3,4,5,6,7,8,9];const C=(u,c)=>{(c.match(/\n/g)||[]).length>=1&&u.preventDefault()};return(u,c)=>{const N=bt;return _(),F("div",{class:"page-head",style:he(`min-height: ${z(t)}px;height: ${z(t)}px;`)},[n.layout.headNum==6?(_(),F("div",rn,[b("div",sn,[c[5]||(c[5]=b("div",{class:"info-item"},[b("span",{class:"label"},"姓名:"),b("span",{"label-key":"name",class:"line"})],-1)),c[6]||(c[6]=b("div",{class:"info-item"},[b("span",{class:"label"},"班级:"),b("span",{"label-key":"class_no",class:"line"})],-1)),z(Q)().setting.examNoTypeId=="FILL"?(_(),F("div",an,c[4]||(c[4]=[b("span",{class:"label"},"考号:",-1),b("span",{"label-key":"code",class:"line"},null,-1)]))):pe("",!0)]),b("div",ln,[b("div",un,[c[8]||(c[8]=b("div",{class:"exam-name"}," 考 号 ",-1)),z(Q)().setting.examNoTypeId=="FILL"?(_(),F("div",cn,[(_(),F(se,null,ae(y,(x,v)=>b("div",{class:"smear-number",key:v},[(_(!0),F(se,null,ae(x,T=>{var R,B,f;return _(),F("span",{style:he(`width:${(R=z(o))==null?void 0:R.examNumberFillBoxSize[0]}px;line-height:${(B=z(o))==null?void 0:B.examNumberFillBoxSize[1]}px;height:${(f=z(o))==null?void 0:f.examNumberFillBoxSize[1]}px;`),class:"smear-item",key:T},ue(T),5)}),128))])),64))])):(_(),F("div",dn,[(_(!0),F(se,null,ae(n.examNumberLength,(x,v)=>(_(),F("div",{class:"item-box",key:v},c[7]||(c[7]=[b("div",{class:"item-com"},null,-1)])))),128))]))]),b("div",{class:$e(["info-qrcode",{pd15:z(Q)().setting.examNoTypeId!="FILL"}]),"data-qrcodeText":n.layout.id+"-"+z(a)},[b("img",{src:z(l),alt:"二维码",style:{"margin-top":"-5px"}},null,8,hn),b("span",gn,ue(n.layout.id),1)],10,fn)])])):(_(),F("div",mn,[b("div",{class:$e(["info-qrcode",{pd15:z(Q)().setting.examNoTypeId!="FILL"}]),"data-qrcodeText":n.layout.id+"-"+z(a)},[b("img",{src:z(l),alt:"二维码"},null,8,pn),b("span",wn,ue(n.layout.id),1)],10,yn),b("div",Cn,[z(Q)().setting.examNoTypeId=="FILL"?(_(),F("div",En,[(_(),F(se,null,ae(y,(x,v)=>b("div",{class:"smear-number",key:v},[(_(!0),F(se,null,ae(x,T=>{var R,B,f;return _(),F("span",{style:he(`width:${(R=z(o))==null?void 0:R.examNumberFillBoxSize[0]}px;line-height:${(B=z(o))==null?void 0:B.examNumberFillBoxSize[1]}px;height:${(f=z(o))==null?void 0:f.examNumberFillBoxSize[1]}px;`),class:"smear-item",key:T},ue(T),5)}),128))])),64))])):(_(),F("div",Sn,[(_(!0),F(se,null,ae(n.examNumberLength,(x,v)=>(_(),F("div",{class:"item-box",key:v},c[9]||(c[9]=[b("div",{class:"item-com"},null,-1)])))),128))]))]),b("div",xn,[c[11]||(c[11]=b("div",{class:"info-item"},[b("span",{class:"label"},"姓名:"),b("span",{"label-key":"name",class:"line"})],-1)),c[12]||(c[12]=b("div",{class:"info-item"},[b("span",{class:"label"},"班级:"),b("span",{"label-key":"class_no",class:"line"})],-1)),z(Q)().setting.examNoTypeId=="FILL"?(_(),F("div",Nn,c[10]||(c[10]=[b("span",{class:"label"},"考号:",-1),b("span",{"label-key":"code",class:"line"},null,-1)]))):pe("",!0)])])),b("div",Tn,[we(N,{modelValue:n.layout.title,"onUpdate:modelValue":c[0]||(c[0]=x=>n.layout.title=x),type:"textarea",style:{"text-align":"center"},maxlength:"50",class:"page-title ant-input el-textareas break-word",placeholder:"请输入标题",autosize:{minRows:1,maxRows:2},onKeydown:c[1]||(c[1]=Rt(x=>C(x,n.layout.title),["enter"]))},null,8,["modelValue"])]),n.types=="subject"?(_(),F("div",Bn,[Ke(b("div",In,[we(N,{class:"total-score-input break-word",modelValue:n.layout.showCreatorInfoVal,"onUpdate:modelValue":c[2]||(c[2]=x=>n.layout.showCreatorInfoVal=x),placeholder:"请输入制卡信息",type:"text"},null,8,["modelValue"])],512),[[Je,z(o).showCreatorInfo]])])):n.layout.HhyptType!="answerSheet"&&n.layout.HhyptType!="writing"?(_(),F("div",An,[b("div",null,"分值："+ue(n.layout.fullMarks||0)+"分 ",1),Ke(b("div",{class:"total-score-input-box",onInput:s,style:he({width:z(i)+"px"})},[we(N,{class:"total-score-input break-word",modelValue:n.layout.showCreatorInfoVal,"onUpdate:modelValue":c[3]||(c[3]=x=>n.layout.showCreatorInfoVal=x),placeholder:"请输入制卡信息",type:"text"},null,8,["modelValue"])],36),[[Je,z(o).showCreatorInfo]])])):pe("",!0),b("span",{ref_key:"textSpanRef",ref:e,class:"total-score",style:{visibility:"hidden","white-space":"nowrap",position:"fixed","z-index":"-9"}},null,512)],4)}}}),_n=vt(Ln,[["__scopeId","data-v-9574e7af"]]);function zn(n){var M;let o=Q().pageStyles,r=Q().setting,t=Q().pageWidth,e=r.pageLayout.printLayout.gap/2;const i=(g,d)=>!!((g==0||g==1&&n.isPreview&&n.layout.column==3)&&(d=="A4_2"||n.isPreview&&(n.layout.column==2||n.layout.column==3)));let s=o.headHeight;Q().setting.examNoTypeId!="FILL"&&(s=o.handWriteHeadHeight||o.headHeight);const l=(g,d)=>{let h=`padding-${g==0?"right":"left"}: ${e}px;`;return r.pageLayout.value=="A4_2"&&d%2==0&&(h+=`height:calc(100% - ${s}px);`),n.isPreview&&n.layout.column==3&&(h+=`width: ${t+e}px;`,g==1&&(h+=`width: ${t+e*2}px;`,h+=`padding-right: ${e}px;`)),h},a=g=>`padding: ${o.pagePadding[0]}px ${o.pagePadding[1]}px;
			height: ${r.pageLayout.columnWidth.height}px;`,m=Et(()=>({fontSize:o.contentFontSize+"px",lineHeight:o.contentLineHeightMMH*1})),y=n.layout.headNum;let C=r.pageLayout.columnWidth.height-(o.pagePadding[0]*2+o.pageNumberHeight+12);const u=document.createElement("div");u.classList.add("ckeditor-box"),u.classList.add("newline-preview-Nobox");const c={border:"1px solid #fff",width:`${t}px`,lineHeight:o.contentLineHeightMMH||1.5,fontSize:(o.contentFontSize||16)+"px",position:"fixed",visibility:"hidden"};(M=n.layout)!=null&&M.familysStyle&&n.layout.familysStyle["--font-ckeditor-familys"]&&(c.fontFamily=n.layout.familysStyle["--font-ckeditor-familys"]),Object.assign(u.style,c);const N=document.getElementById("targetDivs");N?N.appendChild(u):document.body.appendChild(u);function x(g,d){var p,H;d.width&&(u.style.width=d.width+"px"),d.minHeight&&(u.style.minHeight=d.minHeight),d.fontSize&&(u.style.fontSize=d.fontSize+"px"),d.lineHeight&&(u.style.lineHeight=d.lineHeight+"px"),u.innerHTML=g,((p=window==null?void 0:window.MathJax)==null?void 0:p.typesetPromise)instanceof Function&&((H=window==null?void 0:window.MathJax)==null||H.typesetPromise([u]));let h=u.getBoundingClientRect().height;return u.innerHTML="",d.width&&(u.style.width=`${t}px`),d.minHeight&&(u.style.minHeight="0px"),d.lineHeight&&(u.style.lineHeight=o.contentLineHeightMMH||1.5),d.fontSize&&(u.style.fontSize=(o.contentFontSize||16)+"px"),h<26&&(h=26),h}const v=(g,d)=>new Promise(h=>{var H,I;d.width&&(u.style.width=d.width+"px"),d.minHeight&&(u.style.minHeight=d.minHeight),d.fontSize&&(u.style.fontSize=d.fontSize+"px"),d.lineHeight&&(u.style.lineHeight=d.lineHeight+"px"),u.innerHTML=g,((H=window==null?void 0:window.MathJax)==null?void 0:H.typesetPromise)instanceof Function&&((I=window==null?void 0:window.MathJax)==null||I.typesetPromise([u]));const p=u.querySelectorAll("img");if(p.length===0)h(u.getBoundingClientRect().height),u.innerHTML="",d.width&&(u.style.width=`${t}px`),d.minHeight&&(u.style.minHeight="0px"),d.lineHeight&&(u.style.lineHeight=o.contentLineHeightMMH||1.5),d.fontSize&&(u.style.fontSize=(o.contentFontSize||16)+"px");else{let w=0;const V=p.length,k=()=>{w++,w===V&&setTimeout(()=>{h(u.getBoundingClientRect().height),u.innerHTML="",d.width&&(u.style.width=`${t}px`),d.minHeight&&(u.style.minHeight="0px"),d.lineHeight&&(u.style.lineHeight=o.contentLineHeightMMH||1.5),d.fontSize&&(u.style.fontSize=(o.contentFontSize||16)+"px")},50)};p.forEach(O=>{O.complete?k():(O.addEventListener("load",k),O.addEventListener("error",k))})}}),T=(g,d={},h={})=>new Promise(async(p,H)=>{var q,D;h.width&&(u.style.width=h.width),h.minHeight&&(u.style.minHeight=h.minHeight),h.fontSize&&(u.style.fontSize=h.fontSize),u.innerHTML=g,((q=window==null?void 0:window.MathJax)==null?void 0:q.typesetPromise)instanceof Function&&((D=window==null?void 0:window.MathJax)==null||D.typesetPromise([u]));let I=Array.from(u.childNodes);u.innerHTML='<div class="containerCalss clearfix" style="border: 1px solid #ddd;"></div>';let w=u.querySelector(".containerCalss"),V=C-d.Hcopy,k=0,O=d.pages,J=[];const W=()=>{O++,r.pageLayout.value=="A4_2"&&O%y==1||O%y==0?(k=s,V=C-s):(k=0,V=C)},Y=P=>{var X;let K=P.replace(/([^a-zA-Z])/g,"|@$1").split("|@");const Z=Ht(K,20);for(let j=0;j<Z.length;j++){let te=document.createTextNode(Z[j]);if(w.appendChild(te),w.offsetHeight>V){w.removeChild(te);let me=Z[j].replace(/([^a-zA-Z])/g,"|@$1").split("|@");for(let de=0;de<me.length;de++){if(!((X=me[de])!=null&&X.trim()))continue;let ye=document.createTextNode(me[de]);w.appendChild(ye),w.offsetHeight>V&&(W(),w.removeChild(ye),J.push({txt:w.innerHTML,h:w.offsetHeight,pages:O,headHeightNum:k}),w.innerHTML="",w.appendChild(ye))}}}};for(let P=0;P<I.length;P++){let K=I[P];if(K.nodeType===Node.ELEMENT_NODE){if(w.appendChild(K),w.offsetHeight>V){w.removeChild(K);let Z=Array.from(K.childNodes),X=Z.length;if(K.localName=="p"&&X>1)for(let j=0;j<X;j++){let te=Z[j];te.nodeType===Node.ELEMENT_NODE?(w.appendChild(te),w.offsetHeight>V&&(W(),w.removeChild(te),J.push({txt:w.innerHTML,pages:O,h:w.offsetHeight,headHeightNum:k}),w.innerHTML="",w.appendChild(te))):te.nodeType===Node.TEXT_NODE&&Y(te.textContent||"")}else W(),J.push({txt:w.innerHTML,h:w.offsetHeight,pages:O,headHeightNum:k}),w.innerHTML="",w.appendChild(K)}}else K.nodeType===Node.TEXT_NODE&&Y(K.textContent||"")}let ne=w.offsetHeight;J.push({txt:w.innerHTML,pages:d.pages,h:ne,headHeightNum:k}),w.innerHTML="",u.innerHTML="",h.width&&(u.style.width=`${t}px`),h.minHeight&&(u.style.minHeight="auto"),h.fontSize&&(u.style.fontSize=(o.contentFontSize||16)+"px");const G=/<span class="math-tex">[\s\S]*?<\/span>/g,$=g.match(G);if($){const P=/<span class="math-tex">[\s\S]*?<\/span>/g;let K=0;J.forEach(Z=>{let X=Z.txt;Z.txt=X.replace(P,function(j){return K<$.length?$[K++]:j})})}p({bodyList:J,ExceedingTheHeight:ne,headHeightNum:k})}),R=(g,d)=>{let h=d;return g==0&&r.pageLayout.value!="A4_2"||(r.pageLayout.value=="A4_2"&&(g+1)%y==1||(g+1)%y==0)&&(h+=s),h},B=(g,d)=>{let h=0,p=0;const H=I=>{p++,h++;let w=I.bodys||"";g==1?I.topicSortNum=h:I.topicSortNum=p;const V=/<span class="topicSortNum">(\d+)\.<\/span>/g,k=w.replace(V,()=>`<span class="topicSortNum">${I.topicSortNum}.</span>`);return I.bodys=k,I};return d.forEach(I=>{I.itemTypeId=="QUESTION"?I.question.isComplex&&I.question.logicTypeId!=12&&I.question.logicTypeId!=14?(I.question.detailData.children.forEach(w=>{w.detailData=H(w.detailData)}),I.question.logicTypeId==St.ReadingCloze&&I.question.detailData.body&&(I.question.detailData.body=Pt(I.question.detailData.body,I.question.detailData.children))):I.question.detailData=H(I.question.detailData):p=0}),d},f=(g=0)=>parseFloat(g.toFixed(1));function U(g,d,h,p=null){if(p=="obj"){let H=g.querySelector(h);if(H){let I=H.getBoundingClientRect();return{x:f(I.x-d.x),y:f(I.y-d.y),h:f(I.height),w:f(I.width)}}else return{}}else{let H=[];return g.querySelectorAll(h).forEach(w=>{let V=w.getBoundingClientRect();H.push({txt:w.textContent,x:f(V.x-d.x),y:f(V.y-d.y),h:f(V.height),w:f(V.width)})}),H}}const E=[];return{getHtmlTopicSortNum:g=>`<span class="topicSortNum">${g||""}.</span>`,getEnspWidth:()=>{const g=document.createElement("span");g.innerHTML="&ensp;",n.layout.familysStyle["--font-ckeditor-familys"]&&(g.style.fontFamily=n.layout.familysStyle["--font-ckeditor-familys"]),g.style.fontSize=o.contentFontSize+"px",g.style.lineHeight=o.contentLineHeightMMH,document.body.appendChild(g);let d=g.offsetWidth;const h=Math.floor((t-6)/d);Q().storeEnspNum=h,document.body.removeChild(g)},CkeditorStyles:m,shouldApplyClass:i,topicBoxItemStyle:l,topicComStyle:a,pageWidth:t,splitContentBodys:T,domFun:x,setHeight:R,headHeight:s,pageHeight:C,questionNumberingTypeIdChanges:B,getMarke:U,getZBFun:(g,d)=>{let h=0;return g.value.forEach((p,H)=>{let I={page:H+1},w=p.getBoundingClientRect();I.pageInfo={x:0,y:0,h:f(w.height),w:f(w.width)};let k=p.querySelector(".page-number").getBoundingClientRect();I.bottomInfo={x:f(k.x-w.x),y:f(k.y-w.y),h:f(k.height),w:f(k.width)};let O=p.querySelector(".page-head");if(O){let W=O.getBoundingClientRect();I.headInfo={x:f(W.x-w.x),y:f(W.y-w.y),h:f(W.height),w:f(W.width)},[{key:"examNum",calss:".exam-number"},{key:"studentInfo",calss:".student-info-inside"},{key:"title",calss:".title"},{key:"creatorInfo",calss:".total-score"}].forEach($=>{let q=p.querySelector($.calss);if(q){let D=q.getBoundingClientRect();I[$.key]={x:f(D.x-w.x),y:f(D.y-w.y),h:f(D.height),w:f(D.width)}}});let ne=p.querySelector(".info-qrcode"),G=ne.getBoundingClientRect();if(I.qrcode={text:ne.getAttribute("data-qrcodeText"),bound:{x:f(G.x-w.x),y:f(G.y-w.y),h:f(G.height),w:f(G.width)}},Q().setting.examNoTypeId=="FILL"){let $=[];p.querySelectorAll(".smear-number").forEach((D,P)=>{let K=D.querySelectorAll(".smear-item");$[P]||($[P]=[]),K.forEach(Z=>{let X=Z.getBoundingClientRect();$[P].push({x:f(X.x-w.x),y:f(X.y-w.y),h:f(X.height),w:f(X.width),digit:Z.innerText})})}),I.ID=$}else{let $=[];p.querySelectorAll(".shou-box .item-box .item-com").forEach(D=>{let P=D.getBoundingClientRect();$.push({x:f(P.x-w.x),y:f(P.y-w.y),h:f(P.height),w:f(P.width)})}),I.ID=$}}let J={};if(n.isPreview&&(r.pageLayout.value=="A4_2"||n.layout.column==2||n.layout.column==3)){let W=p.querySelectorAll(".topic-box .topic-box-item");for(let Y=0;Y<W.length;Y++)J=d(w,W[Y],h,J),h++}else J=d(w,p,H);if(n.isPreview){let W=["tl","tr","bl","br"],Y={};p.querySelectorAll("#marker-boxs").forEach((G,$)=>{let q=G.getBoundingClientRect();Y[W[$]]={x:f(q.x-w.x),y:f(q.y-w.y),h:f(q.height),w:f(q.width)}}),I.markers=Y}I.optionInfo=J.optionInfo,I.answerAreaInfo=J.answerAreaInfo,I.scoreBoxInfo=J.scoreBoxInfo,E[H]=I}),E},container:u,toFixeds:f,getContainerHeightAfterImagesLoad:v}}export{_n as _,Pn as a,qn as b,Dn as c,Fn as g,kn as q,Pt as s,zn as u};

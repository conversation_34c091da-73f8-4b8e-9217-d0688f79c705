import{d as A,r as u,at as B,aF as P,aK as n,c as I,K as x,L as D,u as r,e as F,a as T,au as b,b as k,p as R,o as m}from"./index.ZZ6UQeaF.js";import{t as V,g as M}from"./index.B5-xac-S.js";import{s as H}from"./index.CCoS0DfY.js";import{g as N}from"./index.pTwlcQaF.js";/* empty css                   */import{_ as W}from"./_plugin-vue_export-helper.DlAUqK2U.js";import"./request.CsKOOJzG.js";import"./index.xsH4HHeE.js";import"./index.6W4rOsHv.js";import"./question.IyuOoK5G.js";import"./index.vue_vue_type_style_index_0_lang.Cq6RDaj7.js";/* empty css                 *//* empty css                       *//* empty css                *//* empty css                  */import"./itemDom.vue_vue_type_script_setup_true_lang.CbGURBLf.js";/* empty css                          *//* empty css                        */const E={class:"cards-boxs",id:"targetDivs"},$={class:"main-boxs scrollBar"},j={key:0,class:"containers"},K=A({__name:"print",setup(U){function q(o){return new URLSearchParams(window.location.search).get(o)}let S=q("uuid"),t={};const l=u({}),w=u({});let e=u({headNum:4,HhyptType:"topics",familysStyle:{}});const y=u([]),L=u(null),f=u(!0);(async()=>{var s,p;const o=await M({uuid:S});if(o.code!=0)return R.error(o.msg);let a=o.data||{};t=a.cardInfo;let{printLayout:c}=t.setting.pageLayout;c.page[0]&&(t.setting.pageLayout.columnWidth.width=c.page[0],t.setting.pageLayout.columnWidth.height=c.page[1]),t.setting.contentTypeId=t.contentTypeId||1,e.value.HhyptType=l.value.contentTypeId==1?"topics":"singleCards",l.value=t.setting,l.scoringResultTypeId===void 0&&(l.scoringResultTypeId="score"),l.value.contentFontFamily&&(e.value.familysStyle={"--font-ckeditor-familys":l.value.contentFontFamily}),n().setting=t.setting,e.value.HhyptType=l.value.contentTypeId==1?"topics":"singleCards",w.value=t.partScorePlan,e.value.title=t.title,e.value.id=t.id,e.value.created_by_name=t.created_by_name||"-",e.value.createdAt=t.createdAt;let d=((p=(s=l.value.pageLayout)==null?void 0:s.printLayout)==null?void 0:p.column)||2;e.value.headNum=d*2,e.value.column=d,e.value.showCreatorInfoVal=t.showCreatorInfoVal||"",y.value=a.cardInfo.questions,L.value=a.cardInfo.cardList||a.cardInfo.cardLsit,n().questionsScoreList=a.questionsScoreList,n().scoreSetting=a.scoreSetting,n().pageStyles=t.pageStyle;const i=a.scoreSetting.reduce((g,_)=>g+(_.full_score||0),0);e.value.fullMarks=i,N(n(),!0,e.value.column),f.value=!1})(),B(()=>{window.$previewSaveFun=C}),P(()=>{delete window.$previewSaveFun});const v=u(null),h=u(null),C=()=>{let o=[],a=[];n().setting.contentTypeId==1?(o=v.value.getZB(),a=v.value.questionsArrCopy):(a=h.value.questionsArrCopy,o=h.value.getZB());function c(i){return i.reduce((s,p)=>{const g=s.find(_=>_.uuid===p.uuid);return g&&g.bounds?g.bounds=g.bounds.concat(p.bounds):s.push(p),s},[])}if(e.value.HhyptType=="topics")for(let i=0;i<o.length;i++){let s=o[i];s.optionInfo.length>0&&(o[i].optionInfo=c(s.optionInfo))}return{uuid:S,coordinateInfo:o,cardInfo:{...t,contentTypeId:n().setting.contentTypeId,title:e.value.title,showCreatorInfoVal:e.value.showCreatorInfoVal,setting:n().setting,pageStyle:n().pageStyles,questions:a,partScorePlan:w.value},scoreSetting:n().scoreSetting,questionsScoreList:n().questionsScoreList}};return(o,a)=>{var d,i,s;const c=D;return m(),I("div",E,[x((m(),I("main",$,[r(f)?F("",!0):(m(),I("div",j,[T("div",{class:"topic-boxs scrollBar",id:"family-box",style:b(r(e).familysStyle)},[T("div",{style:b(`margin: 0 auto;width: ${(s=(i=(d=r(l))==null?void 0:d.pageLayout)==null?void 0:i.columnWidth)==null?void 0:s.width}px;`)},[r(n)().setting.contentTypeId==1?(m(),k(V,{key:0,isPreview:!0,ref_key:"topicRef",ref:v,questionsArr:r(y),layout:r(e)},null,8,["questionsArr","layout"])):(m(),k(H,{key:1,ref_key:"singleCardRef",ref:h,isPreview:!0,questionsArr:r(y),layout:r(e),cardList:r(L)},null,8,["questionsArr","layout","cardList"]))],4)],4)]))])),[[c,r(f)]])])}}}),de=W(K,[["__scopeId","data-v-84625c20"]]);export{de as default};

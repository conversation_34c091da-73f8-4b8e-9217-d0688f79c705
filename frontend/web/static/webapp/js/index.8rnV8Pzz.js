import{d as Ze,r as L,W as Ge,aK as l,c as h,u as t,a as s,t as b,K,f as y,b as q,e as B,w as T,k as Ke,i as Ue,az as Xe,j as Ye,L as et,au as ve,Y as tt,p as X,J as Ae,O as ot,o as c,l as Y,h as W,U as lt,F as E,q as R,T as st,N as at,Z as ne,aL as nt,n as it,M as rt}from"./index.ZZ6UQeaF.js";/* empty css                  *//* empty css                   */import"./el-collapse-transition.l0sNRNKZ.js";/* empty css                          *//* empty css                       *//* empty css                  *//* empty css                 *//* empty css                        */import{q as _}from"./question.IyuOoK5G.js";import{t as ut,g as dt,s as pt,a as ct,d as gt,b as yt,p as ft}from"./index.B5-xac-S.js";import{a as mt,n as vt,u as _t}from"./index.6W4rOsHv.js";import{a as ht}from"./downloads.B3gkFdce.js";import{g as Oe,c as ie,s as St,a as Pe,b as Tt}from"./index.pTwlcQaF.js";import{c as Ct}from"./cardSetting.BS28GH8Z.js";import{p as It,_ as xt}from"./previewDom.vue_vue_type_style_index_0_lang.BzEnMuYY.js";import{s as bt}from"./index.CCoS0DfY.js";/* empty css                   *//* empty css                       */import{_ as wt}from"./_plugin-vue_export-helper.DlAUqK2U.js";import"./request.CsKOOJzG.js";import"./index.xsH4HHeE.js";import"./index.vue_vue_type_style_index_0_lang.Cq6RDaj7.js";/* empty css                *//* empty css                        */import"./itemDom.vue_vue_type_script_setup_true_lang.CbGURBLf.js";const Lt={key:0,class:"pmm2_flex_center",style:{"flex-direction":"column"},id:"targetDivs"},kt={style:{color:"#999"}},Vt={key:1,class:"cards-box scrollBar-C",id:"targetDivs"},Nt={class:"header-box"},qt={class:"containers pmm2_flex_between"},Dt={class:"left-box"},Mt={href:"/"},Ut=["src"],At={class:"right-box"},Ot={class:"pmm2_flex_acenter",style:{"padding-right":"20px"}},Pt={class:"main-box scrollBar"},Ht={key:0,class:"containers"},Et={class:"setup-com",style:{"margin-bottom":"20px"}},Rt={class:"title-box pmm2_flex_between"},Ft={class:"com"},$t={key:0,style:{"padding-top":"10px"}},zt={class:"com"},jt={key:1,style:{padding:"0"}},Bt={class:"com"},Jt={key:2,style:{padding:"0"}},Qt={class:"com"},Wt={class:"com"},Zt={class:"com"},Gt={class:"com"},Kt={class:"com"},Xt={key:0},Yt={key:1},eo={key:0,class:"setup-com"},to={class:"title-box pmm2_flex_between"},oo={class:"title"},lo={style:{"font-size":"14px","padding-left":"14px",color:"#252b3a"}},so={key:0,class:"tig-box"},ao={class:"pmm2_flex_acenter",style:{"flex-wrap":"wrap"}},no={class:"ul-box"},io={class:"Subtitle pmm2_flex_between"},ro=["title"],uo={class:"t2"},po={style:{"padding-right":"5px"}},co=["onClick"],go={class:"com-box"},yo={key:0},fo={class:"t1",style:{"padding-right":"5px","min-width":"20px"}},mo={key:0,class:"t1",style:{color:"#999"}},vo={key:1,class:"t1",style:{color:"#999"}},_o={key:1,style:{"flex-wrap":"wrap"},class:"pmm2_flex_acenter"},ho={class:"t1",style:{"padding-right":"5px","min-width":"20px"}},So={key:2},To={style:{"padding-right":"5px","min-width":"20px"},class:"t1"},Co=Ze({__name:"index",setup(Io){function He(o){return new URLSearchParams(window.location.search).get(o)}const re=L("");let F=He("uuid"),I={};const k=L({}),C=L([]);let v=L({fractionOpen:!0,layoutOpen:!0,headNum:4,HhyptType:"topics",familysStyle:{}});const $=L(null),P=L(null),J=L([]),ee=L(!0),ye=L(-1),Z=(o={})=>{if($.value)if(o!=null&&o.isobjectiveAnswerTypeId){Se(l().setting.objectiveAnswerTypeId);return}else{let e=JSON.parse(JSON.stringify($.value.questionsArrCopy));l().setting.objectiveAnswerTypeId!="FILL"&&(v.value.headNum==6||l().setting.pageLayout.value=="A4_2")&&e.forEach(n=>{n.itemTypeId==_.QUESTION&&n.question.logicTypeId==13&&n.question.detailData.children.forEach(a=>{a.optionLayoutId=="FOUR"&&(a.optionLayoutId="TWO",a.detailData.optionLayoutId="TWO")})}),J.value=e}if(P.value){let e=JSON.parse(JSON.stringify(P.value.quesListCopy));if(o!=null&&o.iscontentTypeId){let n=!1;for(let a=0;a<e.length;a++)e[a].type=="free"&&(e[a].freeList=e[a].freeList.filter(i=>(i.mergeFlag&&(i.mergeFlag=!1),i.type!="merge"?!0:(n=!0,!1))));ue.value=e,n&&he()}else ue.value=e;J.value=JSON.parse(JSON.stringify(P.value.questionsArrCopy))}U.value!=100&&(U.value=100,pe()),l().ckeditorUuid="",ye.value--},fe=L(null),_e=L(!1),ue=L(null),Ee=[{name:"ContainQuestion",value:1,label:"题卡合一"},{name:"OnlyAnswerArea",value:2,label:"题卡分离"}],Re=async()=>{var i,f;if(!F){re.value="非法请求";return}const o=await dt({uuid:F});if(o.code!=0)return ee.value=!1,re.value=o.msg,X.error(o.msg);ee.value=!1;let e=o.data||{};I=e.cardInfo;let n=I.setting;k.value=I.partScorePlan,n.contentFontFamily&&(v.value.familysStyle={"--font-ckeditor-familys":n.contentFontFamily}),n.scoringResultTypeId===void 0&&(n.scoringResultTypeId="score"),I.metadata.cardContentTypes||(I.metadata.cardContentTypes=Ee),n.contentTypeId=I.contentTypeId||2,I.metadata.paperCardPageLayouts||(I.metadata.paperCardPageLayoutsNo=!0,I.metadata.paperCardPageLayouts=[I.metadata.cardPageLayouts]),C.value=I.metadata,v.value.title=I.title,v.value.id=I.id,v.value.created_by_name=I.created_by_name||"-",v.value.createdAt=I.createdAt;let a=((f=(i=n.pageLayout)==null?void 0:i.printLayout)==null?void 0:f.column)||2;if(v.value.headNum=a*2,v.value.column=a,v.value.showCreatorInfoVal=I.showCreatorInfoVal||"",v.value.HhyptType=n.contentTypeId==1?"topics":"singleCards",l().setting=n,e.scoreSetting)te.value=e.cardInfo.isCheckbox,J.value=e.cardInfo.questions,ue.value=e.cardInfo.cardList,l().questionsScoreList=e.questionsScoreList,l().scoreSetting=e.scoreSetting,l().pageStyles=e.cardInfo.pageStyle;else{let g=JSON.parse(JSON.stringify(I.metadata.cardPageStyles[n.pageLayoutId]));if(console.log(g),g.contentLineHeightMM||(g.contentLineHeightMM=Math.round((g==null?void 0:g.contentLineHeight)/4.75)),!g.contentLineHeightMMH){let x=g.contentLineHeightMM;x<=5?g.contentLineHeightMMH=1.5:g.contentLineHeightMMH=(1.5+(x-5)*.3).toFixed(1)}l().pageStyles=g,he(),_e.value=!0}Oe(l(),!1,v.value.column),ee.value=!1},te=L(!1),he=()=>{let o=0,e=0,n=0,a=I.questions,i="";const f=[],g=[];let x=-1;const N=/^<p>(.*?)<\/p>$/s,A=(r,O)=>{o++,e++;const u=r.body||"";l().setting.questionNumberingTypeId==1?r.topicSortNum=e:r.topicSortNum=o,r.topicSortH1=o,r.topicSort=e,r.step_score=O.step_score,n+=O.full_score,r.H1uuid=i;const Q=u.match(N);let H="",z=`<span class="topicSortNum">${r.topicSortNum}.</span>`;return Q?H=z+Q[1]:/^<p>/.test(u)?H=u.replace(/(<p[^>]*>)/,"<p>"+z):H=z+u,r.bodys=H,f.push({uuid:O.uuid,full_score:O.full_score,H1uuid:i}),r};a.forEach((r,O)=>{var Q,H,z,ce;let u={id:r.id,uuid:r.uuid,sequence:r.sequence,itemTypeId:r.itemTypeId,isQuestion:r.isQuestion,step_score:((Q=r.question)==null?void 0:Q.step_score)||.5,full_score:((H=r.question)==null?void 0:H.full_score)||0};if(u.itemTypeId==_.H1headline)i=r.uuid,o=0,u.children=[],u.open=!0,u.visible=!1,O!=0&&(g[x].full_score=n,n=0),x++,u.chineseNum=vt(x+1),r.chineseNum=u.chineseNum,u.title=r.chineseNum+"、"+r.content,r.title=u.title,g.push(u);else if(u.itemTypeId==_.QUESTION){let D=r.question;u.uuid=D.uuid,u.questionUuid=D.uuid,u.logicTypeId=D.logicTypeId,u.logicTypeId==_.MultipleChoice&&(te.value=!0),u.isComplex=D.isComplex;let j=D.detailData;if(D!=null&&D.isComplex&&u.logicTypeId!=_.EssayCorrectError&&u.logicTypeId!=_.GrammarBlankFilling){if(u.logicTypeId==_.MultipleX2Multiple&&(r.question.detailData.optionsCount=r.question.detailData.options.length),j.body){const V=j.body.match(N);V&&(r.question.detailData.body=V[1])}let G=j.children||[];for(let V=0;V<G.length;V++){let w=G[V],S=JSON.parse(JSON.stringify(u));if(S.logicTypeIds=u.logicTypeId,!w.uuid){let se=_t();r.question.detailData.children[V].uuid=se,w.uuid=se}S.uuid=w.uuid,S.logicTypeId=Number(w.logicTypeId),S.step_score=w.step_score||.5,S.full_score=w.full_score||0,S.logicTypeId==_.MultipleChoice&&(te.value=!0),r.question.detailData.children[V].detailData=A(w.detailData,S),S.topicSortH1=o,S.topicSort=e,S.logicTypeIds==_.MultipleX2Multiple?(S.step_score=.5,w.detailData.step_score=S.step_score,S.answer=w.detailData.answer,S.optionsCount=r.question.detailData.optionsCount):[_.MultipleChoice,_.SingleChoice,_.TrueFalse].includes(S.logicTypeId)&&(S.step_score=.5,w.detailData.step_score=S.step_score,_.TrueFalse==S.logicTypeId?S.optionsCount=2:S.optionsCount=w.detailData.options.length||0,S.answer=w.detailData.answer),S.logicTypeIds==_.ReadingCloze&&(r.question.detailData.children[V].detailData.optionLayoutId||(r.question.detailData.children[V].detailData.optionLayoutId="FOUR"),r.question.detailData.children[V].optionLayoutId||(r.question.detailData.children[V].optionLayoutId="FOUR")),w.logicTypeId==_.MultipleChoice&&(S.leak_score=ie(k.value,w.full_score)),g[x].children.push(S)}D.logicTypeId==_.ReadingCloze&&j.body&&(r.question.detailData.body=St(j.body,G))}else n+=u.full_score,r.question.detailData=A(r.question.detailData,u),u.topicSortH1=o,u.topicSort=e,[_.MultipleChoice,_.SingleChoice,_.TrueFalse].includes(u.logicTypeId)&&(u.step_score=.5,r.question.detailData.step_score=u.step_score,_.TrueFalse==u.logicTypeId?u.optionsCount=2:u.optionsCount=((ce=(z=r.question.detailData)==null?void 0:z.options)==null?void 0:ce.length)||0,u.answer=r.question.detailData.answer),u.logicTypeId==_.MultipleChoice?u.leak_score=ie(k.value,u.full_score):u.logicTypeId==_.EnglishWriting?r.question.detailData.wordNum||(r.question.detailData.wordNum=0):u.logicTypeId==_.ChineseWriting&&(r.question.detailData.wordNum||(r.question.detailData.wordNum=500)),g[x].children.push(u)}J.value.push(r)}),l().questionsScoreList=f,l().scoreSetting=g};Re();const Se=o=>{if(l().ckeditorUuid="",$.value){let e=JSON.parse(JSON.stringify($.value.questionsArrCopy));if(o=="FILL")e.forEach(n=>{n.itemTypeId==_.QUESTION&&([1,2,4].includes(n.question.logicTypeId)?n.question.detailData.bodys=n.question.detailData.bodys.replace(Pe,""):[100].includes(n.question.logicTypeId)&&n.question.detailData.children.forEach(a=>{[1,2,4].includes(a.logicTypeId)&&(a.detailData.bodys=a.detailData.bodys.replace(Pe,""))}))});else{const n=a=>{const i=/[（(]\s*[）)]/,f=a.match(i);return(f==null?void 0:f.index)!==void 0&&f.index+f[0].length===a.length&&(a=a.slice(0,f.index)),`${a}${Tt}`};e.forEach(a=>{a.itemTypeId==_.QUESTION&&([1,2,4].includes(a.question.logicTypeId)?a.question.detailData.bodys=n(a.question.detailData.bodys):[100].includes(a.question.logicTypeId)?a.question.detailData.children.forEach(i=>{[1,2,4].includes(i.logicTypeId)&&(i.detailData.bodys=n(i.detailData.bodys))}):a.question.logicTypeId==13&&(v.value.headNum==6||l().setting.pageLayout.value=="A4_2")&&a.question.detailData.children.forEach(i=>{i.optionLayoutId=="FOUR"&&(i.optionLayoutId="TWO",i.detailData.optionLayoutId="TWO")}))})}J.value=e}rt(()=>{ye.value--})},Te=(o={})=>{var n,a;let e=((a=(n=l().setting.pageLayout)==null?void 0:n.printLayout)==null?void 0:a.column)||2;v.value.headNum=e*2,v.value.HhyptType=l().setting.contentTypeId==1?"topics":"singleCards",o.ispageLayoutId&&Oe(l(),!1,v.value.column),Z(o)},Fe=o=>{l().pageStyles.contentLineHeight=Math.round(o*4.75),o<=5?l().pageStyles.contentLineHeightMMH=1.5:l().pageStyles.contentLineHeightMMH=(1.5+(o-5)*.3).toFixed(1),Z()},de=()=>{let o=[],e=[];l().setting.contentTypeId==1?(e=$.value.getZB(),o=$.value.questionsArrCopy):(o=P.value.questionsArrCopy,e=P.value.getZB(),I.cardList=P.value.quesListCopy);let n={uuid:F,coordinateInfo:e,cardInfo:{...I,contentTypeId:l().setting.contentTypeId,isCheckbox:te.value,full_score:we.value,title:v.value.title,showCreatorInfoVal:v.value.showCreatorInfoVal,setting:l().setting,pageStyle:l().pageStyles,questions:o,partScorePlan:k.value},scoreSetting:l().scoreSetting,questionsScoreList:l().questionsScoreList};return Z(),n},Ce=()=>{let o=[],e=0;l().scoreSetting.forEach(a=>{e++,a.children.forEach(i=>{if(i.children)i.children.forEach(f=>{if(!f.score){f.falg=!0,o[e]||(o[e]=[a.title]);let g=l().setting.questionNumberingTypeId==2?i.topicSortH1:i.topicSort;o[e].push(g+"("+f.sort+")")}});else{if(!i.full_score){i.falg=!0,o[e]||(o[e]=[a.title]);let f=l().setting.questionNumberingTypeId==2?i.topicSortH1:i.topicSort;o[e].push(f)}if(i.logicTypeId==_.MultipleChoice&&!i.leak_score){i.falg1=!0,o[e]||(o[e]=[a.title]);let f=l().setting.questionNumberingTypeId==2?i.topicSortH1:i.topicSort;o[e].push(f)}}})}),o=o.filter(a=>a);let n=o.length;if(o.length>0){let a="";for(let i=0;i<n;i++)o[i]=Array.from(new Set(o[i])),a+=`<p>${o[i].join(",")}请设置分数</p>`;return ot.alert(a,"提示",{dangerouslyUseHTMLString:!0}),!1}return!0},Ie=async o=>{let e=o=="save"?"保存":"暂存";const n=Ae.service({lock:!0,text:e+"中..",background:"rgba(0, 0, 0, 0.7)"});let a={};if(o=="save")if(l().setting.scoringResultTypeId=="right_wrong"||Ce()){let i=de();a=await pt({uuid:F,data:i})}else{n.close();return}else{let i=de();a=await ct({uuid:F,data:i})}if(n.close(),a.code==0)X({message:e+"成功",type:"success",plain:!0});else return X.error(e+"失败")},xe=async o=>{var i;const e=Ae.service({lock:!0,text:"下载中..",background:"rgba(0, 0, 0, 0.7)"});let n={},a=!0;if(o==1){if(l().setting.scoringResultTypeId=="score"&&(a=Ce()),a){let f=de();n=await gt({uuid:F,data:f})}}else n=await yt({uuid:F});if(a)try{if(n.code&&n.code!==0)return e.close(),X.error(n.msg);let f=v.value.title+"-"+I.courseName;o==2&&(f+="（答案）"),(i=n==null?void 0:n.data)!=null&&i.url&&ht(n.data.url,f+".pdf")}catch{}setTimeout(()=>{if(n.msg)return X.error(n.msg);e.close()},1e3)},me=L(null),oe=L(!1),$e=async()=>{oe.value=!0,me.value.dialogVisible=!0;let o=de(),e=await ft({uuid:F,data:o});if(e.code==0)me.value.show(e.data),oe.value=!1;else return X.error(e.msg)},ze=o=>{P.value.objectiveQuestionMerging(o)},je=o=>{l().setting.contentTypeId==1?$.value.questionNumberingTypeIdChange(o):P.value.questionNumberingTypeIdChange(o)},Be=o=>{be()},Je=o=>{o==1?k.value.value=0:k.value.value=50,be()},be=()=>{for(let o=0;o<l().scoreSetting.length;o++)l().scoreSetting[o].children.forEach(e=>{e.leak_score=ie(k.value,e.full_score)})},le=(o,e)=>{const n=l().questionsScoreList,a=(f,g)=>{const x=n.findIndex(N=>N.uuid===f);if(x!==-1){const N=n[x];N.full_score=N.children?N.children.reduce((A,r)=>A+(r.score||0),0):g}};e.uuid&&a(e.uuid,e.full_score),e.logicTypeId==_.MultipleChoice&&(e.leak_score=ie(k.value,e.full_score));const i=o.children.reduce((f,g)=>(e==="all"&&a(g.uuid,g.full_score),g.children&&(g.full_score=g.children.reduce((x,N)=>x+N.score,0)),f+g.full_score),0);if(e.uuid&&e.children){let f=l().questionsScoreList.findIndex(g=>g.uuid==e.uuid);l().questionsScoreList[f].children=e.children,l().questionsScoreList[f].full_score=e.children.reduce((g,x)=>g+x.score,0)}o.full_score=i};let we=Ge(()=>{let o=l().scoreSetting.reduce((e,n)=>e+(n.full_score||0),0);return v.value.fullMarks=o,o}),Qe=o=>{let e=o.children.filter(a=>!a.mergeFlag).length,n=o.full_score/e;n%.5!=0?(o.visible=!0,le(o,"all"),setTimeout(()=>{o.visible=!1},2e3)):o.children.forEach(a=>{if(!a.mergeFlag){a.full_score=n,a.logicTypeId==_.MultipleChoice&&(a.leak_score=ie(k.value,a.full_score));let i=l().questionsScoreList.findIndex(f=>f.uuid==a.uuid);i!=-1&&(l().questionsScoreList[i].full_score=a.full_score)}})};const U=L(100),Le=L(null),pe=o=>{U.value<=0||(o=="+"?U.value+=10:o=="-"&&(U.value-=10,U.value==0&&(U.value=10)),Le.value.style.transform=`scale(${U.value/100})`)};return(o,e)=>{var D,j,G,V,w,S,se,ke,Ve,Ne,qe,De;const n=Y("ZoomOut"),a=Ke,i=Xe,f=Y("ZoomIn"),g=Ye,x=Y("ArrowDown"),N=Y("ArrowUp"),A=st,r=lt,O=at,u=tt,Q=nt,H=Y("CaretBottom"),z=Y("CaretTop"),ce=et;return t(re)?(c(),h("div",Lt,[e[28]||(e[28]=s("img",{style:{width:"200px"},src:"https://oss.hhypt.com/resources/mini-parent-helper/err.png"},null,-1)),s("span",kt,b(t(re)),1)])):(c(),h("div",Vt,[s("header",Nt,[s("div",qt,[s("div",Dt,[s("a",Mt,[s("img",{src:t(mt)(),alt:"Logo",width:"218",height:"38"},null,8,Ut)])]),e[35]||(e[35]=s("div",{class:"title",style:{flex:"1","text-align":"center"}},"制作答题卡",-1)),s("div",At,[s("div",Ot,[y(a,{class:"huiA",color:"#828282",size:"18",onClick:e[0]||(e[0]=d=>pe("-"))},{default:T(()=>[y(n)]),_:1}),y(i,{onChange:pe,min:0,controls:!1,modelValue:t(U),"onUpdate:modelValue":e[1]||(e[1]=d=>Ue(U)?U.value=d:null),step:10,"step-strictly":"",style:{width:"60px",margin:"0 8px"}},null,8,["modelValue"]),y(a,{class:"huiA",color:"#828282",size:"18",onClick:e[2]||(e[2]=d=>pe("+"))},{default:T(()=>[y(f)]),_:1})]),y(g,{onClick:e[3]||(e[3]=d=>$e())},{default:T(()=>e[29]||(e[29]=[W("预览")])),_:1,__:[29]}),y(g,{onClick:e[4]||(e[4]=d=>Ie("staging"))},{default:T(()=>e[30]||(e[30]=[W("暂存")])),_:1,__:[30]}),y(g,{onClick:e[5]||(e[5]=d=>Ie("save")),type:"primary"},{default:T(()=>e[31]||(e[31]=[W("保存")])),_:1,__:[31]}),y(g,{onClick:e[6]||(e[6]=d=>xe(1)),type:"primary"},{default:T(()=>e[32]||(e[32]=[W("下载题卡")])),_:1,__:[32]}),y(g,{onClick:e[7]||(e[7]=d=>xe(2)),type:"primary"},{default:T(()=>e[33]||(e[33]=[W("下载答案")])),_:1,__:[33]}),B("",!0)])])]),K((c(),h("main",Pt,[t(ee)?B("",!0):(c(),h("div",Ht,[(c(),h("div",{class:"topic-box scrollBar-C",id:"family-box",key:t(ye),style:ve(t(v).familysStyle)},[s("div",{ref_key:"topicBoxs",ref:Le,style:ve(`transform-origin: top left;transition: transform 0.2s; transform: translate(0, 0);margin: 0 auto;
					 width: ${(G=(j=(D=t(l)().setting)==null?void 0:D.pageLayout)==null?void 0:j.columnWidth)==null?void 0:G.width}px;
					 min-width: ${(S=(w=(V=t(l)().setting)==null?void 0:V.pageLayout)==null?void 0:w.columnWidth)==null?void 0:S.width}px;
					 max-width: ${(Ve=(ke=(se=t(l)().setting)==null?void 0:se.pageLayout)==null?void 0:ke.columnWidth)==null?void 0:Ve.width}px;
					`)},[t(l)().setting.contentTypeId==1?(c(),q(ut,{key:0,ref_key:"topicRef",ref:$,questionsArr:t(J),layout:t(v)},null,8,["questionsArr","layout"])):(c(),q(bt,{key:1,cardList:t(ue),ref_key:"singleCardRef",ref:P,questionsArr:t(J),layout:t(v)},null,8,["cardList","questionsArr","layout"]))],4)],4)),s("div",{class:"setup-box scrollBar",style:ve(`width: calc(100% - ${((De=(qe=(Ne=t(l)().setting)==null?void 0:Ne.pageLayout)==null?void 0:qe.columnWidth)==null?void 0:De.width)+4}px)`)},[s("div",Et,[s("div",Rt,[e[36]||(e[36]=s("div",{class:"title"},[s("span",{class:"bt"},"版面设计")],-1)),s("div",{class:"collapse pmm2_flex_acenter huiA2",onClick:e[9]||(e[9]=d=>t(v).layoutOpen=!t(v).layoutOpen)},[s("span",null,b(t(v).layoutOpen?"收起":"展开"),1),y(a,{size:"14"},{default:T(()=>[t(v).layoutOpen?(c(),q(x,{key:0})):(c(),q(N,{key:1}))]),_:1})])]),y(u,null,{default:T(()=>{var d,ge,ae,m;return[K(s("div",null,[s("ul",null,[s("li",null,[e[37]||(e[37]=s("div",{class:"name"},"考号识别",-1)),s("div",Ft,[y(r,{onChange:e[10]||(e[10]=p=>Z()),modelValue:t(l)().setting.examNoTypeId,"onUpdate:modelValue":e[11]||(e[11]=p=>t(l)().setting.examNoTypeId=p)},{default:T(()=>[(c(!0),h(E,null,R(t(C).examNoTypes,p=>(c(),q(A,{value:p.value},{default:T(()=>[s("div",null,b(p.label),1)]),_:2},1032,["value"]))),256))]),_:1},8,["modelValue"])])]),((d=t(C).scoringResultTypes)==null?void 0:d.length)>0?(c(),h("li",$t,[e[38]||(e[38]=s("div",{class:"name"},"计分方式",-1)),s("div",zt,[y(r,{modelValue:t(l)().setting.scoringResultTypeId,"onUpdate:modelValue":e[12]||(e[12]=p=>t(l)().setting.scoringResultTypeId=p)},{default:T(()=>[(c(!0),h(E,null,R(t(C).scoringResultTypes,p=>(c(),q(A,{value:p.value},{default:T(()=>[s("div",null,b(p.label),1)]),_:2},1032,["value"]))),256))]),_:1},8,["modelValue"])])])):B("",!0),t(l)().setting.contentTypeId==1&&((ge=t(C).objectiveAnswerTypes)==null?void 0:ge.length)>1?(c(),h("li",jt,[e[39]||(e[39]=s("div",{class:"name"},"客观题识别",-1)),s("div",Bt,[y(r,{onChange:Se,modelValue:t(l)().setting.objectiveAnswerTypeId,"onUpdate:modelValue":e[13]||(e[13]=p=>t(l)().setting.objectiveAnswerTypeId=p)},{default:T(()=>[(c(!0),h(E,null,R(t(C).objectiveAnswerTypes,p=>(c(),q(A,{value:p.value},{default:T(()=>[s("div",null,b(p.label),1)]),_:2},1032,["value"]))),256))]),_:1},8,["modelValue"])])])):B("",!0),t(l)().setting.scoringResultTypeId=="score"&&((ae=t(C).subjectiveScoreTypes)==null?void 0:ae.length)>1?(c(),h("li",Jt,[e[40]||(e[40]=s("div",{class:"name"},"主观题识别",-1)),s("div",Qt,[y(r,{onChange:e[14]||(e[14]=p=>Z()),modelValue:t(l)().setting.subjectiveScoreTypeId,"onUpdate:modelValue":e[15]||(e[15]=p=>t(l)().setting.subjectiveScoreTypeId=p)},{default:T(()=>[(c(!0),h(E,null,R(t(C).subjectiveScoreTypes,p=>(c(),q(A,{value:p.value},{default:T(()=>[s("div",null,b(p.label),1)]),_:2},1032,["value"]))),256))]),_:1},8,["modelValue"])])])):B("",!0),s("li",null,[e[41]||(e[41]=s("div",{class:"name"},"页面布局",-1)),s("div",Wt,[s("span",null,b((m=t(l)().setting.pageLayout)==null?void 0:m.label),1),s("span",{class:"huiA",style:{"padding-left":"5px"},onClick:e[16]||(e[16]=(...p)=>t(fe).opens&&t(fe).opens(...p))},"修改")])]),s("li",null,[e[42]||(e[42]=s("div",{class:"name"},"内容字号",-1)),s("div",Zt,[y(i,{modelValue:t(l)().pageStyles.contentFontSize,"onUpdate:modelValue":e[17]||(e[17]=p=>t(l)().pageStyles.contentFontSize=p),min:14,max:28,style:{width:"80px"},size:"small","controls-position":"right",onChange:Z},null,8,["modelValue"])])]),s("li",null,[e[44]||(e[44]=s("div",{class:"name"},"行距",-1)),s("div",Gt,[y(i,{onChange:Fe,modelValue:t(l)().pageStyles.contentLineHeightMM,"onUpdate:modelValue":e[18]||(e[18]=p=>t(l)().pageStyles.contentLineHeightMM=p),min:4,max:20,"controls-position":"right",style:{width:"80px"},size:"small"},null,8,["modelValue"]),e[43]||(e[43]=W(" mm "))])]),s("li",null,[e[45]||(e[45]=s("div",{class:"name"},"题号排序",-1)),s("div",Kt,[y(r,{class:"group",modelValue:t(l)().setting.questionNumberingTypeId,"onUpdate:modelValue":e[19]||(e[19]=p=>t(l)().setting.questionNumberingTypeId=p),onChange:je},{default:T(()=>[(c(!0),h(E,null,R(t(C).questionNumberingTypes,p=>(c(),q(A,{value:p.value},{default:T(()=>[s("div",null,b(p.label),1)]),_:2},1032,["value"]))),256))]),_:1},8,["modelValue"])])])]),t(l)().setting.contentTypeId==2?(c(),h("div",Xt,[y(O,{modelValue:t(l)().pageStyles.showCreatorInfo,"onUpdate:modelValue":e[20]||(e[20]=p=>t(l)().pageStyles.showCreatorInfo=p),label:"显示制卡信息"},null,8,["modelValue"]),y(O,{onChange:ze,modelValue:t(l)().pageStyles.objectiveQuestionMerging,"onUpdate:modelValue":e[21]||(e[21]=p=>t(l)().pageStyles.objectiveQuestionMerging=p),label:"客观题合并"},null,8,["modelValue"]),y(O,{modelValue:t(l)().pageStyles.DashedLine,"onUpdate:modelValue":e[22]||(e[22]=p=>t(l)().pageStyles.DashedLine=p),label:"虚线"},null,8,["modelValue"])])):(c(),h("div",Yt,[y(O,{modelValue:t(l)().pageStyles.showCreatorInfo,"onUpdate:modelValue":e[23]||(e[23]=p=>t(l)().pageStyles.showCreatorInfo=p),label:"显示制卡信息"},null,8,["modelValue"])]))],512),[[ne,t(v).layoutOpen]])]}),_:1})]),t(l)().setting.scoringResultTypeId=="score"?(c(),h("div",eo,[s("div",to,[s("div",oo,[e[46]||(e[46]=s("span",{class:"bt"},"分数设置",-1)),s("span",lo,"满分："+b(t(we))+"分",1)]),s("div",{class:"collapse pmm2_flex_acenter huiA2",onClick:e[24]||(e[24]=d=>t(v).fractionOpen=!t(v).fractionOpen)},[s("span",null,b(t(v).fractionOpen?"收起":"展开"),1),y(a,{size:"14"},{default:T(()=>[t(v).fractionOpen?(c(),q(x,{key:0})):(c(),q(N,{key:1}))]),_:1})])]),y(u,null,{default:T(()=>[K(s("div",null,[t(te)?(c(),h("div",so,[e[47]||(e[47]=s("div",{class:"tig"}," *多选题赋分规则 ： ",-1)),s("div",ao,[y(r,{class:"group",modelValue:t(k).type,"onUpdate:modelValue":e[25]||(e[25]=d=>t(k).type=d),onChange:Je},{default:T(()=>[(c(!0),h(E,null,R(t(C).partScoreTypes,d=>(c(),q(A,{key:d.value,value:d.value},{default:T(()=>[W(b(d.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"]),y(i,{class:"num-box num-boxs",onChange:Be,controls:!1,modelValue:t(k).value,"onUpdate:modelValue":e[26]||(e[26]=d=>t(k).value=d),max:100,min:0,style:{width:"40px"},size:"small","controls-position":"right"},null,8,["modelValue"]),s("span",null,b(t(k).type==1?"分":"%"),1)])])):B("",!0),s("ul",no,[(c(!0),h(E,null,R(t(l)().scoreSetting,(d,ge)=>{var ae;return c(),h("li",{key:ge},[s("div",io,[s("div",{class:"t1 pmm2_ellipsis",title:d.title,style:{"max-width":"95px",width:"initial"}},b(d.title),9,ro),s("span",uo,"(共"+b((ae=d==null?void 0:d.children)==null?void 0:ae.filter(m=>m.type!="merge").length)+"小题)",1),s("div",po,[e[48]||(e[48]=s("span",{class:"t3"},"总分",-1)),y(i,{class:"num-box",modelValue:d.full_score,"onUpdate:modelValue":m=>d.full_score=m,min:0,style:{width:"60px"},size:"small","controls-position":"right"},null,8,["modelValue","onUpdate:modelValue"]),y(Q,{placement:"top",visible:d.visible,width:200,content:"分值输入不合理,请重新输入"},{reference:T(()=>[s("span",{class:"t4 huiA",onClick:m=>t(Qe)(d)},"平均分配",8,co)]),_:2},1032,["visible"]),y(a,{class:"icon",onClick:m=>d.open=!d.open},{default:T(()=>[K(y(H,null,null,512),[[ne,d.open]]),K(y(z,null,null,512),[[ne,!d.open]])]),_:2},1032,["onClick"])])]),y(u,null,{default:T(()=>[K(s("div",null,[s("div",go,[(c(!0),h(E,null,R(d.children,(m,p)=>(c(),h("div",{class:it(["item-box",{"com-checkbox-box":m.logicTypeId==t(_).MultipleChoice,"com-merge-on1":t(l)().setting.contentTypeId!=2&&m.type=="merge","com-merge-on":m.mergeFlag&&t(l)().setting.contentTypeId==2}]),key:p},[m.logicTypeId==t(_).MultipleChoice?(c(),h("div",yo,[s("span",fo,b(t(l)().setting.questionNumberingTypeId==2?m.topicSortH1:m.topicSort)+".",1),y(i,{class:"num-box",modelValue:m.full_score,"onUpdate:modelValue":M=>m.full_score=M,min:0,step:parseFloat(m.step_score),controls:!1,"step-strictly":!0,style:{width:"55px"},size:"small",onChange:M=>le(d,m)},null,8,["modelValue","onUpdate:modelValue","step","onChange"]),e[49]||(e[49]=s("span",{class:"t2"},"分",-1)),t(k).type==2?(c(),h("span",mo," 漏选得 ")):(c(),h("span",vo," 每选对一个得 ")),y(i,{class:"num-box",modelValue:m.leak_score,"onUpdate:modelValue":M=>m.leak_score=M,min:0,step:.5,"step-strictly":!0,max:m.full_score||0,style:{width:"55px"},size:"small",controls:!1,onChange:M=>le(d,"leak")},null,8,["modelValue","onUpdate:modelValue","max","onChange"]),e[50]||(e[50]=s("span",{class:"t2"},"分",-1))])):m.children?(c(),h("div",_o,[(c(!0),h(E,null,R(m.children,(M,We)=>(c(),h("div",{key:We},[s("span",ho,b(t(l)().setting.questionNumberingTypeId==2?m.topicSortH1:m.topicSort)+"("+b(M.sort)+").",1),y(i,{class:"num-box",modelValue:M.score,"onUpdate:modelValue":Me=>M.score=Me,min:0,step:parseFloat(m.step_score),"step-strictly":!0,style:{width:"55px"},size:"small",controls:!1,onChange:Me=>le(d,m)},null,8,["modelValue","onUpdate:modelValue","step","onChange"]),e[51]||(e[51]=s("span",{class:"t2"},"分",-1))]))),128))])):(c(),h("div",So,[s("span",To,b(t(l)().setting.questionNumberingTypeId==2?m.topicSortH1:m.topicSort)+".",1),y(i,{class:"num-box",modelValue:m.full_score,"onUpdate:modelValue":M=>m.full_score=M,min:0,step:parseFloat(m.step_score),"step-strictly":!0,style:{width:"55px"},size:"small",controls:!1,onChange:M=>le(d,m)},null,8,["modelValue","onUpdate:modelValue","step","onChange"]),e[52]||(e[52]=s("span",{class:"t2"},"分",-1))]))],2))),128))])],512),[[ne,d.open]])]),_:2},1024)])}),128))])],512),[[ne,t(v).fractionOpen]])]),_:1})])):B("",!0)],4)]))])),[[ce,t(ee)]]),y(It,{paperCardPageLayoutsNo:t(C).paperCardPageLayoutsNo,paperCardPageLayouts:t(C).paperCardPageLayouts,answerSheetPageLayouts:t(C).answerSheetPageLayouts,ref_key:"papeSizeRef",ref:fe,onSetCardSetting:Te,cardPageStyles:t(C).cardPageStyles,cardContentTypes:t(C).cardContentTypes},null,8,["paperCardPageLayoutsNo","paperCardPageLayouts","answerSheetPageLayouts","cardPageStyles","cardContentTypes"]),t(_e)?(c(),q(Ct,{key:0,objectiveAnswerTypes:t(C).objectiveAnswerTypes,scoringResultTypes:t(C).scoringResultTypes,subjectiveScoreTypes:t(C).subjectiveScoreTypes,paperCardPageLayoutsNo:t(C).paperCardPageLayoutsNo,paperCardPageLayouts:t(C).paperCardPageLayouts,answerSheetPageLayouts:t(C).answerSheetPageLayouts,cardPageStyles:t(C).cardPageStyles,examNoTypes:t(C).examNoTypes,onSetCardSetting:Te,cardContentTypes:t(C).cardContentTypes},null,8,["objectiveAnswerTypes","scoringResultTypes","subjectiveScoreTypes","paperCardPageLayoutsNo","paperCardPageLayouts","answerSheetPageLayouts","cardPageStyles","examNoTypes","cardContentTypes"])):B("",!0),y(xt,{ref_key:"previewDomRef",ref:me,modelValue:t(oe),"onUpdate:modelValue":e[27]||(e[27]=d=>Ue(oe)?oe.value=d:null)},null,8,["modelValue"])]))}}}),Go=wt(Co,[["__scopeId","data-v-78f8d16b"]]);export{Go as default};

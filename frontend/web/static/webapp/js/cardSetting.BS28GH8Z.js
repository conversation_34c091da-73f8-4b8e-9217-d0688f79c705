import{d as F,r as I,aK as S,b as f,E as B,u as a,i as J,w as s,o as n,a as t,c as u,e as j,f as p,F as v,q as g,T as D,t as b,U as q,C as G,az as K,h as $,j as Q,n as W}from"./index.ZZ6UQeaF.js";/* empty css                *//* empty css                  *//* empty css                 *//* empty css                        *//* empty css                        *//* empty css                       */import{_ as X}from"./_plugin-vue_export-helper.DlAUqK2U.js";const Y={class:"pmm2_flex_between"},Z={class:"cardSetting-box scrollBar-C1"},ee={key:0},te={class:"com"},le={class:"com"},oe={key:1},ae={class:"com"},ne={key:2},se={class:"com"},ie={key:3},ue={class:"com"},de={style:{"align-items":"inherit"}},re={class:"com"},pe=["onClick"],ce={class:"com"},me={style:{"padding-top":"15px"}},ve={class:"com"},ge={class:"pmm2_flex_between"},ye={class:"dialog-footer"},fe=F({__name:"cardSetting",props:{paperCardPageLayoutsNo:Boolean,paperCardPageLayouts:Object,answerSheetPageLayouts:Object,cardContentTypes:Object,cardPageStyles:Object,examNoTypes:Object,objectiveAnswerTypes:Object,subjectiveScoreTypes:Object,scoringResultTypes:Object},emits:["setCardSetting"],setup(c,{emit:M}){const x=I(!0),_=c,L=I(_.paperCardPageLayouts),o=I({}),d=I({});o.value=JSON.parse(JSON.stringify(S().setting)),d.value=JSON.parse(JSON.stringify(S().pageStyles));const N=i=>{d.value.contentLineHeight=Math.round(i*4.75),i<=5?d.value.contentLineHeightMMH=1.5:d.value.contentLineHeightMMH=(1.5+(i-5)*.3).toFixed(1)},h=i=>{i.disabled||(o.value.pageLayoutId=i.value)},H=i=>{i==2?(L.value=_.answerSheetPageLayouts,o.value.pageLayoutId=="A4_2"&&(o.value.pageLayoutId=L.value[0][0].value)):L.value=_.paperCardPageLayouts},k=M;function O(i,e){for(const m of i)for(const r of m)if(r.value===e)return r;return{}}const z=()=>{const i=o.value.pageLayoutId;o.value.examNoType=_.examNoTypes.find(r=>r.value==o.value.examNoTypeId),o.value.pageLayout=O(L.value,i);let e={..._.cardPageStyles[i],contentLineHeightMMH:d.value.contentLineHeightMMH,contentLineHeight:d.value.contentLineHeight,contentLineHeightMM:d.value.contentLineHeightMM,contentFontSize:d.value.contentFontSize};S().pageStyles=e,S().setting=o.value;let m={ispageLayoutId:!0,isobjectiveAnswerTypeId:o.value.objectiveAnswerTypeId!="FILL"};k("setCardSetting",m),x.value=!1};return(i,e)=>{const m=D,r=q,P=G,C=K,U=Q,A=B;return n(),f(A,{"append-to-body":"",class:"hhypt-dialog-boxs cardSetting-dialog-boxs","show-close":!1,modelValue:a(x),"onUpdate:modelValue":e[8]||(e[8]=T=>J(x)?x.value=T:null),title:"题卡设置",width:"800","close-on-click-modal":!1,"close-on-press-escape":!1},{header:s(({close:T,titleClass:V})=>[t("div",Y,[t("span",{class:W(V)},"题卡设置",2)])]),footer:s(()=>[t("div",ge,[e[19]||(e[19]=t("div",null,null,-1)),t("div",ye,[p(U,{style:{width:"70px"},type:"primary",onClick:z},{default:s(()=>e[18]||(e[18]=[$(" 确定 ")])),_:1,__:[18]})])])]),default:s(()=>{var T,V,w;return[t("div",Z,[t("ul",null,[c.paperCardPageLayoutsNo?j("",!0):(n(),u("li",ee,[e[9]||(e[9]=t("div",{class:"name"}," 版面设计 ",-1)),t("div",te,[p(r,{modelValue:a(o).contentTypeId,"onUpdate:modelValue":e[0]||(e[0]=l=>a(o).contentTypeId=l),onChange:H},{default:s(()=>[(n(!0),u(v,null,g(c.cardContentTypes,l=>(n(),f(m,{value:l.value,size:"large"},{default:s(()=>[t("div",null,b(l.label),1)]),_:2},1032,["value"]))),256))]),_:1},8,["modelValue"])])])),t("li",null,[e[10]||(e[10]=t("div",{class:"name"}," 考号识别 ",-1)),t("div",le,[p(r,{modelValue:a(o).examNoTypeId,"onUpdate:modelValue":e[1]||(e[1]=l=>a(o).examNoTypeId=l)},{default:s(()=>[(n(!0),u(v,null,g(c.examNoTypes,l=>(n(),f(m,{value:l.value,size:"large"},{default:s(()=>[t("div",null,b(l.label),1)]),_:2},1032,["value"]))),256))]),_:1},8,["modelValue"])])]),((T=c.scoringResultTypes)==null?void 0:T.length)>0?(n(),u("li",oe,[e[11]||(e[11]=t("div",{class:"name"},"计分方式",-1)),t("div",ae,[p(r,{modelValue:a(o).scoringResultTypeId,"onUpdate:modelValue":e[2]||(e[2]=l=>a(o).scoringResultTypeId=l)},{default:s(()=>[(n(!0),u(v,null,g(c.scoringResultTypes,l=>(n(),f(m,{value:l.value},{default:s(()=>[t("div",null,b(l.label),1)]),_:2},1032,["value"]))),256))]),_:1},8,["modelValue"])])])):j("",!0),a(o).contentTypeId==1&&((V=c.objectiveAnswerTypes)==null?void 0:V.length)>1?(n(),u("li",ne,[e[12]||(e[12]=t("div",{class:"name"}," 客观题识别 ",-1)),t("div",se,[p(r,{modelValue:a(o).objectiveAnswerTypeId,"onUpdate:modelValue":e[3]||(e[3]=l=>a(o).objectiveAnswerTypeId=l)},{default:s(()=>[(n(!0),u(v,null,g(c.objectiveAnswerTypes,l=>(n(),f(m,{value:l.value,size:"large"},{default:s(()=>[t("div",null,b(l.label),1)]),_:2},1032,["value"]))),256))]),_:1},8,["modelValue"])])])):j("",!0),a(o).scoringResultTypeId=="score"&&((w=c.subjectiveScoreTypes)==null?void 0:w.length)>1?(n(),u("li",ie,[e[13]||(e[13]=t("div",{class:"name"}," 主观题识别 ",-1)),t("div",ue,[p(r,{modelValue:a(o).subjectiveScoreTypeId,"onUpdate:modelValue":e[4]||(e[4]=l=>a(o).subjectiveScoreTypeId=l)},{default:s(()=>[(n(!0),u(v,null,g(c.subjectiveScoreTypes,l=>(n(),f(m,{value:l.value,size:"large"},{default:s(()=>[t("div",null,b(l.label),1)]),_:2},1032,["value"]))),256))]),_:1},8,["modelValue"])])])):j("",!0),t("li",de,[e[14]||(e[14]=t("div",{class:"name",style:{"padding-top":"15px"}}," 页面布局 ",-1)),t("div",re,[p(r,{modelValue:a(o).pageLayoutId,"onUpdate:modelValue":e[5]||(e[5]=l=>a(o).pageLayoutId=l),class:"radio-boxs pmm2_flex"},{default:s(()=>[(n(!0),u(v,null,g(a(L),(l,R)=>(n(),u("div",{key:R,class:"pmm2_flex_center"},[(n(!0),u(v,null,g(l,(y,E)=>(n(),u("div",{key:E,onClick:be=>h(y),class:"pmm2_flex_center huiA",style:{"flex-direction":"column","margin-right":"20px"}},[p(P,{src:a(o).pageLayoutId==y.value?y.icons.on:y.icons.off,fit:"fill"},null,8,["src"]),p(m,{value:y.value},{default:s(()=>[t("div",null,b(y.label),1)]),_:2},1032,["value"])],8,pe))),128))]))),128))]),_:1},8,["modelValue"])])]),t("li",null,[e[15]||(e[15]=t("div",{class:"name"}," 内容字号 ",-1)),t("div",ce,[p(C,{modelValue:a(d).contentFontSize,"onUpdate:modelValue":e[6]||(e[6]=l=>a(d).contentFontSize=l),style:{width:"100px"},"controls-position":"right",min:12,max:28},null,8,["modelValue"])])]),t("li",me,[e[17]||(e[17]=t("div",{class:"name"}," 行距 ",-1)),t("div",ve,[p(C,{onChange:N,modelValue:a(d).contentLineHeightMM,"onUpdate:modelValue":e[7]||(e[7]=l=>a(d).contentLineHeightMM=l),min:4,max:20,controls:!1,style:{width:"100px"}},{suffix:s(()=>e[16]||(e[16]=[t("span",null,"mm",-1)])),_:1},8,["modelValue"])])])])])]}),_:1},8,["modelValue"])}}}),Ce=X(fe,[["__scopeId","data-v-797e8658"]]);export{Ce as c};

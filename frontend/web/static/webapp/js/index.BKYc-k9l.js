import{aD as lt,as as ot,r as N,aE as at,d as Ke,f as p,h as Z,W as rt,at as st,aF as it,c as j,a as m,b as ue,e as re,w as x,j as ct,F as se,q as ie,u as d,X as Pe,i as me,g as ut,v as dt,G as pt,k as ft,t as R,aG as mt,az as vt,aA as ht,p as W,o as b,l as te,K as De,n as gt,aH as yt,R as bt,C as _t,s as kt,M as wt,aC as Ct,O as Ie,aI as xt}from"./index.ZZ6UQeaF.js";/* empty css                        *//* empty css                 *//* empty css                        */import"./el-tooltip.l0sNRNKZ.js";/* empty css                  *//* empty css                    *//* empty css                     *//* empty css                  *//* empty css                  */import{s as le}from"./requestAi.2QjPPNcI.js";import{t as Nt}from"./index.6W4rOsHv.js";/* empty css                   *//* empty css                       */import{_ as St}from"./_plugin-vue_export-helper.DlAUqK2U.js";import"./index.xsH4HHeE.js";var ve={d:(e,t)=>{for(var l in t)ve.o(t,l)&&!ve.o(e,l)&&Object.defineProperty(e,l,{enumerable:!0,get:t[l]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t)},He={};function Ce(e,t){(t==null||t>e.length)&&(t=e.length);for(var l=0,i=new Array(t);l<t;l++)i[l]=e[l];return i}function Ue(e,t){if(e){if(typeof e=="string")return Ce(e,t);var l=Object.prototype.toString.call(e).slice(8,-1);return l==="Object"&&e.constructor&&(l=e.constructor.name),l==="Map"||l==="Set"?Array.from(e):l==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(l)?Ce(e,t):void 0}}function he(e){return function(t){if(Array.isArray(t))return Ce(t)}(e)||function(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}(e)||Ue(e)||function(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}()}function ge(e,t,l){return t in e?Object.defineProperty(e,t,{value:l,enumerable:!0,configurable:!0,writable:!0}):e[t]=l,e}ve.d(He,{Z:()=>Lt});const r=(Ae={computed:()=>rt,createTextVNode:()=>Z,createVNode:()=>p,defineComponent:()=>Ke,reactive:()=>at,ref:()=>N,watch:()=>ot,watchEffect:()=>lt},we={},ve.d(we,Ae),we),Vt=(0,r.defineComponent)({props:{data:{required:!0,type:String},onClick:Function},render:function(){var e=this.data,t=this.onClick;return(0,r.createVNode)("span",{class:"vjs-tree-brackets",onClick:t},[e])}}),jt=(0,r.defineComponent)({emits:["change","update:modelValue"],props:{checked:{type:Boolean,default:!1},isMultiple:Boolean,onChange:Function},setup:function(e,t){var l=t.emit;return{uiType:(0,r.computed)(function(){return e.isMultiple?"checkbox":"radio"}),model:(0,r.computed)({get:function(){return e.checked},set:function(i){return l("update:modelValue",i)}})}},render:function(){var e=this.uiType,t=this.model,l=this.$emit;return(0,r.createVNode)("label",{class:["vjs-check-controller",t?"is-checked":""],onClick:function(i){return i.stopPropagation()}},[(0,r.createVNode)("span",{class:"vjs-check-controller-inner is-".concat(e)},null),(0,r.createVNode)("input",{checked:t,class:"vjs-check-controller-original is-".concat(e),type:e,onChange:function(){return l("change",t)}},null)])}}),Ot=(0,r.defineComponent)({props:{nodeType:{required:!0,type:String},onClick:Function},render:function(){var e=this.nodeType,t=this.onClick,l=e==="objectStart"||e==="arrayStart";return l||e==="objectCollapsed"||e==="arrayCollapsed"?(0,r.createVNode)("span",{class:"vjs-carets vjs-carets-".concat(l?"open":"close"),onClick:t},[(0,r.createVNode)("svg",{viewBox:"0 0 1024 1024",focusable:"false","data-icon":"caret-down",width:"1em",height:"1em",fill:"currentColor","aria-hidden":"true"},[(0,r.createVNode)("path",{d:"M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"},null)])]):null}});var Ae,we;function xe(e){return xe=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},xe(e)}function Re(e){return Object.prototype.toString.call(e).slice(8,-1).toLowerCase()}function ne(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"root",l=arguments.length>2&&arguments[2]!==void 0?arguments[2]:0,i=arguments.length>3?arguments[3]:void 0,O=i||{},D=O.key,$=O.index,k=O.type,S=k===void 0?"content":k,T=O.showComma,P=T!==void 0&&T,L=O.length,I=L===void 0?1:L,X=Re(e);if(X==="array"){var K=Fe(e.map(function(V,g,n){return ne(V,"".concat(t,"[").concat(g,"]"),l+1,{index:g,showComma:g!==n.length-1,length:I,type:S})}));return[ne("[",t,l,{showComma:!1,key:D,length:e.length,type:"arrayStart"})[0]].concat(K,ne("]",t,l,{showComma:P,length:e.length,type:"arrayEnd"})[0])}if(X==="object"){var J=Object.keys(e),A=Fe(J.map(function(V,g,n){return ne(e[V],/^[a-zA-Z_]\w*$/.test(V)?"".concat(t,".").concat(V):"".concat(t,'["').concat(V,'"]'),l+1,{key:V,showComma:g!==n.length-1,length:I,type:S})}));return[ne("{",t,l,{showComma:!1,key:D,index:$,length:J.length,type:"objectStart"})[0]].concat(A,ne("}",t,l,{showComma:P,length:J.length,type:"objectEnd"})[0])}return[{content:e,level:l,key:D,index:$,path:t,showComma:P,length:I,type:S}]}function Fe(e){if(typeof Array.prototype.flat=="function")return e.flat();for(var t=he(e),l=[];t.length;){var i=t.shift();Array.isArray(i)?t.unshift.apply(t,he(i)):l.push(i)}return l}function Ne(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:new WeakMap;if(e==null)return e;if(e instanceof Date)return new Date(e);if(e instanceof RegExp)return new RegExp(e);if(xe(e)!=="object")return e;if(t.get(e))return t.get(e);if(Array.isArray(e)){var l=e.map(function(D){return Ne(D,t)});return t.set(e,l),l}var i={};for(var O in e)i[O]=Ne(e[O],t);return t.set(e,i),i}function Me(e,t){var l=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter(function(O){return Object.getOwnPropertyDescriptor(e,O).enumerable})),l.push.apply(l,i)}return l}function ze(e){for(var t=1;t<arguments.length;t++){var l=arguments[t]!=null?arguments[t]:{};t%2?Me(Object(l),!0).forEach(function(i){ge(e,i,l[i])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(l)):Me(Object(l)).forEach(function(i){Object.defineProperty(e,i,Object.getOwnPropertyDescriptor(l,i))})}return e}var Je={showLength:{type:Boolean,default:!1},showDoubleQuotes:{type:Boolean,default:!0},renderNodeKey:Function,renderNodeValue:Function,selectableType:String,showSelectController:{type:Boolean,default:!1},showLine:{type:Boolean,default:!0},showLineNumber:{type:Boolean,default:!1},selectOnClickNode:{type:Boolean,default:!0},nodeSelectable:{type:Function,default:function(){return!0}},highlightSelectedNode:{type:Boolean,default:!0},showIcon:{type:Boolean,default:!1},theme:{type:String,default:"light"},showKeyValueSpace:{type:Boolean,default:!0},editable:{type:Boolean,default:!1},editableTrigger:{type:String,default:"click"},onNodeClick:{type:Function},onBracketsClick:{type:Function},onIconClick:{type:Function},onValueChange:{type:Function}};const Tt=(0,r.defineComponent)({name:"TreeNode",props:ze(ze({},Je),{},{node:{type:Object,required:!0},collapsed:Boolean,checked:Boolean,style:Object,onSelectedChange:{type:Function}}),emits:["nodeClick","bracketsClick","iconClick","selectedChange","valueChange"],setup:function(e,t){var l=t.emit,i=(0,r.computed)(function(){return Re(e.node.content)}),O=(0,r.computed)(function(){return"vjs-value vjs-value-".concat(i.value)}),D=(0,r.computed)(function(){return e.showDoubleQuotes?'"'.concat(e.node.key,'"'):e.node.key}),$=(0,r.computed)(function(){return e.selectableType==="multiple"}),k=(0,r.computed)(function(){return e.selectableType==="single"}),S=(0,r.computed)(function(){return e.nodeSelectable(e.node)&&($.value||k.value)}),T=(0,r.reactive)({editing:!1}),P=function(g){var n,s,v=(s=(n=g.target)===null||n===void 0?void 0:n.value)==="null"?null:s==="undefined"?void 0:s==="true"||s!=="false"&&(s[0]+s[s.length-1]==='""'||s[0]+s[s.length-1]==="''"?s.slice(1,-1):typeof Number(s)=="number"&&!isNaN(Number(s))||s==="NaN"?Number(s):s);l("valueChange",v,e.node.path)},L=(0,r.computed)(function(){var g,n=(g=e.node)===null||g===void 0?void 0:g.content;return n===null?n="null":n===void 0&&(n="undefined"),i.value==="string"?'"'.concat(n,'"'):n+""}),I=function(){var g=e.renderNodeValue;return g?g({node:e.node,defaultValue:L.value}):L.value},X=function(){l("bracketsClick",!e.collapsed,e.node)},K=function(){l("iconClick",!e.collapsed,e.node)},J=function(){l("selectedChange",e.node)},A=function(){l("nodeClick",e.node),S.value&&e.selectOnClickNode&&l("selectedChange",e.node)},V=function(g){if(e.editable&&!T.editing){T.editing=!0;var n=function s(v){var y;v.target!==g.target&&((y=v.target)===null||y===void 0?void 0:y.parentElement)!==g.target&&(T.editing=!1,document.removeEventListener("click",s))};document.removeEventListener("click",n),document.addEventListener("click",n)}};return function(){var g,n=e.node;return(0,r.createVNode)("div",{class:{"vjs-tree-node":!0,"has-selector":e.showSelectController,"has-carets":e.showIcon,"is-highlight":e.highlightSelectedNode&&e.checked,dark:e.theme==="dark"},onClick:A,style:e.style},[e.showLineNumber&&(0,r.createVNode)("span",{class:"vjs-node-index"},[n.id+1]),e.showSelectController&&S.value&&n.type!=="objectEnd"&&n.type!=="arrayEnd"&&(0,r.createVNode)(jt,{isMultiple:$.value,checked:e.checked,onChange:J},null),(0,r.createVNode)("div",{class:"vjs-indent"},[Array.from(Array(n.level)).map(function(s,v){return(0,r.createVNode)("div",{key:v,class:{"vjs-indent-unit":!0,"has-line":e.showLine}},null)}),e.showIcon&&(0,r.createVNode)(Ot,{nodeType:n.type,onClick:K},null)]),n.key&&(0,r.createVNode)("span",{class:"vjs-key"},[(g=e.renderNodeKey,g?g({node:e.node,defaultKey:D.value||""}):D.value),(0,r.createVNode)("span",{class:"vjs-colon"},[":".concat(e.showKeyValueSpace?" ":"")])]),(0,r.createVNode)("span",null,[n.type!=="content"&&n.content?(0,r.createVNode)(Vt,{data:n.content.toString(),onClick:X},null):(0,r.createVNode)("span",{class:O.value,onClick:!e.editable||e.editableTrigger&&e.editableTrigger!=="click"?void 0:V,onDblclick:e.editable&&e.editableTrigger==="dblclick"?V:void 0},[e.editable&&T.editing?(0,r.createVNode)("input",{value:L.value,onChange:P,style:{padding:"3px 8px",border:"1px solid #eee",boxShadow:"none",boxSizing:"border-box",borderRadius:5,fontFamily:"inherit"}},null):I()]),n.showComma&&(0,r.createVNode)("span",null,[","]),e.showLength&&e.collapsed&&(0,r.createVNode)("span",{class:"vjs-comment"},[(0,r.createTextVNode)(" // "),n.length,(0,r.createTextVNode)(" items ")])])])}}});function $e(e,t){var l=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter(function(O){return Object.getOwnPropertyDescriptor(e,O).enumerable})),l.push.apply(l,i)}return l}function z(e){for(var t=1;t<arguments.length;t++){var l=arguments[t]!=null?arguments[t]:{};t%2?$e(Object(l),!0).forEach(function(i){ge(e,i,l[i])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(l)):$e(Object(l)).forEach(function(i){Object.defineProperty(e,i,Object.getOwnPropertyDescriptor(l,i))})}return e}const Lt=(0,r.defineComponent)({name:"Tree",props:z(z({},Je),{},{data:{type:[String,Number,Boolean,Array,Object],default:null},collapsedNodeLength:{type:Number,default:1/0},deep:{type:Number,default:1/0},pathCollapsible:{type:Function,default:function(){return!1}},rootPath:{type:String,default:"root"},virtual:{type:Boolean,default:!1},height:{type:Number,default:400},itemHeight:{type:Number,default:20},selectedValue:{type:[String,Array],default:function(){return""}},collapsedOnClickBrackets:{type:Boolean,default:!0},style:Object,onSelectedChange:{type:Function},theme:{type:String,default:"light"}}),slots:["renderNodeKey","renderNodeValue"],emits:["nodeClick","bracketsClick","iconClick","selectedChange","update:selectedValue","update:data"],setup:function(e,t){var l=t.emit,i=t.slots,O=(0,r.ref)(),D=(0,r.computed)(function(){return ne(e.data,e.rootPath)}),$=function(n,s){return D.value.reduce(function(v,y){var w,_=y.level>=n||y.length>=s,F=(w=e.pathCollapsible)===null||w===void 0?void 0:w.call(e,y);return y.type!=="objectStart"&&y.type!=="arrayStart"||!_&&!F?v:z(z({},v),{},ge({},y.path,1))},{})},k=(0,r.reactive)({translateY:0,visibleData:null,hiddenPaths:$(e.deep,e.collapsedNodeLength)}),S=(0,r.computed)(function(){for(var n=null,s=[],v=D.value.length,y=0;y<v;y++){var w=z(z({},D.value[y]),{},{id:y}),_=k.hiddenPaths[w.path];if(n&&n.path===w.path){var F=n.type==="objectStart",H=z(z(z({},w),n),{},{showComma:w.showComma,content:F?"{...}":"[...]",type:F?"objectCollapsed":"arrayCollapsed"});n=null,s.push(H)}else{if(_&&!n){n=w;continue}if(n)continue;s.push(w)}}return s}),T=(0,r.computed)(function(){var n=e.selectedValue;return n&&e.selectableType==="multiple"&&Array.isArray(n)?n:[n]}),P=(0,r.computed)(function(){return!e.selectableType||e.selectOnClickNode||e.showSelectController?"":"When selectableType is not null, selectOnClickNode and showSelectController cannot be false at the same time, because this will cause the selection to fail."}),L=function(){var n=S.value;if(e.virtual){var s,v=e.height/e.itemHeight,y=((s=O.value)===null||s===void 0?void 0:s.scrollTop)||0,w=Math.floor(y/e.itemHeight),_=w<0?0:w+v>n.length?n.length-v:w;_<0&&(_=0);var F=_+v;k.translateY=_*e.itemHeight,k.visibleData=n.filter(function(H,q){return q>=_&&q<F})}else k.visibleData=n},I=function(){L()},X=function(n){var s,v,y=n.path,w=e.selectableType;if(w==="multiple"){var _=T.value.findIndex(function(C){return C===y}),F=he(T.value);_!==-1?F.splice(_,1):F.push(y),l("update:selectedValue",F),l("selectedChange",F,he(T.value))}else if(w==="single"&&T.value[0]!==y){var H=(s=T.value,v=1,function(C){if(Array.isArray(C))return C}(s)||function(C,E){var Q=C==null?null:typeof Symbol<"u"&&C[Symbol.iterator]||C["@@iterator"];if(Q!=null){var Y,M,ae=[],ce=!0,de=!1;try{for(Q=Q.call(C);!(ce=(Y=Q.next()).done)&&(ae.push(Y.value),!E||ae.length!==E);ce=!0);}catch(ye){de=!0,M=ye}finally{try{ce||Q.return==null||Q.return()}finally{if(de)throw M}}return ae}}(s,v)||Ue(s,v)||function(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}())[0],q=y;l("update:selectedValue",q),l("selectedChange",q,H)}},K=function(n){l("nodeClick",n)},J=function(n,s){if(n)k.hiddenPaths=z(z({},k.hiddenPaths),{},ge({},s,1));else{var v=z({},k.hiddenPaths);delete v[s],k.hiddenPaths=v}},A=function(n,s){e.collapsedOnClickBrackets&&J(n,s.path),l("bracketsClick",n,s)},V=function(n,s){J(n,s.path),l("iconClick",n,s)},g=function(n,s){var v=Ne(e.data),y=e.rootPath;new Function("data","val","data".concat(s.slice(y.length),"=val"))(v,n),l("update:data",v)};return(0,r.watchEffect)(function(){P.value&&function(n){throw new Error("[VueJSONPretty] ".concat(n))}(P.value)}),(0,r.watchEffect)(function(){S.value&&L()}),(0,r.watch)(function(){return e.deep},function(n){n&&(k.hiddenPaths=$(n,e.collapsedNodeLength))}),(0,r.watch)(function(){return e.collapsedNodeLength},function(n){n&&(k.hiddenPaths=$(e.deep,n))}),function(){var n,s,v=(n=e.renderNodeKey)!==null&&n!==void 0?n:i.renderNodeKey,y=(s=e.renderNodeValue)!==null&&s!==void 0?s:i.renderNodeValue,w=k.visibleData&&k.visibleData.map(function(_){return(0,r.createVNode)(Tt,{key:_.id,node:_,collapsed:!!k.hiddenPaths[_.path],theme:e.theme,showDoubleQuotes:e.showDoubleQuotes,showLength:e.showLength,checked:T.value.includes(_.path),selectableType:e.selectableType,showLine:e.showLine,showLineNumber:e.showLineNumber,showSelectController:e.showSelectController,selectOnClickNode:e.selectOnClickNode,nodeSelectable:e.nodeSelectable,highlightSelectedNode:e.highlightSelectedNode,editable:e.editable,editableTrigger:e.editableTrigger,showIcon:e.showIcon,showKeyValueSpace:e.showKeyValueSpace,renderNodeKey:v,renderNodeValue:y,onNodeClick:K,onBracketsClick:A,onIconClick:V,onSelectedChange:X,onValueChange:g,style:e.itemHeight&&e.itemHeight!==20?{lineHeight:"".concat(e.itemHeight,"px")}:{}},null)});return(0,r.createVNode)("div",{ref:O,class:{"vjs-tree":!0,"is-virtual":e.virtual,dark:e.theme==="dark"},onScroll:e.virtual?I:void 0,style:e.showLineNumber?z({paddingLeft:"".concat(12*Number(D.value.length.toString().length),"px")},e.style):e.style},[e.virtual?(0,r.createVNode)("div",{class:"vjs-tree-list",style:{height:"".concat(e.height,"px")}},[(0,r.createVNode)("div",{class:"vjs-tree-list-holder",style:{height:"".concat(S.value.length*e.itemHeight,"px")}},[(0,r.createVNode)("div",{class:"vjs-tree-list-holder-inner",style:{transform:"translateY(".concat(k.translateY,"px)")}},[w])])]):w])}}});var Et=He.Z;const oe="/";function Bt(){return le({url:oe+"ai/course",method:"post"})}function Pt(e){return le({url:oe+"ai/create-session",method:"post",data:e,headers:{"Content-Type":"multipart/form-data"}})}function Dt(e){return le({url:oe+"ai/session-list",method:"post",data:e})}function It(e){return le({url:oe+"ai/create-token",method:"post",data:e})}function At(e){return le({url:oe+"ai/token-list",method:"post",data:e})}function Ft(e){return le({url:oe+"ai/delete-session",method:"post",data:e})}function Mt(e){return le({url:oe+"ai/delete-token",method:"post",data:e})}const zt={class:"ai-boxs pmm2_flex_between"},$t={class:"ai-left-box"},Kt={class:"list-box scrollBar"},Ht=["onClick"],Ut={class:"iconMore"},Rt={class:"ai-right-box"},Jt={key:0,class:"ai-right-com pmm2_flex_center"},Qt={class:"dialogueInput-box"},qt={key:0,class:"img-list"},Gt={class:"dialogueInput-bottom pmm2_flex_between"},Yt={class:"left-box pmm2_flex_acenter"},Wt={class:"righ-box"},Zt={key:1,class:"ai-right-com2"},Xt={class:"right-fixed-list"},en={key:0},tn={style:{"padding-right":"5px"}},nn=["onClick"],ln={key:0,class:"chat-window scrollBar-C",id:"scrollableElement"},on=["id"],an={class:"message-content"},rn=["onDblclick"],sn={style:{display:"flex","justify-content":"flex-end"}},cn={class:"message ai-message"},un={class:"message-content"},dn=["onDblclick"],pn={key:0,class:"skz"},fn=["innerHTML"],mn={key:3},vn={key:0,style:{"justify-content":"flex-end"},class:"pmm2_flex_acenter"},hn={class:"dialogueInput-box"},gn={class:"dialogueInput-bottom pmm2_flex_between",style:{"padding-top":"7px"}},yn={class:"left-box pmm2_flex_acenter"},bn={class:"wz"},_n={class:"pmm2_flex_acenter"},kn=Ke({__name:"index",setup(e){const t=(u="")=>u.replace(/\n/g,"<br/>"),l=async u=>{if(u)try{await navigator.clipboard.writeText(u),W.success("复制成功")}catch{}},i=N(!1);st(()=>{document.addEventListener("paste",$)}),it(()=>{document.removeEventListener("paste",$)});const O=()=>{i.value=!0},D=()=>{i.value=!1},$=u=>{if(!i.value)return;u.preventDefault();const c=u.clipboardData.items;for(let f=0;f<c.length;f++){const h=c[f];if(h.type.indexOf("image")!==-1){const U=h.getAsFile();U&&Se(U)}}},k=N(!0),S=N([]),T=N(0),P=N(""),L=N(""),I=N({temperature:1,top_p:.7});(async()=>{let o=(await Bt()).data||{},c=o.stages||[],f=o.courseList||[];const h=c.map(U=>{const _e=f.filter(ke=>ke.stages.some(fe=>fe.value===U.id));return{id:U.id,label:U.label,list:_e}});I.value={temperature:o.temperature||1,top_p:o.top_p||.7},S.value=h,P.value=h[0].id,L.value=h[0].list[0].code})();let K=[];const J=()=>{V.value=-1,_.value={},P.value=S.value[0].id,L.value=S.value[0].list[0].code,C.value=!1,q.value=!1,E.value=[],K=[],ee.value=[],H.value="",M.value=!1},A=N([]),V=N(-1);let g={page:1,pageSize:20},n=!0,s=!0;const v=async()=>{let o=(await Dt(g)).data||[],c=o.length;(c==0||c<g.pageSize)&&(s=!1),g.page==1&&(A.value=[]),A.value=A.value.concat(o),n=!0},y=(u,o)=>{Ie.confirm("确认删除当前会话，删除后不可恢复?","提示",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then(()=>{A.value.splice(o,1),V.value==o&&J(),Ft({sessionId:u.id}).then(c=>{c.code!=0&&c.msg&&(W.error(c.msg),A.value.splice(o,0,u))})}).catch(()=>{})},w=()=>{!n||!s||(n=!1,g.page++,v())},_=N({}),F=(u,o)=>{V.value!=o&&(H.value=u.prompt||"",V.value=o,_.value=u,ee.value=u.images||[],q.value=!0,E.value=[],ye(u.id))};V.value=-1,v();const H=N(""),q=N(!1),C=N(!1),E=N([]);let Q="";const Y=N(""),M=N(!1),ae=async()=>{if(K.length==0)return W.error("请上传图片");if(!Y.value)return W.error("请输入会话标题");M.value=!0,i.value=!1,C.value=!0;let u=Y.value,o=S.value.find(h=>h.id==P.value);_.value={stage_label:o.label,course_name:o.list.find(h=>h.code==L.value).name,title:u};const c=new FormData;c.append("stageId",P.value),c.append("courseCode",L.value),c.append("title",u);for(let h=0;h<K.length;h++)c.append("images[]",K[h]);Y.value="",V.value=0,A.value.unshift({..._.value});let f=await Pt(c);M.value=!0,f.code===0?(Q=f.data,C.value=!1,v(),q.value=!0):f.msg&&(C.value=!1,W.error(f.msg))};N(!1);let ce={page:1,pageSize:10};const de=u=>{const o=document.getElementById(u);o&&o.scrollIntoView({behavior:"smooth"})},ye=async u=>{u&&(Q=u);let o=await At({sessionId:Q,...ce});if(C.value=!1,o.code===0){let c=(o.data||[]).reverse();for(let f=0;f<c.length;f++){let h=c[f];c[f].ismd=!1;try{c[f].result=JSON.parse(h.result),c[f].isJson=!0}catch{c[f].result=h.result.trim(),c[f].resultMd=t(h.result),c[f].isJson=!1}}E.value=c,be()}else o.msg&&W.error(o.msg)},be=async()=>{await wt();const u=document.getElementById("scrollableElement");u&&(u.scrollTop=u.scrollHeight)},ee=N([]),Se=u=>{const o=URL.createObjectURL(u);ee.value.push(o),K.push(u)},pe=N(!1),Ve=N(0),je=()=>{if(!H.value)return W.error("请输入问题");const u=Date.now();let o={prompt:H.value,result:"思考中",created_at:Nt(u),...I.value,ismd:!1},c=E.value.length;E.value.push(o),C.value=!0,be(),It({sessionId:Q,text:o.prompt,...I.value}).then(f=>{if(C.value=!1,f.code==0){let h=f.data||"-";try{E.value[c].result=JSON.parse(h),E.value[c].isJson=!0}catch{E.value[c].isJson=!1,E.value[c].result=h.trim(),E.value[c].resultMd=t(h)}be()}else f.msg&&W.error(f.msg)}).catch(()=>{E.value[c].result="服务器繁忙",C.value=!1})},Qe=(u,o)=>{Ie.confirm("确认删除当前提问记录，删除后不可恢复?","提示",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then(()=>{E.value.splice(o,1),Mt({tokenId:u.id}).then(c=>{c.code!=0&&c.msg&&(W.error(c.msg),E.value.splice(o,0,u))})}).catch(()=>{})},qe=u=>{T.value=S.value.findIndex(c=>c.id==u),S.value[T.value].list.findIndex(c=>c.code==L.value)==-1&&(L.value=S.value[T.value].list[0].code)};return(u,o)=>{const c=te("Grape"),f=ft,h=ct,U=yt,_e=te("Delete"),ke=_t,fe=te("CircleClose"),Oe=ut,Te=kt,Le=dt,Ge=pt,Ee=te("Position"),Ye=te("Menu"),We=te("Loading"),Ze=te("QuestionFilled"),Xe=mt,Be=vt,et=ht,tt=xt,nt=Ct("latexs");return b(),j("div",zt,[m("div",$t,[m("div",{class:"ai-old-btn",onClick:J},[p(h,{size:"large",round:""},{default:x(()=>[p(f,null,{default:x(()=>[p(c)]),_:1}),o[12]||(o[12]=m("span",null,"开启新会话",-1))]),_:1,__:[12]})]),m("div",Kt,[(b(!0),j(se,null,ie(d(A),(a,B)=>De((b(),j("div",{class:gt(["item-box pmm2_ellipsis huiA2",{on:B==d(V)}]),key:a,onClick:G=>F(a,B)},[p(U,{type:"info"},{default:x(()=>[Z(R(a.stage_label)+"-"+R(a.course_name),1)]),_:2},1024),Z(" "+R(a.title||"")+" ",1),m("div",Ut,[p(f,{onClick:bt(G=>y(a,B),["stop"])},{default:x(()=>[p(_e)]),_:2},1032,["onClick"])])],10,Ht)),[[tt,w]])),128))])]),m("div",Rt,[d(q)?(b(),j("div",Zt,[m("div",Xt,[m("div",{class:"icon huiA2",onClick:o[5]||(o[5]=a=>k.value=!d(k))},[p(f,null,{default:x(()=>[p(Ye)]),_:1})]),d(k)?(b(),j("ul",en,[(b(!0),j(se,null,ie(d(E),(a,B)=>(b(),j("li",{key:B,class:"pmm2_flex_acenter"},[m("div",tn,[p(f,{class:"huiA3",onClick:G=>Qe(a,B),title:"删除"},{default:x(()=>[p(fe)]),_:2},1032,["onClick"])]),m("span",{class:"huiA2",title:"定位",onClick:G=>de("chat-"+a.id+"-"+B)},R(a.created_at),9,nn)]))),128))])):re("",!0)]),d(E).length>0?(b(),j("div",ln,[(b(!0),j(se,null,ie(d(E),(a,B)=>(b(),j("div",{class:"chat-messages",key:B},[m("div",{class:"message user-message",id:"chat-"+a.id+"-"+B},[o[14]||(o[14]=m("div",{class:"avatar",style:{"font-size":"12px"}},"用户",-1)),m("div",an,[m("pre",{onDblclick:G=>l(a.prompt)},R(a.prompt),41,rn),m("div",sn,[p(U,{style:{"margin-right":"10px"},type:"warning"},{default:x(()=>[Z("temperature："+R(a.temperature||"1.0"),1)]),_:2},1024),p(U,{type:"danger"},{default:x(()=>[Z("top_p："+R(a.top_p||"0.7"),1)]),_:2},1024)])])],8,on),m("div",cn,[o[16]||(o[16]=m("div",{class:"avatar avatar-ai"},"AI",-1)),m("div",un,[m("div",{onDblclick:G=>l(a.result)},[a.result=="思考中"?(b(),j("div",pn,[p(f,{class:"rotating-element"},{default:x(()=>[p(We)]),_:1}),o[15]||(o[15]=m("div",null,"思考中...",-1))])):a.isJson?(b(),ue(d(Et),{key:1,showSelectController:!0,showIcon:!0,data:a.result},null,8,["data"])):a.ismd?De((b(),j("div",{key:2,innerHTML:a.resultMd},null,8,fn)),[[nt]]):(b(),j("pre",mn,R(a.result)+" ",1))],40,dn),a.result!="思考中"&&!a.isJson?(b(),j("div",vn,[p(h,{plain:"",type:"primary",onClick:G=>a.ismd=!a.ismd},{default:x(()=>[Z(R(a.ismd?"原文":"web格式"),1)]),_:2},1032,["onClick"])])):re("",!0)])])]))),128))])):re("",!0),m("div",hn,[p(Oe,{modelValue:d(H),"onUpdate:modelValue":o[6]||(o[6]=a=>me(H)?H.value=a:null),modelModifiers:{trim:!0},resize:"none",onKeydown:o[7]||(o[7]=Pe(a=>je(),["enter"])),autosize:{minRows:2,maxRows:5},class:"scrollBar textarea-box",type:"textarea",placeholder:"请输入问题",disabled:d(C)},null,8,["modelValue","disabled"]),m("div",gn,[m("div",yn,[m("div",bn,[m("span",null,R(d(_).stage_label)+"-"+R(d(_).course_name),1)]),p(h,{onClick:o[8]||(o[8]=a=>pe.value=!0),style:{"margin-left":"15px"},round:""},{default:x(()=>o[17]||(o[17]=[Z("查看试卷")])),_:1,__:[17]})]),m("div",_n,[p(Xe,{content:"temperature默认1.0，top_p默认0.7。通常建议仅调整 temperature 或 top_p 其中之一，不建议两者都修改。",placement:"top"},{default:x(()=>[p(f,{style:{color:"red"},class:"huiA"},{default:x(()=>[p(Ze)]),_:1})]),_:1}),o[18]||(o[18]=m("span",{style:{"padding-left":"10px"}},"temperature：",-1)),p(Be,{disabled:d(C),modelValue:d(I).temperature,"onUpdate:modelValue":o[9]||(o[9]=a=>d(I).temperature=a),step:.1,"step-strictly":!0,min:0,max:1,style:{width:"80px","margin-right":"10px"},size:"small"},null,8,["disabled","modelValue"]),o[19]||(o[19]=m("span",null,"top_p：",-1)),p(Be,{disabled:d(C),modelValue:d(I).top_p,"onUpdate:modelValue":o[10]||(o[10]=a=>d(I).top_p=a),step:.1,"step-strictly":!0,min:0,size:"small",style:{width:"80px"},max:1},null,8,["disabled","modelValue"]),p(h,{type:"primary",loading:d(C),onClick:je,size:"large",round:"",style:{width:"100px","margin-left":"10px"}},{default:x(()=>[p(f,null,{default:x(()=>[p(Ee)]),_:1})]),_:1},8,["loading"])])])])])):(b(),j("div",Jt,[m("div",Qt,[d(ee).length>0?(b(),j("div",qt,[(b(!0),j(se,null,ie(d(ee),(a,B)=>(b(),j("div",{class:"img-box",key:B},[p(ke,{class:"img",src:a,fit:"cover",onClick:G=>(pe.value=!0,Ve.value=B)},null,8,["src","onClick"]),p(f,{class:"icon huiA",style:{color:"red"},onClick:G=>(d(ee).splice(B,1),d(K).splice(B,1))},{default:x(()=>[p(fe)]),_:2},1032,["onClick"])]))),128))])):re("",!0),p(Oe,{clearable:"",modelValue:d(Y),"onUpdate:modelValue":o[0]||(o[0]=a=>me(Y)?Y.value=a:null),modelModifiers:{trim:!0},onFocus:O,disabled:d(M),onBlur:D,resize:"none",class:"textarea-box",rows:2,type:"textarea",placeholder:"请输入会话标题",onKeydown:o[1]||(o[1]=Pe(a=>ae(),["enter"]))},null,8,["modelValue","disabled"]),m("div",Gt,[m("div",Yt,[p(Le,{disabled:d(M),modelValue:d(P),"onUpdate:modelValue":o[2]||(o[2]=a=>me(P)?P.value=a:null),style:{width:"100px"},onChange:qe},{default:x(()=>[(b(!0),j(se,null,ie(d(S),a=>(b(),ue(Te,{key:a.id,label:a.label,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["disabled","modelValue"]),p(Le,{disabled:d(M),modelValue:d(L),"onUpdate:modelValue":o[3]||(o[3]=a=>me(L)?L.value=a:null),style:{width:"100px",margin:"0 10px"}},{default:x(()=>{var a;return[(b(!0),j(se,null,ie((a=d(S)[d(T)])==null?void 0:a.list,(B,G)=>(b(),ue(Te,{key:G,label:B.name,value:B.code},null,8,["label","value"]))),128))]}),_:1},8,["disabled","modelValue"]),p(Ge,{loading:d(M),class:"upload-demo",action:"#",multiple:"","http-request":()=>{},"before-upload":Se,accept:".jpg, .jpeg, .png","show-file-list":!1},{default:x(()=>[p(h,{type:"primary"},{default:x(()=>o[13]||(o[13]=[Z("上传图片")])),_:1,__:[13]})]),_:1},8,["loading"])]),m("div",Wt,[p(h,{type:"primary",loading:d(M),onClick:o[4]||(o[4]=a=>ae()),size:"large",round:"",style:{width:"120px"}},{default:x(()=>[d(M)?re("",!0):(b(),ue(f,{key:0},{default:x(()=>[p(Ee)]),_:1})),m("span",null,R(d(M)?"创建中":"新建会话"),1)]),_:1},8,["loading"])])])])]))]),d(pe)?(b(),ue(et,{key:0,"initial-index":d(Ve),"hide-on-click-modal":"",onClose:o[11]||(o[11]=()=>{pe.value=!1}),"url-list":d(ee)},null,8,["initial-index","url-list"])):re("",!0)])}}}),Fn=St(kn,[["__scopeId","data-v-f593c1cc"]]);export{Fn as default};

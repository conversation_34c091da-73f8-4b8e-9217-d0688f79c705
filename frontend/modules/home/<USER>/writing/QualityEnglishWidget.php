<?php

namespace frontend\modules\home\widgets\writing;

use frontend\modules\home\services\ExamReportService;
use frontend\modules\home\widgets\ExamDataBaseWidget;
use yii\helpers\Json;

class QualityEnglishWidget extends ExamDataBaseWidget
{
    protected string $view = 'quality-english';


    public function init(): void
    {
        parent::init();
        $examInfo = ExamReportService::getExamInfo($this->examId);
        $this->setExamResultGeneratedAt($examInfo->generate_result_at);
    }

    public function getDataFromDb()
    {
        // TODO: Implement getDataFromDb() method.
        $examInfo = ExamReportService::getExamInfo($this->examId);
        return Json::decode($examInfo->ai_result_stats);
    }


}
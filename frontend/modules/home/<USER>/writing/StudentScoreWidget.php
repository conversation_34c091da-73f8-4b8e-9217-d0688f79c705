<?php

namespace frontend\modules\home\widgets\writing;

use common\enums\CardContentType;
use frontend\modules\home\services\ExamReportService;
use frontend\modules\home\widgets\ExamDataBaseWidget;
use yii\helpers\Json;
use yii\helpers\Url;

class StudentScoreWidget extends ExamDataBaseWidget
{
    protected string $view = 'student-score';



    public function init(): void
    {
        parent::init();
        $examInfo = ExamReportService::getExamInfo($this->examId);
        $this->setExamResultGeneratedAt($examInfo->generate_result_at);
    }

    public function getDataFromDb()
    {
        // TODO: Implement getDataFromDb() method.
        $examInfo = ExamReportService::getExamInfo($this->examId); 
        $aiResultStats = Json::decode($examInfo->ai_result_stats);  
        $studentData = Json::decode($examInfo->student_data);

        $cardInfo = Json::decode($examInfo->card_info);
        $isEnglish = false;
        if($cardInfo['contentTypeId'] == CardContentType::EnglishWriting->value){
            $isEnglish = true;
        }
        $dimensionNames = $aiResultStats['dimensionNames'] ?? [];
        $studentErrors = $aiResultStats['studentErrors'] ?? [];

        $studentList = [];
        foreach ($studentData as $student) {
            if(!empty($student['absent']) || !isset($student['score'])){
                continue;
            }  
            $studentError = !empty($studentErrors[$student['student_id']]) ? $studentErrors[$student['student_id']] : [];
            foreach($student['card_struct_stats'] as $struct){
                if(!empty($struct['ai_result']['feedback']['dimensions'])){
                    foreach($struct['ai_result']['feedback']['dimensions'] as $dimension){
                        $student[$dimension['dimenName']] = $dimension['dimenScore'];
                    }
                }
                $student['wordCount'] = !empty($struct['ai_result']['feedback']['wordCount']) ? $struct['ai_result']['feedback']['wordCount']:0; 
            }
            $student['error'] = $studentError; 
            
            $student['actionUrl'] = Url::toRoute(['report/student','exam_id' => $examInfo->id,'student_id' => $student['student_id']]); 
            foreach($dimensionNames as $dimensionName){
                if(!isset($student[$dimensionName])){
                    $student[$dimensionName] = 0;
                }
            }
            $studentList[] = $student;
        }

        return [
            'isEnglish' => $isEnglish,
            'dimensions' => !$isEnglish ? $aiResultStats['dimensionNames'] : [],
            'studentData' => $studentList,
        ];
    }


}
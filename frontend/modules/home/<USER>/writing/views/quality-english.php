<?php

\frontend\assets\EChartAsset::register($this);
$dataArray = is_array($data) ? $data : \yii\helpers\Json::decode($data); 
?>

<div class="work-report-item-box report-overall" id="report-quality-score">
    <div class="row">
        <div class="work-report-title pb-3">
            严重问题分析
        </div>
        <div class="pt-3 pe-3">
            <table class="table table-bordered align-middle table-test-point">
                <tr class="table-point-head no-hover">
                    <td class="ps-3">类别</td>
                    <td class="text-center">解释</td>
                    <td class="ps-3 text-center">人数</td>
                </tr>
                <tr v-for="(item,index) in errors">
                    <td class="ps-3">{{item.label}}</td>
                    <td class="ps-3">{{item.desc}}</td>
                    <td class="text-center">
                        <div class="position-relative">
                            <span class="cursor-pointer"  style="color:#2C76FF;" @mouseenter="handleMouseEnter(index)"
                                  @mouseleave="handleMouseLeave(index)">{{item.num}}</span>
                            <div class="position-absolute top-0 point-contrast-table" style="width: 120px;"
                                 v-if="index == showContrastIndex && item.num>0">
                                <div class="d-flex border-top pb-1 pt-1" v-for="(student,contrastIndex) in item.students">
                                    <div class="col ps-2 pe-2">{{student.student_name}}<span v-if="item.key && item.key == '1'">({{student.wordCount}})</span></div>
                                </div>
                            </div>
                        </div>
                        <span></span>

                    </td>
                </tr>
            </table>
        </div>
    </div>
</div>

<script>
    var qualityApp = Vue.createApp({
        data(){
            return {
                errors:<?=\yii\helpers\Json::encode($dataArray['errors'])?>,
                chart:null,
                showContrastIndex:-1,
            }
        },
        created(){
        },
        mounted() {
        },
        methods:{
            handleMouseEnter:function (index){
                this.showContrastIndex = index;
            },
            handleMouseLeave:function (index){
                this.showContrastIndex = -1;
            },
        }
    }).use(ElementPlus, {
        locale: ElementPlusLocaleZhCn,
    }).mount("#report-quality-score")
</script>
<?php

use common\enums\CardStructType;
use frontend\assets\DataTableAsset;
use yii\helpers\Json;

DataTableAsset::register($this);
\frontend\assets\EChartAsset::register($this);

$studentUrl = \yii\helpers\Url::toRoute(['report/student','exam_id' =>Yii::$app->request->get('exam_id')]);

$dataArray = is_array($data) ? $data : \yii\helpers\Json::decode($data);
?>
<style>
    .el-table .cell{
        padding:0 7px;
    }
    .el-table{
        color:#252B3A
    }
    .student-score-table .student-score-head.el-table__cell {
        background: #1BA1E4 !important;
        color: #ffffff;
        font-weight: bold;
    }
    .student-score-table .student-score-head.el-table__cell .cell,.student-score-table .student-obj-head.el-table__cell .cell{
        color: #ffffff;
    }
    .student-score-table .student-obj-head.el-table__cell {
        background:#1BA1E4 !important;
        color: #ffffff;
        font-weight: bold;
    }
    .student-score-table .student-score-sub-head.el-table__cell {
        background: #1BA1E4 !important;
        font-weight: normal;
    }
    .student-score-table .student-obj-sub-head.el-table__cell {
        background: #E2F5FF !important;
        font-weight: normal;
    }
</style>
<div class="work-report-item-box report-student-score" id="report-student-score">
    <div class="work-report-title pb-1">
        <div class="row align-items-center">
            <div class="col-8">学生成绩</div>
            <div class="col text-end">
                <div class="d-flex justify-content-end">
                    <input class="form-control form-control-sm report-student-search" v-model="studentName" placeholder="输入姓名或考号">
                    <a target="_blank"  href="<?=\yii\helpers\Url::toRoute(['report/student-excel','exam_id' =>Yii::$app->request->get('exam_id')])?>" class="col-form-label ms-3 cursor-pointer student-import-excel">导出Excel</a>
                </div>
            </div>
        </div>
    </div>

    <div class="mt-2"> 
        <el-table
                class="student-score-table"
                :data="showStudentData"
                border
                style="width: 100%"
                :header-cell-class-name="headClass"
        >

            <el-table-column prop="exam_number" align="center" label="考号" rowspan="2">

            </el-table-column>
            <el-table-column prop="ranking" align="center" label="排名" rowspan="2">

            </el-table-column>
            <el-table-column prop="student_name" align="center" label="姓名" rowspan="2">
                <template #default="{ row }">
                    <div class="d-flex ps-2 pe-2">
                        <span> {{row.student_name}}</span>
                        <div class="text-end col"><img v-on:click="showStudentCard(row)" src="/img/home/<USER>" class="cursor-pointer"></div>
                    </div>
                </template>
            </el-table-column> 
            <el-table-column prop="score"  align="center" label="得分" rowspan="2">

            </el-table-column>
            <el-table-column prop="wordCount"  align="center" label="字数" rowspan="2">

            </el-table-column>
            <el-table-column  label="维度得分" align="center" :colspan="dimensions.length" v-if="dimensions && dimensions.length>0">
                <el-table-column v-for="(item,index) in dimensions" align="center"  :prop="item" :key="item" :label="item">
                    <template #default="{ row,column }">
                        {{row[column.property]}}
                    </template>
                </el-table-column>
            </el-table-column>
            <el-table-column prop="error"  align="center" label="严重错误" rowspan="2">
                <template #default="{ row,column }">
                    <span v-if="row.error.length==0">无</span>
                    <span v-else class="text-danger" v-for="(error,errorIndex) in row.error"
                          v-bind:class="{'ms-2':errorIndex>0}">{{error.label}}</span>
                </template>
            </el-table-column>
            <el-table-column prop="actionUrl"  align="center" label="操作" rowspan="2">
                <template #default="{ row,column }">
                    <a v-bind:href="'<?=$studentUrl?>&student_id='+row.student_id" style="color: #2C76FF;" target="_blank">查看详情</a>
                </template>
            </el-table-column>
        </el-table>
        <div class="clearfix pt-3">
            <div class="float-end">
                <el-pagination
                        v-model:current-page="currentPage"
                        v-model:page-size="pageSize"
                        layout="prev, pager, next,slot"
                        v-model:total="totalCount"
                        :hide-on-single-page="false"
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                >
                </el-pagination>
            </div>
        </div>
    </div>
</div>
<?=$this->render('/common/_student_card')?>

<script>
    var studentScoreApp = Vue.createApp({
        data(){
            return {
                studentName:'',
                chart:null, 
                studentData:<?=Json::encode($dataArray['studentData'])?>,
                searchStudentData:<?=Json::encode($dataArray['studentData'])?>,
                dimensions:<?=Json::encode($dataArray['dimensions'])?>,
                totalCount:<?=count($dataArray['studentData'])?>,
                levelDescShow:false,
                levelNameShow:false,
                currentPage:1,
                levelIndex:-1,
                levelIndexData:{},
                pageSize:10,
                pageStudentList:[],
                chartOption:{
                    color:['#2672FF','#0DD7C6'],
                    tooltip: {
                        trigger: 'item'
                    },
                    series: [
                        {
                            type: 'pie',
                            radius: ['40%', '70%'],
                            avoidLabelOverlap: false,
                            itemStyle: {
                                borderColor: '#fff',
                                borderWidth: 2
                            },
                            data: [
                                { value: 1048, name: 'Search Engine' },
                                { value: 735, name: 'Direct' },
                            ]
                        }
                    ]
                }
            }
        },
        computed:{
            showStudentData:function (){
               var list = [];
               for (var i in this.pageStudentList){
                   var item = this.pageStudentList[i]; 
                   list.push(item)
               }
               return list;
            } ,
        },
        watch:{
            studentName:function() {

                var list = [];
                for (var index in this.studentData){
                    var item = this.studentData[index];
                    if(this.studentName == "" || item.student_name.indexOf(this.studentName) != -1 ){
                        list.push(item)
                    }else if((item.exam_number+'').indexOf(this.studentName) != -1){
                        list.push(item)
                    }
                }
                this.searchStudentData = list;
                this.totalCount = list.length;
                this.currentPage = 1;
                this.handleCurrentChange()
            }
        },
        created:function (){
            this.handleCurrentChange();
        },
        mounted() {
            //this.initChart();
        },
        methods:{
            headClass:function ({row,column,rowIndex,columnIndex}){
                return 'student-score-head' 
            },
            showStudentCard:function (student){
                console.log(student)
                studentCardApp.showCard(student.answer_images)
            },
            initChart:function (){
                this.chart = echarts.init(this.$refs.studentScoreLevel);
            },
            handleMouseEnter:function (){
                this.levelDescShow = true;
            },
            handleMouseLeave:function (){
                this.levelDescShow = false;
            },
            changeLevelIndex:function (index){
                var info = this.levelData[index];
                if(info.num==0){
                    this.levelNameShow = false;
                    return;
                }
                if(this.levelIndex == index){
                    if(this.levelNameShow == false){
                        this.levelNameShow = true;
                    }else{
                        this.levelIndex = -1;
                        this.levelNameShow = false;
                    }
                }else{
                    this.levelIndex = index;
                    this.levelIndexData = info;
                    this.levelNameShow = true;
                }
            },
            closeLevelStudents:function (){
                this.levelNameShow = false;
            },
            getLevelStyle:function (){
                return "top:"+(this.levelIndex * 30)+"px";
            },
            handelShowData:function (){
                var data = [];
                var colors = [];
                for(var i in this.levelData){
                    var item = this.levelData[i];
                    if(item.num == 0){
                        continue;
                    }
                    data.push({name:item.name,value:item.num})
                    colors.push(item.color)
                }
                this.chartOption.color = colors;
                this.chartOption.series[0].data = data;
                this.chart.setOption(this.chartOption)

            },
            getObjCss:function (obj,struct){
                console.log(obj,struct)
                for(var i in this.structList){
                    var item = this.structList[i];
                    if(item.id == obj.property){
                        if(!struct.card_struct_stats[obj.property]){
                            return 'text-danger';
                        }else{
                            var data  = struct.card_struct_stats[obj.property]
                            if(item.typeId == '<?=CardStructType::Subjective->value?>'){
                                if(!data.score){
                                    data.score = 0;
                                }
                                if(data.score ==0 ){
                                    return 'text-danger';
                                }else if(data.score<item.full_score){
                                    return 'text-warning'
                                }
                            }else{
                                if(data.score == 0){
                                    return 'text-danger';
                                }else if(item.answer != data.answer){
                                    return 'text-warning';
                                }
                            }
                        }
                    }
                }
                return '';
            },
            handleCurrentChange:function (){
                var offset = (parseInt(this.currentPage) - 1) * parseInt(this.pageSize);
                var num = 0;
                var list = [];
                for(var index in this.searchStudentData){
                    if(num == parseInt(this.pageSize)){
                        break;
                    }
                    if(index>=offset){
                        list.push(this.searchStudentData[index])
                        num ++;
                    }
                }
                console.log(list)
                this.pageStudentList = list;
            }
        }
    }).use(ElementPlus, {
        locale: ElementPlusLocaleZhCn,
    }).mount("#report-student-score")
</script>
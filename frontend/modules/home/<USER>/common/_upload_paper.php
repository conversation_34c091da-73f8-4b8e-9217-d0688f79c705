<?php

use common\models\school\School;
use yii\helpers\Url;

/**
 * @var School $school
 */

?>

<div class="modal fade hh-modal" id="upload-paper-box" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered  modal-lg">
        <div class="modal-content">
            <div class="modal-header hh-modal-header">
                <h5 class="modal-title">导入试卷</h5>
                <button type="button" class="btn-close"   data-bs-dismiss="modal" aria-label="Close" ></button>
            </div>
            <div class="modal-body">
                <div class="pt-3 ps-5 pe-5">
                    <div class="d-flex justify-content-end">
                        <div>
                            <label class="cursor-pointer btn-hh-upload-desc" v-on:click="showUploadXkDesc">
                                <img src="/img/home/<USER>">
                                <span class="ps-2 pe-2">学科网试题上传规则</span>
                                <span class="text-primary-active">查看</span>
                            </label>
                            <label class="cursor-pointer btn-hh-upload-desc ms-3" v-on:click="showUploadDesc">
                                <img src="/img/home/<USER>">
                                <span class="ps-2 pe-2">上传规则</span>
                                <span class="text-primary-active">查看</span>
                            </label>
                            <a href="<?= Yii::$app->params['tpl.wordImportNoteFilename'] ?>" target="_blank">
                                <label class="cursor-pointer btn-hh-upload-desc ms-3">
                                    <img src="/img/home/<USER>">
                                    <span class="ps-2 pe-2 text-normal">试卷样例</span>
                                    <span class="text-primary-active">下载</span>
                                </label>
                            </a>
                        </div>
                    </div>
                    <div class="mt-3">
                        <el-upload
                                class="upload-paper-custom"
                                ref="uploadRef"
                                v-model:file-list="fileList"
                                :show-file-list="false"
                                action="<?=Url::toRoute(['custom-paper/upload-word'])?>"
                                :data="importData"
                                :on-success="handleImportSuccess"
                                :on-error ="handleImportError"
                                :on-change="handleChange"
                                :auto-upload="false"
                                drag
                        >
                            <div v-if="!hasUploadFile">
                                <div class="text-center font-weight-bold">
                                    点击选择文件或将文件拖入框中
                                </div>
                                <div class="text-center text-999 mt-2">
                                    <?php if($school->enable_paper_ocr_import): ?>
                                    导入文件支持.docx或.pdf格式，小于<span class="text-danger">10M</span>
                                    <?php else:?>
                                        导入文件仅支持.docx格式，小于<span class="text-danger">10M</span>
                                    <?php endif; ?>    
                                </div>
                                <div class="text-center  mt-3">
                                    <button class="btn btn-outline-primary">选择文件</button>
                                </div>
                            </div>
                            <div v-else>
                                <div class="text-center font-size-18">
                                    <img src="/img/home/<USER>">
                                    <span class="ps-3 text-success">导入成功</span>
                                </div>
                                <div class="text-center text-999 mt-3 mb-3">
                                    {{uploadFileInfo.fileName}}
                                </div>
                                <div class="text-center ">
                                    <button class="btn btn-outline-primary">重新选择</button>
                                </div>
                            </div>
                        </el-upload>
                    </div>

                    <div class="mt-3">
                        <div class="row">
                            <div class="col-9">
                                <div class="d-flex align-items-center">
                                    <label class="col-form-label col-auto font-weight-bold me-2"><span class="text-danger">*</span>试卷</label>
                                    <input class="form-control" v-model="paperName">
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-3">
                                <div class="d-flex align-items-center">
                                    <label class="col-form-label col-auto font-weight-bold me-2">&nbsp;&nbsp;学段</label>
                                    <select class="form-select" v-model="stage">
                                        <option v-for="(item,index) in stageList" v-bind:value="item.value">{{item.label}}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-3">
                                <div class="d-flex align-items-center">
                                    <label class="col-form-label col-auto font-weight-bold me-2">年级</label>
                                    <select class="form-select" v-model="grade">
                                        <option value="">年级</option>
                                        <?php if(!empty($gradeList)):foreach ($gradeList as $grade): ?>
                                            <option value="<?=$grade->value?>"><?=$grade->label()?></option>
                                        <?php endforeach; endif; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-3">
                                <div class="d-flex align-items-center">
                                    <label class="col-form-label col-auto font-weight-bold me-2">科目</label>
                                    <select class="form-select" v-model="course_code">
                                        <option v-for="(item,index) in courseList" v-bind:value="item.code">{{item.name}}</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-3">
                                <div class="d-flex align-items-center">
                                    <label class="col-form-label col-auto font-weight-bold me-2">&nbsp;&nbsp;类型</label>
                                    <select class="form-select" v-model="category_id">
                                        <option value="">请选择</option>
                                        <template v-for="(item,index) in paperCategoryList">
                                            <option v-bind:value="item.id">{{item.name}}</option>
                                        </template>
                                    </select>
                                </div>
                            </div>
                            <div class="col-3">
                                <div class="d-flex align-items-center">
                                    <label class="col-form-label col-auto  font-weight-bold me-2">版本</label>
                                    <select class="form-select" v-model="textbookVersion">
                                        <option value="">请选择</option>
                                        <template v-for="(item,index) in versionList">
                                            <option v-bind:value="item.id">{{item.name}}</option>
                                        </template>
                                      </select>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="d-flex align-items-center">
                                    <label class="col-form-label col-auto  font-weight-bold me-2">模块</label>
                                    <select class="form-select" v-model="textbook_id">
                                        <option value="">模块</option>

                                        <template v-for="(item,index) in textBookList">
                                            <option v-if="item.version_id == textbookVersion" v-bind:value="item.id">{{item.name}}</option>
                                        </template>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <div class="text-end w-100 pe-5 pb-3">
                    <button type="button" class="btn btn-hh-default"  data-bs-dismiss="modal" >取消</button>
                    <button type="button" class="btn btn-primary ms-3" v-on:click="uploadPaper" v-bind:disabled="!hasUpload" >下一步</button>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="modal fade hh-modal" id="uploading-paper-box" tabindex="-1" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog  modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header hh-modal-header">
                <h5 class="modal-title">导入试卷</h5>
            </div>
            <div class="modal-body pt-5 pb-5">
                <div class="text-center mt-5">
                    <img src="/img/home/<USER>">
                </div>
                <div class="text-center mb-5 mt-2 text-999">
                    试卷正在努力解析中，请耐心等待..
                </div>
                <div class="mb-5"></div>
            </div>
        </div>
    </div>
</div>
<?=$this->render('/common/_upload_desc') ?>
<?=$this->render('/common/_upload_xk_desc') ?>
<script>
    var uploadPaperApp = Vue.createApp({
        data(){
            return {
                hasUpload:false,
                hasUploadFile:false,
                uploadFileInfo:{},
                uploadType:'<?=$type?>',
                headers:{ 'X-Csrf-Token': '<?=Yii::$app->request->getCsrfToken()?>'},
                importData: {},
                paperName:'',
                stageCourses:<?=\yii\helpers\Json::encode($teacherStageCourses)?>,
                stage:'<?=!empty($requestData['stage']) ? $requestData['stage'] : $teacherStageCourse['stage']['value']?>',
                course_code:'<?=!empty($requestData['course_code']) ? $requestData['course_code'] : $teacherStageCourse['course']['code']?>',
                form_group:'',
                textbookVersion:'',
                textbook_id:'',
                category_id:'',
                grade:'',
                paperCategoryList:<?=\yii\helpers\Json::encode($paperCategoryList)?>,
                versionList:<?=\yii\helpers\Json::encode($versionList)?>,
                textBookList:<?=\yii\helpers\Json::encode($textBookList)?>,
            }
        },
        computed:{
            courseList:function (){
                var list = [];
                var courseIds = [];
                for(var i in this.stageCourses){
                    var item = this.stageCourses[i];
                    if(courseIds.indexOf(item.course.code) >=0){
                        continue;
                    }
                    courseIds.push(item.course.code);
                    list.push(item.course)
                }
                return list;
            },
            stageList:function (){
                var list = [];
                var courseIds = [];
                for(var i in this.stageCourses){
                    var item = this.stageCourses[i];
                    if(courseIds.indexOf(item.stage.value) >=0){
                        continue;
                    }
                    courseIds.push(item.stage.value);
                    list.push(item.stage)
                }
                return list;
            },
        },
        watch: {
            stage: function () {
                this.category_id = "";
                this.textbook_id = '';
                this.textbookVersion = '';
                this.getBasicInfo();
            },
            course_code: function () {
                this.category_id = "";
                this.textbook_id = '';
                this.textbookVersion = '';
                this.getBasicInfo();
            },
        },
        methods:{
            getBasicInfo:function (){
                var _this = this;
                $.ajax({
                    type: 'post',
                    url: '<?=Url::toRoute('custom-paper/basic-info')?>',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        stage: this.stage,
                        courseCode: this.course_code,
                    }),
                    success: function (data) {
                        if(data.code == 1){
                            _this.paperCategoryList = data.data.categoryList;
                            _this.versionList = data.data.versionList;
                            _this.textBookList = data.data.textBookList;
                        }else{
                            toastr.error(data.msg, '错误提示!', {positionClass: 'toast-center'})
                        }
                    },
                    error:function (data){
                        toastr.error(data.responseText, '错误提示!', {positionClass: 'toast-center'})
                    }
                });
            },
            showUploadDesc:function (){
                $("#upload_page_desc_box").modal('show')
            },
            showUploadXkDesc:function (){
                $("#upload_page_xk_desc_box").modal('show')
            },
            handleChange:function (uploadFile){
                console.log(uploadFile)
                const fileName = uploadFile.name;
                const extension = fileName.split('.').pop(); // 获取扩展名
                <?php if($school->enable_paper_ocr_import): ?>
                if(extension != 'docx' && extension != 'pdf'){
                    toastr.error('请选择docx或者pdf文件上传', '错误提示!', {positionClass: 'toast-center'})
                    this.hasUploadFile = false;
                    this.hasUpload = false;
                    return;
                }
                <?php else: ?>
                if(extension != 'docx'){
                    toastr.error('请选择docx文件上传', '错误提示!', {positionClass: 'toast-center'})
                    this.hasUploadFile = false;
                    this.hasUpload = false;
                    return;
                }
                <?php endif; ?>    
                const isLt500K = uploadFile.size / 1024 / 1024 < 1024 * 1024 * 10;
                if (!isLt500K) {
                    toastr.error('上传文件大小不能超过 10M!', '错误提示!', {positionClass: 'toast-center'})
                    this.hasUploadFile = false;
                    this.hasUpload = false;
                    return;
                }
                this.hasUploadFile = true;
                this.hasUpload = true;
                const fileNameWithoutExtension = fileName.split('.').slice(0, -1).join('.');
                this.paperName = fileNameWithoutExtension;
                this.uploadFileInfo = {fileName:fileNameWithoutExtension}
            },
            uploadPaper:function (){
                if(!this.hasUploadFile){
                    toastr.error('请选择要上传的文件', '错误提示!', {positionClass: 'toast-center'})
                    return;
                }
                if(this.paperName == ''){
                    toastr.error('请输入试卷名称', '错误提示!', {positionClass: 'toast-center'})
                    return;
                }
                const uploadRef = this.$refs.uploadRef;
                this.importData= {category_id:this.category_id,paperName:this.paperName,stage:this.stage,grade:this.grade,course_code: this.course_code,from_group:this.uploadType,textbook_version_id:this.textbookVersion,textbook_id:this.textbook_id}

                $("#upload-paper-box").modal('hide')
                $("#uploading-paper-box").modal('show')

                uploadRef.submit(); // 手动触发上传
            },

            beforeUpload:function (){
            },
            handleImportError:function (error){
                toastr.error('解析失败', '错误提示!', {positionClass: 'toast-center'})
                $("#uploading-paper-box").modal('hide')
                console.log(error)
            },
            handleImportSuccess:function(response) {
                console.log(response)
                if(response.code == 1){
                    toastr.success('解析完成', '成功', {positionClass: 'toast-center'})
                    $("#uploading-paper-box").modal('hide')
                    // window.location.reload();
                    window.location.href= response.data.url; 
                }else{
                    toastr.error(response.msg, '错误提示!', {positionClass: 'toast-center'})
                    $("#uploading-paper-box").modal('hide')
                }
            },
            showProp:function(){
                this.hasUpload = false;
                this.grade = '';
                $("#upload-paper-box").modal('show')
            }
        },
    }).use(ElementPlus, {
            locale: ElementPlusLocaleZhCn,
        }).mount("#upload-paper-box");
</script>

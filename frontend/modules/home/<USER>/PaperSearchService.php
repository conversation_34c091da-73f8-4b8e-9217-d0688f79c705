<?php

namespace frontend\modules\home\services;

use common\enums\PaperContentSplitType;
use common\enums\PaperFromGroup;
use common\enums\PaperStatus;
use common\enums\RecentTimes;
use common\models\base\CityArea;
use common\models\user\MyPaper;
use common\services\YearService;
use frontend\modules\api\models\paper\Paper;
use frontend\modules\api\models\paper\PaperCategory;
use yii\base\BaseObject;

class PaperSearchService extends BaseObject
{
    /**
     * 根据学段和科目 获取试卷类型
     * @return array
     */
    public static function getPaperCategoryList(int $stage,string $courseCode) : array
    {
        $categoryList = PaperCategory::find()->shown()->andWhere([
            'stage' => $stage,
            'course_code' => $courseCode
        ])->orderIdDesc()->all();
        return $categoryList;
    }

    public static function getSearchAreaList()
    {
        return  CityArea::find()->active()->provinces()->all();
    }


    public static function searchPremiumPaper($stage,$courseCode,$versionId,$requestData) : array
    {
        $query = Paper::find()->online()->fromGroup(PaperFromGroup::BestPaper->value);
        if(!empty($stage)){
            $query->andWhere(['stage' => $stage]);
        }
        //todo 临时注释处理
        if(!empty($courseCode)){
            $query->andWhere(['course_code' => $courseCode]);
        }
//        if(!empty($versionId)){
//            $query->andWhere(['textbook_version_id' => $versionId]);
//        }
        if(!empty($requestData['year'])){
            $realYear = YearService::getRealYear($requestData['year']);
            if($requestData['year'] == YearService::MORN_YEAR){
                $query->andWhere(['<=','year' , $realYear]);
            }else{
                $query->andWhere(['year' => $realYear]);
            }
        }
        if(!empty($requestData['area_code'])){
            $query->andWhere(['province_code' => $requestData['area_code']]);
        }
        if(!empty($requestData['category_id'])){
            $query->andWhere(['category_id' => $requestData['category_id']]);
        }
        if(!empty($requestData['grade'])){
            $query->andWhere(['grade' => $requestData['grade']]);
        }
        if(!empty($requestData['semester'])){
            $query->andWhere(['semester' => $requestData['semester']]);
        }
        if(!empty($requestData['keyword'])){
            $query->andWhere(['like', 'title', $requestData['keyword']]);
        }


        $totalCount = $query->count();
        if(!empty($requestData['order'])){
            if($requestData['order'] == 'time'){
                $query->orderBy(['updated_at' => SORT_DESC]);
            }elseif($requestData['order'] == 'upload'){
                $query->orderBy(['download_count' => SORT_DESC]);
            }
        }else{
            $query->orderBy(['updated_at' => SORT_DESC]);
        }
        $page = !empty($requestData['page']) ? $requestData['page'] : 1;
        $pageSize = !empty($requestData['pageSize']) ? $requestData['pageSize'] : 10;
        $offset = ($page - 1) * $pageSize;
        $pageList = $query->offset($offset)->limit($pageSize)->all();
//        echo $query->createCommand()->getRawSql();
        $list = [];
        foreach ($pageList as $item){
            $list[] = self::paperToArray($item);
        }


        return [
            'totalCount' => $totalCount,
            'currentPage' => $page,
            'pageSize' => $pageSize,
            'list' => $list,
            'totalPage' => ceil($totalCount / $pageSize),
        ];
    }

    public static function searchPremiumBookPaper($stage,$courseCode,$bookId,$requestData) : array
    {
        $query = Paper::find()->online();
        if(!empty($stage)){
            $query->andWhere(['stage' => $stage]);
        }
        //todo 临时注释处理
        if(!empty($courseCode)){
            $query->andWhere(['course_code' => $courseCode]);
        }
        if(!empty($bookId)){
            $query->andWhere(['book_id' => $bookId]);
        }
        if(!empty($requestData['order'])){
            if($requestData['order'] == 'time'){
                $query->orderBy(['updated_at' => SORT_DESC]);
            }elseif($requestData['order'] == 'upload'){
                $query->orderBy(['download_count' => SORT_DESC]);
            }
        }else{
            $query->orderBy(['order_id' => SORT_ASC]);
        }
        $pageList = $query->all();
        $list = [];
        foreach ($pageList as $item){
            $list[] = self::paperToArray($item);
        }

        return $list;
    }


    public static function paperCardStatus(Paper $paper)
    {
        $cardInfo = $paper->card;
        if(!empty($cardInfo)){

        }
    }

    /**
     * @param Paper $paper
     * @return array
     */
    public static function paperToArray($paper) : array
    {
        $status = $paper->status;
        $paperData = $paper->paperData;
        $info = $paper->toArray();
        //todo 判断是否已经收藏或者下载过
        $info['hasCard'] = $paper->hasCard;
        $info['hasDownload'] = $paper->getHasDownload();
        $info['statusId'] = $status->value;
        $info['wordStatus'] = true;
        if(empty($paperData->word_content)){
            $info['wordStatus'] = false;
        }
        if(!empty($paperData->content_split_type_id) && $paperData->content_split_type_id == PaperContentSplitType::PdfManual->value){
            $info['fileType'] = 'pdf';
        }else{
            $info['fileType'] = 'word';
        }
        $info['statusLabel'] = $status->label();
        $info['card_uuid'] = !empty($paper->card) ? $paper->card->uuid : '';
        $info['word_pics'] = [];
        if(!empty($paperData)){
            $word_pics = $paperData->word_pics;
            if(!empty($word_pics)){
                $info['word_pics'] = $word_pics;
            }
        }

        $info['update_date'] = date('Y-m-d',$paper->updated_at);
        $info['create_date'] = date('Y-m-d',$paper->created_at);
        $info['categoryName'] = !empty($paper->category) ? $paper->category->name : '未设置';
        $info['textbook_name'] =  $paper->textbook_id && $paper->textbook ? $paper->textbook->name : '未设置';
        $info['course_name'] = !empty($paper->course) ? $paper->course->name : '';
        $info['icon'] = !empty($paper->category) ? $paper->category->getPicUrl() :\Yii::$app->params['paperCategory']['icon'];
        return $info;
    }


    public static function searchMyPaper($userId,$stage,$courseCode,$versionId,$requestData):array
    {
        $query = MyPaper::find()->alias('mp')->joinWith('paper as p')
            ->andWhere(['<>', 'p.status', PaperStatus::Deleted->value]);
        //todo 临时注释处理
        $query->andWhere(['mp.user_id' => $userId]);
        if(!empty($requestData['course_code'])){
            $query->andWhere(['p.course_code' => $requestData['course_code']]);
        }elseif(!empty($courseCode)){
            $query->andWhere(['p.course_code' => $courseCode]);
        }

        if(!empty($requestData['versionId'])){
            $query->andWhere(['textbook_version_id' => $requestData['versionId']]);
        }
        if(!empty($requestData['stage'])){
            $query->andWhere(['p.stage' => $requestData['stage']]);
        }elseif(!empty($stage)){
            $query->andWhere(['p.stage' => $stage]);
        }
        if(!empty($requestData['recent_type'])){
            $recentTime = RecentTimes::tryFrom($requestData['recent_type']);
            $dateRange = $recentTime->getDateRange();
        }elseif(!empty($requestData['start_date']) && !empty($requestData['end_date'])){
            $dateRange = ['startDate' => $requestData['start_date'],'endDate' => $requestData['end_date']];
        }
        if(!empty($dateRange)){
            $query->andWhere(['between', 'mp.created_at', strtotime($dateRange['startDate']), strtotime($dateRange['endDate'].' 23:59:59')]);
        }
        if(!empty($requestData['grade'])){
            $query->andWhere(['p.grade' => $requestData['grade']]);
        }
        if(!empty($requestData['from_group'])){
            $query->andWhere(['p.from_group' => $requestData['from_group']]);
        }
        if(!empty($requestData['category_id'])){
            $query->andWhere(['p.category_id' => $requestData['category_id']]);
        }
        if(!empty($requestData['textbook_id'])){
            $query->andWhere(['p.textbook_id' => $requestData['textbook_id']]);
        }
        if(!empty($requestData['keyword'])){
            $query->andWhere(['like', 'p.title', $requestData['keyword']]);
        }
        $totalCount = $query->count();

        $query->orderBy(['mp.created_at' => SORT_DESC]);
        $page = !empty($requestData['page']) ? $requestData['page'] : 1;
        $pageSize = !empty($requestData['pageSize']) ? $requestData['pageSize'] : 10;
        $offset = ($page - 1) * $pageSize;
        $pageList = $query->offset($offset)->limit($pageSize)->all();
        $list = [];
        foreach ($pageList as $item){
            $paper = $item->paper;
            $paperInfo = self::paperToArray($paper);
            $paperInfo['create_date'] = date('Y-m-d',$item->created_at);
            $paperInfo['source_id'] = $item->source_id;
            $list[] = $paperInfo;
        }



        return [
            'totalCount' => $totalCount,
            'currentPage' => $page,
            'pageSize' => $pageSize,
            'list' => $list,
            'totalPage' => ceil($totalCount / $pageSize),
        ];
    }



    public static function searchThirdPaper($useId,$stage,$courseCode,$versionId,$requestData) : array
    {
        $query = Paper::find()->notIsBasket()->andWhere(['<>','status', PaperStatus::Deleted->value]);
        //todo 临时注释处理
        $query->fromGroup(PaperFromGroup::XueKeWang->value);
        $query->andWhere(['created_by' => $useId]);
        if(!empty($requestData['course_code'])){
            $query->andWhere(['course_code' => $requestData['course_code']]);
        }elseif(!empty($courseCode)){
            $query->andWhere(['course_code' => $courseCode]);
        }
        if(!empty($versionId)){
            $query->andWhere(['textbook_version_id' => $versionId]);
        }

        if(!empty($requestData['stage'])){
            $query->andWhere(['stage' => $requestData['stage']]);
        }elseif(!empty($stage)){
            $query->andWhere(['stage' => $stage]);
        }
        if(!empty($requestData['recent_type'])){
            $recentTime = RecentTimes::tryFrom($requestData['recent_type']);
            $dateRange = $recentTime->getDateRange();
        }elseif(!empty($requestData['start_date']) && !empty($requestData['end_date'])){
            $dateRange = ['startDate' => $requestData['start_date'],'endDate' => $requestData['end_date']];
        }
        if(!empty($dateRange)){
            $query->andWhere(['between', 'created_at', strtotime($dateRange['startDate']), strtotime($dateRange['endDate'].' 23:59:59')]);
        }
        if(!empty($requestData['grade'])){
            $query->andWhere(['grade' => $requestData['grade']]);
        }
        if(!empty($requestData['category_id'])){
            $query->andWhere(['category_id' => $requestData['category_id']]);
        }
        if(!empty($requestData['textbook_id'])){
            $query->andWhere(['textbook_id' => $requestData['textbook_id']]);
        }
        if(!empty($requestData['keyword'])){
            $query->andWhere(['like', 'title', $requestData['keyword']]);
        }
        $totalCount = $query->count();
        if(!empty($requestData['order'])){
            if($requestData['order'] == 'time'){
                $query->orderBy(['created_at' => SORT_DESC]);
            }elseif($requestData['order'] == 'upload'){
                $query->orderBy(['download_count' => SORT_DESC]);
            }
        }else{
            $query->orderBy(['created_at' => SORT_DESC]);
        }
        $page = !empty($requestData['page']) ? $requestData['page'] : 1;
        $pageSize = !empty($requestData['pageSize']) ? $requestData['pageSize'] : 10;
        $offset = ($page - 1) * $pageSize;
        $pageList = $query->offset($offset)->limit($pageSize)->all();
        $list = [];
        foreach ($pageList as $item){
            $paperInfo = self::paperToArray($item);
            $list[] = $paperInfo;
        }



        return [
            'totalCount' => $totalCount,
            'currentPage' => $page,
            'pageSize' => $pageSize,
            'list' => $list,
            'totalPage' => ceil($totalCount / $pageSize),
        ];
    }



    public static function searchPersonPaper($useId,$stage,$courseCode,$versionId,$requestData) : array
    {
        $query = Paper::find()->andWhere(['<>', 'status', PaperStatus::Deleted->value])->notIsBasket();
        //todo 临时注释处理
        $query->fromGroup(PaperFromGroup::PersonalUpload->value);
        $query->andWhere(['created_by' => $useId]);
        if(!empty($requestData['course_code'])){
            $query->andWhere(['course_code' => $requestData['course_code']]);
        }elseif(!empty($courseCode)){
            $query->andWhere(['course_code' => $courseCode]);
        }
        if(!empty($requestData['versionId'])){
            $query->andWhere(['textbook_version_id' => $requestData['versionId']]);
        }

        if(!empty($requestData['stage'])){
            $query->andWhere(['stage' => $requestData['stage']]);
        }elseif(!empty($stage)){
            $query->andWhere(['stage' => $stage]);
        }
        if(!empty($requestData['recent_type'])){
            $recentTime = RecentTimes::tryFrom($requestData['recent_type']);
            $dateRange = $recentTime->getDateRange();
        }elseif(!empty($requestData['start_date']) && !empty($requestData['end_date'])){
            $dateRange = ['startDate' => $requestData['start_date'],'endDate' => $requestData['end_date']];
        }
        if(!empty($dateRange)){
            $query->andWhere(['between', 'created_at', strtotime($dateRange['startDate']), strtotime($dateRange['endDate'].' 23:59:59')]);
        }
        if(!empty($requestData['grade'])){
            $query->andWhere(['grade' => $requestData['grade']]);
        }
        if(!empty($requestData['category_id'])){
            $query->andWhere(['category_id' => $requestData['category_id']]);
        }
        if(!empty($requestData['textbook_id'])){
            $query->andWhere(['textbook_id' => $requestData['textbook_id']]);
        }
        if(!empty($requestData['keyword'])){
            $query->andWhere(['like', 'title', $requestData['keyword']]);
        }
        $totalCount = $query->count();
        if(!empty($requestData['order'])){
            if($requestData['order'] == 'time'){
                $query->orderBy(['created_at' => SORT_DESC]);
            }elseif($requestData['order'] == 'upload'){
                $query->orderBy(['download_count' => SORT_DESC]);
            }
        }else{
            $query->orderBy(['created_at' => SORT_DESC]);
        }
        $page = !empty($requestData['page']) ? $requestData['page'] : 1;
        $pageSize = !empty($requestData['pageSize']) ? $requestData['pageSize'] : 10;
        $offset = ($page - 1) * $pageSize;
        $pageList = $query->offset($offset)->limit($pageSize)->all();
        $list = [];
        foreach ($pageList as $item){
            $paperInfo = self::paperToArray($item);
            $list[] = $paperInfo;
        }



        return [
            'totalCount' => $totalCount,
            'currentPage' => $page,
            'pageSize' => $pageSize,
            'list' => $list,
            'totalPage' => ceil($totalCount / $pageSize),
        ];
    }

    public function buildQuery($requestData,$query)
    {

    }

}

<?php

namespace frontend\modules\home\services;

use common\enums\CardContentType;
use common\enums\CardStructType;
use common\models\exam\Exam;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use yii\base\BaseObject;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;
use yii\helpers\Url;

class ExcelService extends BaseObject
{
    public static function exportStudentScore($examId)
    {
        $examInfo = ExamReportService::getExamInfo($examId);
        $cardInfo = Json::decode($examInfo->card_info);
        if(!empty($cardInfo['contentTypeId']) && $cardInfo['contentTypeId'] == CardContentType::ChineseWriting->value
            && !empty($examInfo->ai_result_stats)){
            self::exportChineseWritingStudent($examInfo);
        }elseif(!empty($cardInfo['contentTypeId']) && $cardInfo['contentTypeId'] == CardContentType::EnglishListening->value
            && !empty($examInfo->ai_result_stats)){
            self::exportChineseWritingStudent($examInfo);
        }

        $studentData = Json::decode($examInfo->student_data);

        $structInfo = Json::decode($examInfo->card_struct_info);

        $structList = StudentBlockService::handleStruct($structInfo);

        [$objFullScore, $objList,$subFullScore,$subList,$allList] = ExamReportService::getStudentStructScores($structList);

        $studentList = [];
        $sortData = [];
        foreach ($studentData as $student) {
            if(!empty($student['absent']) || !isset($student['score'])){
                continue;
            }
            $objScore = $subScore = 0;
            foreach ($allList as $obj) {
                $structScore = 0;
                $info = [];
                if(isset($student['card_struct_stats'][$obj['id']]['score'])){
                    $structScore = $student['card_struct_stats'][$obj['id']]['score'];
                }

                if($obj['typeId'] == CardStructType::Subjective->value){
                    $subScore += $structScore;
                    $info['answer'] = !empty($student['card_struct_stats'][$obj['id']]['answer']) ? $student['card_struct_stats'][$obj['id']]['answer'] : '';
                }else{
                    $objScore += $structScore;
                    $info['answer'] = !empty($student['card_struct_stats'][$obj['id']]['answer']) ? $student['card_struct_stats'][$obj['id']]['answer'] : '';
                }
                $info['score'] = $structScore;
                $student['structs'][$obj['id']] = $info;
            } 
            $student['objScore'] = $objScore;
            $student['subScore'] = $subScore;
            $studentList[$student['student_id']] = $student;
            $sortData[$student['student_id']] = $student['ranking'];
        }
        asort($sortData);
        $cardInfo = Json::decode($examInfo->card_info);
        if($cardInfo['contentTypeId'] == CardContentType::EnglishListening->value){
            self::exportEnglishStudentExcel($examInfo,$studentList,$allList,$objFullScore,$subFullScore);
        }else{
            $students = [];
            foreach($sortData as $studentId => $rank){
                $students[] =  $studentList[$studentId];
            }
            self::exportStudentExcel($examInfo,$students,$allList,$objFullScore,$subFullScore);
        }


    }

    public  static function exportChineseWritingStudent(Exam $examInfo)
    {
        $aiResultStats = Json::decode($examInfo->ai_result_stats);
        $studentData = Json::decode($examInfo->student_data);
        $cardInfo = Json::decode($examInfo->card_info);
        $isEnglish = false;
        if($cardInfo['contentTypeId'] == CardContentType::EnglishWriting->value){
            $isEnglish = true;
        }
        $dimensionNames = $aiResultStats['dimensionNames'] ?? [];
        $studentErrors = $aiResultStats['studentErrors'] ?? [];

        $studentList = [];
        $sortData = [];
        foreach ($studentData as $student) {
            if(!empty($student['absent']) || !isset($student['score'])){
                continue;
            }
            $studentError = !empty($studentErrors[$student['student_id']]) ? $studentErrors[$student['student_id']] : [];
            foreach($student['card_struct_stats'] as $struct){
                if(!empty($struct['ai_result']['feedback']['dimensions'])){
                    foreach($struct['ai_result']['feedback']['dimensions'] as $dimension){
                        $student[$dimension['dimenName']] = $dimension['dimenScore'];
                    }
                }
                $student['wordCount'] = !empty($struct['ai_result']['feedback']['wordCount']) ? $struct['ai_result']['feedback']['wordCount']:0;
            }
            $student['error'] = $studentError;

            $student['actionUrl'] = Url::toRoute(['report/student','exam_id' => $examInfo->id,'student_id' => $student['student_id']]);
            foreach($dimensionNames as $dimensionName){
                if(!isset($student[$dimensionName])){
                    $student[$dimensionName] = 0;
                }
            }
            $studentList[$student['student_id']] = $student;
            $sortData[$student['student_id']] = $student['ranking'];
        }
        asort($sortData);
        self::exportChineseWritingStudentExcel($examInfo,$aiResultStats['dimensionNames'],$studentList,$sortData,$isEnglish);
    }

    public static function exportChineseWritingStudentExcel(Exam $examInfo, array $dimensionNames,array $studentList,array $sortData,bool $isEnglish)
    {
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        $row = 1;
        $sheet->mergeCells([1,$row,1,$row+1])->setCellValue([1,$row],'考号');
        $sheet->mergeCells([2,$row,2,$row+1])->setCellValue([2,$row],'排名');
        $sheet->mergeCells([3,$row,3,$row+1])->setCellValue([3,$row],'姓名');
        $sheet->mergeCells([4,$row,4,$row+1])->setCellValue([4,$row],'得分');
        $sheet->mergeCells([5,$row,5,$row+1])->setCellValue([5,$row],'字数');
        $column = 6;
        if(!$isEnglish){
            $sheet->mergeCells([$column,$row,$column+count($dimensionNames)-1,$row])->setCellValue([$column,$row],'维度得分');
            $column = $column + count($dimensionNames);
        }
        $sheet->mergeCells([$column,$row,$column,$row+1])->setCellValue([$column,$row],'严重错误');
        $column = 6;
        $row ++;
        foreach($dimensionNames as $dimensionName){
            $sheet->setCellValue([$column,$row],$dimensionName);
            $column ++;
        }
        $row ++;
        foreach($sortData as $studentId => $rank){
            $student =  $studentList[$studentId];
            $sheet->setCellValue([1,$row],$student['exam_number']);
            $sheet->setCellValue([2,$row],$student['ranking']);
            $sheet->setCellValue([3,$row],$student['student_name']);
            $sheet->setCellValue([4,$row],$student['score']);
            $sheet->setCellValue([5,$row],$student['wordCount']);

            $column = 6;

            if(!$isEnglish){
                foreach($dimensionNames as $dimensionName){
                    $sheet->setCellValue([$column,$row],$student[$dimensionName] ?? 0);
                    $column ++;
                }
            }
            $error = '';
            foreach($student['error'] as $item){
                if(!empty($error)){
                    $error .= "  ";
                }
                $error .= $item['label'];
            }
            $sheet->setCellValue([$column,$row],$error);
            $row ++;
        }
        // 设置整个表格的样式
        $styleArray = [
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER, // 水平居中
                'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER, // 垂直居中
            ],
        ];

// 获取数据范围
        $highestRow = $sheet->getHighestRow();
        $highestColumn = $sheet->getHighestColumn();

// 应用样式到整个表格
        $sheet->getStyle('A1:' . $highestColumn . $highestRow)->applyFromArray($styleArray);
        $title = $examInfo->title;
        if(empty($title)){
            $cardInfo = Json::decode($examInfo->card_info);
            $title = $cardInfo['title'];
        }
        if(strpos($title,"\n") !== false){
            $title = str_replace("\n","",$title);
        }
        $fileName = $title.'.xlsx';
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');

        header('Content-Disposition: attachment;filename="'. $fileName .'"');

        header('Cache-Control: max-age=0');

        header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT'); // always modified

        header('Cache-Control: cache, must-revalidate'); // HTTP/1.1

        header('Pragma: public'); // HTTP/1.0

        $writer = new Xlsx($spreadsheet);

        $writer->save('php://output');

        exit;
    }



    public static function exportEnglishQuestionScore($examId)
    {
        $examInfo = ExamReportService::getExamInfo($examId);
        $structInfo = Json::decode($examInfo->card_struct_info);
        $structStatsData = Json::decode($examInfo->card_struct_data);

        $structList = StudentBlockService::handleStruct($structInfo);
        $structStatsData =  ArrayHelper::index($structStatsData,'struct_id');
        $data = [];
        foreach ($structList as $structId => &$structInfo) {
            $statsData = !empty($structStatsData[$structId]) ? $structStatsData[$structId] : [];
            $structInfo['class_rate'] = !empty($statsData['score_rate']) ? $statsData['score_rate'] : 0;
            $structInfo['average_score'] = !empty($statsData['average_score']) ? $statsData['average_score'] : 0;
            $wrong_rate = !empty($statsData['answer_stats']['zeroPercent']) ? $statsData['answer_stats']['zeroPercent'] : 0;
            $structInfo['wrong_rate'] = $wrong_rate;
            $structInfo['wrong_count'] = !empty($statsData['wrong_count']) ? $statsData['wrong_count'] : 0;
        }
        self::exportEnglishQuestionExcel($examInfo,$structList);

    }


    public static function exportQuestionScore($examId)
    {
        $examInfo = ExamReportService::getExamInfo($examId);
        $structInfo = Json::decode($examInfo->card_struct_info);
        $structStatsData = Json::decode($examInfo->card_struct_data);

        $structList = StudentBlockService::handleStruct($structInfo);
        $structStatsData =  ArrayHelper::index($structStatsData,'struct_id');
        $data = [];
        foreach ($structList as $structId => &$structInfo) {
            $statsData = !empty($structStatsData[$structId]) ? $structStatsData[$structId] : [];
            $structInfo['class_rate'] = !empty($statsData['score_rate']) ? $statsData['score_rate'] : 0;
            $structInfo['average_score'] = !empty($statsData['average_score']) ? $statsData['average_score'] : 0;
        }
        $schoolData = ExamQuestionService::getSchoolData($examId);
        self::exportQuestionExcel($examInfo,$structList,$schoolData);

    }

    public static function exportEnglishQuestionExcel($examInfo,$structList)
    {
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->setCellValue([1,1],'单词');
        $sheet->setCellValue([2,1],'错误数');
        $sheet->setCellValue([3,1],'单词');
        $sheet->setCellValue([4,1],'错误数');
        $row = 2;
        $num = 0;
        $column = 1;
        foreach ($structList as $struct) {
            $sheet->setCellValue([$column,$row],$struct['answer']);
            $column ++;
            $sheet->setCellValue([$column,$row],$struct['wrong_count']);
            $column ++;

            $num ++;
            if($num == 2){
                $row ++;
                $column = 1;
                $num = 0;
            }
        }

        // 设置整个表格的样式
        $styleArray = [
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER, // 水平居中
                'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER, // 垂直居中
            ],
        ];

// 获取数据范围
        $highestRow = $sheet->getHighestRow();
        $highestColumn = $sheet->getHighestColumn();

// 应用样式到整个表格
        $sheet->getStyle('A1:' . $highestColumn . $highestRow)->applyFromArray($styleArray);
        $title = $examInfo->title;
        if(empty($title)){
            $cardInfo = Json::decode($examInfo->card_info);
            $title = $cardInfo['title'];
        }
        if(strpos($title,"\n") !== false){
            $title = str_replace("\n","",$title);
        }
        $fileName = $title.'.xlsx';
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');

        header('Content-Disposition: attachment;filename="'. $fileName .'"');

        header('Cache-Control: max-age=0');

        header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT'); // always modified

        header('Cache-Control: cache, must-revalidate'); // HTTP/1.1

        header('Pragma: public'); // HTTP/1.0

        $writer = new Xlsx($spreadsheet);

        $writer->save('php://output');

        exit;


    }


    public static function exportQuestionExcel($examInfo,$structList,$schoolData)
    {
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->mergeCells([1, 1, 2, 1]);
        $sheet->setCellValue([1,1],'题号');
        $sheet->mergeCells([1, 2, 1, 3])->setCellValue([1,2],'班级');
        $sheet->mergeCells([1, 4, 1, 5])->setCellValue([1,4],'学校');
        $sheet->setCellValue([2,2],'得分率')->setCellValue([2,3],'平均分');
        $sheet->setCellValue([2,4],'得分率')->setCellValue([2,5],'平均分');
        $column = 3;
        $row = 1;
        foreach ($structList as $struct) {
            $sheet->setCellValue([$column,$row],$struct['title']);
            $sheet->setCellValue([$column,$row+1],$struct['class_rate'].'%');
            $sheet->setCellValue([$column,$row+2],$struct['average_score']);
            $column ++;
        }

        $column = 3;
        $row = 4;
        foreach ($schoolData as $struct) {
            $sheet->setCellValue([$column,$row],$struct['score_rate'].'%');
            $sheet->setCellValue([$column,$row+1],$struct['average_score']);
            $column ++;
        }

        // 设置整个表格的样式
        $styleArray = [
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER, // 水平居中
                'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER, // 垂直居中
            ],
        ];

// 获取数据范围
        $highestRow = $sheet->getHighestRow();
        $highestColumn = $sheet->getHighestColumn();

// 应用样式到整个表格
        $sheet->getStyle('A1:' . $highestColumn . $highestRow)->applyFromArray($styleArray);
        $title = $examInfo->title;
        if(empty($title)){
            $cardInfo = Json::decode($examInfo->card_info);
            $title = $cardInfo['title'];
        }
        if(strpos($title,"\n") !== false){
            $title = str_replace("\n","",$title);
        }
        $fileName = $title.'.xlsx';
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');

        header('Content-Disposition: attachment;filename="'. $fileName .'"');

        header('Cache-Control: max-age=0');

        header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT'); // always modified

        header('Cache-Control: cache, must-revalidate'); // HTTP/1.1

        header('Pragma: public'); // HTTP/1.0

        $writer = new Xlsx($spreadsheet);

        $writer->save('php://output');

        exit;


    }

    public static function exportStudentExcel(Exam $examInfo,$studentData,$structList,$objFullScore,$subFullScore)
    {
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        $classInfo = Json::decode($examInfo->class_info);
        $row = 1;
        $sheet->mergeCells([1, $row, 8, $row]);
        $sheet->setCellValue([1,$row],$examInfo->title.'-'.$classInfo['name']);

        $row ++;
        $sheet->mergeCells([1, $row, 8, $row]);
        $sheet->setCellValue([1,$row],'题卡ID：'.$examInfo->card_id.' 创建时间：'.date("Y-m-d",$examInfo->created_at));

        $row ++;
        $sheet->mergeCells([1, $row, 8, $row]);
        $sheet->setCellValue([1,$row],'学生成绩');
        $sheet->mergeCells([9,$row,9+count($structList)-1,$row])->setCellValue([9,$row],'题目作答情况');
      
        $row ++;
        $sheet->mergeCells([1,$row,1,$row+1])->setCellValue([1,$row],'考号')
            ->mergeCells([2,$row,2,$row+1])->setCellValue([2,$row],'排名')
            ->mergeCells([3,$row,3,$row+1])->setCellValue([3,$row],'姓名')
            ->mergeCells([4,$row,4,$row+1])->setCellValue([4,$row],'总分')
            ->mergeCells([5,$row,6,$row])->setCellValue([5,$row],'客观题')
            ->mergeCells([7,$row,8,$row])->setCellValue([7,$row],'主观题')
            ->setCellValue([5,$row+1],'满分')
            ->setCellValue([6,$row+1],'得分')
            ->setCellValue([7,$row+1],'满分')
            ->setCellValue([8,$row+1],'得分');
        $column = 9;
        foreach ($structList as $struct) {
            $sheet->getColumnDimensionByColumn($column)->setWidth(6.5);
            $sheet->setCellValue([$column,$row],$struct['title']);
            if($struct['typeId'] == CardStructType::Subjective->value){
                $sheet->setCellValue([$column,$row+1],$struct['full_score']);
            }else{
                $sheet->setCellValue([$column,$row+1],$struct['answer']);
            }
            $column ++;
        }

        $row +=2;
        foreach ($studentData as $student) {
            $sheet->setCellValue([1,$row],$student['exam_number'])
                ->setCellValue([2,$row],$student['ranking'])
                ->setCellValue([3,$row],$student['student_name'])
                ->setCellValue([4,$row],$student['score'])
                ->setCellValue([5,$row],$objFullScore)
                ->setCellValue([6,$row],$student['objScore'])
                ->setCellValue([7,$row],$subFullScore)
                ->setCellValue([8,$row],$student['subScore']);
            $column = 9;
            foreach ($structList as $struct) {
                $studentStruct = !empty($student['structs'][$struct['id']]) ? $student['structs'][$struct['id']] : [];
                $score = !empty($studentStruct) ? $studentStruct['score']:0;
                $answer = !empty($studentStruct) ? $studentStruct['answer']:'';
                $color = '';
                if($struct['typeId'] == CardStructType::Subjective->value){
                    $sheet->setCellValue([$column,$row],$score);
                    if($score == 0){
                        $color = 'EF474A';
                    }else if($score < $struct['full_score']){
                        $color = 'FF9500';
                    }
                }else{
                    $sheet->setCellValue([$column,$row],$answer);
                    if(strtoupper($answer) != strtoupper($struct['answer'])){
                        $color = 'EF474A';
                    }
                }
                if(!empty($color)){
                    $sheet->getCell([$column,$row])->getStyle()->getFont()->getColor()->setRGB($color);
                }
                $column ++;
            }

            $row ++;
        }

        // 设置整个表格的样式
        $styleArray = [
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER, // 水平居中
                'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER, // 垂直居中
            ],
        ];

// 获取数据范围
        $highestRow = $sheet->getHighestRow();
        $highestColumn = $sheet->getHighestColumn();

// 应用样式到整个表格
        $sheet->getStyle('A1:' . $highestColumn . $highestRow)->applyFromArray($styleArray);
        $title = $examInfo->title;
        if(empty($title)){
            $cardInfo = Json::decode($examInfo->card_info);
            $title = $cardInfo['title'];
        }
        if(strpos($title,"\n") !== false){
            $title = str_replace("\n","",$title);
        }
        $fileName = $title.'.xlsx';
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');

        header('Content-Disposition: attachment;filename="'. $fileName .'"');

        header('Cache-Control: max-age=0');

        header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT'); // always modified

        header('Cache-Control: cache, must-revalidate'); // HTTP/1.1

        header('Pragma: public'); // HTTP/1.0

        $writer = new Xlsx($spreadsheet);

        $writer->save('php://output');

        exit;


    }

    public static function exportEnglishStudentExcel(Exam $examInfo,$studentData,$structList,$objFullScore,$subFullScore)
    {
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        $classInfo = Json::decode($examInfo->class_info);
        $row = 1;
        $sheet->mergeCells([1, $row, 5, $row]);
        $sheet->setCellValue([1,$row],$examInfo->title.'-'.$classInfo['name']);

        $row ++;
        $sheet->mergeCells([1, $row, 5, $row]);
        $sheet->setCellValue([1,$row],'题卡ID：'.$examInfo->card_id.' 创建时间：'.date("Y-m-d",$examInfo->created_at));

        $row ++; 
        $sheet->setCellValue([1,$row],'考号')
             ->setCellValue([2,$row],'排名')
             ->setCellValue([3,$row],'姓名')
            ->setCellValue([4,$row],'满分')
            ->setCellValue([5,$row],'总分');

        $row ++; 
        foreach ($studentData as $student) {
            $sheet->setCellValue([1,$row],$student['exam_number'])
                ->setCellValue([2,$row],$student['ranking'])
                ->setCellValue([3,$row],$student['student_name'])
                ->setCellValue([4,$row],$subFullScore)
                ->setCellValue([5,$row],$student['score']) ;
            $row ++;
        }

        // 设置整个表格的样式
        $styleArray = [
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER, // 水平居中
                'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER, // 垂直居中
            ],
        ];

// 获取数据范围
        $highestRow = $sheet->getHighestRow();
        $highestColumn = $sheet->getHighestColumn();

// 应用样式到整个表格
        $sheet->getStyle('A1:' . $highestColumn . $highestRow)->applyFromArray($styleArray);
        $title = $examInfo->title;
        if(empty($title)){
            $cardInfo = Json::decode($examInfo->card_info);
            $title = $cardInfo['title'];
        }
        if(strpos($title,"\n") !== false){
            $title = str_replace("\n","",$title);
        }
        $fileName = $title.'.xlsx';
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');

        header('Content-Disposition: attachment;filename="'. $fileName .'"');

        header('Cache-Control: max-age=0');

        header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT'); // always modified

        header('Cache-Control: cache, must-revalidate'); // HTTP/1.1

        header('Pragma: public'); // HTTP/1.0

        $writer = new Xlsx($spreadsheet);

        $writer->save('php://output');

        exit;


    }
}
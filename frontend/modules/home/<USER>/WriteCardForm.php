<?php 
namespace frontend\modules\home\form;

use common\domains\card\CardSetting;
use common\enums\AiDataSyncStatus;
use common\enums\CardContentType;
use common\enums\CardFormatSource;
use common\enums\CardQuestionType;
use common\enums\CourseCode;
use common\enums\Grade;
use common\enums\PaperItemType;
use common\enums\QuestionLogicType;
use common\enums\Stage;
use frontend\modules\api\models\card\Card;
use frontend\modules\api\services\ChineseWritingCardDetailService;
use Ramsey\Uuid\Uuid;
use Yii;
use yii\helpers\Json;

class WriteCardForm extends BaseForm
{
    public $name;
    public $stageId;
    public $grade;
    public $writeType;
    public $fullScore;
    public $minWordNum;
    public $imageUrl;
    public $imageContent;
    public $body;
    public $dimensions;

    public function rules()
    {
        return [
            [['name', 'stageId', 'grade', 'fullScore', 'minWordNum'], 'required'],
            [['name', 'body','imageUrl','imageContent','writeType'], 'string'],
            [['stageId', 'minWordNum'], 'integer'],
            [['fullScore'], 'number'],
            [['writeType'], 'string'],
            [['dimensions'], 'safe'],
        ];
    }

    public function attributeLabels()
    {
        return [
            'name' => '名称',
            'stageId' => '学段',
            'grade' => '年级',
            'writeType' => '体裁',
            'fullScore' => '满分',
            'minWordNum' => '字数',
            'body' => '说明'
        ];
    }

    public function setCardInfo(Card $cardInfo)
    {
        $this->cardInfo = $cardInfo;
    }

    public function getCardInfo()
    {
        return $this->cardInfo;
    }

    public function save()
    {
        if(!empty($this->writeType)){
            $this->addError('writeType','体裁不能为空');
            return false;
        }
        return $this->saveByType(CardContentType::ChineseWriting);
    }


    public function saveByType(CardContentType $cardContentType)
    {
        $tran = Card::getDb()->beginTransaction();
        try{
            $card = new Card();
            $card->stage = Stage::tryFrom($this->stageId);
            $card->grade = Grade::tryFrom($this->grade);
            $card->course_code = $cardContentType->value == CardContentType::ChineseWriting->value ? CourseCode::Chinese->value : CourseCode::English->value;
            $card->title = $this->name;
            $card->content_type = $cardContentType->value;
            $card->format_source = CardFormatSource::AutomaticDefined;
            $card->ai_data_sync_status = AiDataSyncStatus::Synced->value;
            $card->setting = CardSetting::default();
            if(!empty($copyCard)){
                $card->setting = $copyCard->setting;
            }
            if($card->save()){
                $cardInfo = ChineseWritingCardDetailService::buildCardData($card);
                $cardInfo['cardList'] = $this->buildCardList($card);
                $markingData = [
                    'uuid' => $card->uuid,
                    'cardInfo' => $cardInfo,
                ];
                $card->making_data = Json::encode($markingData);
                if($card->save()){
                    $tran->commit();
                    $this->setCardInfo($card);
                    return true;
                }else{
                    $tran->rollBack();
                    $this->addErrors($card->getErrors());
                    return false;
                }
            }else{
                $tran->rollBack();
                $this->addErrors($card->getErrors());
                return false;
            }
        }catch(\Exception $e){
            $tran->rollBack();
            Yii::error($e->getMessage()."\n".$e->getTraceAsString());
            $this->addError('name',$e->getMessage());
            return false;
        }
    }


    public function saveEnglish()
    {
        return $this->saveByType(CardContentType::EnglishWriting);
    }


    public function buildCardList(Card $card)
    {
        $list[] = [ 
            'itemTypeId' => PaperItemType::H1->value,
            'title' => '作文',
            'uuid' => Uuid::uuid7()->toString(), 
            'type' => $card->content_type == CardContentType::ChineseWriting->value ? CardQuestionType::ChineseWriting->value : CardQuestionType::EnglishWriting->value,
        ];

        $list[] = [
            'full_score' => $this->fullScore,
            'itemTypeId' => PaperItemType::Question->value, 
            'type' => 'writing', 
            'uuid' => Uuid::uuid7()->toString(),
            'wordNum' => $card->content_type == CardContentType::ChineseWriting->value ? ($this->minWordNum + 100) : 10,
            'compositionSize' => 22,
            'minWordNum' => $this->minWordNum,
            'wordKing' => 1,
            'topicSortNum' => 1,
            'step_score' => 0.5,
            "bodyFlag" => true,
            'logicTypeId' => $card->content_type == CardContentType::ChineseWriting->value ? QuestionLogicType::ChineseWriting->value : QuestionLogicType::EnglishWriting->value,
            'body' =>  str_replace(['<br>', '<br/>'], "\n", $this->body),
            'topicImage' => $this->imageUrl ?? '',
            'topicImageContent' => $this->imageContent ?? '',
//            'dimensions' => $this->dimensions ?? [],
            'writeType' => $this->writeType,
        ];
        return $list;
    }


}
<?php

namespace frontend\modules\home\controllers;

use common\enums\CardContentType;
use common\models\question\Question;
use frontend\modules\home\services\ChineseWritingReportService;
use frontend\modules\home\services\ExamQuestionService;
use frontend\modules\home\services\ExamReportService;
use frontend\modules\home\services\ExamStudentReportService;
use frontend\modules\home\services\ExamStudentService;
use yii\helpers\Json;
use yii\web\Controller;

class PreviewController extends Controller
{
    public $layout = 'pdf-preview';

    public function actionStudent($examId,$studentId,$type = '',$extendInfo = '')
    {
        
        $examInfo = ExamReportService::getExamInfo($examId);
        $examStudent = ExamStudentReportService::getExamStudentInfo($examId,$studentId)->toArray();
        $cardInfo = Json::decode($examInfo->card_info);
        if(!empty($cardInfo['contentTypeId']) && ($cardInfo['contentTypeId'] == CardContentType::ChineseWriting->value || $cardInfo['contentTypeId'] == CardContentType::EnglishWriting->value) &&
            !empty($examInfo->ai_result_stats)) {
            $isEnglish = false;
            if($cardInfo['contentTypeId'] == CardContentType::EnglishWriting->value){
                $isEnglish = true;
            }
            $data = ChineseWritingReportService::getStudentInfo($examInfo,$studentId,$isEnglish);
            $questionInfo = ChineseWritingReportService::getQuestionInfo($examInfo);
            $view = '';
            if($isEnglish){
                if($type =='pdf'){
                    $view = 'student_english_writing_pdf';
                }else{
                    $view = 'student_english_writing';
                }
            }else{
                if($type =='pdf'){
                    $view = 'student_writing_pdf';
                }else{
                    $view = 'student_writing';
                }
            }
            return $this->render($view, [
                'examInfo' => $examInfo,
                'student_id' => $studentId,
                'studentInfo' => $data['student'],
                'aiResult' => $data['aiResult'],
                'questionInfo' => $questionInfo,
                'levelStandards' => $data['levelStandards'],
            ]);
        }else{
            return $this->render(($type =='pdf'? "student-pdf":'student'),[
                'examInfo' => $examInfo,
                'examStudent' => $examStudent,
                'requestData' => \Yii::$app->request->get()
            ]);
        }


    }


    public function actionStudentTrend($examId,$studentId,$type = '',$startDate,$endDate)
    {
        $examInfo = ExamReportService::getExamInfo($examId);
        $examStudent = ExamStudentReportService::getExamStudentInfo($examId,$studentId)->toArray();
        $view = 'student-trend';
        if($type == 'pdf'){
            $view = 'student-trend-pdf';
        }
        $trendExams = ExamStudentService::getStudentTrends($studentId,$startDate,$endDate,$examInfo->course_code);

        return $this->render($view,[
            'examInfo' => $examInfo,
            'examStudent' => $examStudent,
            'requestData' => \Yii::$app->request->get(),
            'trendExams' => $trendExams,
            'startDate' => $startDate,
            'endDate' => $endDate,
            'courseCode' => $examInfo->course_code,
        ]);
    }


    public function actionQuestion($questionId)
    {
        $question = Question::findOne($questionId);
        if(empty($question)){
            return $this->render('question',[
                'content' => ''
            ]);
        }
        $questionDetail = $question->questionDetail;
        if(empty($questionDetail) || empty($questionDetail->content)){
            return $this->render('question',[
                'content' => ''
            ]);
        }
        $content = ExamQuestionService::getExamQuestionContent(['content' => $questionDetail->getContentArray(),'logic_type' => !empty($question->logic_type) ? $question->logic_type?->value : '']);
        $answers = $analysis = [];
        if(!empty($content['children'])){
            foreach ($content['children'] as $key=> $child) {
                if(!empty($child['answer'])){
                    $answers = array_merge($answers, $child['answer']);
                }
                if(!empty($child['analysis'])){
                    $analysis = array_merge($analysis, $child['analysis']);
                }
            }
        }else{
            if(!empty($content['answer'])){
                $answers = array_merge($answers, $content['answer']);
            }
            if(!empty($content['analysis'])){
                $analysis = array_merge($analysis, $content['analysis']);
            }
        }

        $content['answers'] = $answers;
        $content['analysis'] = $analysis;
        return $this->render('question',[
            'content' => $content
        ]);
    }
}
<?php

namespace frontend\modules\home\controllers;

use common\base\storage\AliyunOssStorage;
use common\components\PageConverter;
use common\enums\CardContentType;
use common\enums\RecentTimes;
use common\services\PaperWordService;
use frontend\modules\api\models\paper\Paper;
use frontend\modules\home\models\ExamStudent;
use frontend\modules\home\services\BasketService;
use frontend\modules\home\services\ChineseWritingReportService;
use frontend\modules\home\services\ExamContrastService;
use frontend\modules\home\services\ExamQuestionService;
use frontend\modules\home\services\ExamReportService;
use frontend\modules\home\services\ExamStudentReportService;
use frontend\modules\home\services\ExamStudentService;
use frontend\modules\home\services\ExcelService;
use frontend\modules\home\services\KnowledgeReportService;
use frontend\modules\home\services\TeacherInfoService;
use frontend\modules\home\services\TestPointReportService;
use frontend\modules\home\services\WrongQuestionService;
use Yii;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;
use yii\helpers\Url;
use yii\web\ServerErrorHttpException;

class ReportController extends BaseWorkController
{


    public function actionIndex($exam_id)
    {
        $cardInfo = Json::decode($this->examInfo->card_info);
        if(!empty($cardInfo['contentTypeId']) && ($cardInfo['contentTypeId'] == CardContentType::ChineseWriting->value || $cardInfo['contentTypeId'] == CardContentType::EnglishWriting->value) && !empty($this->examInfo->ai_result_stats)){
            $this->layout = 'only-report';
            $this->getView()->params['questionInfo'] = ChineseWritingReportService::getQuestionInfo($this->examInfo);
            return $this->render('writing-index', [
                'examInfo' => $this->examInfo
            ]);
        }elseif(!empty($cardInfo['contentTypeId']) && $cardInfo['contentTypeId'] == CardContentType::EnglishListening->value){

            return $this->render('english-index', [
                'examInfo' => $this->examInfo
            ]);
        }else{
            return $this->render('index', [
                'examInfo' => $this->examInfo
            ]);
        }
    }

    public function actionCommit($exam_id)
    {
        $cardInfo = Json::decode($this->examInfo->card_info);
        $basketQuestionIds =  BasketService::getQuestionIds($this->user->id,$cardInfo['stageId'],$cardInfo['courseCode'] ?? $cardInfo['course_code']);
        return $this->render('commit', [
            'examInfo' => $this->examInfo,
            'basketQuestionIds' => $basketQuestionIds,
        ]);
    }



    public function actionClassContrast($exam_id)
    {
        return $this->render('class-contrast', [
            'examInfo' => $this->examInfo,
            'contrastExams' =>  ExamReportService::getContrastExamsByUserId($this->examInfo->id,$this->user->id),
        ]);
    }



    public function actionWrong($exam_id)
    {
        $wrongQuestions = ExamQuestionService::getWrongQuestionByExam($exam_id);
        $questions = ExamReportService::getExamQuestions($exam_id);
        $cardInfo = Json::decode($this->examInfo->card_info);
        $basketQuestionIds =  BasketService::getQuestionIds($this->user->id,$cardInfo['stageId'],$cardInfo['courseCode'] ?? $cardInfo['course_code']);
        $similarQuestionIds = [];
        $myQuestionIds = [];
        if(!empty($wrongQuestions)){
            $wrongQuestionUuids = ArrayHelper::getColumn($wrongQuestions, 'uuid');
            $similarQuestionIds = ExamQuestionService::getSimilarQuestionIds($wrongQuestionUuids);
            $myQuestionIds = ExamQuestionService::getMyQuestionIds($wrongQuestionUuids);
        }


        return $this->render('wrong', [
            'examInfo' => $this->examInfo,
            'hasQuestions' => !empty($questions) ? true:false,
            'wrongQuestions' => $wrongQuestions,
            'basketQuestionIds' => $basketQuestionIds,
            'similarQuestionIds' => $similarQuestionIds,
            'myQuestionIds' => $myQuestionIds,
            'stageId' => $cardInfo['stageId'],
            'courseCode' => $cardInfo['courseCode'] ?? $cardInfo['course_code'],
        ]);
    }


    public function actionExport($exam_id,$student_id)
    {
        /**
         * @var PageConverter $pageConverter
         */
        $pageConverter = Yii::$app->get('pageConverter');
        $data = Yii::$app->request->get();
        $params = ['exam_id','student_id'];
        $url = ['preview/student','type' => 'pdf','examId' => $exam_id,'studentId' => $student_id];
        foreach ($data as $k=>$v){
            if(in_array($k,$params)){
                continue;
            }
            $url[$k] = $v;
        }
        $examStudent = ExamStudentReportService::getExamStudentInfo($this->examInfo->id,$student_id);

        $content = $pageConverter->generatePagePdf(Url::toRoute($url,true));
        $response = Yii::$app->response;
        if(strpos($this->examInfo->title,"\n") !== false){
            $this->examInfo->title = str_replace("\n","",$this->examInfo->title);
        }
        $response->sendContentAsFile($content,$examStudent->student_name.'-'.$this->examInfo->title.'学情报告.pdf');
    }


    public function actionExportTrend($exam_id,$student_id,$startDate,$endDate)
    {
        /**
         * @var PageConverter $pageConverter
         */
        $pageConverter = Yii::$app->get('pageConverter');
        $data = Yii::$app->request->get();
        $params = ['exam_id','student_id'];
        $url = ['preview/student-trend','type' => 'pdf','examId' => $exam_id,'studentId' => $student_id];
        foreach ($data as $k=>$v){
            if(in_array($k,$params)){
                continue;
            }
            $url[$k] = $v;
        }
        $examStudent = ExamStudentReportService::getExamStudentInfo($this->examInfo->id,$student_id);
        $content = $pageConverter->generatePagePdf(Url::toRoute($url,true));
        $response = Yii::$app->response;
        $response->sendContentAsFile($content,$examStudent->student_name.'阶段学情报告.pdf');
    }


    public function actionStageWrong($exam_id)
    {
        $requestData = \Yii::$app->request->get();
        $teacherList = TeacherInfoService::getTeacherClassInfo($this->school,$this->user);
        $data = TeacherInfoService::handelTeacherClassList($teacherList,$requestData);
        $cardInfo = Json::decode($this->examInfo->card_info);
        if(empty($requestData['stage'])){
            $requestData['stage'] = $cardInfo['stageId'];
        }
        if(empty($requestData['grade'])){
            $requestData['grade'] = $data['gradeId'];
        }
        if(empty($requestData['courseCode'])){
            $requestData['courseCode'] = $cardInfo['courseCode'] ?? $cardInfo['course_code'];
        }
        if(empty($requestData['classId'])){
            $requestData['classId'] = $this->examInfo->class_id.'';
        }
        $stageList = ArrayHelper::map($teacherList,'stageId','stageLabel');

        $paperData = WrongQuestionService::getTeacherWrongQuestionList($this->user->id,$requestData);
        $similarQuestionIds = [];
        $myQuestionIds = [];
        if(!empty($paperData['list'])){
            $wrongQuestionUuids = ArrayHelper::getColumn($paperData['list'], 'uuid');
            $similarQuestionIds = ExamQuestionService::getSimilarQuestionIds($wrongQuestionUuids);
            $myQuestionIds = ExamQuestionService::getMyQuestionIds($wrongQuestionUuids);
        }

        $basketQuestionIds =  BasketService::getQuestionIds($this->user->id,$requestData['stage'],$cardInfo['courseCode'] ?? $requestData['courseCode']);

        return $this->render('stage-wrong', [
            'examInfo' => $this->examInfo,
            'stageList' => $stageList,
            'requestData' => $requestData,
            'similarQuestionIds' => $similarQuestionIds,
            'teacherList' => $teacherList,
            'basketQuestionIds' => $basketQuestionIds,
            'myQuestionIds' => $myQuestionIds,
            'gradeList' => $data['gradeList'],
            'courseList' => $data['courseList'],
            'paperList' => $paperData['list'],
            'totalPage' => $paperData['totalPage'],
            'currentPage' => $paperData['currentPage'],
            'totalCount' => intval($paperData['totalCount']),
        ]);
    }


    public function actionStudy($exam_id,$student_id = 0)
    {
        $contrastExams = ExamReportService::getContrastExamsByUserId($this->examInfo->id,$this->user->id,true);
        $examStudentList = ExamStudent::find()->andWhere([
            'exam_id' => $exam_id,
        ])->orderBy(['score' => SORT_DESC])->indexBy('student_id')->asArray()->all();
        $handleStudentList = ExamStudentService::handelStudentList($examStudentList);
        $studentInfo = null;
        if(empty($student_id)){
            if(!empty($handleStudentList)){
                foreach ($handleStudentList as $item){
                    $studentInfo = $item;
                    $student_id = $studentInfo['student_id'];
                    break;
                }
            }
        }elseif(empty($examStudentList[$student_id])){
            throw new ServerErrorHttpException('该学生没参与该次作业');
        }else{
            $studentInfo = $examStudentList[$student_id];
        }

        return $this->render('study', [
            'examInfo' => $this->examInfo,
            'studentList' => array_values($handleStudentList),
            'student_id' => $student_id,
            'studentInfo' => $studentInfo,
            'gradeClassList' => ExamReportService::getGradeContrastExams($contrastExams),
            'contrastExams' => $contrastExams,
        ]);
    }


    public function actionStudent($exam_id,$student_id = 0)
    {
        $examStudentList = ExamStudent::find()->andWhere([
            'exam_id' => $exam_id,
        ])->orderBy(['score' => SORT_DESC,'absent' => SORT_ASC])->select(['student_name','student_id','absent','exam_number','score'])->orderBy('score desc')->indexBy('student_id')->asArray()->all();
        $this->layout = 'only-report';
 
        if(empty($student_id)){
            if(!empty($examStudentList)){
                foreach ($examStudentList as $item){
                    
                    $student_id = $item['student_id'];
                    break;
                }
            }
        }elseif(empty($examStudentList[$student_id])){
            throw new ServerErrorHttpException('该学生没参与该次作业');
        }

        $cardInfo =  Json::decode($this->examInfo->card_info);
        $isEnglish = false;
        if($cardInfo['contentTypeId'] == CardContentType::EnglishWriting->value){
            $isEnglish = true;
        }
        $data = ChineseWritingReportService::getStudentInfo($this->examInfo,$student_id,$isEnglish);
        $questionInfo = ChineseWritingReportService::getQuestionInfo($this->examInfo);
        $this->getView()->params['studentInfo'] = $data['student'];
        $this->getView()->params['student_id'] = $student_id;
        $this->getView()->params['questionInfo'] = $questionInfo;
        $view = 'student';
        if($isEnglish){
            $view = 'english-student';
        }
        return $this->render($view, [
            'examInfo' => $this->examInfo,
            'studentList' => array_values($examStudentList),
            'student_id' => $student_id,
            'studentInfo' => $data['student'],
            'aiResult' => $data['aiResult'],
            'isEnglish' => $isEnglish,
            'questionInfo' => $questionInfo,
            'levelStandards' => $data['levelStandards'],
        ]);
    }


    public function actionAiLevelTrend($exam_id)
    {
        return $this->asJson([
            'code' => 1,
            'data' => ChineseWritingReportService::levelTrend($exam_id),
        ]);
    }



    public function actionStudyTrend($exam_id,$student_id = 0)
    {
        $contrastExams = ExamReportService::getContrastExamsByUserId($this->examInfo->id,$this->user->id,true);
        $examStudentList = ExamStudent::find()->andWhere([
            'exam_id' => $exam_id,
        ])->orderBy(['score' => SORT_DESC])->indexBy('student_id')->asArray()->all();
        $handleStudentList = ExamStudentService::handelStudentList($examStudentList);
        $studentInfo = null;
        if(empty($student_id)){
            if(!empty($handleStudentList)){
                foreach ($handleStudentList as $item){
                    $studentInfo = $item;
                    $student_id = $studentInfo['student_id'];
                    break;
                }
            }
        }elseif(empty($examStudentList[$student_id])){
            throw new ServerErrorHttpException('该学生没参与该次作业');
        }else{
            $studentInfo = $examStudentList[$student_id];
        }
        $requestData = Yii::$app->request->get();

        if(!empty($requestData['recent_type'])){
            $recentTime = RecentTimes::tryFrom($requestData['recent_type']);
            $dateRange = $recentTime->getDateRange();
        }elseif(!empty($requestData['start_date']) && !empty($requestData['end_date'])){
            $dateRange = ['startDate' => $requestData['start_date'],'endDate' => $requestData['end_date']];
        }
        if(empty($dateRange)){
            $recentTime = RecentTimes::tryFrom(RecentTimes::SixMonth->value);
            $dateRange = $recentTime->getDateRange();
        }
        $trendExams = ExamStudentService::getStudentTrends($student_id,$dateRange['startDate'],$dateRange['endDate'],$this->examInfo->course_code);


        return $this->render('study-trend', [
            'examInfo' => $this->examInfo,
            'studentList' => array_values($handleStudentList),
            'student_id' => $student_id,
            'studentInfo' => $studentInfo,
            'gradeClassList' => ExamReportService::getGradeContrastExams($contrastExams),
            'contrastExams' => $contrastExams,
            'requestData' => Yii::$app->request->get(),
            'trendExams' => $trendExams,
            'startDate' => $dateRange['startDate'],
            'endDate' => $dateRange['endDate'],
            'courseCode' => $this->examInfo->course_code,
        ]);
    }

    public function actionExportWord($exam_id)
    {
        $cardInfo = Json::decode($this->examInfo->card_info);
        if(empty($cardInfo['paper_id'])){
            throw new ServerErrorHttpException('作业没有上传试卷');
        }
        $paper = Paper::findOne($cardInfo['paper_id']);
        if(empty($paper)){
            throw new ServerErrorHttpException('作业没有上传试卷');
        }

        $service = new PaperWordService();
        if(!$service->exportWord($paper)){
            throw new ServerErrorHttpException($service->getMsg());
        }
        $response =  \Yii::$app->response;
        /**
         * @var AliyunOssStorage $cloudStorage
         */
        $cloudStorage  = \Yii::$app->cloudStorage;
        $pdf_file = ltrim(parse_url($service->getOssUrl(), PHP_URL_PATH), '/');

        if(strpos($paper->title,"\n") !== false){
            $paper->title = str_replace("\n","",$paper->title);
        }
        return $response->sendContentAsFile($cloudStorage->read($pdf_file),"{$paper->title}.docx");

    }


    public function actionPointContrast($exam_id)
    {
        return $this->asJson([
            'code' => 1,
            'data' => TestPointReportService::testPointContrast($exam_id),
        ]);
    }


    
    public function actionKnowledgeContrast($exam_id)
    {
        return $this->asJson([
            'code' => 1,
            'data' => KnowledgeReportService::knowledgeContrast($exam_id),
        ]);
    }


    public function actionPointSchool($exam_id)
    {
        return $this->asJson([
            'code' => 1,
            'data' => !empty($this->examInfo->points_stats) ? Json::decode($this->examInfo->points_stats) : [],
        ]);
    }





    //todo 学校数据临时处理
    public function actionSchoolData($exam_id)
    {
        $schoolStats = ExamReportService::getSchoolStats($this->examInfo->card_id,$this->examInfo->school_id);
        if(!empty($schoolStats)){
            $data = Json::decode($schoolStats->data);
            return $this->asJson([
                'code' => 1,
                'data' => ['average' => $data['average']],
            ]);
        }else{
            $overallStats = $this->examInfo->overall_stats;
            $overStatus = is_array($overallStats) ? $overallStats : Json::decode($this->examInfo->overall_stats);
            return $this->asJson([
                'code' => 1,
                'data' => ['average' => $overStatus['average']],
            ]);
        }
    }

    /**
     * 学校结构数据
     * @param $exam_id
     * @return \yii\web\Response
     */
    //todo 学校数据临时处理
    public function actionQuestionSchool($exam_id)
    {
        return $this->asJson([
            'code' => 1,
            'data' => ExamQuestionService::getSchoolData($exam_id),
        ]);
    }

    /**
     * 根据考试查找推荐题
     * @param $exam_id
     * @return void
     */
    public function actionSimilarQuestion($exam_id)
    {
        return $this->asJson([
            'code' => 1,
            'data' => ExamQuestionService::getSimilarQuestion($exam_id),
        ]);
    }


    public function actionStudentExcel($exam_id)
    {
         ExcelService::exportStudentScore($exam_id);
         exit;
    }


    public function actionWordExcel($exam_id)
    {
        ExcelService::exportEnglishQuestionScore($exam_id);
        exit;
    }

    public function actionQuestionExcel($exam_id)
    {
        ExcelService::exportQuestionScore($exam_id);
        exit;
    }

    public function actionContrastRank($exam_id)
    {
        $post = Yii::$app->request->post();
        $contrastExams = ExamReportService::getContrastExamsByExamId($post['examIds']);

        array_unshift($contrastExams,$this->examInfo->toArray());
        $data = ExamContrastService::getRankPortData($contrastExams,$post['startRank'],$post['endRank']);

        return $this->asJson([
            'code' => 1,
            'data' => $data,
        ]);
    }


}

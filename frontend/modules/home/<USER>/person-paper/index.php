<?php

use common\enums\PaperFromGroup;
use common\enums\PaperStatus;
use common\enums\RecentTimes;
use frontend\modules\home\utils\UrlUtil;
use yii\helpers\Json;
use yii\helpers\Url;

$this->title = '自传试卷';
?>
<div class="homework-content mt-4 clearfix" id="homework-box">
    <div class="paper-while-8-box paper-search-box person-paper-search">
        <div class="d-flex">
            <div class="paper-search-title pt-1">类&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;型：</div>
            <div style="flex: 3">
                <label class="p-1 ps-2 pe-2 ms-1 me-1 mb-1 " v-bind:class="{'active':category_id==''}" >
                    <a href="<?=UrlUtil::buildUrl('',['category_id' => '','page' => 1])?>">全部</a>
                </label>
                <?php foreach ($paperCategoryList as $item): ?>
                    <label class="p-1 ps-2 pe-2 ms-1 me-1 mb-1 " v-bind:class="{'active':category_id=='<?=$item->id?>'}" >
                        <a href="<?=UrlUtil::buildUrl('',['category_id' => $item->id,'page' => 1])?>"><?=$item->name?></a>
                    </label>
                <?php endforeach; ?>
            </div>
            <div style="flex:1" class="text-end">
                <button href="" class="btn ms-2 btn-sm btn-primary" style="height: 30px;font-size: 13px; padding: 0 10px;" v-on:click="showUploadPaper">上传试卷</button>
            </div>
        </div>
        <div class="d-flex pt-3">
            <div class="paper-search-title pt-1">上传时间：</div>
            <div style="flex: 1">
                <label class="p-1 ps-2 pe-2 ms-1 me-1 mb-1 " v-bind:class="{'active':recent_type=='' && date_range ==''}" >
                    <a href="<?=UrlUtil::buildUrl('',['start_date' => '','end_date' =>'','recent_type' => '','page' => 1])?>">全部</a>
                </label>
                <?php foreach (RecentTimes::labels() as $key => $label): ?>
                    <label class="p-1 ps-2 pe-2 ms-1 me-1 mb-1 " v-bind:class="{'active':recent_type=='<?=$key?>'}" >
                        <a href="<?=UrlUtil::buildUrl('',['start_date' => '','end_date' =>'','recent_type' => $key,'page' => 1])?>"><?=$label?></a>
                    </label>
                <?php endforeach; ?>
                <label class="date-range">
                    <el-date-picker
                            v-model="date_range"
                            type="daterange"
                            format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD"
                            start-placeholder="开始时间"
                            end-placeholder="结束时间"
                    />
                </label>
            </div>
        </div>
        <div class="d-flex pt-3 paper-search-group">
            <div class="paper-search-title pt-2">高级选项：</div>
            <div style="flex: 5">
                <div class="row g-3 align-items-center">
                    <div class="col-auto">
                        <select class="form-select" v-model="stage">
                            <option v-for="(item,index) in stageList" v-bind:value="item.value">{{item.label}}</option>
                        </select>
                    </div>
                    <div class="col-auto">
                        <select class="form-select" v-model="course_code">
                            <option v-for="(item,index) in courseList" v-bind:value="item.code">{{item.name}}</option>
                        </select>
                    </div>
                    <div class="col-auto">
                        <select class="form-select" v-model="versionId">
                            <option value="">版本</option>
                            <?php if(!empty($versionList)):foreach ($versionList as $version): ?>
                            <option value="<?=$version['id']?>"><?=$version['name']?></option>

                            <?php endforeach; endif; ?>
                        </select>
                    </div>
                    <div class="col-auto">
                        <select class="form-select" v-model="grade">
                            <option value="">年级</option>
                            <?php if(!empty($gradeList)):foreach ($gradeList as $grade): ?>
                                <option value="<?=$grade->value?>"><?=$grade->label()?></option>
                            <?php endforeach; endif; ?>
                        </select>
                    </div>
                    <div class="col-auto">
                        <select class="form-select" v-model="textbook_id">
                            <option value="">模块</option>
                            <template v-for="(item,index) in textBookList">
                                <option v-if="item.version_id == versionId" v-bind:value="item.id">{{item.name}}</option>
                            </template>
                        </select>
                    </div>
                </div>
            </div>

            <div  style="flex: 1" class="text-end input-group">
                <input type="text" class="form-control form-control-sm" v-model="keyword" placeholder="请输入关键词"  aria-describedby="basic-addon2">
                <button class="btn btn-sm btn-primary text-white" v-on:click="searchList" type="button" id="basic-addon2">
                    <svg style="    margin-top: -3px;" width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M15.9396 15.1938L13.7441 12.9983C14.2107 12.4532 14.5828 11.8422 14.853 11.1762C15.1775 10.3758 15.342 9.53031 15.342 8.66318C15.342 7.79605 15.1775 6.95055 14.853 6.15021C14.5169 5.32141 14.0233 4.57785 13.3859 3.94047C12.7485 3.30309 12.005 2.80949 11.1762 2.4734C10.3758 2.14891 9.53031 1.98438 8.66318 1.98438C7.79605 1.98438 6.95055 2.14891 6.15021 2.4734C5.32141 2.80949 4.57785 3.30309 3.94047 3.94047C3.30309 4.57785 2.80949 5.32141 2.4734 6.15021C2.14891 6.95055 1.98438 7.79605 1.98438 8.66318C1.98438 9.53031 2.14891 10.3758 2.4734 11.1762C2.80949 12.005 3.30309 12.7485 3.94047 13.3859C4.57785 14.0233 5.32141 14.5169 6.15021 14.853C6.95055 15.1775 7.79605 15.342 8.66318 15.342C9.53031 15.342 10.376 15.1775 11.1762 14.853C11.8424 14.5828 12.4532 14.2107 12.9983 13.7441L15.1938 15.9396C15.2968 16.0427 15.4318 16.0942 15.5666 16.0942C15.7015 16.0942 15.8366 16.0427 15.9395 15.9396C16.1455 15.7336 16.1455 15.3998 15.9395 15.1938H15.9396ZM4.68631 12.6401C2.49344 10.4472 2.49344 6.879 4.68631 4.68613C5.78283 3.58961 7.22301 3.04152 8.66318 3.04152C10.1034 3.04152 11.5437 3.58978 12.6401 4.68613C13.7364 5.78248 14.2873 7.16078 14.2873 8.66301C14.2873 10.1652 13.7023 11.5776 12.6401 12.6399C10.4472 14.8328 6.879 14.8328 4.68613 12.6399L4.68631 12.6401Z" fill="white"/>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <?php if(!empty($paperList)): ?>
    <div class="paper-while-box mt-2 min-height-500">
        <div class="paper-content pb-3">
                <div class="paper-item border-bottom  pb-4 " v-for="(paper,index) in paperList" v-bind:class="{'pt-4':index>0}">
                    <?=$this->render('/common/_paper_list_item')?>

                </div>

                <div class="clearfix pt-3">
                    <div class="float-end">
                        <el-pagination
                                v-model:current-page="currentPage"
                                :page-size="10"
                                layout="prev, pager, next, jumper,slot"
                                v-model:total="totalCount"
                                :hide-on-single-page="false"
                                @size-change="handleSizeChange"
                                @current-change="handleCurrentChange"
                        >
                        </el-pagination>
                    </div>
                </div>
        </div>

    </div>
    <?php endif; ?>

    <?php if(empty($paperList)): ?>
    <?=$this->render('/common/_no_data',['type' => 'paper','msg' => '暂无试卷'])?>
    <?php endif; ?>

</div>
<?=$this->render('/common/_confirm_delete') ?>
<?=$this->render('/common/_paper_preview') ?>
<?=$this->render('/common/_upload_paper',['school' => $school,'requestData' => $requestData,'teacherStageCourses' =>$teacherStageCourses,'paperCategoryList' =>$paperCategoryList,'type' => PaperFromGroup::PersonalUpload->value,'teacherStageCourse' =>$teacherStageCourse,'gradeList' => $gradeList,'textBookList' => $textBookList,'versionList' => $versionList])?>


<script>
    var app = Vue.createApp({
        data(){
            return {
                category_id:'<?=!empty($requestData['category_id']) ? $requestData['category_id'] : ''?>',
                recent_type:'<?=!empty($requestData['recent_type']) ? $requestData['recent_type'] : ''?>',
                area_code:'<?=!empty($requestData['area_code']) ? $requestData['area_code'] : ''?>',
                keyword:'<?=!empty($requestData['keyword']) ? $requestData['keyword'] : ''?>',
                order:'<?=!empty($requestData['order']) ? $requestData['order'] : ''?>',
                paperList:<?=\yii\helpers\Json::encode($paperList)?>,
                stageCourses:<?=\yii\helpers\Json::encode($teacherStageCourses)?>,
                stage:'<?=!empty($requestData['stage']) ? $requestData['stage'] : $teacherStageCourse['stage']['value']?>',
                course_code:'<?=!empty($requestData['course_code']) ? $requestData['course_code'] : $teacherStageCourse['course']['code']?>',
                currentPage:<?=$currentPage?>,
                totalCount:<?=$totalCount?>,
                date_range:<?=!empty($requestData['start_date']) ? Json::encode([$requestData['start_date'],$requestData['end_date']]) : Json::encode([])?>,
                grade:'<?=!empty($requestData['grade']) ? $requestData['grade'] : ''?>',
                textbook_id:'<?=!empty($requestData['textbook_id']) ? $requestData['textbook_id'] : ''?>',
                start_date:'<?=!empty($requestData['start_date']) ? $requestData['start_date'] : ''?>',
                end_date:'<?=!empty($requestData['end_date']) ? $requestData['end_date'] : ''?>',
                versionId:'<?=!empty($requestData['versionId']) ? $requestData['versionId']: ''?>',
                textBookList:<?=\yii\helpers\Json::encode($textBookList)?>,
            }
        },
        computed:{
            courseList:function (){
                var list = [];
                var courseIds = [];
                for(var i in this.stageCourses){
                    var item = this.stageCourses[i];
                    if(courseIds.indexOf(item.course.code) >=0){
                        continue;
                    }
                    courseIds.push(item.course.code);
                    list.push(item.course)
                }
                return list;
            },
            stageList:function (){
                var list = [];
                var courseIds = [];
                for(var i in this.stageCourses){
                    var item = this.stageCourses[i];
                    if(courseIds.indexOf(item.stage.value) >=0){
                        continue;
                    }
                    courseIds.push(item.stage.value);
                    list.push(item.stage)
                }
                return list;
            },
        },
        mounted() {

            // document.getElementsByClassName("el-pagination__goto")[0].childNodes[0].nodeValue = "跳至";
        },
        watch:{
            versionId:function(){
                this.textbook_id = '';
            },
            stage:function (){
                this.category_id = "";
                this.textbook_id = '';
                this.versionId = '';
                this.buildUrl(1);
            },
            course_code:function (){
                this.category_id = "";
                this.textbook_id = '';
                this.versionId = '';
                this.buildUrl(1);
            },
            grade:function (){
                this.buildUrl(1);
            },
            textbook_id:function (){
                this.buildUrl(1);
            },
            date_range:function (){
                this.start_date = this.date_range[0]
                this.end_date = this.date_range[1]
                this.buildUrl(1);
            },
        },
        methods:{
            jumpCardUrl:function(){
                window.location.href='<?=Url::toRoute(['paper-card/index'])?>?uuid='+this.operateInfo.card_uuid+'#card';
            },
            previewWord:function (paper){
                paperPreviewApp.showProp(paper)
            },
            jumpPaperEdit:function (paper,index){
                this.operateType = 'card';
                this.operateInfo = paper;
                this.operateIndex = index;
                confirmApp.showProp('提示！','如对答题卡进行了修改，务必重新下载打印最新版答题卡，否则会造成答题卡无法识别或识别不准确。');
            },
            getTitleUrl:function (paper){
                if(paper.statusId == '<?=PaperStatus::Editing->value?>'){
                    return '<?=Url::toRoute(['paper-editing/index'])?>?uuid='+paper.uuid;
                }else if(paper.statusId == '<?=PaperStatus::OnLine->value?>'){
                    return '<?=Url::toRoute(['/home/<USER>'])?>/?uuid='+paper.uuid+'#/testPaperCenter';
                }
                return '#'
            },
            showUploadPaper:function (){
                uploadPaperApp.showProp();
            },
            getPaperStatusCss:function (paper){
                console.log(paper)
                if(paper.hasDownload){
                    return 'paper-status-card'
                }
                return '';
            },
            getPaperStatusLabel:function (paper){
                console.log(paper)
                if(paper.hasDownload){
                    return '已制卡'
                }
                //if(paper.statusId == '<?php //=PaperStatus::OnLine->value?>//'){
                //    return ''
                //}else if(paper.statusId == '<?php //=PaperStatus::Editing->value?>//'){
                //    return '未解析'
                //}else if(paper.statusId == '<?php //=PaperStatus::OffLine->value?>//'){
                //    return '已下载'
                //}
                return '';
            },
            searchList:function (){
                this.buildUrl(1);
            },
            buildUrl:function (page){
                var url = '<?=Url::toRoute(['','tag' => $requestData['tag']])?>'

                if(this.stage != ''){
                    url += '&stage='+this.stage;
                }
                if(this.course_code != ''){
                    url += '&course_code='+this.course_code;
                }
                if(this.category_id != ''){
                    url += '&category_id='+this.category_id;
                }
                if(this.start_date != ''){
                    url += '&start_date='+this.start_date;
                }else{
                    if(this.recent_type != ''){
                        url += '&recent_type='+this.recent_type;
                    }
                }
                if(this.end_date != ''){
                    url += '&end_date='+this.end_date;
                }
                if(this.textbook_id != ''){
                    url += '&textbook_id='+this.textbook_id;
                }
                if(this.grade != ''){
                    url += '&grade='+this.grade;
                }
                if(this.keyword != ''){
                    url += '&keyword='+this.keyword;
                }
                if(this.order != ''){
                    url += '&order='+this.order;
                }
                if(this.versionId !=''){
                    url += '&versionId='+this.versionId;
                }
                url += '&page='+page;
                window.location.href=url;

            },
            handleCurrentChange:function (number){
                console.log(this.currentPage)
                console.log(this.currentPage)
                this.buildUrl(number)
            },
            deletePaper:function (paper,index){
                if(paper.hasDownload){
                    return;
                }
                this.operateType = 'del';
                this.operateInfo = paper;
                this.operateIndex = index;
                confirmApp.showProp('确定删除此作业吗？','删除后将不可恢复');
            },
            confirmDelete:function (){
                if(this.operateType == 'card'){
                    this.jumpCardUrl();
                    return;
                }
                var _this = this;
                $.ajax({
                    type: 'post',
                    url: '<?=Url::toRoute('custom-paper/delete')?>',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        paperId: this.operateInfo.id,
                    }),
                    success: function (data) {
                        if(data.code == 1){
                            toastr.success('删除成功', '', {positionClass: 'toast-center'})
                            confirmApp.hideProp();
                            location.reload(); // 刷新页面
                        }else{
                            toastr.error(data.msg, '错误提示!', {positionClass: 'toast-center'})
                        }
                    },
                    error:function (data){
                        toastr.error(data.responseText, '错误提示!', {positionClass: 'toast-center'})
                    }
                });
            }
        }

    }).use(ElementPlus, {
        locale: ElementPlusLocaleZhCn,
    }).mount("#homework-box")
</script>

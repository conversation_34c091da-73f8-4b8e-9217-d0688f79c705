<?php

use yii\helpers\Json;
use yii\helpers\Url;

?>
<style>
    .upload-demo .el-upload-dragger img{
        max-width: 100%;
    }
    .upload-demo .el-upload-dragger{
        padding-top:5px;
        padding-bottom: 5px;
    }
</style>
<div class="modal fade hh-modal" id="word_card_info_form" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered  modal-lg" style="width: 600px">
        <div class="modal-content">
            <div class="modal-header hh-modal-header">
                <h5 class="modal-title">新建答题卡</h5>
                <button type="button" class="btn-close"   data-bs-dismiss="modal" aria-label="Close" ></button>
            </div>
            <div class="modal-body">
                <div class="pt-3 ps-5 pe-5">
                    <div class="mt-3">
                        <div class="row">
                            
                            <div class="d-flex align-items-center" style="padding-left:38px;">
                                <label class="col-form-label col-auto font-weight-bold me-2"><span class="text-danger">*</span>名称</label>
                                <input class="form-control" v-bind:class="{'is-invalid':contentError}"  v-model="cardName" placeholder="请输入题卡名称">
                                <div class="invalid-feedback">题卡名称不能为空！</div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-6">
                                <div class="d-flex align-items-center" style="padding-left:28px;">
                                    <label class="col-form-label col-auto font-weight-bold me-2"><span class="text-danger">*</span>学段</label>
                                    <select class="form-select" v-model="stage">
                                        <template v-for="(item,index) in stageList">
                                        <option v-bind:value="item.value">{{item.label}}</option>
                                        </template>
                                     </select>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="d-flex align-items-center">
                                    <label class="col-form-label col-auto font-weight-bold me-2"><span class="text-danger">*</span>年级</label>
                                    <select class="form-select" v-model="grade">
                                        <template v-for="(item,index) in stageList">
                                            <template v-if="item.value==stage" v-for="(gradeItem,gradeIndex) in gradeList">
                                                <option v-bind:value="gradeItem.value">{{gradeItem.label}}</option> 
                                            </template>
                                        </template>
                                     </select>
                                </div>
                            </div>
                        </div>
                        <!--<div class="row mt-3">
                            <div class="d-flex align-items-center" style="padding-left:38px;">
                                <label class="col-form-label col-auto font-weight-bold me-2"><span class="text-danger">*</span>体裁</label>
                                <select class="form-select" v-model="writeType">
                                    <option value="">请选择体裁</option>
                                    <?php /*foreach(ChineseWritingType::labels() as $key => $label): */?>
                                        <option value="<?php /*=$label*/?>"><?php /*= $label*/?></option>
                                     <?php /*endforeach; */?>
                                </select>
                                <div class="invalid-feedback">请选择体裁</div>
                            </div>
                        </div> -->
                        <div class="row mt-3">
                            <div class="d-flex align-items-start" style="padding-left:38px;">
                                <label class="col-form-label col-auto font-weight-bold me-2"><span class="text-danger">*</span>满分</label>
                                <div class="col">
                                    <div class="d-flex align-items-center">
                                        <div class="form-check form-check-inline ">
                                            <input class="form-check-input" type="radio" name="writeFullScoreType" v-model="writeFullScoreType" id="writeFullScoreType1" value="1">
                                            <label class="form-check-label ps-2" for="writeFullScoreType1">15分</label>
                                        </div>
                                        <div class="form-check form-check-inline d-flex align-items-center">
                                            <input class="form-check-input" type="radio" name="writeFullScore" v-model="writeFullScoreType" id="writeFullScoreType2" value="2">
                                            <label class="form-check-label ps-2" for="writeFullScoreType2">20分</label>
                                        </div>
                                        <div class="form-check form-check-inline d-flex align-items-center">
                                            <input class="form-check-input" type="radio" name="writeFullScore" v-model="writeFullScoreType" id="writeFullScoreType4" value="4">
                                            <label class="form-check-label ps-2" for="writeFullScoreType4">25分</label>
                                        </div>
                                        <div class="form-check form-check-inline d-flex align-items-center">
                                            <input class="form-check-input" type="radio" name="writeFullScore" v-model="writeFullScoreType" id="writeFullScoreType3" value="3">
                                            <label class="form-check-label ps-2 pe-2" for="writeFullScoreType3">其他</label>
                                            <input type="text" style="width:80px" v-on:focus="setCursorAtEnd('fullScore')" class="form-control" v-model="fullScore">
                                        </div>
                                    </div>
                                   <!-- <div class="mt-2">
                                        <div class="d-flex align-items-center">
                                            <div class="col-auto">
                                                <select class="form-control form-select" v-model="dimensionIndex">
                                                    <option v-for="(item,index) in allDimensions" v-bind:value="index">{{item.title}}</option>
                                                </select>
                                            </div>
                                            <label class="ps-3">{{allDimensions[dimensionIndex].info}}</label>
                                        </div>
                                        <div class="mt-3">
                                            <table class="table table-bordered">
                                                <thead>
                                                    <tr>
                                                        <th width="110px;">维度</th>
                                                        <th>子项</th>
                                                        <th width="90px;">满分(占比)</th>
                                                    </tr>
                                                </thead>
                                                <tr v-for="(item,index) in allDimensions[dimensionIndex].items">
                                                    <td>
                                                        <input class="form-control form-control-sm" v-model="item.dimenName">
                                                    </td>
                                                    <td>
                                                        <input class="form-control form-control-sm" v-model="item.dimenInfo">
                                                    </td>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <input class="form-control form-control-sm" v-model="item.dimenFullScoreRatio">
                                                            <label>%</label>
                                                        </div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>-->
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="d-flex align-items-center">
                                <label class="col-form-label col-auto font-weight-bold me-2"><span class="text-danger">*</span>词数要求</label>
                                <div class="form-check form-check-inline ">
                                    <input class="form-check-input" type="radio" name="minWordNumType" v-model="minWordNumType" id="minWordNumType1" value="1">
                                    <label class="form-check-label ps-2" for="minWordNumType1">80</label>
                                </div>
                                <div class="form-check form-check-inline d-flex align-items-center">
                                    <input class="form-check-input" type="radio" name="minWordNumType" v-model="minWordNumType" id="minWordNumType2" value="2">
                                    <label class="form-check-label ps-2" for="minWordNumType2">100</label>
                                </div>
                                <div class="form-check form-check-inline d-flex align-items-center">
                                    <input class="form-check-input" type="radio" name="minWordNumType" v-model="minWordNumType" id="minWordNumType4" value="4">
                                    <label class="form-check-label ps-2" for="minWordNumType4">120</label>
                                </div>
                                <div class="form-check form-check-inline d-flex align-items-center">
                                    <input class="form-check-input" type="radio" name="minWordNumType" v-model="minWordNumType" id="minWordNumType3" value="3">
                                    <label class="form-check-label ps-2 pe-2" for="minWordNumType3">其他</label>
                                    <input type="text" style="width:80px" v-on:focus="setCursorAtEnd('wordNum')" class="form-control" v-model="minWordNum">
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="d-flex">
                                <label class="col-form-label col-auto font-weight-bold me-2"><span class="text-danger">*</span>题目说明</label>
                                <textarea class="form-control" rows="8" v-model="body"></textarea>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="d-flex">
                                <label class="col-form-label col-auto font-weight-bold me-2"><span>&nbsp;&nbsp;</span>上传图片</label>
                                <el-upload
                                        class="upload-demo col"
                                        :show-file-list="false"
                                        drag
                                        action="<?=Url::toRoute(['write/upload-img'])?>"
                                        :before-upload="beforeUpload"
                                        :on-success="handleSuccess"
                                        :headers="headers"
                                        :on-error="handleError"  >
                                    <img v-if="imageUrl" :src="imageUrl" class="avatar" />
                                    <el-icon v-if="!imageUrl" class="el-icon--upload"><upload-filled /></el-icon>
                                    <div class="el-upload__text" v-if="!imageUrl">
                                        请选择上传的图片
                                    </div>
                                    <template #tip>
                                        <div class="el-upload__tip">只能上传jpg/png文件，且不超过5MB</div>
                                    </template>
                                </el-upload>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <div class="text-end w-100 pe-5 pb-3">
                    <button type="button" class="btn btn-hh-default"  data-bs-dismiss="modal" >取消</button>
                    <button type="button" class="btn btn-primary ms-3" v-on:click="saveCard" v-bind:disabled="!hasUpload || isAjax" >确定</button>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    var wordCardInfoApp = Vue.createApp({
        data(){
            return {
                type:'add',
                cardName:'',
                copy_card_id:'',
                hasUpload:false,
                teacherStageCourses:<?=Json::encode($teacherStageCourses)?>,
                gradeList:<?=Json::encode($gradeList)?>, 
                writeFullScoreType:'1',
                fullScore:'',
                minWordNumType:'1',
                minWordNum:'',
                writeType:'',
                hasUploadFile:false,
                contentError:false,
                uploadFileInfo:{},
                paperName:'',
                stage:'<?=$teacherStageCourse['stage']['value']?>',
                grade:'<?=!empty($requestData['grade']) ? $requestData['grade'] : ''?>',
                course_code:'<?=$teacherStageCourse['course']['code']?>',
                form_group:'',
                textbookVersion:'<?=$teacherStageCourse['textbookVersion']['id']?>',
                textbook_id:'<?=!empty($requestData['textbook_id']) ? $requestData['textbook_id'] : ''?>',
                category_id:'',
                isAjax:false,
                headers:{ 'X-Csrf-Token': '<?=Yii::$app->request->getCsrfToken()?>'},
                imageUrl:'',
                imageContent:'',
                dimensions:[],
                oriAllDimensions:<?=Json::encode(\frontend\modules\home\services\WritingService::getEnglishDimensions())?>,
                allDimensions:<?=Json::encode(\frontend\modules\home\services\WritingService::getEnglishDimensions())?>,
                dimensionIndex:0,
            }
        },
        created:function(){
            
            console.log(this.grade)
        },
        computed:{
            stageList:function (){
                var stageIds = [];
                var list = [];
                for(var i in this.teacherStageCourses){
                    var item = this.teacherStageCourses[i];
                    if(stageIds.indexOf(item.stage.value) === -1){
                        stageIds.push(item.stage.value);
                        list.push(item.stage)
                    }
                }
                return list;
            }
        },
        watch:{
            stage:function(){
                this.grade = '';
            },
            cardName:function (){
                if(this.cardName != ""){
                    this.hasUpload = true;
                }else{
                    this.hasUpload = false;
                }
            }
        },
        methods:{

            beforeUpload:function (file){
                console.log('上传处理',file)
                const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
                if (!isJpgOrPng) {
                    alert('上传图片只能是 JPG 或 PNG 格式!');
                }

                const isLt5M = file.size / 1024 / 1024 < 5;
                if (!isLt5M) {
                    alert('上传图片大小不能超过 5MB!');
                }
                this.hasUpload = false;
                return isJpgOrPng && isLt5M;
            },
            handleSuccess :function (response, file){
                this.hasUpload = true;
                console.log('上传成功:', response);
                if (response.code == 1) {
                    this.imageUrl = response.data.url;
                    this.imageContent = response.data.content;
                } else {
                    alert(response.msg || '上传失败');
                }

            },
            handleError:function (){
                console.log('上传失败:', error);
                this.hasUpload = true;
                alert('上传过程中发生错误');
            },
            setCursorAtEnd:function (type){
                if(type == 'fullScore'){
                    this.writeFullScoreType = 3;
                }else{
                    this.minWordNumType = 3;
                }
            },
            isPositiveInteger:function(value){
                return /^[1-9]\d*$/.test(String(value));
            }, 
            saveCard:function (){
                if(this.cardName == ''){
                    this.contentError = true;
                    return;
                }
                if(this.writeFullScoreType == 1){
                    this.fullScore = 15;
                }else if(this.writeFullScoreType == 2){
                    this.fullScore = 20;
                }else if(this.writeFullScoreType == 4){
                    this.fullScore = 25;
                }else if(!this.isPositiveInteger(this.fullScore)){
                    alert('请输入合法的分数');
                    return;
                }
                if(this.minWordNumType == 1){
                    this.minWordNum = 80;
                }else if(this.minWordNumType == 2){
                    this.minWordNum = 100;
                }else if(this.minWordNumType == 4){
                    this.fullScore = 120;
                }else if(!this.isPositiveInteger(this.minWordNum)){
                    alert('请输入合法的单词数');
                    return;
                }
                this.dimensions = this.allDimensions[this.dimensionIndex].items;
                var sum = 0;
                for(var i in this.dimensions){
                    var item = this.dimensions[i];
                    sum += parseFloat(item.dimenFullScoreRatio);
                }
                if(sum != 100){
                    alert("评分维度占比必须是100%");
                    return;
                }
                this.isAjax = true;
                var _this = this;
                $.ajax({
                    url: '<?=Url::toRoute(['write/create-english'])?>', // 替换为你的 API 地址
                    type: 'POST', // 请求类型
                    contentType: 'application/json',
                    data: JSON.stringify({
                        name:this.cardName,
                        stageId: this.stage,
                        grade: this.grade,
                        writeType:this.writeType,
                        imageUrl:this.imageUrl,
                        dimensions:this.dimensions,
                        imageContent:this.imageContent,
                        fullScore: this.fullScore,
                        minWordNum: this.minWordNum,
                        body: this.body, 
                    }),
                    success: function(response) {
                        console.log('请求成功:', response);
                        if(response.code == 1){
                            toastr.success('创建成功', '', {positionClass: 'toast-center'})
                            window.location.href="<?=Url::toRoute(['/home/<USER>/index'])?>?uuid="+response.data+"#/writing"
                         }else{
                            _this.isAjax =false;
                            toastr.error(response.msg, '', {positionClass: 'toast-center'})
                        }
                    },
                    error: function(xhr, status, error) {
                        _this.isAjax =false;
                        console.error('请求失败:', status, error);
                    }
                });
            },
            copyCard:function (cardInfo){
                console.log(cardInfo)
                this.contentError = false;
                this.type = 'copy'
                this.copy_card_id = cardInfo.id;
                this.cardName = cardInfo.title;
                this.imageUrl = '';
                this.imageContent = '';
                this.stage = cardInfo.stageId;
                this.grade = cardInfo.gradeId;
                this.course_code = cardInfo.courseCode;
                this.allDimensions = [...this.oriAllDimensions];
                this.dimensionIndex = 0;
                this.category_id = cardInfo.category_id;
                this.textbookVersion = '<?=$teacherStageCourse['textbookVersion']['id']?>'
                this.textbook_id = '<?=!empty($requestData['textbook_id']) ? $requestData['textbook_id'] : ''?>';
            },
            createCard:function (){
                this.contentError = false;
                this.type = 'add'
                this.cardName = '';
                this.copy_card_id = '';
                this.imageUrl = '';
                this.imageContent = '';
                this.stage = '<?=$teacherStageCourse['stage']['value']?>';
                this.allDimensions = [...this.oriAllDimensions];
                this.dimensionIndex = 0;
                this.grade = '<?=!empty($requestData['grade']) ? $requestData['grade'] : ''?>';
                this.course_code = '<?=$teacherStageCourse['course']['code']?>';
                this.category_id = '';
                this.textbookVersion = '<?=$teacherStageCourse['textbookVersion']['id']?>'
                this.textbook_id = '<?=!empty($requestData['textbook_id']) ? $requestData['textbook_id'] : ''?>';
            },
            showModal:function (){
                $("#word_card_info_form").modal('show')
            }
        }
    }).use(ElementPlus, {
        locale: ElementPlusLocaleZhCn,
    }).mount("#word_card_info_form")
</script>
<?php
namespace frontend\modules\home\services;

use common\domains\exam\StudentLevelStats;
use common\models\exam\Exam;
use common\models\exam\ExamCardStruct;
use common\models\exam\ExamStudent;
use common\models\scan\ScanBlock;
use Exception;
use frontend\modules\home\form\WritingWordRecoverForm;
use Yii;
use yii\helpers\Json;

class ChineseWritingReportService 
{

    public static function setLevelStandard(Exam $examInfo,array $levels)
    {
        $lastScore = 0;
        $levelStandards = [];
        foreach($levels as $item){
            $item['interval']['start'] = $item['standard'];
            if(!empty($lastScore)){
                $item['interval']['end'] = $lastScore;
            }
            $levelStandards[] = $item;
            $lastScore = $item['standard'];
        }
        try{
            $aiResultStats = Json::decode($examInfo->ai_result_stats);
            $students = [];
            $levelStandardStudents = [];
            foreach($aiResultStats['scoreLevels'] as $level){
                if(!empty($level['students'])){
                    foreach($level['students'] as $student){
                        $studentStandard = StudentLevelStats::getScoreStandard($levelStandards,$student['score']);
                        $levelStandardStudents[$studentStandard][] = [
                            'student_id' => $student['student_id'],
                            'student_name' => $student['student_name'],
                            'score' => $student['score'],
                        ];
                    }
                }
            }
            $aiResultStats['scoreLevels']  = StudentLevelStats::handleLevelStats($levelStandards,$levelStandardStudents);
            $examInfo->ai_result_stats = Json::encode($aiResultStats);
            if($examInfo->save(false,['ai_result_stats'])){
                return $aiResultStats;
            }else{
                return false;
            }
        }catch(Exception $e){
            Yii::error(($e->getMessage()."\n".$e->getTraceAsString()));
            return false;
        }
    }
    
    private static $trendExams = null;
    public static function levelTrend($examId)
    {
        $trendExams = self::getTrendExams($examId);
        /**
         * @var Exam $exam
         */
        $list = [];
        $levelNames = [];
        foreach($trendExams as $exam){
            $date = date("Y-m-d",$exam->created_at);
            $overallStats = $exam->getOverallStats();
            $aiResultStats = Json::decode($exam->ai_result_stats);
            
            foreach($aiResultStats['scoreLevels'] as $level){
                if(empty($levelNames) || !in_array($level['name'],$levelNames)){
                    $levelNames[] = $level['name'];
                }
            }

            $list[] = [
                'id' => $exam->id,
                'title' => $exam->title,
                'date' => $date,
                'levels' =>  $aiResultStats['scoreLevels'] ?? [],
                'fullScore' => $overallStats->fullScore,
                'average' => $overallStats->average,
                'rate' => $overallStats->classScoreRate, 
            ]; 
        }
        return [
            'exams' => $list,
            'levelNames' => $levelNames
        ];
    }



    public static function getTrendExams($examId)
    {
        $newTrend = false;
        if($examId != self::$examId){
            $newTrend = true;
            self::setExamId($examId);
        }
        if($newTrend){
            $examInfo = ExamReportService::getExamInfo($examId);
            $examList = Exam::find()->andWhere(['class_id' => $examInfo->class_id,'course_code' => $examInfo->course_code,'result_generated' => 1])
                ->andWhere(['<','id',$examInfo->id])
                ->andWhere(['!=','ai_result_stats', ''])
                ->orderBy('id desc')->select(['id','title','created_at','overall_stats','ai_result_stats'])->limit(3)->all();
            if(!empty($examList)){
                $examList = array_reverse($examList);
                $examList[] = $examInfo;
            }
            self::$trendExams = $examList ?? [];
        }
        return self::$trendExams;
    }


    private static $examId = null;
    public static function setExamId($examId)
    {
        self::$examId = $examId;
    }



    public static function getStudentInfo(Exam $exam,int $studentId,bool $isEnglish)
    {
        $studentInfo = ExamStudent::find()->andWhere(['exam_id' => $exam->id,'student_id' => $studentId])->one();
        $imageIds = [];
        foreach ($studentInfo->scan_pages as $scanPage){
            $imageIds = array_merge($scanPage['imageIds'],$imageIds);
        }
        $aiResultStats = Json::decode($exam->ai_result_stats);
        $scanBlock = Scanblock::find()->andWhere(['image_id' => $imageIds])->indexBy('id')->asArray()->all();
        $aiResult = [];
        foreach ($scanBlock as $block){
            if(!empty($block['ai_result'])){
                $aiResult = Json::decode($block['ai_result']);
                $oriResult = $aiResult;
                if(!empty($aiResult['student'])){
                    $aiResult = $aiResult['student'];
                }else{
                    $aiResult = self::getWordAiResult($oriResult['overall_feedback'] ?? [],$isEnglish);
                }
                if(empty($aiResult['report'])){
                    $aiResult['report'] = $oriResult['overall_feedback']['report'] ?? [];
                }
                break;
            }
        }
        return [
            'student' => $studentInfo,
            'aiResult' => $aiResult,
            'levelStandards' => $aiResultStats['scoreLevels'] ?? [],
        ];
    }

    /**
     * 分析数据的时候从手工编辑的数据处理分析情况
     * @param array $realAiResult
     * @return arary
     */
    public static function getStudentRealResult(array $realAiResult):array
    {
        $delBoxes = [];
        $usageSen = $goodSen = $errorSen = $wrongWords = [];
        if(!empty($aiResult['imageSigns'])) {
            foreach ($aiResult['imageSigns'] as $sign) {
                if(!$sign['show']){//已删除的数据
                    continue;
                }
                if($sign['type'] == 'del'){
                    $delBoxes[] = $sign['ori'];
                }else{
                    $temp = $sign['ori'];
                    $temp['analysis'] = $sign['analysis'];
                    if($sign['type'] == 'usage'){
                        $usageSen[] = $temp;
                    }elseif($sign['type'] == 'good'){
                        $goodSen[] = $temp;
                    }elseif($sign['type'] == 'error'){
                        $errorSen[] = $temp;
                    }elseif($sign['type'] == 'wrong'){
                        $wrongWords[] = $temp;
                    }
                }
            }
        }
        unset($realAiResult['imageSigns']);
        $realAiResult['delBoxes'] = $delBoxes;
        $realAiResult['details']['goodSen'] = $goodSen;
        $realAiResult['details']['errorSen'] = $errorSen;
        $realAiResult['details']['wrongWord'] = $wrongWords;
        $realAiResult['details']['usageSen'] = $usageSen;

        return $realAiResult;
    }


    public static function getSaveStudentResult(array $aiResult):array
    {
        $imageSigns = [];
        if(!empty($aiResult['imageSigns'])){
            foreach ($aiResult['imageSigns'] as $sign){
                if(!empty($sign['isSave'])){
                     $sign['analysis'] = $sign['editContent'];
                }
                unset($sign['editContent']);
                unset($sign['isSave']);
                unset($sign['isEdit']);
                $imageSigns[] = $sign;
            }
        }
        $dimensions = [];
        if(!empty($aiResult['dimensions'])){
            foreach ($aiResult['dimensions'] as $dimension){
                if(!empty($dimension['isSave'])){
                    $dimension['dimenScore'] = $dimension['editScore'];
                    $dimension['comment'] = $dimension['editComment'];
                }
                unset($dimension['editScore']);
                unset($dimension['editComment']);
                unset($dimension['isSave']);
                unset($dimension['isEdit']);
                $dimensions[] = $dimension;
            }
        }
        $aiResult['dimensions'] = $dimensions;
        $aiResult['imageSigns'] = $imageSigns;
        return $aiResult;

    }


    public static function handelWordStudentResult(array $result):array
    {
        $imageSigns = [];
        $signs = [];
        if(!empty($result['delBoxes'])){//涂抹
            foreach ($result['delBoxes'] as $index => $delBox){
                $delBox['type'] = 'del';
                $delBox['index'] = $index;
                $delBox['ori'] = $delBox;
                $signs[$delBox['top']][] = $delBox;
            }
        }
        if(!empty($result['details']['usageSen'])){
            foreach ($result['details']['usageSen'] as $index =>  $goodSen){
                $goodSen['type'] = 'usage';
                $goodSen['index'] = $index;
                $goodSen['ori'] = $goodSen;
                $minTop = 0;
                foreach ($goodSen['bboxs'] as $sen){
                    if(empty($minTop) || $sen['top'] < $minTop){
                        $minTop = $sen['top'];
                    }
                }
                $signs[$minTop][] = $goodSen;
            }
        }
        if(!empty($result['details']['goodSen'])){
            foreach ($result['details']['goodSen'] as $index =>  $goodSen){
                $goodSen['type'] = 'good';
                $goodSen['index'] = $index;
                $goodSen['ori'] = $goodSen;
                $minTop = 0;
                foreach ($goodSen['bboxs'] as $sen){
                    if(empty($minTop) || $sen['top'] < $minTop){
                        $minTop = $sen['top'];
                    }
                }
                $signs[$minTop][] = $goodSen;
            }
        }
        if(!empty($result['details']['errorSen'])){
            foreach ($result['details']['errorSen'] as $index => $goodSen){
                $goodSen['type'] = 'error';
                $goodSen['index'] = $index;
                $goodSen['ori'] = $goodSen;
                $minTop = 0;
                foreach ($goodSen['bboxs'] as $sen){
                    if(empty($minTop) || $sen['top'] < $minTop){
                        $minTop = $sen['top'];
                    }
                }
                $signs[$minTop][] = $goodSen;
            }
        }
        if(!empty($result['details']['wrongWord'])){
            foreach ($result['details']['wrongWord'] as $index =>  $goodSen){
                $goodSen['type'] = 'wrong';
                $goodSen['index'] = $index;
                $goodSen['ori'] = $goodSen;
                $minTop = 0;
                foreach ($goodSen['bboxs'] as $sen){
                    if(empty($minTop) || $sen['top'] < $minTop){
                        $minTop = $sen['top'];
                    }
                }
                $signs[$minTop][] = $goodSen;
            }
        }
        if(!empty($result['wrongWord'])){
            foreach ($result['wrongWord']as $index =>  $goodSen){
                $goodSen['type'] = 'wrong';
                $goodSen['index'] = $index;
                $goodSen['ori'] = $goodSen;
                $minTop = 0;
                foreach ($goodSen['bboxs'] as $sen){
                    if(empty($minTop) || $sen['top'] < $minTop){
                        $minTop = $sen['top'];
                    }
                }
                $signs[$minTop][] = $goodSen;
            }
        }
        ksort($signs);
        foreach ($signs as $top=> $items){
            foreach ($items as $item){
                $item['show'] = true;
                $imageSigns[] = $item;
            }
        }

        $dimensions = [];
        if(!empty($result['dimensions'])){
            foreach ($result['dimensions'] as $dimension) {
                $dimension['ori'] = $dimension;
                $dimensions[] = $dimension;
            }
        }
        $teacherComment = '';
        if(!empty($result['evaluate']['strengths'])){
            $teacherComment.='作文亮点：'.$result['evaluate']['strengths'];
        }
        if(!empty($result['evaluate']['weaknesses'])){
            if(!empty($teacherComment)){
                $teacherComment .= "\n";
            }
            $teacherComment.='作文不足：'.$result['evaluate']['weaknesses'];
        }
        if(!empty($result['evaluate']['suggestions'])){
            if(!empty($teacherComment)){
                $teacherComment .= "\n";
            }
            $teacherComment.='改进和建议：'.$result['evaluate']['suggestions'];
        }
        if(!empty($result['evaluate']['comment'])){
            if(!empty($teacherComment)){
                $teacherComment .= "\n";
            }
            $teacherComment.='综合评价：'.$result['evaluate']['comment'];
        }
        if(!empty($result['generalComments'])){
            $teacherComment = $result['generalComments'];
        }
        return [
            'status' => !empty($result['status']) ? $result['status'] : 0,
            'error_user' => !empty($result['error_user']) ? $result['error_user'] : '',
            'imageSigns' => $imageSigns,
            'image_url' => !empty($result['image_url']) ? $result['image_url'] : '',
            'img_width' => !empty($result['img_width']) ? $result['img_width'] : '',
            'img_height' => !empty($result['img_height']) ? $result['img_height'] : '',
            'wordCount' => !empty($result['wordCount']) ? $result['wordCount'] : 0,
            'paperFullScore' => !empty($result['paperFullScore']) ? $result['paperFullScore'] : 0,
            'paperScore' => !empty($result['paperScore']) ? $result['paperScore'] : 0,
            'type' => !empty($result['type']) ? $result['type'] : '',
            'dimensions' => $dimensions ?? [],
            'detailedAnalysis' => !empty($result['detailedAnalysis']) ? $result['detailedAnalysis'] : [],
            'inlineCorrections' => !empty($result['inlineCorrections']) ? $result['inlineCorrections'] : [],
            'evaluate' => [
                'strengths' => !empty($result['evaluate']['strengths']) ? $result['evaluate']['strengths'] : '',
                'weaknesses' => !empty($result['evaluate']['strengths']) ? $result['evaluate']['weaknesses'] : '',
                'comment' => !empty($result['evaluate']['strengths']) ? $result['evaluate']['comment'] : '',
                'suggestions' => !empty($result['evaluate']['strengths']) ? $result['evaluate']['suggestions'] : '',
            ],
            'teacherComment' => [
                'content' => $teacherComment,
            ],
            'report' => $result['report'] ?? []
        ];
    }

    public static function getWordAiResult(array $result):array
    {
        if(!empty($result['student'])){
            return $result['student'];
        }
        return self::handelWordStudentResult($result);
    }


    public static function recoverWordAiResult(array $result,string $type):array
    {
        $dimensions = [];
        if(!empty($result['dimensions'])){
            $paperScore = 0;
            foreach ($result['dimensions'] as $dimension){
                $dimension['dimenScore'] = $dimension['ori']['dimenScore'];
                $dimension['comment'] = $dimension['ori']['comment'];
                $dimensions[] = $dimension;
                $paperScore += $dimension['dimenScore'];
            }
            $result['paperScore'] = $paperScore;
            $result['dimensions'] = $dimensions;
        }else{}

       
        if($type == WritingWordRecoverForm::TYPE_ALL){
            $signs = [];
            foreach ($result['imageSigns'] as $sign){
                if(!$sign['show']){
                    $sign['show'] = true;
                }
                if($sign['type'] != 'del'){
                    $sign['analysis'] = $sign['ori']['analysis'];
                }
                $signs[] = $sign;
            }
            $result['imageSigns'] = $signs;
        }
        return $result;
    }


    public static function getQuestionInfo(Exam $examInfo):array
    {
        $structList = ExamCardStruct::find()->andWhere(['exam_id' => $examInfo->id])->asArray()->all();
        foreach ($structList as $struct){
             $structInfo = Json::decode($struct['struct_info']);
             if(!empty($structInfo['question_info'])){
                 if(is_array($structInfo['question_info'])){
                     return $structInfo['question_info'];
                 }else{
                     return Json::decode($structInfo['question_info']);
                 }
             }
        }
        return [];
    }


}
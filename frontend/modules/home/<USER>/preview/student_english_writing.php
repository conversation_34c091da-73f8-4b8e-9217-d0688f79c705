<?php

use yii\helpers\Json;

/**
 * @var \common\models\exam\Exam $examInfo
 */
$cardInfo = \yii\helpers\Json::decode($examInfo->card_info);
$courseCode = !empty($cardInfo['courseCode']) ? $cardInfo['courseCode'] :  $cardInfo['course_code'];
$courseName = \common\models\base\Course::find()->andWhere(['code' => $courseCode])->select(['name'])->scalar();

$classInfo = Json::decode($examInfo->class_info);
$gradeId = $classInfo['grade']['value'];
\frontend\modules\home\assets\HomeWorkAsset::register($this);
?>

<style>
    .preview_level_score_sign{
        background: #E0E9FF;
        border-radius: 11px;
        padding:3px 8px;
    }
</style>
<div>
    <div class="study-report-item-box" id="report-writing-preview">
        <div class="ps-3 pt-3 pb-3 pe-3">

            <!--<div class="d-flex">
                <div class="">
                    <div style="width:800px">
                        <div>
                            <?php /*= str_replace("\n", "<br>", $questionInfo['topic'] ?? '')*/?>
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="fw-bold font-size-16">姓名：{{studentInfo.student_name}}</div>
                    <div class="fw-bold font-size-16 pt-2">得分：{{aiResult.paperScore}}</div>
                </div>
            </div>-->
            <div class="mt-3 d-flex">
                <div class="position-relative" style="width:800px" >
                    <div>
                        <img v-bind:src="aiResult.image_url" style="width:100%;">
                    </div>
                    <template v-if="aiResult.status == 0" v-for="(item,index) in aiResult.imageSigns">
                        <template v-if="item.show && item.type == 'good'" v-for="(box,boxIndex) in item.bboxs">
                            <div class="good-line position-absolute" v-bind:style="getGoodStyle(box)">
                                <span class="num position-absolute" v-if="boxIndex == 0">{{getSignNum(item,index)}}</span>
                            </div>
                        </template>
                        <template v-if="item.show && item.type == 'usage'" v-for="(box,boxIndex) in item.bboxs">
                            <div class="usage-line position-absolute" v-bind:style="getGoodStyle(box)">
                                <span class="num position-absolute" v-if="boxIndex == 0">{{getSignNum(item,index)}}</span>
                            </div>
                        </template>
                        <template v-if="item.show && item.type == 'error'" v-for="(box,boxIndex) in item.bboxs">
                            <div class="error-line position-absolute" v-bind:style="getGoodStyle(box)">
                                <span class="num position-absolute" v-if="boxIndex == 0">{{getSignNum(item,index)}}</span>
                            </div>
                        </template>
                        <template v-if="item.show && item.type == 'wrong'"  v-for="(box,boxIndex) in item.bboxs">
                            <div class="wrong-word-box text-danger position-absolute" v-if="boxIndex == 0" v-bind:style="getBoxStyle(box,item)">
                                <icon class="fa fa-close "></icon>
                                <span class="num position-absolute" v-if="boxIndex == 0">{{getSignNum(item,index)}}</span>
                            </div>
                        </template>
                        <template v-if="item.show && item.type == 'del'">
                            <div class="del-word-box  position-absolute"  v-bind:style="getBoxStyle(item)">
                                <icon class="fa fa-navicon"></icon>
                                <span class="num position-absolute" >{{getSignNum(item,index)}}</span>
                            </div>
                        </template>
                    </template>
                </div>

                <div class="col mt-3 pe-3 ps-3" v-bind:style="getDetailStyle()">
                    <div class="mt-3 h-100">
                        <div class="position-relative h-100" v-if="aiResult.status == 0">
                            <template v-for="(item,index) in aiResult.imageSigns">
                                <template v-if="item.show && item.type != 'del'" v-for="(box,boxIndex) in item.bboxs">
                                    <div class="position-absolute w-100" v-bind:class="{'mt-4':index>0}" v-if="boxIndex==0" v-bind:style="getAreaStyle(box,index)">
                                        <div class=" sentence-text-box" v-bind:class="{'good-area-box':item.type=='good','usage-area-box':item.type=='usage','error-area-box':item.type=='error','wrong-area-box':item.type=='wrong'}">
                                            <div v-if="!item.isEdit">
                                                {{item.analysis}}
                                            </div>
                                            <div v-else>
                                                <textarea class="form-control" v-model="item.editContent" rows="2" v-if="item.type!='wrong'"></textarea>
                                                <textarea class="form-control" v-model="item.editContent" rows="1" v-else></textarea>
                                            </div>
                                            <span class="num position-absolute" v-if="item.type=='good'" >好句{{getSignNum(item,index)}}</span>
                                            <span class="num position-absolute" v-if="item.type=='usage'" >引用{{getSignNum(item,index)}}</span>
                                            <span class="num position-absolute" v-if="item.type=='error'" >问题句{{getSignNum(item,index)}}</span>
                                            <span class="num position-absolute" v-if="item.type=='wrong'" >错词{{getSignNum(item,index)}}</span>
                                        </div>
                                    </div>
                                </template>
                            </template>
                        </div>
                    </div>
                </div>
            </div>


            <div class="mt-3 pt-3 ps-3 pe-3 pb-3">
                <div class="d-flex align-items-center">
                    <div class="d-flex align-items-center">
                        <span class="fw-bold">分数</span>
                        <span class="fw-bold color-action ps-2" style="font-size:32px;">{{aiResult.paperScore}}分</span> 
                        <span class="preview_level_score_sign ms-2">{{aiResult.wordCount}}字</span>
                        <span class="preview_level_score_sign ms-2" >{{levelLabel}} </span>
                    </div>
                </div>
                <div class="pt-3">
                    <div v-if="aiResult.status == 0 && aiResult.detailedAnalysis &&  aiResult.detailedAnalysis.length>0">
                        <div class="fw-bold font-size-18 color-action" >
                            维度分析
                        </div>
                        <div class="row">
                            <template v-for="(item,index) in aiResult.detailedAnalysis">
                                <div class="col-6 mt-3">
                                    
                                    <div class="dimension-box pb-3" style="min-height: 110px;">
                                        <div class="d-flex align-items-center">
                                                <div class="d-flex align-items-center">
                                                    <span class="fw-bold font-size-16 pe-2">{{item.name}}</span>
                                                </div>
                                            </div>

                                            <div class="pt-3 d-flex">
                                                <div class="fw-bold pe-3">优点：</div>
                                                <div class="col">{{teacherHtml(item.advantage)}}</div>
                                            </div>
                                            <div class="pt-2 d-flex" >
                                                <div class="fw-bold pe-3">不足：</div>
                                                <div class="col">{{teacherHtml(item.comment)}}</div>
                                            </div>    
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div> 
                    <div v-if="seriousErrors.length>0">
                        <div class="fw-bold font-size-18 mt-3 color-action" >
                            严重问题诊断
                        </div>
                        <div class="pt-3">
                            <span class="serious-error-item pe-3" v-for="(label,index) in seriousErrors">
                                    {{label}}
                            </span>
                        </div>
                    </div>
                    <div class="mt-3" v-if="aiResult.status == 0">
                        <div class="fw-bold font-size-18 color-action" >
                            教师评语
                        </div>
                        <div class="row mt-2">
                            <div v-html="teacherHtml(aiResult.teacherComment.content)"></div>
                        </div>
                    </div>
                    <div v-if="aiResult.status == 0 && aiResult.inlineCorrections && aiResult.inlineCorrections.length>0">
                            <div class="fw-bold font-size-18 mt-3 color-action" >
                                逐句批改
                            </div>
                            <div class="pt-2">
                                <template v-for="(item,index) in aiResult.inlineCorrections">
                                    <div v-bind:class="{'mt-3':index>0}">
                                        <div class="dimension-box ps-3">句子{{index+1}}</div>
                                        <div class="ps-3">
                                            <div class="pt-3 d-flex">
                                                <div class="fw-bold pe-3">原句：</div>
                                                <div class="col">{{teacherHtml(item.originalSen)}}</div>
                                            </div>
                                            <div class="pt-3 d-flex">
                                                <div class="fw-bold pe-3">问题：</div>
                                                <div class="col">{{teacherHtml(item.issue)}}</div>
                                            </div>
                                            <div class="pt-3 d-flex">
                                                <div class="fw-bold pe-3">改正：</div>
                                                <div class="col">{{teacherHtml(item.correction)}}</div>
                                            </div>
                                            <div class="pt-3 d-flex">
                                                <div class="fw-bold pe-3">解释：</div>
                                                <div class="col">{{teacherHtml(item.explanation)}}</div>
                                            </div>
                                        </div>
                                    </div>
                                </template>
                            </div>
                        </div>
                </div>
            </div>
        </div>



    </div>
</div>
<script>
    var app = Vue.createApp({
        data(){
            return {
                studentInfo:<?=Json::encode($studentInfo)?>,
                aiResult:<?=Json::encode($aiResult)?>,
                levelStandards:<?=Json::encode($levelStandards)?>,
                questionInfo:<?=Json::encode($questionInfo)?>,
                studentName:'',
                maxHeight:0,
                wrongNum:0,
                delNum:0,
            }
        },
        computed:{
            seriousErrors:function (){
                var list = [];

                if(parseFloat(this.aiResult.wordCount) < (parseFloat(this.questionInfo.wordNumber))){
                    list.push('字数不足')
                } 
                if(this.wrongNum > 0 && this.wrongNum > (parseFloat(this.aiResult.wordCount) * 0.2)){
                    list.push('错别字严重')
                }
                if(this.delNum > 0 && this.delNum > (parseFloat(this.aiResult.wordCount) * 0.1)){
                    list.push('卷面不整')
                }
                if(this.errorNum > 10){
                    list.push("语病严重")
                }
                return list;
            },
            errorNum:function (){
                num = 0;
                var wrongNum = 0;
                var delNum = 0;
                for(var i in this.aiResult.imageSigns){
                    var item = this.aiResult.imageSigns[i];
                    if(item.show){
                        if(item.type == 'error'){
                            num ++;
                        }else if(item.type == 'wrong'){
                            wrongNum ++;
                        }else if(item.type == 'del'){
                            delNum ++;
                        }
                    }
                }
                this.wrongNum = wrongNum;
                this.delNum = delNum;
                if(this.aiResult.inlineCorrections){
                    return this.aiResult.inlineCorrections.length;
                }
                return 0;
            },
            imageRate:function (){
                return 800/this.aiResult.img_width;
            },
            levelLabel:function (){
                for(var i in this.levelStandards){
                    var item = this.levelStandards[i];
                    console.log(item)
                    if(parseFloat(this.aiResult.paperScore) >= parseFloat(item.standard)){
                        return item.name;
                    }
                }
                return '';
            },
            textPositions:function(){
                var lineHeight = 22;
                var areaPadding = 40;
                var wrongHeight = 35;
                var delHeight = 35;
                var lastBottom = 0;
                var list = [];
                var delNum = 0;
                var lastLeft = 0;
                var lastType = '';
                for(var i in this.aiResult.imageSigns){
                    var item = this.aiResult.imageSigns[i];
                    var coordinate = {};

                    if(item.type == 'del'){
                    }else{
                        if(item.show){
                            var top = 0;
                            var height = 0;
                            if(item.type == 'del'){
                                top = this.imageRate * item.top;
                                height = lineHeight + delHeight;
                            }else{
                                delNum = 0;
                                for(var j in item.bboxs){
                                    var box = item.bboxs[j];
                                    top = this.imageRate * box.top;
                                    break;
                                }
                                var strlength = item.analysis.length;
                                var rowNum = Math.ceil(strlength/15);
                                if(item.type == 'wrong'){
                                    height = rowNum * lineHeight + wrongHeight;
                                }else{
                                    height = rowNum * lineHeight + areaPadding;
                                }
                                lastLeft = 0;
                            }
                            if(lastBottom < top){
                                coordinate = {top:top,left:0};
                                lastBottom = top + height;
                                lastLeft = 0;
                                delNum = 0;
                            }else{
                                if(item.type != 'del' || lastType != item.type){
                                    lastLeft = 0;
                                    coordinate = {top:lastBottom,left:0};
                                    lastBottom = lastBottom + height;
                                    delNum = 0;
                                }else{
                                    lastLeft += 80;
                                    if(lastLeft>150){
                                        lastLeft = 0;
                                        coordinate = {top:lastBottom,left:0};
                                        lastBottom = lastBottom + height;
                                    }else{
                                        coordinate = {top:lastBottom-height,left:lastLeft};
                                    }
                                }
                            }
                            lastType = item.type;

                        }
                    }
                    list.push(coordinate)
                }
                this.maxHeight = lastBottom;
                return list;
            }

        },
        created:function (){
            console.log(this.aiResult)
            console.log(this.questionInfo)
        },
        methods:{
            getDetailStyle:function (){
                if(this.aiResult.status == 0){
                    var imgShowHeight = this.imageRate * this.aiResult.img_height;
                    if(imgShowHeight<this.maxHeight){
                        return "height:"+(this.maxHeight + 30)+'px';
                    }
                }
                return '';
            },
            teacherHtml:function (content){
                return content.replace(/\n/g, '<br>')
            },
            getEvaluateUseClass:function (type){
                for(var i in this.aiResult.teacherComment.evaluate){
                    var temp = this.aiResult.teacherComment.evaluate[i]
                    if(temp == type){
                        return 'selected';
                    }
                }
                return '';
            },
            getSignNum:function (sign,index){
                var num = 0;
                for(var i in this.aiResult.imageSigns){
                    var item = this.aiResult.imageSigns[i];
                    if(item.show && item.type == sign.type){
                        num ++;
                    }
                    if(i == index){
                        if(sign.type == 'wrong'){
                            console.log(item)
                            console.log(sign)
                            console.log(index)
                            console.log(num)
                        }
                        break;
                    }
                }

                return num;
            },
            getAreaStyle:function (box,index){
                var textPosition = this.textPositions[index];
                var top = this.imageRate * box.top;
                return "top:"+textPosition.top+"px;left:"+textPosition.left+"px;";
            },
            getGoodStyle:function (box){
                var top = this.imageRate * box.bottom;
                var left = this.imageRate * box.left;
                var width = (box.right - box.left) * this.imageRate;
                return "top:"+top+"px;left:"+left+'px;width:'+width+"px;";
            },
            getBoxStyle:function (box,item){
                if(box.type == 'del'){
                    var top = this.imageRate * (box.top + box.height);
                    var left = this.imageRate * (box.left + box.width);
                }else if(item.type == 'wrong'){
                    var top = this.imageRate * box.top;
                    var left = this.imageRate * box.left;
                    var width = (box.right - box.left) * this.imageRate;
                    var height = (box.bottom - box.top) * this.imageRate; 
                    return "top:"+top+"px;left:"+left+"px;width:"+width+"px;height:"+height+"px;";
                }else{
                    var top = this.imageRate * box.bottom;
                    var left = this.imageRate * box.right;
                }
                return "top:"+top+"px;left:"+left+'px;';
            }
        }

    }).mount('#report-writing-preview')
</script>


<?php

use common\enums\CardContentType;
use frontend\modules\home\widgets\writing\DimensionScoreWidget;
use frontend\modules\home\widgets\writing\EvaluateWidget;
use frontend\modules\home\widgets\writing\ExamLevelScoreWidget;
use frontend\modules\home\widgets\writing\ExamOverallWidget;
use frontend\modules\home\widgets\writing\QualityEnglishWidget;
use frontend\modules\home\widgets\writing\QualityWidget;
use frontend\modules\home\widgets\writing\StudentScoreWidget;

/**
 * @var \common\models\exam\Exam $examInfo
 */

$this->title = '作业报告-'.$this->params['examGroup']->title;
$cardInfo = \yii\helpers\Json::decode($examInfo->card_info);
$isEnglish = false;
if($cardInfo['contentTypeId'] == CardContentType::EnglishWriting->value){
    $isEnglish = true;
}
?>

<div class="mt-4">
    <div class="homework-content">
         <?=ExamOverallWidget::widget(['examId' => $examInfo->id])?>

        <div class="mt-4">
            <?=StudentScoreWidget::widget(['examId' => $examInfo->id])?>
        </div> 
        <div class="mt-4">
            <?=ExamLevelScoreWidget::widget(['examId' => $examInfo->id])?>
        </div>
        <?php if($isEnglish): ?>
            <div class="mt-4">
                <?=QualityEnglishWidget::widget(['examId' => $examInfo->id])?>
            </div>
        <?php else: ?>
            <div class="mt-4">
                <?=DimensionScoreWidget::widget(['examId' => $examInfo->id])?>
            </div>
            <div class="mt-4">
                <?=QualityWidget::widget(['examId' => $examInfo->id])?>
            </div>
            <div class="mt-4">
                <?=EvaluateWidget::widget(['examId' => $examInfo->id])?>
            </div>
        <?php endif; ?>
    </div>
</div>

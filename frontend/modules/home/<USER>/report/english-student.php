<?php

use yii\helpers\Json;

/**
 * @var \common\models\exam\Exam $examInfo
 */

$this->title = '学情报告-'.$this->params['examGroup']->title;
$classInfo = Json::decode($examInfo->class_info);
$gradeId = $classInfo['grade']['value'];

?>
<style>
    .good-line{
        background-image: url("/img/home/<USER>");
        background-repeat: repeat-x;
        height: 8px;
    }
    .good-line .num{
        top:-25px;
        padding:0px 5px;
        background: #3C71F6;
        border-radius: 8px;
        color: #ffffff;
        font-size: 12px;
    }
    .usage-line{
        background-image: url("/img/home/<USER>");
        background-repeat: repeat-x;
        height: 8px;
    }
    .usage-line .num{
        top:-25px;
        padding:0px 5px;
        background: #3FC14E;
        border-radius: 8px;
        color: #ffffff;
        font-size: 12px;
    }
    .del-word-box{
        color:#FF8608;
    }

    .error-line{
        background-image: url("/img/home/<USER>");
        background-repeat: repeat-x;
        height: 7px;
    }
    .error-line .num{
        top:-25px;
        padding:0px 5px;
        background: #F0A402;
        border-radius: 8px;
        color: #ffffff;
        font-size: 12px;
    }
    .sentence-text-box .num{
        top:-16px;
        padding:0px 5px;
        border-radius: 8px;
        color: #ffffff;
        font-size: 12px;
    }
    .sentence-text-box{
        border-radius: 8px;
        padding:5px;
        min-width: 60px;
    }
    .wrong-word-box{
        border:1px solid #DE4646;
        border-radius: 50%;
    }
    .good-area-box{
        border: 1px solid #DBE5FF;
    }
    .good-area-box .num{
        background: #3C71F6;
    }
    .error-area-box{
        border: 1px solid #EFE5D2;
    }
    .error-area-box .num{
        background: #F0A402;
    }

    .wrong-area-box{
        border: 1px solid #FBDCDC;
    }
    .usage-area-box{
        border: 1px solid #DBF0DD;
    }
    .usage-area-box .num{
        background: #3FC14E;
    }
    .wrong-area-box .num{
        background: #DE4646;
    }
    .del-area-box {
        background: #F0A402;
        padding:3px;
    }
    .color-action{
        color:#3C71F6;
    }
    .level-score-sign{
        border: 1px solid #DADADA;
        border-radius: 4px;
        padding:5px 10px;
    }
    .dimension-box{
        background: #F1F5FD;
        padding:6px 12px;
    }
    .serious-error-item{
        border: 1px solid #F88815;
        border-radius: 4px;
        padding:6px 12px;
        color: #F88815;
    }
    .evaluate-area-box{
        border: 1px solid #8EBF4D;
        border-radius: 4px;
        padding:5px 10px;
    }
    .evaluate-area-box .evaluate-area-content::after{
        content:'';
        position: absolute;
        right: -1px;
        top: -1px;
        width: 21px;
        height: 21px;
        background-image: url("/img/home/<USER>");
        background-size: 100%;
    }
    .evaluate-area-box.selected .evaluate-area-content::after{
        background-image: url("/img/home/<USER>");
    }
    .ai_report_write_elements{
        background: #F7F7F7;
        border-radius: 4px;
    }
    .ai_report_write_elements_levels span{
        background: #E8E8E8;
        border-radius: 2px;
        padding:5px 10px;
    }
    .ai_report_write_elements_levels span.selected1{
        background: #69B85C;
        color:#ffffff;
    }
    .ai_report_write_elements_levels span.selected2{
        background: #8EC129;
        color:#ffffff;
    }
    .ai_report_write_elements_levels span.selected3{
        background: #69B85C;
        color:#C9D203;
    }
    .good-sign-tag{ 
    }
    .good-sign-tag::after {
        content: "";
        position: absolute;
        bottom: -7px; /* 调整波浪位置 */
        left: 0;
        width: 100%;
        height: 7px; /* 波浪高度 */
        background-image: url("/img/home/<USER>");
        background-repeat: repeat-x; 
    }
    .error-sign-tag::after {
        content: "";
        position: absolute;
        bottom: -7px; /* 调整波浪位置 */
        left: 0;
        width: 100%;
        height: 7px; /* 波浪高度 */
        background-image: url("/img/home/<USER>");emphasize_mark
        background-repeat: repeat-x; 
    }
    .wrong-sign-tag{
        border:1px solid #DE4646;
        padding:0px 2px;        
        border-radius: 50%;
    }

</style>
<div class="mt-3">
    <div class=" d-flex align-items-start">
        <div class="study-student-list-box writing-word-student-box" >
            <?=$this->render('_word_student_list',['studentList' => $studentList,'examInfo' => $examInfo,'student_id' => $student_id,])  ?>
        </div>
        <div class="study-student-report-box ms-3 student-report-box " id="student-ai-score-box">
            <div v-if="studentInfo.absent == 0">
                <div class="bg-white-box-show-box">
                    <div class="d-flex">
                        <div class="pt-3 ps-3 pe-3 pb-3">
                            <div class="d-flex ps-2 pe-2">
                                <div><span class="cursor-pointer" v-on:click="showQuestionInfo" style="color:#8FA1D0;">题目要求
            <i class="fa fa-question-circle"></i>   </span></div>
                                <div class="col text-end">
                                    <label class="me-2 ps-1 pe-1 position-relative ">
                                        <span class="wrong-sign-tag">错</span>字
                                        <span class="position-absolute" style="bottom: -8px;
    right: 10px;
    font-size: 12px;"><i class="fa fa-close text-danger"></i></span>
                                    </label>
                                    <label class=" position-relative">涂抹
                                        <span class="position-absolute" style="bottom: -8px;color: #F0A402;
    right: 1px;
    font-size: 12px;"><i class="fa fa-navicon"></i></span>
                                    </label>
                                </div>
                            </div>
                            <div class="position-relative mt-3" style="width:770px" >
                                <div>
                                    <img v-bind:src="aiResult.image_url" style="width:100%;">
                                </div>
                                <template v-if="aiResult.status == 0" v-for="(item,index) in aiResult.imageSigns">
                                    <template v-if="item.show && item.type == 'good'" v-for="(box,boxIndex) in item.bboxs">
                                        <div class="good-line position-absolute" v-bind:style="getGoodStyle(box,item)">
                                            <span class="num position-absolute" v-if="boxIndex == 0">{{getSignNum(item,index)}}</span>
                                        </div>
                                    </template>
                                    <template v-if="item.show && item.type == 'usage'" v-for="(box,boxIndex) in item.bboxs">
                                        <div class="usage-line position-absolute" v-bind:style="getGoodStyle(box,item)">
                                            <span class="num position-absolute" v-if="boxIndex == 0">{{getSignNum(item,index)}}</span>
                                        </div>
                                    </template>
                                    <template v-if="item.show && item.type == 'error'" v-for="(box,boxIndex) in item.bboxs">
                                        <div class="error-line position-absolute" v-bind:style="getGoodStyle(box)">
                                            <span class="num position-absolute" v-if="boxIndex == 0">{{getSignNum(item,index)}}</span>
                                        </div>
                                    </template>
                                    <template v-if="item.show && item.type == 'wrong'"  v-for="(box,boxIndex) in item.bboxs">
                                        <div class="wrong-word-box text-danger position-absolute"  v-bind:style="getBoxStyle(box,item)">
                                            <icon class="fa fa-close "></icon>
                                            <span class="num position-absolute" v-if="boxIndex == 0">{{getSignNum(item,index)}}</span>
                                        </div>
                                    </template>
                                    <template v-if="item.show && item.type == 'del'">
                                        <div class="del-word-box  position-absolute"  v-bind:style="getBoxStyle(item)">
                                            <icon class="fa fa-navicon"></icon>
                                            <span class="num position-absolute" >{{getSignNum(item,index)}}</span>
                                        </div>
                                    </template>
                                </template>
                            </div>

                        </div>
                        <div class="col mt-3 pe-3 ps-3" v-bind:style="getDetailStyle()">
                            <div class="mt-3 h-100">
                                <div class="position-relative h-100" v-if="aiResult.status == 0">
                                    <template v-for="(item,index) in aiResult.imageSigns">
                                        <template v-if="item.show && item.type != 'del'" v-for="(box,boxIndex) in item.bboxs">
                                            <div class="position-absolute w-100" v-bind:class="{'mt-4':index>0}" v-if="boxIndex==0" v-bind:style="getAreaStyle(box,index)">
                                                <div class=" sentence-text-box" v-bind:class="{'good-area-box':item.type=='good','usage-area-box':item.type=='usage','error-area-box':item.type=='error','wrong-area-box':item.type=='wrong'}">
                                                    <div v-if="!item.isEdit">
                                                        {{item.analysis}}
                                                    </div>
                                                    <div v-else>
                                                        <textarea class="form-control" v-model="item.editContent" rows="2" v-if="item.type!='wrong'"></textarea>
                                                        <textarea class="form-control" v-model="item.editContent" rows="1" v-else></textarea>
                                                    </div>
                                                    <span class="num position-absolute" v-if="item.type=='good'" >好句{{getSignNum(item,index)}}</span>
                                                    <span class="num position-absolute" v-if="item.type=='usage'" >引用{{getSignNum(item,index)}}</span>
                                                    <span class="num position-absolute" v-if="item.type=='error'" >问题句{{getSignNum(item,index)}}</span>
                                                    <span class="num position-absolute" v-if="item.type=='wrong'" >错词{{getSignNum(item,index)}}</span>
                                                </div>
                                                <div class="text-end">
                                                    <span class="color-action cursor-pointer" v-on:click="editSign(item,index)" v-if="!item.isEdit">编辑</span>
                                                    <span class="color-action cursor-pointer" v-on:click="signSave(item,index)" v-else>保存</span>
                                                    <span class="cursor-pointer ps-3" v-on:click="cancelSignEdit(item,index)" v-if="item.isEdit">取消</span>
                                                    <span class="color-action cursor-pointer ps-3 text-danger" v-on:click="deleteSign(item,index)" >删除</span>
                                                </div>
                                            </div>
                                        </template>
                                    </template>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mt-3 bg-white-box-show-box pt-3 ps-5 pe-5 pb-3">
                    <div class="d-flex align-items-center">
                        <div class="d-flex align-items-center">
                            <span class="fw-bold">分数</span>
                            <span class="fw-bold color-action ps-2" style="font-size:32px;" >{{aiResult.paperScore}}分</span>
                            <span class="level-score-sign ms-2">{{aiResult.wordCount}}字</span>
                            <span class="level-score-sign ms-2" v-if="levelLabel!=''">{{levelLabel}} </span>
                        </div>
                        <div class="col text-end"  v-if="aiResult.status == 0">
                            <span class="color-action cursor-pointer" v-on:click="recoverDimensions">
                                <i class="fa fa-refresh"></i>恢复初始分析
                            </span>
                        </div>
                    </div>
                    <div class="pt-3">
                        <div  v-if="aiResult.status == 0">
                            <div class="fw-bold font-size-18" >
                                维度分析
                            </div>
                            <div class="row">
                                <template v-for="(item,index) in aiResult.detailedAnalysis">
                                    <div class="col-6 mt-3">
                                        <div class="dimension-box pb-3" style="min-height: 110px;">
                                            <div class="d-flex align-items-center">
                                                <div class="d-flex align-items-center">
                                                    <span class="fw-bold font-size-16 pe-2">{{item.name}}</span>
                                                </div>
                                            </div>

                                            <div class="pt-3 d-flex">
                                                <div class="fw-bold pe-3">优点：</div>
                                                <div class="col">{{teacherHtml(item.advantage)}}</div>
                                            </div>
                                            <div class="pt-2 d-flex" >
                                                <div class="fw-bold pe-3">不足：</div>
                                                <div class="col">{{teacherHtml(item.comment)}}</div>
                                            </div>
                                        </div>
                                    </div>
                                </template>
                            </div>
                        </div>
                        <div v-if="seriousErrors.length>0">
                            <div class="fw-bold font-size-18 mt-3" >
                                严重问题诊断
                            </div>
                            <div class="pt-3">
                                <span class="serious-error-item pe-3" v-for="(label,index) in seriousErrors">
                                    {{label}}
                                </span>
                            </div>
                        </div>
                        <div v-if="aiResult.status == 0 && aiResult.inlineCorrections && aiResult.inlineCorrections.length>0">
                            <div class="fw-bold font-size-18 mt-3" >
                                逐句批改
                            </div>
                            <div class="pt-3">
                                <template v-for="(item,index) in aiResult.inlineCorrections">
                                    <div  v-bind:class="{'mt-3':index>0}">
                                        <div class="dimension-box ps-3 d-flex">
                                            <div>句子{{index+1}}</div>
                                            
                                            <div class="text-end col">
                                                <span class="color-action cursor-pointer ps-3 text-danger" v-on:click="deleteInlineCorrect(item,index)" >删除</span>
                                            </div>

                                        </div>
                                        <div class="ps-3">
                                            <div class="pt-3 d-flex">
                                                <div class="fw-bold pe-3">原句：</div>
                                                <div class="col">{{teacherHtml(item.originalSen)}}</div>
                                            </div>
                                            <div class="pt-3 d-flex">
                                                <div class="fw-bold pe-3">问题：</div>
                                                <div class="col">{{teacherHtml(item.issue)}}</div>
                                            </div>
                                            <div class="pt-3 d-flex">
                                                <div class="fw-bold pe-3">改正：</div>
                                                <div class="col">{{teacherHtml(item.correction)}}</div>
                                            </div>
                                            <div class="pt-3 d-flex">
                                                <div class="fw-bold pe-3">解释：</div>
                                                <div class="col">{{teacherHtml(item.explanation)}}</div>
                                            </div>
                                        </div>
                                    </div>
                                </template>
                            </div>
                        </div>
                        <div class="mt-3"  v-if="aiResult.status == 0">
                            <div class="fw-bold font-size-18" >
                                教师评语
                            </div>
                            <div class="mt-2">
                                <div class="mt-3 dimension-box pt-2 pb-2 position-relative"  style="min-height: 250px;">
                                        <textarea class="form-control" v-model="aiResult.teacherComment.content" rows="8">

                                    </textarea>

                                    <div  class="position-absolute" style="bottom: 1rem;right: 1rem;">
                                        <span v-on:click="updateComment" class="cursor-pointer color-action">保存</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div v-else class="bg-white-box-show-box min-height-500 d-flex align-items-center">

                <div class="col text-center">
                    <div class="text-center">
                        <img src="/img/home/<USER>">
                    </div>
                    <div class="text-center pt-3 pb-3 text-desc">学生未参与该次作业</div>
                </div>
            </div>
        </div>
    </div>
</div>

<?=$this->render('/common/_report_writing',['isEnglish' => $isEnglish,'examInfo' => $examInfo,'examStudent' => $studentInfo,'questionInfo' => $questionInfo,'levelStandards' => $levelStandards,'aiResult' => $aiResult])?>
<script>
    var app = Vue.createApp({
        data(){
            return {
                studentInfo:<?=Json::encode($studentInfo)?>,
                aiResult:<?=Json::encode($aiResult)?>,
                levelStandards:<?=Json::encode($levelStandards)?>,
                questionInfo:<?=Json::encode($questionInfo)?>, 
                studentName:'',
                maxHeight:0,
                wrongNum:0,
                delNum:0,
            }
        },
        computed:{
            seriousErrors:function (){
                var list = [];
                if(parseFloat(this.aiResult.wordCount) < (parseFloat(this.questionInfo.wordNumber))){
                    list.push('字数不足')
                }
                if(this.wrongNum > 0 && this.wrongNum > (parseFloat(this.aiResult.wordCount) * 0.2)){
                    list.push('错别字严重')
                }
                if(this.delNum > 0 && this.delNum > (parseFloat(this.aiResult.wordCount) * 0.1)){
                    list.push('卷面不整')
                }
                if(this.errorNum > 10){
                    list.push("语病严重")
                }
                return list;
            },
            errorNum:function (){
                num = 0;
                var wrongNum = 0;
                var delNum = 0;
                for(var i in this.aiResult.imageSigns){
                    var item = this.aiResult.imageSigns[i];
                    if(item.show){
                        if(item.type == 'wrong'){
                            wrongNum ++;
                        }else if(item.type == 'del'){
                            delNum ++;
                        }
                    }
                }
                this.wrongNum = wrongNum;
                this.delNum = delNum;
                if(this.aiResult.inlineCorrections){
                    return this.aiResult.inlineCorrections.length;
                }
                return 0;
            },
            imageRate:function (){
                return 770/this.aiResult.img_width;
            },
            levelLabel:function (){
                for(var i in this.levelStandards){
                    var item = this.levelStandards[i];
                    console.log(item)
                    if(parseFloat(this.aiResult.paperScore) >= parseFloat(item.standard)){
                        return item.name;
                    }
                }
                return '';
            },
            textPositions:function(){
                var lineHeight = 22;
                var areaPadding = 60;
                var wrongHeight = 35;
                var delHeight = 35;
                var lastBottom = 0;
                var list = [];
                var delNum = 0;
                var lastLeft = 0;
                var lastType = '';
                var maxHeight = 0;
                for(var i in this.aiResult.imageSigns){
                    var item = this.aiResult.imageSigns[i];
                    var coordinate = {};

                    if(item.type == 'del'){
                    }else{
                        if(item.show){
                            var top = 0;
                            var height = 0;
                            if(item.type == 'del'){
                                top = this.imageRate * item.top;
                                height = lineHeight + delHeight;
                            }else{
                                delNum = 0;
                                for(var j in item.bboxs){
                                    var box = item.bboxs[j];
                                    top = this.imageRate * box.top;
                                    break;
                                }
                                var strlength = item.analysis.length;
                                var rowNum = Math.ceil(strlength/15);
                                if(item.type == 'wrong'){
                                    height = rowNum * lineHeight + wrongHeight;
                                }else{
                                    height = rowNum * lineHeight + areaPadding;
                                }
                                lastLeft = 0;
                            }
                            if(lastBottom < top){
                                coordinate = {top:top,left:0};
                                lastBottom = top + height;
                                lastLeft = 0;
                                delNum = 0;
                            }else{
                                if(item.type != 'del' || lastType != item.type){
                                    lastLeft = 0;
                                    coordinate = {top:lastBottom,left:0};
                                    lastBottom = lastBottom + height;
                                    delNum = 0;
                                }else{
                                    lastLeft += 80;
                                    if(lastLeft>150){
                                        lastLeft = 0;
                                        coordinate = {top:lastBottom,left:0};
                                        lastBottom = lastBottom + height;
                                    }else{
                                        coordinate = {top:lastBottom-height,left:lastLeft};
                                    }
                                }
                            }
                            lastType = item.type;

                        }
                    }

                    list.push(coordinate)
                }
                this.maxHeight = lastBottom;
                return list;
            }

        },
        created:function (){
        },
        methods:{
            getDetailStyle:function (){
                if(this.aiResult.status == 0){
                    var imgShowHeight = this.imageRate * this.aiResult.img_height;
                    console.log("图片高度====="+imgShowHeight)
                    console.log("计算高度====="+this.maxHeight)
                    if(imgShowHeight<this.maxHeight){
                        return "height:"+(this.maxHeight + 30)+'px';
                    }
                }
                return '';
            },
            updateRecoverResult:function (type,result){
                if(type == 'all'){
                    this.aiResult['imageSigns'] = result['imageSigns'];
                }
                this.aiResult.inlineCorrections = result.inlineCorrections;
                this.aiResult['paperScore'] = result['paperScore'];
                this.aiResult['dimensions'] = result['dimensions'];
            },
            recoverDimensions:function (){
                this.recoverAiResult('comment')
            },
            recoverAiResult:function (type){

                var _this = this;
                $.ajax({
                    type: 'post',
                    url: '<?=\yii\helpers\Url::toRoute('report-writing/recover-ai-result')?>',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        examId:<?=Yii::$app->request->get('exam_id')?>,
                        studentId:this.studentInfo.student_id,
                        type:type
                    }),
                    success: function (data) {
                        if(data.code == 1){
                            _this.updateRecoverResult(type,data.data)
                        }else{
                            toastr.error(data.msg, '错误提示!', {positionClass: 'toast-center'})
                        }
                    },
                    error:function (data){
                        toastr.error(data.responseText, '错误提示!', {positionClass: 'toast-center'})
                    }
                });
            },
            showQuestionInfo:function (){
                $("#word_question_info_box").modal('show')
            },
            updateComment:function (){
                this.saveAiResult('comment');
            },
            editSign:function (item,index){
                item.editContent = item.analysis;
                item.isSave = 0;
                item.isEdit = true;
            },
            signSave:function (item,index){
                item.isSave = 1;
                this.saveAiResult('updateSign',item,index)
            },
            cancelSignEdit:function (item,index){
                item.isEdit = false;
            },
            deleteInlineCorrect:function(item,index){
                console.log(item)
                console.log(index)
                this.saveAiResult('deleteInlineCorrect',item,index)
            },
            deleteSign:function (item,index){
                console.log(item)
                console.log(index)
                this.saveAiResult('delSign',item,index)
            },
            dimensionEdit:function (item,index){
                item.editScore = item.dimenScore;
                item.editComment = item.comment;
                item.isSave = 0;
                item.isEdit = true;
            },
            dimensionSave:function (item,index){
                item.isSave = 1;
                this.saveAiResult('dimension',item,index)
            },
            dimensionCancelEdit:function (item,index){
                item.isEdit = false;
            },
            updateSaveItem:function (type,item,index,cloneResult){
                if(type == 'dimension'){
                    item = this.aiResult.dimensions[index];
                    item.comment = item.editComment;
                    item.dimenScore = item.editScore;
                    item.isSave = 0;
                    item.isEdit = false;
                    this.aiResult.paperScore = cloneResult.paperScore;
                }else if(type =='updateSign'){
                    item = this.aiResult.imageSigns[index];
                    item.analysis = item.editContent;
                    item.isSave = 0;
                    item.isEdit = false;
                }else if(type == 'delSign'){
                    console.log(item)
                    item = this.aiResult.imageSigns[index];
                    item.show = false;
                }else if(type == 'deleteInlineCorrect'){
                    console.log(item)
                    item = this.aiResult.inlineCorrections.splice(index,1); 
                }
            },
            saveAiResult:function (type,item,index){
                const cloneResult = JSON.parse(JSON.stringify(this.aiResult));
                if(type == 'dimension'){
                    var paperScore = 0;
                    for(var i in cloneResult.dimensions){
                        var dimension = cloneResult.dimensions[i];
                        if(i == index){
                            paperScore += parseFloat(dimension.editScore)
                        }else{
                            paperScore += parseFloat(dimension.dimenScore)
                        }
                    }
                    cloneResult.paperScore = paperScore;
                }else if(type == 'delSign'){
                    var item = cloneResult.imageSigns[index];
                    item.show = false;
                }else if(type == 'deleteInlineCorrect'){
                    cloneResult.inlineCorrections.splice(index,1);
                }

                var _this = this;
                $.ajax({
                    type: 'post',
                    url: '<?=\yii\helpers\Url::toRoute('report-writing/student-result-save')?>',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        examId:<?=Yii::$app->request->get('exam_id')?>,
                        studentId:this.studentInfo.student_id,
                        studentResult:cloneResult
                    }),
                    success: function (data) {
                        if(data.code == 1){
                            _this.updateSaveItem(type,item,index,cloneResult)
                        }else{
                            toastr.error(data.msg, '错误提示!', {positionClass: 'toast-center'})
                        }
                    },
                    error:function (data){
                        toastr.error(data.responseText, '错误提示!', {positionClass: 'toast-center'})
                    }
                });
            },
            userEvaluate:function (type){
                if(!this.aiResult.teacherComment.evaluate){
                    this.aiResult.teacherComment.evaluate = [];
                }
                if(this.aiResult.teacherComment.evaluate.length>0){
                    var typeIndex = this.aiResult.teacherComment.evaluate.indexOf(type)
                    if(typeIndex === -1){
                        this.aiResult.teacherComment.evaluate.push(type)
                    }else{
                        this.aiResult.teacherComment.evaluate.splice(typeIndex,1)
                    }
                }else{
                    this.aiResult.teacherComment.evaluate.push(type)
                }
                var content = '';
                for(var i in this.aiResult.teacherComment.evaluate){
                    var temp = this.aiResult.teacherComment.evaluate[i];
                    if(content != ''){
                        content += "\n";
                    }
                    if(temp == 'strengths'){
                        content += "作文亮点："+this.aiResult.evaluate.strengths
                    }else if(temp == 'weaknesses'){
                        content += "作文不足："+this.aiResult.evaluate.weaknesses
                    }else if(temp == 'comment'){
                        content += "综合评价："+this.aiResult.evaluate.comment
                    }else if(temp == 'suggestions'){
                        content += "改进和建议："+this.aiResult.evaluate.suggestions
                    }
                }
                this.aiResult.teacherComment.content = content;

            },
            teacherHtml:function (content){
                return content.replace(/\n/g, '<br>')
            },
            getEvaluateUseClass:function (type){
                for(var i in this.aiResult.teacherComment.evaluate){
                    var temp = this.aiResult.teacherComment.evaluate[i]
                    if(temp == type){
                        return 'selected';
                    }
                }
                return '';
            },
            getSignNum:function (sign,index){
                var num = 0;
                for(var i in this.aiResult.imageSigns){
                    var item = this.aiResult.imageSigns[i];
                    if(item.show && item.type == sign.type){
                        num ++;
                    }
                    if(i == index){
                        if(sign.type == 'wrong'){
                        }
                        break;
                    }
                }

                return num;
            },
            getAreaStyle:function (box,index){
                var textPosition = this.textPositions[index];
                var top = this.imageRate * box.top;
                return "top:"+textPosition.top+"px;left:"+textPosition.left+"px;";
            },
            getGoodStyle:function (box){
                var top = this.imageRate * box.bottom;
                var left = this.imageRate * box.left;
                var width = (box.right - box.left) * this.imageRate;
                return "top:"+top+"px;left:"+left+'px;width:'+width+"px;";
            },
            getBoxStyle:function (box,item){
                if(box.type == 'del'){
                    var top = this.imageRate * (box.top + box.height);
                    var left = this.imageRate * (box.left + box.width);
                }else if(item.type == 'wrong'){
                    var top = this.imageRate * box.top;
                    var left = this.imageRate * box.left;
                    var width = (box.right - box.left) * this.imageRate;
                    var height = (box.bottom - box.top) * this.imageRate; 
                    return "top:"+top+"px;left:"+left+"px;width:"+width+"px;height:"+height+"px;";
                }else{
                    var top = this.imageRate * box.bottom;
                    var left = this.imageRate * box.right;
                }
                return "top:"+top+"px;left:"+left+'px;';
            }
        }

    }).mount('#student-ai-score-box')
</script>
<?php

namespace frontend\modules\home\form;

use common\models\exam\Exam;
use common\models\exam\ExamStudent;
use common\models\scan\ScanBlock;
use frontend\modules\home\services\ChineseWritingReportService;
use Yii;
use yii\helpers\Json;

class WritingWordRecoverForm extends BaseForm
{
    public $examId;
    public $studentId;
    public $type;

    public function rules()
    {
        return [
            [['examId', 'studentId', 'type'], 'required'],
            [['examId', 'studentId'], 'integer'],
            [['examId'],'validateExamId'],
        ];
    }

    const TYPE_COMMENT = 'comment';
    const TYPE_ALL = 'all';

    public Exam|null $_exam;
    public ExamStudent|null $_examStudent;
    public function validateExamId($attribute, $params)
    {
        $this->_exam = Exam::findOne($this->examId);
        if(empty($this->_exam)){
            $this->addError($attribute,'考试不存在');
        }else{
            $userId = Yii::$app->user->id;
            if($userId != $this->_exam->created_by){
                $this->addError($attribute,'没有权限处理该考试');
            }
            $this->_examStudent = ExamStudent::find()->andWhere([
                'exam_id' => $this->examId,
                'student_id' => $this->studentId,
            ])->one();
            if(empty($this->_examStudent)){
                $this->addError('studentId','该学生未参与本次作业');
            }
        }
    }


    public function save()
    {
        $transaction = Yii::$app->db->beginTransaction();
        try{
            $imageIds = [];
            foreach ($this->_examStudent->scan_pages as $scanPage){
                $imageIds = array_merge($scanPage['imageIds'],$imageIds);
            }
            $scanBlocks = Scanblock::find()->andWhere(['image_id' => $imageIds])->all();
            $saveResult = null;
            foreach ($scanBlocks as $block){
                if(!empty($block->ai_result)){
                    $aiResult = Json::decode($block->ai_result);
                    if(!empty($aiResult['student'])){                         
                        $aiResult['student']['inlineCorrections'] = !empty($aiResult['overall_feedback']['inlineCorrections']) ? $aiResult['overall_feedback']['inlineCorrections']:[];
                        $saveResult = ChineseWritingReportService::recoverWordAiResult($aiResult['student'],$this->type);
                    }else{
                        $saveResult = ChineseWritingReportService::getWordAiResult($aiResult['overall_feedback'] ?? []);
                    }
                    $aiResult['student'] = $saveResult;
                    $block->ai_result = Json::encode($aiResult);
                    if(!$block->save(false,['ai_result'])){
                        $transaction->rollBack();
                        return false;
                    }
                    break;
                }
            }
            $this->_examStudent->score = $saveResult['paperScore'];
            if(!$this->_examStudent->save(false,['score'])){
                $this->addErrors($this->_examStudent->getErrors());
                $transaction->rollBack();
                return false;
            }
            $this->_exam->result_generated = false;
            if(!$this->_exam->save(false,['result_generated'])){
                $transaction->rollBack();
                $this->addErrors($this->_exam->getErrors());
                return false;
            }
            $transaction->commit();
            $this->setStudentResult($saveResult);
            return true;
        }catch (\Exception $e){
            Yii::error($e->getMessage()."\n".$e->getTraceAsString());
            $transaction->rollBack();
            $this->addError('examId',$e->getMessage());
            return false;
        }
    }

    private array $studentResult;
    public function setStudentResult($result)
    {
        $this->studentResult = $result;
    }

    public function getStudentResult()
    {
        return $this->studentResult;
    }
}
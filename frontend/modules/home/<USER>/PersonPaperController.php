<?php

namespace frontend\modules\home\controllers;

use common\enums\Stage;
use frontend\modules\home\services\PaperSearchService;
use frontend\modules\home\services\TeacherInfoService;
use Yii;
use yii\web\ForbiddenHttpException;

class PersonPaperController extends BaseZhiengController
{
    public function actionIndex()
    {

        $teacherStageCourse = TeacherInfoService::getStageCourse($this->school,$this->user);
        if(empty($teacherStageCourse)){
            throw new ForbiddenHttpException('请联系管理员设置任课信息');
        }
        $teacherStageCourses = TeacherInfoService::getStageCourseList($this->school,$this->user);
        $params = Yii::$app->request->get();
        $paperCategoryList = PaperSearchService::getPaperCategoryList(!empty($params['stage']) ? $params['stage'] : $teacherStageCourse['stage']['value'],!empty($params['course_code']) ? $params['course_code'] : $teacherStageCourse['course']['code']);

        $stage = Stage::tryFrom(!empty($params['stage']) ? $params['stage'] : $teacherStageCourse['stage']['value']);

        $gradeList =  $stage->stageGrades($this->school->getGradeType());
        $data = TeacherInfoService::getTextVersionBookList(!empty($params['stage']) ? $params['stage'] : $teacherStageCourse['stage']['value'],!empty($params['course_code']) ? $params['course_code'] : $teacherStageCourse['course']['code']);

//        $textBookList = Textbook::find()->shown()->orderIdDesc()->andWhere(['version_id' => $teacherStageCourse['textbookVersion']['id']])->all();

        $paperData = PaperSearchService::searchPersonPaper($this->user->id,$teacherStageCourse['stage']['value'],$teacherStageCourse['course']['code'],'',$params);

        return $this->render('index',[
            'teacherStageCourse' => $teacherStageCourse,
            'teacherStageCourses' => $teacherStageCourses,
            'paperCategoryList' => $paperCategoryList,
            'requestData' => $params,
            'paperList' => $paperData['list'],
            'school' => $this->school,
            'gradeList' => $gradeList,
            'textBookList' => $data['bookList'],
            'versionList' => $data['versionList'],
            'totalPage' => $paperData['totalPage'],
            'currentPage' => $paperData['currentPage'],
            'totalCount' => intval($paperData['totalCount']),
        ]);
    }
}
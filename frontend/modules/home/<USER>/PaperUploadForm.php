<?php 
namespace frontend\modules\home\form;

use backend\services\MathPixService;
use backend\services\WordAnalysisService;
use common\enums\Grade;
use common\enums\OwnerType;
use common\enums\PaperContentSplitType;
use common\enums\PaperFromGroup;
use common\enums\PaperStatus;
use common\enums\Stage;
use common\models\paper\PaperData;
use common\services\PdfQuestionService;
use common\uploader\PaperUploader;
use common\validators\EnumValidator;
use frontend\modules\home\models\CustomPaper;
use Yii;
use yii\base\Model;
use yii\helpers\Json;
use yii\web\UploadedFile;

class PaperUploadForm extends Model
{
    public $school;
    public $from_group;
    public $paperName;

    public $course_code;

    public $stage;

    public $grade;

    public $textbook_version_id;

    public $textbook_id;

    public $category_id;
    /**
     * @var UploadedFile
     */
    public $file;


    

    public function rules()
    {
        return [
            [['file'], 'file', 'skipOnEmpty' => false,'checkExtensionByMimeType' => false, 'extensions' => 'docx,pdf', 'wrongExtension' => '请选择docx或pdf文件上传', 'maxSize' => 1024 * 1024 * 10,'tooBig' => '文件过大，最大上传10M'], // 最大5MB
            [['paperName'], 'required','message'  => '试卷名称不能为空'],
            [['stage'], 'required','message'  => '请选择学段'],
            [['course_code'], 'required','message'  => '请选择科目'],
            [['textbook_version_id','textbook_id','category_id'],'integer'],
            [['grade'], EnumValidator::class, 'enumClass' => Grade::class],
            [['stage'], EnumValidator::class, 'enumClass' => Stage::class],   
            [['from_group'], EnumValidator::class, 'enumClass' => PaperFromGroup::class],
        ];
    }

    private $paperUUid;


    public function saveFile()
    {
        $transaction = PaperData::getDb()->beginTransaction();
        try{
            $model = new CustomPaper();
            $model->from_group = $this->from_group;
            $model->title = $this->paperName; 
            $model->course_code = $this->course_code;
            $model->stage = $this->stage;
            $model->grade = $this->grade;
            $model->textbook_version_id = $this->textbook_version_id;
            $model->textbook_id = $this->textbook_id;
            $model->owner_type = OwnerType::User->value;
            $model->category_id = $this->category_id;
            $model->status = PaperStatus::Editing->value;    
            if(!$model->save()){
                $transaction->rollBack();
                $this->addErrors($model->getErrors());
                return false;
            }
            
            if($this->file->extension == 'docx'){
                $result = $this->wordUpload($model);
            }elseif($this->file->extension == 'pdf'){
                $result = $this->pdfMathPixUpload($model);
            }
            if(!$result){
                $transaction->rollBack();
                return false;
            }else{
                $this->paperUUid = $model->uuid;
                $transaction->commit();
                return true;
            }
        }catch(\Exception $e){
            $transaction->rollBack();
            Yii::error($e->getMessage()."\n".$e->getTraceAsString());
            $this->addError('file',$e->getMessage());
            return false;
        }
        
    }


    public function getFileType()
    {
        return $this->file->extension;
    }

    public function getPaperUuid()
    {
        return $this->paperUUid;
    }

    public function wordUpload($paper)
    {  
        
        set_time_limit(300);
        $paperData = PaperData::find()->andWhere(['paper_id' => $paper->id])->one();
        $wordMd5 = md5_file($this->file->tempName);
        if (!empty($paperData) && $paperData->word_md5 == $wordMd5) {
            $this->addError('file','文件已经解析过');
            return false; 
        }
        $wordPics = WordAnalysisService::wordToPng($this->file,$paper->id);
        if ($wordPics['code'] == 0) {
            
            $this->addError('file','word转图片失败');
            return false;  
        }
        $data = WordAnalysisService::wordToHtml($this->file,$paper->id);
        Yii::error($data);
        if ($data['code'] == 1 && !empty($data['data'])) {
            if (empty($paperData)) {
                $paperData = new PaperData();
                $paperData->paper_id = $paper->id;
            }
            $paperData->word_md5 = $wordMd5;
            $paperData->content_split_type_id = PaperContentSplitType::WordManual;
            $paperData->word_raw_content = Json::encode($data['data']);
            $paperData->word_pics = $wordPics['data'];
            try {
                $uploader = new PaperUploader();
                $uploader->generatePdfFilename($paper->id,$this->file->name);
                $wordUrl = $uploader->writeUploadedFile($this->file); 
                $paperData->word_url = $wordUrl;
            } catch (\Exception $e) {
                $this->addError('file',$e->getMessage());
                return false; 
            } 
            if (!$paperData->save()) {
                $this->addErrors($paperData->getErrors());
                return false;  
            } else {
                return true;
            };
        } else { 
            $this->addError('file','解析失败');
            return false;  
        }
    }

     /**
     * @return array
     */
    public function pdfMathPixUpload($paper): bool
    { 
        Yii::error("开始解析pdf");
        $wordMd5 = md5_file($this->file->tempName);
        $paperData = PaperData::find()->andWhere(['paper_id' => $paper->id])->one();
        $uploader = new PaperUploader();
        $uploader->generatePdfFilename($paper->id,$this->file->name);
        $pdfUrl = $uploader->writeUploadedFile($this->file);
        $mathPixService = new MathPixService();
        $pdfId = $mathPixService->postPdf($pdfUrl);
        $pdfId = Json::decode($pdfId);
        if(empty($pdfId['pdf_id'])){
            Yii::error($pdfId);
            $this->addError('file','解析失败');
            return false;  
        }
        $pdfId = $pdfId['pdf_id'];
        $wordData = [];
        $pics = [];
        $sleepNum = 0;
        while(true){
            if($sleepNum == 30){
                Yii::error("等待超过30次===".$pdfId);
                $this->addError('file','解析失败');
                return false; 
            }
            $pdfStatus = $mathPixService->checkPreStatus($pdfId); 
            $pdfStatus = Json::decode($pdfStatus);
            if($pdfStatus['status'] == 'completed'){
                [$pics,$wordData] = PdfQuestionService::analysisJsonData($pdfId,$paper->id); 
                break;
            } 
            sleep(1);

            $sleepNum ++;
            
        } 
        if (empty($paperData)) {
            $paperData = new PaperData();
            $paperData->paper_id = $paper->id;
        }
        $paperData->word_md5 = $wordMd5;
        $paperData->content_split_type_id = PaperContentSplitType::PdfManual;
        $paperData->content = Json::encode($wordData);
        $paperData->word_pics = $pics; 
        $paperData->word_url = $pdfUrl;
        if (!$paperData->save()) { 
            $this->addErrors($paperData->getErrors());
            \Yii::error($paperData->getErrors());
            return false; 
        } else {
            return true; 
        }; 
    }

}
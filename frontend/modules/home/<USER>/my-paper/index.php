<?php

use common\enums\MyPaperSource;
use common\enums\PaperFromGroup;
use common\enums\PaperStatus;
use common\enums\RecentTimes;
use frontend\modules\home\utils\UrlUtil;
use yii\helpers\Json;
use yii\helpers\Url;


$this->title = '我的试卷';
?>
<div class="homework-content mt-4 clearfix" id="homework-box">
    <div class="paper-while-8-box paper-search-box person-paper-search">
        <div class="d-flex">
            <div class="paper-search-title pt-1">试卷来源：</div>
            <div style="flex: 3">
                <label class="p-1 ps-2 pe-2 ms-1 me-1 mb-1 " v-bind:class="{'active':from_group==''}" >
                    <a href="<?=UrlUtil::buildUrl('',['from_group' => '','page' => 1])?>">全部</a>
                </label>
                <?php foreach (PaperFromGroup::myPaperLabels() as $key=> $label): ?>
                    <label class="p-1 ps-2 pe-2 ms-1 me-1 mb-1 " v-bind:class="{'active':from_group=='<?=$key?>'}" >
                        <a href="<?=UrlUtil::buildUrl('',['from_group' => $key,'page' => 1])?>"><?=$label?></a>
                    </label>
                <?php endforeach; ?>
            </div>
            <div style="flex:1" class="text-end">
                <button href="javascript:;" class="btn ms-2 btn-sm btn-primary" v-on:click="showUploadPaper">上传试卷</button>
            </div>
        </div>
        <div class="d-flex pt-3">
            <div class="paper-search-title pt-1">类&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;型：</div>
            <div style="flex: 3">
                <label class="p-1 ps-2 pe-2 ms-1 me-1 mb-1 " v-bind:class="{'active':category_id==''}" >
                    <a href="<?=UrlUtil::buildUrl('',['category_id' => '','page' => 1])?>">全部</a>
                </label>
                <?php foreach ($paperCategoryList as $item): ?>
                    <label class="p-1 ps-2 pe-2 ms-1 me-1 mb-1 " v-bind:class="{'active':category_id=='<?=$item->id?>'}" >
                        <a href="<?=UrlUtil::buildUrl('',['category_id' => $item->id,'page' => 1])?>"><?=$item->name?></a>
                    </label>
                <?php endforeach; ?>
            </div>
        </div>
        <div class="d-flex pt-3">
            <div class="paper-search-title pt-1">加入时间：</div>
            <div style="flex: 1">
                <label class="p-1 ps-2 pe-2 ms-1 me-1 mb-1 " v-bind:class="{'active':recent_type=='' && date_range ==''}" >
                    <a href="<?=UrlUtil::buildUrl('',['start_date' => '','end_date' =>'','recent_type' => '','page' => 1])?>">全部</a>
                </label>
                <?php foreach (RecentTimes::labels() as $key => $label): ?>
                    <label class="p-1 ps-2 pe-2 ms-1 me-1 mb-1 " v-bind:class="{'active':recent_type=='<?=$key?>'}" >
                        <a href="<?=UrlUtil::buildUrl('',['start_date' => '','end_date' =>'','recent_type' => $key,'page' => 1])?>"><?=$label?></a>
                    </label>
                <?php endforeach; ?>
                <label class="date-range">
                    <el-date-picker
                        v-model="date_range"
                        type="daterange"
                        format="YYYY-MM-DD"
                        value-format="YYYY-MM-DD"
                        start-placeholder="开始时间"
                        end-placeholder="结束时间"
                    />
                </label>
            </div>
        </div>
        <div class="d-flex pt-3 paper-search-group">
            <div class="paper-search-title pt-2">高级选项：</div>
            <div style="flex: 5">
                <div class="row g-3 align-items-center">
                    <div class="col-auto">
                        <select class="form-select" v-model="stage">
                            <option v-for="(item,index) in stageList" v-bind:value="item.value">{{item.label}}</option>
                        </select>
                    </div>
                    <div class="col-auto">
                        <select class="form-select" v-model="course_code">
                            <option v-for="(item,index) in courseList" v-bind:value="item.code">{{item.name}}</option>
                        </select>
                    </div>
                    <div class="col-auto">
                        <select class="form-select" v-model="versionId">
                            <option value="">版本</option>
                            <?php if(!empty($versionList)):foreach ($versionList as $version): ?>
                                <option value="<?=$version['id']?>"><?=$version['name']?></option>
                            <?php endforeach; endif; ?>
                        </select>
                    </div>
                    <div class="col-auto">
                        <select class="form-select" v-model="grade">
                            <option value="">年级</option>
                            <?php if(!empty($gradeList)):foreach ($gradeList as $grade): ?>
                                <option value="<?=$grade->value?>"><?=$grade->label()?></option>
                            <?php endforeach; endif; ?>
                        </select>
                    </div>
                    <div class="col-auto">
                        <select class="form-select" v-model="textbook_id">
                            <option value="">模块</option>

                            <template v-for="(item,index) in textBookList">
                                <option v-if="item.version_id == versionId" v-bind:value="item.id">{{item.name}}</option>
                            </template>
                        </select>
                    </div>
                </div>
            </div>

            <div  style="flex: 1" class="text-end input-group">
                <input type="text" class="form-control form-control-sm" v-model="keyword" placeholder="请输入关键词"  aria-describedby="basic-addon2">
                <button class="btn btn-sm btn-primary text-white" v-on:click="searchList" type="button" id="basic-addon2"><img src="/img/home/<USER>"></button>
            </div>
        </div>
    </div>

    <?php if(!empty($paperList)): ?>
        <div class="paper-while-box mt-2 min-height-500">
            <div class="paper-content pb-3">
                <div class="paper-item border-bottom  pb-4 " v-for="(paper,index) in paperList" v-bind:class="{'pt-4':index>0}">
                    <div class="d-flex">
                        <div>
                            <img v-bind:src="paper.icon">
                        </div>
                        <div class="ps-3" style="flex:1;">
                            <div class="paper-item-title pb-2">
                                <template v-if="getPaperStatusCss(paper) != ''">
                                    <label class="p-1 paper-status" v-bind:class="getPaperStatusCss(paper)" >{{getPaperStatusLabel(paper)}}</label>
                                    <span class="pt-1">
                                        <a v-if="getTitleUrl(paper)=='javascript:;'" v-bind:href="getTitleUrl(paper)"  v-on:click="previewWord(paper)"> <span v-if="paper.course_name != ''">【{{paper.course_name}}】</span>{{paper.title}}</a>
                                        <a v-else v-bind:href="getTitleUrl(paper)"><span v-if="paper.course_name != ''">【{{paper.course_name}}】</span>{{paper.title}}</a>
                                    </span>
                                </template>
                                <span v-else>
                                    <a v-if="getTitleUrl(paper)=='javascript:;'" v-bind:href="getTitleUrl(paper)"  v-on:click="previewWord(paper)"> <span v-if="paper.course_name != ''">【{{paper.course_name}}】</span>{{paper.title}}</a>
                                    <a v-else v-bind:href="getTitleUrl(paper)"><span v-if="paper.course_name != ''">【{{paper.course_name}}】</span>{{paper.title}}</a>
                                </span>
                            </div>
                            <div class="d-flex">
                                <div class="text-desc" v-bind:class="{'ps-2':getPaperStatusCss(paper) == ''}">
                                    <span class="pe-3">加入时间：{{paper.create_date}}</span>
                                    <span class="pe-3">类型：{{paper.categoryName}}</span>
                                    <span>模块：{{paper.textbook_name}}</span>
                                </div>
                                <div class="text-end paper-operate person-paper-operate" style="flex:1;">
                                    <template v-if="paper.source_id=='<?=MyPaperSource::Bookmarked->value?>'"">

                                        <a v-bind:href="'<?=Url::toRoute(['premium-paper/preview','tag' => \common\enums\MenuTags::SmartWork->value,'source' => 'my-paper'])?>&paperId='+paper.id" class="ps-3 paper-view">
                                            <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                                                <g>
                                                    <path id="Vector" d="M7.84299 14.0138H2.60147C2.45658 14.0134 2.31773 13.9557 2.21528 13.8532C2.11283 13.7508 2.0551 13.6119 2.05473 13.467V2.53155C2.0551 2.38666 2.11283 2.24781 2.21528 2.14535C2.31773 2.0429 2.45658 1.98518 2.60147 1.98481H12.4421C12.5871 1.98481 12.7261 2.04241 12.8287 2.14494C12.9312 2.24748 12.9888 2.38654 12.9888 2.53155V7.36284H14.0823V2.53155C14.0819 2.09658 13.909 1.67953 13.6015 1.3719C13.294 1.06427 12.877 0.891184 12.4421 0.890625H2.59795C2.16287 0.890998 1.74571 1.064 1.43806 1.37165C1.13041 1.6793 0.957404 2.09646 0.957031 2.53155V13.4692C0.957404 13.9042 1.13041 14.3214 1.43806 14.629C1.74571 14.9367 2.16287 15.1097 2.59795 15.1101H7.83948L7.84299 14.0138Z"/>
                                                    <path id="Vector_2" d="M14.8744 14.1668L13.8014 13.0937C14.2605 12.4408 14.4582 11.6394 14.3552 10.8479C14.2522 10.0564 13.8561 9.3322 13.2452 8.81849C12.6343 8.30478 11.8529 8.03887 11.0554 8.07328C10.2579 8.10769 9.50236 8.43992 8.93793 9.00434C8.37351 9.56876 8.04128 10.3243 8.00687 11.1218C7.97246 11.9193 8.23838 12.7007 8.75209 13.3116C9.2658 13.9225 9.98995 14.3186 10.7815 14.4216C11.573 14.5246 12.3744 14.3269 13.0273 13.8678L14.1004 14.9408C14.1508 14.9935 14.2112 15.0355 14.2781 15.0645C14.345 15.0935 14.417 15.1088 14.4899 15.1096C14.5628 15.1104 14.6351 15.0966 14.7026 15.0691C14.7701 15.0416 14.8314 15.0009 14.8829 14.9493C14.9344 14.8978 14.9752 14.8365 15.0027 14.769C15.0302 14.7015 15.044 14.6292 15.0432 14.5563C15.0424 14.4834 15.0271 14.4114 14.9981 14.3445C14.9691 14.2776 14.9271 14.2172 14.8744 14.1668ZM9.73775 12.7461C9.44646 12.4549 9.24808 12.0837 9.16771 11.6797C9.08734 11.2757 9.12858 10.8569 9.28622 10.4763C9.44386 10.0957 9.71082 9.77041 10.0533 9.54155C10.3959 9.31268 10.7986 9.19052 11.2105 9.19052C11.6224 9.19052 12.0251 9.31268 12.3677 9.54155C12.7102 9.77041 12.9771 10.0957 13.1348 10.4763C13.2924 10.8569 13.3337 11.2757 13.2533 11.6797C13.1729 12.0837 12.9745 12.4549 12.6833 12.7461C12.2921 13.1367 11.7619 13.3561 11.2091 13.3561C10.6563 13.3561 10.1261 13.1367 9.73494 12.7461H9.73775Z"/>
                                                    <path id="Vector_3" d="M9.21046 4.82785H4.04845C3.90344 4.82785 3.76438 4.77025 3.66185 4.66772C3.55931 4.56518 3.50171 4.42612 3.50171 4.28111C3.50171 4.13611 3.55931 3.99704 3.66185 3.89451C3.76438 3.79198 3.90344 3.73438 4.04845 3.73438H9.21046C9.35546 3.73438 9.49453 3.79198 9.59706 3.89451C9.69959 3.99704 9.7572 4.13611 9.7572 4.28111C9.7572 4.42612 9.69959 4.56518 9.59706 4.66772C9.49453 4.77025 9.35546 4.82785 9.21046 4.82785Z"/>
                                                    <path id="Vector_4" d="M6.63016 7.9177H4.04845C3.90344 7.9177 3.76438 7.8601 3.66185 7.75756C3.55931 7.65503 3.50171 7.51596 3.50171 7.37096C3.50171 7.22595 3.55931 7.08689 3.66185 6.98436C3.76438 6.88182 3.90344 6.82422 4.04845 6.82422H6.63016C6.77516 6.82422 6.91423 6.88182 7.01676 6.98436C7.11929 7.08689 7.1769 7.22595 7.1769 7.37096C7.1769 7.51596 7.11929 7.65503 7.01676 7.75756C6.91423 7.8601 6.77516 7.9177 6.63016 7.9177Z"/>
                                                </g>
                                            </svg>
                                            <span class="ps-1">预览</span>
                                        </a>

                                        <a   v-bind:href="'<?=Url::toRoute(['premium-paper/card-upload'])?>?paperId='+paper.id" target="_blank" class="ps-3 paper-view">
                                            <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                                                <g>
                                                    <path id="Vector" d="M4.03753 7.77445L7.19723 10.9347C7.41166 11.1484 7.69684 11.266 7.99988 11.266C8.30361 11.266 8.58875 11.1481 8.80291 10.9339L11.9625 7.77453C12.2093 7.52767 12.2093 7.12627 11.9625 6.87948C11.843 6.75994 11.6841 6.69416 11.515 6.69416C11.346 6.69416 11.1871 6.75995 11.0676 6.87948L8.63286 9.31391V1.47273C8.63286 1.12366 8.34898 0.839844 7.99997 0.839844C7.65095 0.839844 7.36716 1.12377 7.36716 1.47273V9.31444L4.9325 6.87948C4.81298 6.75994 4.65403 6.69416 4.48502 6.69416C4.31598 6.69416 4.15708 6.75997 4.03753 6.87948C3.91798 6.999 3.85217 7.15795 3.85217 7.32697C3.85217 7.49598 3.91802 7.65494 4.03753 7.77445ZM12.6867 2.51375C12.3377 2.51375 12.0539 2.79755 12.0539 3.14656C12.0539 3.49558 12.3378 3.77938 12.6867 3.77938C13.4453 3.77938 14.0625 4.39658 14.0625 5.15519V12.52C14.0625 13.2786 13.4453 13.8958 12.6867 13.8958H3.31333C2.5547 13.8958 1.93753 13.2786 1.93753 12.52V5.15519C1.93753 4.39656 2.55466 3.77938 3.31333 3.77938C3.66234 3.77938 3.94614 3.49558 3.94614 3.14656C3.94614 2.79755 3.66225 2.51375 3.31333 2.51375C1.85681 2.51375 0.671875 3.69866 0.671875 5.15523V12.52C0.671875 13.9766 1.85677 15.1615 3.31333 15.1615H12.6867C14.1432 15.1615 15.3281 13.9766 15.3281 12.52V5.15523C15.3281 3.69869 14.1432 2.51375 12.6867 2.51375Z" />
                                                </g>
                                            </svg>
                                            <span class="ps-1">下载</span>
                                        </a>


                                        <a href="javascript:;"  v-on:click="removeMyPaper(paper)" class="ps-3">
                                            <text class="page_join_person">
                                                <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                                                    <g>
                                                        <path id="Vector" d="M8.00552 13.2461L4.4698 15.0631C3.8327 15.3905 3.04467 15.1511 2.70972 14.5283C2.57635 14.2803 2.53032 13.9962 2.57878 13.7201L3.25403 9.87147L0.39358 7.14587C-0.121852 6.65473 -0.132397 5.84813 0.370024 5.34427C0.57009 5.14364 0.832251 5.01306 1.11589 4.97278L5.06895 4.41128L6.83681 0.909683C7.15537 0.278712 7.93685 0.019662 8.58232 0.331071C8.83935 0.455057 9.04739 0.658436 9.17424 0.909683L10.9421 4.41126L14.8952 4.97276C15.6075 5.07396 16.101 5.72045 15.9975 6.41677C15.9563 6.69404 15.8227 6.95029 15.6175 7.14587L12.757 9.87145L13.4323 13.7201C13.554 14.4136 13.0775 15.0722 12.368 15.1911C12.0855 15.2385 11.7949 15.1935 11.5412 15.0631L8.00552 13.2461Z"/>
                                                    </g>
                                                </svg>
                                            </text>
                                            <span class="ps-1">移除我的试卷</span>
                                        </a>
                                    </template>
                                    <template v-else >

                                        <?=$this->render('/common/_paper_operate_btn',['type' => 'my-paper'])?>

                                    </template>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="clearfix pt-3">
                    <div class="float-end">
                        <el-pagination
                            v-model:current-page="currentPage"
                            :page-size="10"
                            layout="prev, pager, next, jumper,slot"
                            v-model:total="totalCount"
                            :hide-on-single-page="false"
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                        >
                        </el-pagination>
                    </div>
                </div>
            </div>

        </div>
    <?php endif; ?>
    <?php if(empty($paperList)): ?>
        <?=$this->render('/common/_no_data',['type' => 'paper','msg' => '暂无试卷'])?>
    <?php endif; ?>
</div>
<?=$this->render('/common/_confirm_delete') ?>
<?=$this->render('/common/_paper_preview') ?>
<?=$this->render('/common/_upload_paper',['school' => $school,'requestData' => $requestData,'paperCategoryList' =>$paperCategoryList,'type' => PaperFromGroup::PersonalUpload->value,'teacherStageCourse' =>$teacherStageCourse,'teacherStageCourses' =>$teacherStageCourses,'gradeList' => $gradeList,'textBookList' => $textBookList,'versionList' => $versionList])?>
<script>
    var app = Vue.createApp({
        data(){
            return {
                from_group:'<?=!empty($requestData['from_group']) ? $requestData['from_group'] : ''?>',
                category_id:'<?=!empty($requestData['category_id']) ? $requestData['category_id'] : ''?>',
                recent_type:'<?=!empty($requestData['recent_type']) ? $requestData['recent_type'] : ''?>',
                area_code:'<?=!empty($requestData['area_code']) ? $requestData['area_code'] : ''?>',
                keyword:'<?=!empty($requestData['keyword']) ? $requestData['keyword'] : ''?>',
                order:'<?=!empty($requestData['order']) ? $requestData['order'] : ''?>',
                paperList:<?=\yii\helpers\Json::encode($paperList)?>,
                stageCourses:<?=\yii\helpers\Json::encode($teacherStageCourses)?>,
                stage:'<?=!empty($requestData['stage']) ? $requestData['stage'] : $teacherStageCourse['stage']['value']?>',
                course_code:'<?=!empty($requestData['course_code']) ? $requestData['course_code'] : $teacherStageCourse['course']['code']?>',
                currentPage:<?=$currentPage?>,
                totalCount:<?=$totalCount?>,
                operateIndex:-1,
                operateInfo:{},
                operateType:'',
                date_range:<?=!empty($requestData['start_date']) ? Json::encode([$requestData['start_date'],$requestData['end_date']]) : Json::encode([])?>,
                grade:'<?=!empty($requestData['grade']) ? $requestData['grade'] : ''?>',
                textbook_id:'<?=!empty($requestData['textbook_id']) ? $requestData['textbook_id'] : ''?>',
                start_date:'<?=!empty($requestData['start_date']) ? $requestData['start_date'] : ''?>',
                end_date:'<?=!empty($requestData['end_date']) ? $requestData['end_date'] : ''?>',
                versionId:'<?=!empty($requestData['versionId']) ? $requestData['versionId']: ''?>',
                textBookList:<?=\yii\helpers\Json::encode($textBookList)?>,
            }
        },
        computed:{
            courseList:function (){
                var list = [];
                var courseIds = [];
                for(var i in this.stageCourses){
                    var item = this.stageCourses[i];
                    if(courseIds.indexOf(item.course.code) >=0){
                        continue;
                    }
                    courseIds.push(item.course.code);
                    list.push(item.course)
                }
                return list;
            },
            stageList:function (){
                var list = [];
                var courseIds = [];
                for(var i in this.stageCourses){
                    var item = this.stageCourses[i];
                    if(courseIds.indexOf(item.stage.value) >=0){
                        continue;
                    }
                    courseIds.push(item.stage.value);
                    list.push(item.stage)
                }
                return list;
            },
        },
        mounted() {

            // document.getElementsByClassName("el-pagination__goto")[0].childNodes[0].nodeValue = "跳至";
        },
        watch:{
            versionId:function(){
                this.textbook_id = '';
            },
            stage:function (){
                this.category_id = "";
                this.textbook_id = '';
                this.versionId = '';
                this.buildUrl(1);
            },
            course_code:function (){
                this.category_id = "";
                this.textbook_id = '';
                this.versionId = '';
                this.buildUrl(1);
            },
            grade:function (){
                this.buildUrl(1);
            },
            textbook_id:function (){
                this.buildUrl(1);
            },
            date_range:function (){
                this.start_date = this.date_range[0]
                this.end_date = this.date_range[1]
                this.buildUrl(1);
            },
        },
        methods:{
            jumpCardUrl:function(){
                window.location.href='<?=Url::toRoute(['paper-card/index'])?>?uuid='+this.operateInfo.card_uuid+'#card';
            },
            previewWord:function (paper){
                paperPreviewApp.showProp(paper)
            },
            jumpPaperEdit:function (paper,index){
                this.operateType = 'card';
                this.operateInfo = paper;
                this.operateIndex = index;
                confirmApp.showProp('提示！','如对答题卡进行了修改，务必重新下载打印最新版答题卡，否则会造成答题卡无法识别或识别不准确。');
            },
            getTitleUrl:function (paper){
                if(paper.source_id == '<?=MyPaperSource::Bookmarked->value?>'){
                    return '<?=Url::toRoute(['premium-paper/preview','tag' => \common\enums\MenuTags::SmartWork->value,'source' => 'my-paper'])?>&paperId='+paper.id;
                }else if(paper.statusId == '<?=PaperStatus::Editing->value?>'){
                    return 'javascript:;';
                }else if(paper.statusId == '<?=PaperStatus::OnLine->value?>'){
                    return '<?=Url::toRoute(['/home/<USER>'])?>/?uuid='+paper.uuid+'#/testPaperCenter'; 
                }
                return '#'
            },
            removeMyPaper:function (paper){
                var _this = this;
                removeMyPaper(paper.id,'<?=Yii::$app->user->id?>',function (data){
                    if(data.code == 1){
                        _this.buildUrl(_this.currentPage);
                    }else{
                        toastr.error(data.msg, '错误提示!', {positionClass: 'toast-center'})
                    }
                })
            },
            showUploadPaper:function (){
                uploadPaperApp.showProp();
            },
            getPaperStatusCss:function (paper){
                if(paper.source_id == '<?=MyPaperSource::Bookmarked->value?>'){
                    return '';
                }

                if(paper.hasDownload){
                    return 'paper-status-card'
                }
                return '';
            },
            getPaperStatusLabel:function (paper){
                console.log(paper)
                if(paper.source_id == '<?=MyPaperSource::Bookmarked->value?>'){
                    return '';
                }
                if(paper.hasDownload){
                    return '已制卡'
                }
                return '';
            },
            searchList:function (){
                this.buildUrl(1);
            },
            buildUrl:function (page){
                var url = '<?=Url::toRoute(['','tag' => $requestData['tag']])?>'
                if(this.stage != ''){
                    url += '&stage='+this.stage;
                } 
                if(this.course_code != ''){
                    url += '&course_code='+this.course_code;
                }
                if(this.from_group != ''){
                    url += '&from_group='+this.from_group;
                }
                if(this.category_id != ''){
                    url += '&category_id='+this.category_id;
                }
                if(this.start_date != ''){
                    url += '&start_date='+this.start_date;
                }else{
                    if(this.recent_type != ''){
                        url += '&recent_type='+this.recent_type;
                    }
                }
                if(this.end_date != ''){
                    url += '&end_date='+this.end_date;
                }
                if(this.textbook_id != ''){
                    url += '&textbook_id='+this.textbook_id;
                }
                if(this.grade != ''){
                    url += '&grade='+this.grade;
                }
                if(this.keyword != ''){
                    url += '&keyword='+this.keyword;
                }
                if(this.order != ''){
                    url += '&order='+this.order;
                }
                if(this.versionId !=''){
                    url += '&versionId='+this.versionId;
                }
                url += '&page='+page;
                window.location.href=url;

            },
            handleCurrentChange:function (number){
                this.buildUrl(number)
            },

            deletePaper:function (paper,index){
                if(paper.hasDownload){
                    return;
                }
                this.operateType = 'del'
                this.operateInfo = paper;
                this.operateIndex = index;
                confirmApp.showProp('确定删除此作业吗？','删除后将不可恢复');
            },
            confirmDelete:function (){
                if(this.operateType == 'card'){
                    this.jumpCardUrl();
                    return;
                }
                var _this = this;
                $.ajax({
                    type: 'post',
                    url: '<?=Url::toRoute('custom-paper/delete')?>',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        paperId: this.operateInfo.id,
                    }),
                    success: function (data) {
                        if(data.code == 1){
                            toastr.success('删除成功', '', {positionClass: 'toast-center'})
                            confirmApp.hideProp();
                            location.reload(); // 刷新页面
                        }else{
                            toastr.error(data.msg, '错误提示!', {positionClass: 'toast-center'})
                        }
                    },
                    error:function (data){
                        toastr.error(data.responseText, '错误提示!', {positionClass: 'toast-center'})
                    }
                });
            }
        }

    }).use(ElementPlus, {
        locale: ElementPlusLocaleZhCn,
    }).mount("#homework-box")
</script>

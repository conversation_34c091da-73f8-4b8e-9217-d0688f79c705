<?php

namespace frontend\modules\api\controllers\v1;

use common\models\base\User;
use common\models\school\School;
use frontend\modules\api\enums\ErrorCode;
use Yii;
use yii\base\Model;
use yii\filters\Cors;
use yii\rest\Controller;
use yii\web\Response;
use yii\web\UnauthorizedHttpException;

abstract class BaseController extends Controller
{
    public ?int $userId;
    public ?User $user = null;
    public ?School $school = null;

    private ErrorCode $code = ErrorCode::Ok;
    private string|array $msg = '';

    /**
     * @return array
     * @todo 临时设置允许所有跨域请求
     */
    public function behaviors(): array
    {
        $behaviors = parent::behaviors();
        // add CORS filter
        $behaviors['corsFilter'] = [
            'class' => Cors::class,
            'cors' => [
                'Origin' => ['*'],
                'Access-Control-Request-Method' => ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'HEAD', 'OPTIONS'],
                'Access-Control-Request-Headers' => ['*'],
                'Access-Control-Allow-Credentials' => false,
                'Access-Control-Max-Age' => 86400,
                'Access-Control-Expose-Headers' => [],
            ],
        ];

        return $behaviors;
    }

    public function init()
    {
        parent::init();
        $this->userId = Yii::$app->user->id;
    }

    public function beforeAction($action): bool
    {
        if (!parent::beforeAction($action)) {
            return false;
        }

        if (!Yii::$app->user->isGuest) {
            $this->user = User::findOne($this->userId);
            $this->school = $this->user?->school;
        }

        // if ($this->user === null) {
        //    throw new UnauthorizedHttpException('用户未认证', ErrorCode::Unauthorized->value);
        // }

        // if ($this->school === null) {
            // TODO 测试临时注释
        //    throw new UnauthorizedHttpException('该用户未找到所在的学校', ErrorCode::Unauthorized->value);
        // }

        return true;
    }

    protected function setCodeMsg(ErrorCode $code, string|array $msg = ''): static
    {
        $this->code = $code;
        $this->msg = $msg;
        return $this;
    }

    /**
     * @deprecated use `success` instead
     * @param mixed $data
     * @return Yii\web\Response
     */
    protected function output($data = null): Response
    {
        return $this->success($data);
    }

    protected function success($data = null): Response
    {
        $output = [
            'code' => $this->code->value,
            'name' => $this->code->label(),
            'msg' => $this->msg,
        ];
        if ($data !== null) {
            $output['data'] = $data;
        }
        return $this->asJson($output);
    }

    protected function error(Model $model, ErrorCode $code = null, string $message = null): Response
    {
        return $this->setCodeMsg($code ?: ErrorCode::ParamsRuleInvalid, $message ?: current($model->firstErrors))
            ->output($model->errors);
    }

    protected function exception(\Throwable $exception, string $message = null): Response
    {
        Yii::error($exception);
        return $this->setCodeMsg(ErrorCode::ServerError, $message ?: $exception->getMessage())
            ->output(YII_DEBUG ? $exception->getMessage() : null);
    }
}

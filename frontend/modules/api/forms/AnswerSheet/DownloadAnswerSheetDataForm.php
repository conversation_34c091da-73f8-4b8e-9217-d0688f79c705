<?php

namespace frontend\modules\api\forms\AnswerSheet;

use common\domains\card\AnswerSheetDataHandler;
use common\domains\card\PaperFullCardDataHandler;
use common\enums\CardContentType;
use common\enums\CardPageLayout;
use common\enums\CardStatus;
use common\models\scan\Scan;
use common\uploader\CardUploader;
use frontend\modules\api\models\card\Card;
use frontend\modules\api\services\CardMakerService;
use Yii;
use yii\base\Model;
use yii\db\Exception;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;

class DownloadAnswerSheetDataForm extends Model
{
    public $uuid;
    public $data;
    private $_card;

    public function rules(): array
    {
        return [
            [['uuid', 'data'], 'required'],
            [['uuid'], 'string'],
            [['data'], 'validateData'],
            [['uuid'], 'exist', 'targetClass' => Card::class, 'targetAttribute' => ['uuid' => 'uuid'], 'message' => '题卡不存在'],
            [['uuid'], 'validateCardHasUsed']
        ];
    }

    public function validateData($attribute, $params): void
    {
        if (empty($this->data['cardInfo']['setting']['pageLayout'])) {
            $this->addError($attribute, '页面布局数据不完整');
        }
    }
    public function validateCardHasUsed($attribute, $params): void
    {
        $this->loadCard();
        $exist = Scan::find()->where(['card_id' => $this->_card->id])->exists();
        if ($exist) {
            $this->addError($attribute, '该题卡已经打印扫描过，不允许再修改。');
        }
    }


    public function handleDownloadJob(): array
    {
        $this->loadCard();
        $this->saveMakingData();
        $transaction = Card::getDb()->beginTransaction();
        try {
            $pdfUrl = $this->generateCardPdfAndCardImages();
            // 生成XML，生成CardPage和CardStruct
            $paperFullCardDataHandler = $this->_card->id > OLD_ANSWER_SHEET_MAX_ID
                ? new PaperFullCardDataHandler($this->_card)
                : $paperFullCardDataHandler = new AnswerSheetDataHandler($this->_card);
            $paperFullCardDataHandler->generateCardExtraData();
            $transaction->commit();

            return ['url' => $pdfUrl];
        } catch (\Exception $e) {
            $transaction->rollBack();
            throw $e;
        }
    }

    private function saveMakingData(): void
    {
        $userId = (int) (Yii::$app->user?->id);
        $this->_card->title = trim($this->data['cardInfo']['title']);
        $this->_card->setting = ArrayHelper::merge($this->_card->setting->toArray(), $this->data['cardInfo']['setting']);
        $this->_card->making_data = Json::encode($this->data);
        $this->_card->staging_at = $this->_card->saved_at = time();
        $this->_card->staging_by = $this->_card->saved_by = $userId;
        $this->_card->status = CardStatus::Saved;
        $this->_card->full_score = $this->data['cardInfo']['full_score'] ?? 0;
        if (!empty($this->data['cardInfo']['partScorePlan'])) {
            $this->_card->part_score_plan = $this->data['cardInfo']['partScorePlan'];
        } else {
            Yii::warning('data.cardInfo.partScorePlan is empty.');
        }
        if (
            !$this->_card->save(true, [
                'title',
                'setting',
                'making_data',
                'format_data',
                'staging_at',
                'staging_by',
                'saved_at',
                'saved_by',
                'status',
                'full_score',
                'setting',
                'updated_at',
                'updated_by'
            ])
        ) {
            Yii::error($this->_card->getErrors());
            throw new Exception('保存题卡数据出错');
        }
    }

    private function generateCardPdfAndCardImages(): string
    {
        $userId = (int) Yii::$app->user?->id;
        $scaleFactor = CardPageLayout::A4_SCALE_RATIO;
        $this->_card->preview_files = $this->_card->id > OLD_ANSWER_SHEET_MAX_ID
            ? CardMakerService::generateAnswerSheetImageUrlListV2($this->_card->id, $this->_card->uuid, $scaleFactor)
            : CardMakerService::generateAnswerSheetImageUrlList($this->_card->id, $this->_card->uuid, $scaleFactor);

        $cardData = $this->_card->id > OLD_ANSWER_SHEET_MAX_ID
            ? CardMakerService::generateAnswerSheetPdfDataV2($this->_card->uuid)
            : CardMakerService::generateAnswerSheetPdfData($this->_card->uuid);
        $binaryData = base64_decode($cardData['pdfData']);
        $uploader = new CardUploader();
        $uploader->generatePdfFilename($this->_card->id);
        $pdfUrl = $uploader->writeFileContent($binaryData);

        $this->_card->download_at = $this->_card->generated_at = time();
        $this->_card->download_by = $this->_card->generated_by = $userId;
        $this->_card->status = CardStatus::Downloaded;
        $this->_card->download_url = $pdfUrl;
        $this->_card->format_data = $cardData['formatData'];

        if (
            !$this->_card->save(true, [
                'preview_files',
                'format_data',
                'download_at',
                'download_by',
                'download_url',
                'generated_at',
                'generated_by',
                'status',
                'answer_url'
            ])
        ) {
            Yii::error($this->_card->getErrors());
            throw new Exception('保存题卡下载数据出错');
        }

        return $pdfUrl;
    }

    private function loadCard(): void
    {
        $this->_card = Card::find()->uuid($this->uuid)->contentType(CardContentType::OnlyAnswerArea)->one();
    }
}

<?php

namespace frontend\modules\api\forms\ChineseWriting;

use common\domains\card\PaperFullCardDataHandler;
use common\enums\CardContentType;
use common\enums\CardPageLayout;
use common\enums\CardStatus;
use common\models\scan\Scan;
use common\uploader\CardUploader;
use frontend\modules\api\models\card\Card;
use frontend\modules\api\services\CardMakerService;
use Yii;
use yii\base\Model;
use yii\db\Exception;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;

/**
 * @deprecated 语文作文和英语作文合并
 */
class DownloadChineseWritingCardDataForm extends Model
{
    public $uuid;
    public $data;
    private $_card;

    public function rules(): array
    {
        return [
            [['uuid', 'data'], 'required'],
            [['data'], 'validateData'],
            [['uuid'], 'validateCard'],
            [['uuid'], 'validateCardHasUsed'],
        ];
    }

    public function validateCard($attribute, $params): void
    {
        $this->loadCard();
        if ($this->_card === null) {
            $this->addError($attribute, '作文题卡不存在');
        }
    }

    public function validateData($attribute, $params): void
    {
        if (empty($this->data['uuid'])) {
            $this->addError($attribute, 'data[uuid]不能为空');
        }
        if (empty($this->data['cardInfo']['setting']['pageLayout'])) {
            $this->addError($attribute, '数据不完整');
        }
    }

    public function validateCardHasUsed($attribute, $params): void
    {
        $this->loadCard();
        $exist = Scan::find()->where(['card_id' => $this->_card->id])->exists();
        if ($exist) {
            $this->addError($attribute, '该题卡已经打印扫描过，不允许再修改。');
        }
    }

    public function handleDownloadJob(): array
    {
        $this->loadCard();
        $this->saveMakingData();
        $transaction = Card::getDb()->beginTransaction();
        try {
            $pdfUrl = $this->generateCardPdfAndCardImages();
            // 生成XML，生成CardPage和CardStruct
            $paperFullCardDataHandler = new PaperFullCardDataHandler($this->_card);
            $paperFullCardDataHandler->generateCardExtraData();
            $transaction->commit();

            return ['url' => $pdfUrl];
        } catch (\Exception $e) {
            $transaction->rollBack();
            throw $e;
        }
    }

    private function saveMakingData(): void
    {
        $userId = (int) (Yii::$app->user?->id);
        $this->_card->title = trim($this->data['cardInfo']['title']);
        $this->_card->setting = ArrayHelper::merge($this->_card->setting->toArray(), $this->data['cardInfo']['setting']);
        $this->_card->making_data = Json::encode($this->data);
        $this->_card->staging_at = $this->_card->saved_at = time();
        $this->_card->staging_by = $this->_card->saved_by = $userId;
        $this->_card->status = CardStatus::Saved;
        $this->_card->full_score = $this->data['cardInfo']['full_score'] ?? 0;
        if (
            !$this->_card->save(true, [
                'title',
                'setting',
                'making_data',
                'format_data',
                'staging_at',
                'staging_by',
                'saved_at',
                'saved_by',
                'status',
                'full_score',
                'updated_at',
                'updated_by'
            ])
        ) {
            Yii::error($this->_card->getErrors());
            throw new Exception('保存题卡数据出错');
        }
    }

    private function generateCardPdfAndCardImages(): string
    {
        $userId = (int) (Yii::$app->user?->id);
        $this->_card->preview_files = CardMakerService::generateWritingCardImageUrlList($this->_card->id, $this->_card->uuid, CardPageLayout::A4_SCALE_RATIO);

        $cardData = CardMakerService::generateWritingCardPdfData($this->_card->uuid);
        $binaryData = base64_decode($cardData['pdfData']);
        $uploader = new CardUploader();
        $uploader->generatePdfFilename($this->_card->id);
        $pdfUrl = $uploader->writeFileContent($binaryData);

        $this->_card->download_at = $this->_card->generated_at = time();
        $this->_card->download_by = $this->_card->generated_by = $userId;
        $this->_card->status = CardStatus::Downloaded;
        $this->_card->download_url = $pdfUrl;
        $this->_card->format_data = $cardData['formatData'];

        if (
            !$this->_card->save(true, [
                'preview_files',
                'format_data',
                'download_at',
                'download_by',
                'download_url',
                'generated_at',
                'generated_by',
                'status'
            ])
        ) {
            Yii::error($this->_card->getErrors());
            throw new Exception('保存题卡下载数据出错');
        }

        return $pdfUrl;
    }

    private function loadCard(): void
    {
        $this->_card = Card::find()->uuid($this->uuid)->contentType(CardContentType::ChineseWriting)->one();
    }
}

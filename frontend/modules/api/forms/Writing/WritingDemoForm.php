<?php

namespace frontend\modules\api\forms\Writing;

use common\enums\AiTaskStatus;
use common\enums\CourseCode;
use common\enums\WritingStage;
use common\models\demo\WritingDemo;
use common\uploader\WritingDemoImageUploader;
use common\validators\EnumValidator;
use Yii;
use yii\base\Model;
use yii\httpclient\Client;
use yii\web\UploadedFile;

class WritingDemoForm extends Model
{
    public $word_count;
    public $full_score;
    public $topic;
    public $course_code;
    public $write_type;
    public $stage;
    public $images;

    public function rules()
    {
         return [
             [['word_count', 'full_score', 'topic', 'course_code',  'stage', 'images'],'required'],
             [['word_count', 'topic', 'write_type','course_code', 'stage'], 'string'],
             [['full_score'], 'integer'],
             [['images'],'safe'],
             [['stage'], EnumValidator::class, 'enumClass' => WritingStage::class],
             [['course_code'], EnumValidator::class, 'enumClass' => CourseCode::class],
         ];
    }

    public function createDemo()
    {
        if($this->course_code == CourseCode::ChineseWriting->value && empty($this->write_type)){
            $this->addError('write_type','体裁不能为空！');
            return false;
        }
        $model = new WritingDemo();
        $model->word_count = $this->word_count;
        $model->full_score = $this->full_score;
        $model->topic = $this->topic;
        $model->course_code = $this->course_code;
        $model->write_type = $this->write_type;
        $model->stage = $this->stage;
        $model->status = AiTaskStatus::Doing->value;
        $model->created_at = time();
        $model->created_by = Yii::$app->user->id ?? null;
        $images = [];
        $uploader = new WritingDemoImageUploader();
        /**
         * @var UploadedFile $image
         */
        if(!empty($this->images)){
            foreach ($this->images as $image) {
                $uploader->generateImageFilename($this->stage,$this->course_code, rand(10000,max: 99999).'.png');
                $url = $uploader->writeUploadedFile($image);
                $images[] = $url;
            }
            $model->images = $images;
        }
        if($model->save()){
            if($this->aiPost($model->id)){
                $this->setDemo($model);
                return true;
            }else{
                $model->delete();
                return false;
            }
        }else{
            $this->addErrors($model->getErrors());
            return false;
        }
    }

    public $_demo;
    protected function setDemo(WritingDemo $model)
    {
        $this->_demo = $model;
    }

    public function getDemo()
    {
        return $this->_demo;
    }

    public function aiPost($pid)
    {
        $apiUrl = Yii::$app->params['ai.chinese.writing.score.url'].'ai-writing-demo';
        $client = new Client([
            'transport' => 'yii\httpclient\CurlTransport'
        ]);
        $response = $client->createRequest()
            ->setMethod('POST')
            ->setUrl($apiUrl)
            ->setData(['pid' => $pid])
            ->setOptions([
                CURLOPT_CONNECTTIMEOUT => 10,
                CURLOPT_TIMEOUT => 30,
            ])
            ->send();
        if ($response->isOk) {
            $resData = $response->data;

            if($resData['code'] == 0){
                return true;
            }else{
                Yii::error($resData);
                $this->addError('topic',$resData['message']);
            }
        } else {
            Yii::error($response->content);
            $this->addError('topic','批阅失败');
        }
        return false;
    }

}
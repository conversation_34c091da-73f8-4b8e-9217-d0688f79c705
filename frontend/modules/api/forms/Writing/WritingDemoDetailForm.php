<?php

namespace frontend\modules\api\forms\Writing;

use common\domains\exam\ChineseWritingAiStats;
use common\domains\exam\StudentLevelStats;
use common\enums\AiTaskStatus;
use common\enums\CourseCode;
use common\enums\Stage;
use common\enums\WritingStage;
use common\models\demo\WritingDemo;
use frontend\modules\home\services\ChineseWritingReportService;
use yii\base\Model;
use yii\helpers\Json;

class WritingDemoDetailForm extends Model
{
    public $demoId;

    public function rules()
    {
        return [
            [['demoId'], 'required','message' => '请选择批阅记录'],
            [['demoId'], 'validateDemoId'],
        ];
    }

    /**
     * @var WritingDemo
     */
    public $_demo;

    public function validateDemoId($attribute)
    {
        $this->_demo = WritingDemo::findOne($this->demoId);
        if(empty($this->_demo)){
            $this->addError($attribute,'批阅记录不存在');
        }
    }


    public function delete()
    {
        if($this->_demo->delete()){
            return true;
        }
        $this->addError('demoId','删除失败');
        return false;
    }


    public function getData()
    {
        if($this->_demo->status != AiTaskStatus::Done->value || empty($this->_demo->ai_result)){
            return [
                'isDone' => 0,
                'images' => $this->_demo->images,
            ] ;
        } 
        $AIResult = Json::decode($this->_demo->ai_result);
        $oriResult = $AIResult;
        $aiResult = ChineseWritingReportService::getWordAiResult($oriResult['overall_feedback'] ?? []);
        $aiResult['topic'] = nl2br($this->_demo->topic);

        $levelStandards = StudentLevelStats::getStandards($this->_demo->stage == WritingStage::HighSchool->value ? Stage::HighSchool: Stage::MiddleSchool,$this->_demo->full_score);
        $studentStandard = StudentLevelStats::getScoreStandard($levelStandards,$aiResult['paperScore'] ?? 0);
        $aiResult['levelLabel'] = $studentStandard;

        $isEnglish = false;
        if($this->_demo->course_code == CourseCode::EnglishWriting->value){
            $isEnglish = true;
        }
        $errors = ChineseWritingAiStats::getStudentErrors($this->_demo->word_count,$this->_demo->write_type,$aiResult,$isEnglish);
        if($isEnglish){
            $labels = ChineseWritingAiStats::studentEnglishErrorLabels();
        }else{
            $labels = ChineseWritingAiStats::studentErrorLabels();
        }
        $errorData = [];
        foreach($labels as $error => $labelItem){
            if(in_array($error,$errors)){
                $errorData[] = $labelItem['label'];
            }
        }
        $aiResult['isDone'] = 1;
        $aiResult['errors'] = $errorData;
        return $aiResult;
    }
}
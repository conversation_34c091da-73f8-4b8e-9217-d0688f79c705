import{d as _e,r as k,b as X,w as n,i as ae,u as l,E as ye,o as s,c as d,e as R,a as e,t as L,f as o,F as ue,q as de,s as He,v as Me,P as ze,K as he,L as Le,n as j,Q as Re,h as z,I as Oe,j as fe,k as ge,l as pe,m as ke,p as Y,M as De,R as Ze,B as Fe,N as Te,S as Je,g as $e,O as Pe,T as Qe,U as Ke,V as Ge,W as We,X as Xe,Y as Ye,Z as Ie,_ as je,$ as et,a0 as tt,a1 as lt}from"./index.ZZ6UQeaF.js";/* empty css                          */import"./el-tooltip.l0sNRNKZ.js";/* empty css                  *//* empty css                     *//* empty css                   *//* empty css                  */import"./el-collapse-transition.l0sNRNKZ.js";/* empty css                 */import{d as qe,_ as st}from"./vuedraggable.umd.BJGhys5F.js";import{q as ne}from"./question.IyuOoK5G.js";import{g as ot,n as nt,a as Be}from"./index.6W4rOsHv.js";import{c as O,v as at,w as it,x as ut,y as dt,q as rt,s as ct,_ as pt,b as mt,t as _t,z as ft,i as vt,m as gt,A as Ct,B as ht,C as yt,l as kt,k as xt,D as bt,E as wt,a as Ve}from"./testPaperCenter.BktRbhxU.js";import{_ as Se}from"./itemDom.vue_vue_type_script_setup_true_lang.CbGURBLf.js";/* empty css                *//* empty css                      *//* empty css                  *//* empty css                *//* empty css                   */import{_ as Ce}from"./_plugin-vue_export-helper.DlAUqK2U.js";/* empty css                       *//* empty css                       */import{a as $t}from"./downloads.B3gkFdce.js";import"./request.CsKOOJzG.js";import"./index.xsH4HHeE.js";import"./index.vue_vue_type_style_index_0_lang.Cq6RDaj7.js";const Ue="data:image/svg+xml,%3csvg%20width='16'%20height='16'%20viewBox='0%200%2016%2016'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M2.81214%2014.4798H13.1778C13.3509%2014.4811%2013.5226%2014.4482%2013.683%2014.3829C13.8434%2014.3175%2013.9892%2014.2211%2014.1121%2014.0991C14.235%2013.9771%2014.3326%2013.832%2014.3992%2013.6722C14.4657%2013.5123%2014.5%2013.3408%2014.5%2013.1677V7.98993C14.5%207.83906%2014.4401%207.69437%2014.3334%207.58769C14.2267%207.48101%2014.082%207.42108%2013.9312%207.42108C13.7803%207.42108%2013.6356%207.48101%2013.5289%207.58769C13.4222%207.69437%2013.3623%207.83906%2013.3623%207.98993V13.1778C13.3623%2013.2241%2013.3439%2013.2684%2013.3112%2013.3011C13.2785%2013.3339%2013.2341%2013.3522%2013.1879%2013.3522H2.82225C2.77598%2013.3522%202.73161%2013.3339%202.6989%2013.3011C2.66618%2013.2684%202.6478%2013.2241%202.6478%2013.1778V2.81217C2.6478%202.76591%202.66618%202.72154%202.6989%202.68882C2.73161%202.65611%202.77598%202.63773%202.82225%202.63773H8C8.0747%202.63773%208.14867%202.62302%208.21769%202.59443C8.2867%202.56584%208.34941%202.52394%208.40223%202.47112C8.45506%202.4183%208.49696%202.35559%208.52554%202.28657C8.55413%202.21756%208.56885%202.14359%208.56885%202.06888C8.56885%201.99418%208.55413%201.92021%208.52554%201.8512C8.49696%201.78218%208.45506%201.71947%208.40223%201.66665C8.34941%201.61383%208.2867%201.57193%208.21769%201.54334C8.14867%201.51475%208.0747%201.50004%208%201.50004H2.82225C2.64993%201.49871%202.47905%201.53133%202.31934%201.59604C2.15963%201.66075%202.01424%201.75629%201.89146%201.87719C1.76867%201.9981%201.67091%202.142%201.60374%202.30069C1.53657%202.45938%201.50132%202.62975%201.5%202.80206V13.1677C1.5%2013.34%201.53394%2013.5106%201.59988%2013.6698C1.66582%2013.829%201.76247%2013.9737%201.88432%2014.0955C2.00616%2014.2173%202.15081%2014.314%202.31%2014.3799C2.4692%2014.4459%202.63982%2014.4798%202.81214%2014.4798ZM6.8172%209.40603L7.77539%209.15321L14.1793%202.74928C14.2415%202.68727%2014.2909%202.61359%2014.3246%202.53246C14.3583%202.45133%2014.3756%202.36435%2014.3756%202.27651C14.3756%202.18866%2014.3583%202.10168%2014.3246%202.02055C14.2909%201.93942%2014.2415%201.86574%2014.1793%201.80373C14.1173%201.74151%2014.0436%201.69214%2013.9625%201.65845C13.8814%201.62476%2013.7944%201.60742%2013.7065%201.60742C13.6187%201.60742%2013.5317%201.62476%2013.4506%201.65845C13.3695%201.69214%2013.2958%201.74151%2013.2338%201.80373L6.8172%208.19502L6.56438%209.15321C6.55446%209.18826%206.55407%209.22534%206.56325%209.26059C6.57243%209.29585%206.59086%209.32803%206.61662%209.35379C6.64238%209.37955%206.67456%209.39798%206.70982%209.40716C6.74507%209.41634%206.78215%209.41595%206.8172%209.40603Z'%20fill='%232672FF'/%3e%3c/svg%3e",It="data:image/svg+xml,%3csvg%20width='16'%20height='16'%20viewBox='0%200%2016%2016'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M9.49796%2012.0016C9.22113%2012.0016%208.99805%2011.7785%208.99805%2011.5017V5.99992C8.99805%205.72308%209.22113%205.5%209.49796%205.5C9.7748%205.5%209.99788%205.72308%209.99788%205.99992V11.499C9.99788%2011.7758%209.7748%2012.0016%209.49796%2012.0016Z'%20fill='%232672FF'/%3e%3cpath%20d='M6.49796%2012.0016C6.22113%2012.0016%205.99805%2011.7785%205.99805%2011.5017V5.99992C5.99805%205.72308%206.22113%205.5%206.49796%205.5C6.7748%205.5%206.99788%205.72308%206.99788%205.99992V11.499C6.99788%2011.7758%206.7748%2012.0016%206.49796%2012.0016Z'%20fill='%232672FF'/%3e%3cpath%20d='M14.5004%203.49958H12.0008V2.49975C12.0008%201.67193%2011.3343%201%2010.5118%201H5.49924C4.67143%201%203.9995%201.67193%203.9995%202.49975V3.49958H1.49992C1.22308%203.49958%201%203.72266%201%203.9995C1%204.27633%201.22308%204.49941%201.49992%204.49941H14.5004C14.7773%204.49941%2015.0003%204.27633%2015.0003%203.9995C15.0003%203.72266%2014.7773%203.49958%2014.5004%203.49958ZM4.99933%202.49975C4.99933%202.2256%205.2251%201.99983%205.49924%201.99983H10.5118C10.786%201.99983%2011.001%202.22023%2011.001%202.49975V3.49958H4.99933V2.49975Z'%20fill='%232672FF'/%3e%3cpath%20d='M11.5005%2014.9987H4.5017C3.67388%2014.9987%203.00195%2014.3267%203.00195%2013.4989V5.9921C3.00195%205.71527%203.22503%205.49219%203.50187%205.49219C3.7787%205.49219%204.00179%205.71527%204.00179%205.9921V13.4989C4.00179%2013.7757%204.22755%2013.9988%204.5017%2013.9988H11.5032C11.78%2013.9988%2012.0031%2013.7757%2012.0031%2013.4989V6.01092C12.0031%205.73408%2012.2262%205.511%2012.503%205.511C12.7799%205.511%2013.003%205.73408%2013.003%206.01092V13.4989C13.0003%2014.324%2012.3283%2014.9987%2011.5005%2014.9987Z'%20fill='%232672FF'/%3e%3c/svg%3e",Vt="data:image/svg+xml,%3csvg%20width='26'%20height='26'%20viewBox='0%200%2026%2026'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M24.1233%208.78691C23.5418%208.52285%2014.424%204.39941%2014.424%204.39941C14.424%204.39941%2013.4237%203.92969%2013.0072%203.92969C12.5934%203.92969%2011.5905%204.39941%2011.5905%204.39941C11.5905%204.39941%202.47268%208.52285%201.89123%208.78691C1.30979%209.05098%201.89123%209.39121%201.89123%209.39121C1.89123%209.39121%209.69631%2012.9332%2011.1182%2013.5654C12.2811%2014.0834%2012.5401%2014.0732%2013.0072%2014.0732C13.4744%2014.0732%2013.7334%2014.0859%2014.8963%2013.5654C15.8764%2013.1287%2019.8906%2011.3107%2022.2901%2010.2215V13.2658L21.503%2014.0529L22.5998%2015.1498L23.6967%2014.0529L22.9096%2013.2658V9.94219C23.6459%209.60703%2024.1207%209.39121%2024.1207%209.39121C24.1207%209.39121%2024.7047%209.04844%2024.1233%208.78691Z'%20fill='%232672FF'/%3e%3cpath%20d='M13.0133%2015.0697C12.5461%2015.0697%2012.2871%2015.0824%2011.1242%2014.5619C10.4184%2014.2471%208.13574%2013.2137%206.01562%2012.2539V19.249C8.1332%2020.2088%2010.4184%2021.2422%2011.1242%2021.557C12.2871%2022.075%2012.5461%2022.0648%2013.0133%2022.0648C13.4805%2022.0648%2013.7395%2022.0775%2014.9023%2021.557C15.6082%2021.2422%2017.8908%2020.2088%2020.0109%2019.249V12.2539C17.8934%2013.2137%2015.6082%2014.2471%2014.9023%2014.5619C13.7395%2015.0824%2013.4805%2015.0697%2013.0133%2015.0697Z'%20fill='%236098FF'/%3e%3c/svg%3e",Ne="data:image/svg+xml,%3csvg%20width='30'%20height='30'%20viewBox='0%200%2030%2030'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M0%200L30%2030V5C30%202.23858%2027.7614%200%2025%200H0ZM18.578%2014.4495L13.7615%209.49541L15.1376%208.11927L18.7156%2011.6972L25.4587%204.95413L26.8349%206.33028L18.578%2014.4495Z'%20fill='%232672FF'/%3e%3c/svg%3e",Ee="data:image/svg+xml,%3csvg%20width='30'%20height='30'%20viewBox='0%200%2030%2030'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M0%200L30%2030V5C30%202.23858%2027.7614%200%2025%200H0ZM18.578%2014.4495L13.7615%209.49541L15.1376%208.11927L18.7156%2011.6972L25.4587%204.95413L26.8349%206.33028L18.578%2014.4495Z'%20fill='%23C9C9C9'/%3e%3c/svg%3e",Lt={class:"pmm2_flex_between"},St={key:0,class:"quesBank-box"},Ft={class:"left-box"},qt={class:"left-com"},Bt={class:"top pmm2_flex_center"},Ut={class:"t1"},Tt={class:"select-top pmm2_flex_between"},Pt={key:0,class:"catalog-box scrollBar"},Nt=["title"],Et={key:1,class:"pmm2_flex_center",style:{"flex-direction":"column"}},At={class:"right-box"},Ht={class:"right-com scrollBar",id:"parentContainer"},Mt={class:"filter-box"},zt={class:"filter-item"},Rt={class:"pmm2_flex_acenter"},Ot=["onClick"],Dt={class:"filter-item"},Zt={class:"pmm2_flex_acenter"},Jt=["onClick"],Qt={class:"filter-item"},Kt={class:"pmm2_flex_acenter"},Gt=["onClick"],Wt={class:"filter-item"},Xt={class:"pmm2_flex_acenter"},Yt=["onClick"],jt={class:"demo-date-picker"},el={key:0},tl={key:0,class:"searchList-box"},ll=["onClick"],sl={style:{display:"none"}},ol={class:"top-tig"},nl={style:{"padding-left":"20px"}},al={class:"pagination-boxs"},il={key:1,class:"pmm2_flex_center",style:{"flex-direction":"column"}},ul={class:"pmm2_flex_between"},dl={class:"dialog-footer"},rl=_e({__name:"quesBank",emits:["confirm"],setup(ee,{expose:le,emit:S}){const f=k(!1),{paperInfo:g}=O(),C=k([]),c=k({}),q=k({}),m=k([]);let x={};const Q=(T,u)=>{x={idx:T,j:u},Z.value=1,C.value=[],w.value=[];const A=O().textbookLists;let i="";if(A.length>0){let t=A.findIndex(b=>b.is_default);t==-1&&(t=0),i=A[t].uuid}c.value={fromGroupId:null,difficultyId:null,categoryId:null,times:null,subjectVal:i},i&&h(i),q.value=O().basicInfo,m.value=O().categoriesInfo,V(),f.value=!0},F=S,B=()=>{let T=[],u=C.value.length,A=[];if(u==0)return Y({message:"请选择需要添加的题目",type:"warning"});for(let i=0;i<u;i++){let t=C.value[i];A.push(t.uuid);let b={isQuestion:!0,itemTypeId:ne.QUESTION,itemTypeLabel:"试题",content:t.uuid,question:t};T.push(b)}O().paperUuidList.push(...A),F("confirm",x,T),f.value=!1},P=ot(),_=k([]),h=T=>{var i;let u=O().textbookLists.find(t=>t.uuid==T)||{};_.value=u.textbooks||[];let A=((i=_.value[0])==null?void 0:i.id)||"";A&&(c.value.textbookVal=A,c.value.textbookId=A,v(A))},U=k([]),v=(T,u)=>{at({textbookId:T}).then(A=>{c.value.catalogIds=[],c.value.textbookId=T,U.value=A.data||[]})},p=k([]),M=async T=>{p.value=[];const u=document.getElementById("parentContainer");u.scrollTop=0,await De(),p.value=K.value[T-1]},K=k([]),Z=k(1),a=k(0),r=k(!0),V=()=>{r.value=!0,K.value=[];let T={stageId:g.stageId,courseCode:g.courseCode,...c.value};w.value&&(T.endAt=w.value[1]||"",T.startAt=w.value[0]||""),it(T).then(u=>{var i;let A=((i=u==null?void 0:u.data)==null?void 0:i.questions)||[];K.value=be(A,3),p.value=K.value[0],a.value=A.length,r.value=!1})},I=(T,u)=>{if(O().paperUuidList.findIndex(t=>t==T.uuid)!=-1){Y({message:"该题已经添加进试卷了，请重新选择",type:"warning"});return}let i=C.value.findIndex(t=>t.uuid==T.uuid);i==-1?(K.value[Z.value-1][u].onFalgs=!0,C.value.push(T)):(K.value[Z.value-1][u].onFalgs=!1,C.value.splice(i,1))},w=k([]),N=T=>{c.value.times=null,Z.value=1,V()},G=(T,u)=>{if(T=="times")if(u!==null){let A=P[u];w.value=[A.start,A.end]}else w.value=[];c.value[T]=u,Z.value=1,V()},se=(T,u,A)=>{u.expanded=!u.expanded,c.value.textbookId=T.textbook_id,c.value.catalogIds=[];const i=t=>{c.value.catalogIds.push(t.id),t.children&&t.children.length>0&&t.children.forEach(b=>{i(b)})};i(T),Z.value=1,V()};function be(T,u){const A=[];for(let i=0;i<T.length;i+=u){const t=T.slice(i,i+u);A.push(t)}return A}return le({opens:Q}),(T,u)=>{const A=pe("CloseBold"),i=ge,t=ke,b=He,oe=Me,te=ze,H=Re,J=Oe,D=fe,W=ye,re=Le;return s(),X(W,{"append-to-body":"","close-on-click-modal":!1,class:"hhypt-dialog-boxs","show-close":!1,modelValue:l(f),"onUpdate:modelValue":u[9]||(u[9]=y=>ae(f)?f.value=y:null),title:"题库选题",width:"1200","align-center":""},{header:n(({close:y,titleClass:E})=>[e("div",Lt,[e("span",{class:j(E)},"题库选题",2),o(t,{onClick:y,underline:!1},{default:n(()=>[o(i,{size:"20"},{default:n(()=>[o(A)]),_:1})]),_:2},1032,["onClick"])])]),footer:n(()=>[e("div",ul,[u[21]||(u[21]=e("div",null,null,-1)),e("div",dl,[o(D,{style:{width:"70px"},onClick:u[8]||(u[8]=y=>f.value=!1)},{default:n(()=>u[19]||(u[19]=[z("取消")])),_:1,__:[19]}),o(D,{style:{width:"70px"},type:"primary",onClick:B},{default:n(()=>u[20]||(u[20]=[z(" 确定 ")])),_:1,__:[20]})])])]),default:n(()=>[l(f)?(s(),d("div",St,[e("div",Ft,[e("div",qt,[e("div",Bt,[u[10]||(u[10]=e("img",{class:"img",src:Vt},null,-1)),e("span",Ut,L(l(g).stageLabel)+" - "+L(l(g).courseName),1)]),e("div",Tt,[o(oe,{modelValue:l(c).subjectVal,"onUpdate:modelValue":u[0]||(u[0]=y=>l(c).subjectVal=y),onChange:h,placeholder:"版本",style:{width:"100px"}},{default:n(()=>[(s(!0),d(ue,null,de(l(O)().textbookLists,(y,E)=>(s(),X(b,{key:E,label:y.name,value:y.uuid},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),o(oe,{modelValue:l(c).textbookVal,"onUpdate:modelValue":u[1]||(u[1]=y=>l(c).textbookVal=y),onChange:v,placeholder:"模块",style:{width:"100px"}},{default:n(()=>[(s(!0),d(ue,null,de(l(_),(y,E)=>(s(),X(b,{key:E,label:y.name,value:y.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),l(U).length>0?(s(),d("div",Pt,[o(te,{data:l(U),props:{children:"children",label:"name"},class:"hhypt-el-tree","empty-text":"暂无考点/题点数据","check-strictly":!0,"default-expand-all":"","node-key":"id",onNodeClick:se,"highlight-current":!0},{default:n(({node:y})=>[e("span",{title:y.label,class:"pmm2_ellipsis"},L(y.label),9,Nt)]),_:1},8,["data"])])):(s(),d("div",Et,u[11]||(u[11]=[e("img",{style:{width:"100px"},src:"https://oss.hhypt.com/resources/mini-parent-helper/err.png"},null,-1),e("span",{style:{color:"#999"}},"暂无数据",-1)])))])]),e("div",At,[he((s(),d("div",Ht,[e("div",Mt,[e("div",zt,[u[12]||(u[12]=e("div",{class:"bt"},"来源：",-1)),e("ul",Rt,[e("li",{onClick:u[2]||(u[2]=y=>G("fromGroupId",null)),class:j({on:l(c).fromGroupId===null})}," 全部",2),(s(!0),d(ue,null,de(l(q).questionFromGroups,(y,E)=>(s(),d("li",{onClick:ie=>G("fromGroupId",E),class:j({on:l(c).fromGroupId==E}),key:E},L(y),11,Ot))),128))])]),e("div",Dt,[u[13]||(u[13]=e("div",{class:"bt"},"题型：",-1)),e("ul",Zt,[e("li",{onClick:u[3]||(u[3]=y=>G("categoryId",null)),class:j({on:l(c).categoryId===null})},"全部 ",2),(s(!0),d(ue,null,de(l(m),(y,E)=>(s(),d("li",{onClick:ie=>G("categoryId",y.id),class:j({on:l(c).categoryId==y.id}),key:E},L(y.name),11,Jt))),128))])]),e("div",Qt,[u[14]||(u[14]=e("div",{class:"bt"},"难度：",-1)),e("ul",Kt,[e("li",{onClick:u[4]||(u[4]=y=>G("difficultyId",null)),class:j({on:l(c).difficultyId===null})},"全部",2),(s(!0),d(ue,null,de(l(q).difficulties,(y,E)=>(s(),d("li",{onClick:ie=>G("difficultyId",E),class:j({on:l(c).difficultyId==E}),key:E},L(y),11,Gt))),128))])]),e("div",Wt,[u[15]||(u[15]=e("div",{class:"bt"},"加入：",-1)),e("ul",Xt,[e("li",{onClick:u[5]||(u[5]=y=>G("times",null)),class:j({on:l(c).times===null})},"全部",2),(s(!0),d(ue,null,de(l(P),(y,E)=>(s(),d("li",{onClick:ie=>G("times",E),class:j({on:l(c).times==E}),key:E},L(y.name),11,Yt))),128))]),e("div",jt,[o(H,{modelValue:l(w),"onUpdate:modelValue":u[6]||(u[6]=y=>ae(w)?w.value=y:null),type:"daterange","range-separator":"至","value-format":"x","start-placeholder":"开始时间","end-placeholder":"结束时间",onChange:N},null,8,["modelValue"])])])]),l(K).length>0?(s(),d("div",el,[l(p).length>0?(s(),d("div",tl,[(s(!0),d(ue,null,de(l(p),(y,E)=>(s(),d("div",{class:j(["item-box",{on:y.onFalgs}]),key:E+l(Z),onClick:ie=>I(y,E)},[e("div",sl,L(y.answerKeyFalg=!0),1),u[17]||(u[17]=e("div",{class:"vector-box"},[e("img",{class:"img on",src:Ne,alt:""}),e("img",{class:"img",src:Ee})],-1)),e("div",ol,[e("span",null,L(y.categoryName),1),u[16]||(u[16]=z()),e("span",nl,L(y.difficultyLabel),1)]),o(Se,{childItem:y,isSortNumNo:!0},null,8,["childItem"])],10,ll))),128))])):R("",!0),e("div",al,[o(J,{"current-page":l(Z),"onUpdate:currentPage":u[7]||(u[7]=y=>ae(Z)?Z.value=y:null),"page-size":3,background:"",layout:"prev, pager, next",total:l(a),onCurrentChange:M},null,8,["current-page","total"])])])):(s(),d("div",il,u[18]||(u[18]=[e("img",{style:{width:"200px"},src:"https://oss.hhypt.com/resources/mini-parent-helper/err.png"},null,-1),e("span",{style:{color:"#999"}},"暂无数据",-1)])))])),[[re,l(r)]])])])):R("",!0)]),_:1},8,["modelValue"])}}}),cl=Ce(rl,[["__scopeId","data-v-9133107a"]]),pl={class:"pmm2_flex_between"},ml={key:0},_l={class:"searchList-box scrollBar"},fl=["onClick"],vl=["onClick"],gl={key:1,class:"searchList-box pmm2_flex_center",style:{"flex-direction":"column"}},Cl={class:"pmm2_flex_between"},hl={class:"dialog-footer pmm2_flex_center"},yl={class:"tig"},kl=_e({__name:"quesPush",emits:["confirm"],setup(ee,{expose:le,emit:S}){const f=k(!1),g=k([]),C=k(!0),c=k([]);let q={},m={};const x=(_,h)=>{q={uuid:_.content,clientId:_.uuid||_.content},m=h,Q(),f.value=!0},Q=()=>{C.value=!0,g.value=[],ut(q).then(_=>{var h;c.value=((h=_==null?void 0:_.data)==null?void 0:h.questions)||[],C.value=!1})},F=(_,h)=>{if(O().paperUuidList.findIndex(p=>p==_.uuid)!=-1){Y({message:"该题已经添加进试卷了，请重新选择",type:"warning"});return}let v=g.value.findIndex(p=>p.uuid==_.uuid);v==-1?(c.value[h].onFalgs=!0,g.value.push(_)):(c.value[h].onFalgs=!1,g.value.splice(v,1))};le({opens:x});const B=S,P=()=>{let _=[],h=g.value.length,U=[];if(h==0)return Y({message:"请选择需要添加的题目",type:"warning"});for(let v=0;v<h;v++){let p=g.value[v];U.push(p.uuid);let M={isQuestion:!0,itemTypeId:ne.QUESTION,itemTypeLabel:"试题",content:p.uuid,question:p};_.push(M)}O().paperUuidList.push(...U),B("confirm",m,_),f.value=!1};return(_,h)=>{const U=pe("CloseBold"),v=ge,p=ke,M=pe("Refresh"),K=fe,Z=ye,a=Le;return s(),X(Z,{"append-to-body":"","close-on-click-modal":!1,class:"hhypt-dialog-boxs","show-close":!1,modelValue:l(f),"onUpdate:modelValue":h[1]||(h[1]=r=>ae(f)?f.value=r:null),title:"类题推送",width:"1200","align-center":""},{header:n(({close:r,titleClass:V})=>[e("div",pl,[e("span",{class:j(V)},"类题推送",2),o(p,{onClick:r,underline:!1},{default:n(()=>[o(v,{size:"20"},{default:n(()=>[o(U)]),_:1})]),_:2},1032,["onClick"])])]),footer:n(()=>[e("div",Cl,[h[8]||(h[8]=e("div",null,null,-1)),e("div",hl,[e("div",yl," 已选择"+L(l(g).length)+"题 ",1),o(K,{style:{width:"70px"},onClick:h[0]||(h[0]=r=>f.value=!1)},{default:n(()=>h[6]||(h[6]=[z("取消")])),_:1,__:[6]}),o(K,{style:{width:"70px"},type:"primary",onClick:P},{default:n(()=>h[7]||(h[7]=[z(" 确定 ")])),_:1,__:[7]})])])]),default:n(()=>[he((s(),d("div",null,[l(c).length>0?(s(),d("div",ml,[e("div",{class:"ChangeAbatch huiA",onClick:Q},[o(v,null,{default:n(()=>[o(M)]),_:1}),h[2]||(h[2]=z(" 换一批 "))]),e("div",_l,[(s(!0),d(ue,null,de(l(c),(r,V)=>(s(),d("div",{class:j(["item-box",{on:r.onFalgs}]),key:V,onClick:I=>F(r,V)},[h[4]||(h[4]=e("div",{class:"vector-box"},[e("img",{class:"img on",src:Ne,alt:""}),e("img",{class:"img",src:Ee})],-1)),o(Se,{childItem:r,isSortNumNo:!0},{default:n(()=>[e("div",{class:j(["lookansw pmm2_flex_acenter",{on:r.answerKeyFalg}]),onClick:Ze(I=>r.answerKeyFalg=!r.answerKeyFalg,["stop"])},h[3]||(h[3]=[e("svg",{class:"svg-icon",width:"16",height:"16",viewBox:"0 0 16 16",xmlns:"http://www.w3.org/2000/svg"},[e("path",{d:"M11.9987 1C12.7059 1 13.3842 1.28095 13.8843 1.78105C14.3844 2.28115 14.6654 2.95942 14.6654 3.66667V12.3333C14.6654 13.0406 14.3844 13.7189 13.8843 14.219C13.3842 14.719 12.7059 15 11.9987 15H3.9987C3.29145 15 2.61318 14.719 2.11308 14.219C1.61298 13.7189 1.33203 13.0406 1.33203 12.3333V3.66667C1.33203 2.95942 1.61298 2.28115 2.11308 1.78105C2.61318 1.28095 3.29145 1 3.9987 1H11.9987ZM11.9987 2H3.9987C3.56921 2.00002 3.15631 2.16585 2.84611 2.46289C2.5359 2.75993 2.35234 3.16525 2.3337 3.59433L2.33203 3.66667V12.3333C2.33206 12.7628 2.49788 13.1757 2.79492 13.4859C3.09196 13.7961 3.49728 13.9797 3.92636 13.9983L3.9987 14H11.9987C12.4282 14 12.8411 13.8342 13.1513 13.5371C13.4615 13.2401 13.6451 12.8347 13.6637 12.4057L13.6654 12.3333V3.66667C13.6653 3.23718 13.4995 2.82428 13.2025 2.51407C12.9054 2.20387 12.5001 2.02031 12.071 2.00167L11.9987 2ZM10.588 4.55667L11.7427 5.22333L10.076 8.11L8.83203 8.93133L8.92136 7.44333L10.588 4.55667ZM11.5434 3.56867L12.1207 3.902C12.1973 3.9462 12.2531 4.01901 12.276 4.1044C12.2989 4.18979 12.2869 4.28077 12.2427 4.35733L11.9094 4.93467L10.7547 4.268L11.088 3.69067C11.1322 3.61411 11.205 3.55825 11.2904 3.53537C11.3758 3.51249 11.4668 3.52447 11.5434 3.56867Z"}),e("path",{d:"M5.16797 10.6667H10.8346C10.9672 10.6667 11.0944 10.7193 11.1882 10.8131C11.282 10.9069 11.3346 11.0341 11.3346 11.1667C11.3346 11.2993 11.282 11.4265 11.1882 11.5202C11.0944 11.614 10.9672 11.6667 10.8346 11.6667H5.16797C5.03536 11.6667 4.90818 11.614 4.81442 11.5202C4.72065 11.4265 4.66797 11.2993 4.66797 11.1667C4.66797 11.0341 4.72065 10.9069 4.81442 10.8131C4.90818 10.7193 5.03536 10.6667 5.16797 10.6667ZM5.16797 8H7.5013C7.63391 8 7.76109 8.05268 7.85486 8.14645C7.94862 8.24021 8.0013 8.36739 8.0013 8.5C8.0013 8.63261 7.94862 8.75979 7.85486 8.85355C7.76109 8.94732 7.63391 9 7.5013 9H5.16797C5.03536 9 4.90818 8.94732 4.81442 8.85355C4.72065 8.75979 4.66797 8.63261 4.66797 8.5C4.66797 8.36739 4.72065 8.24021 4.81442 8.14645C4.90818 8.05268 5.03536 8 5.16797 8Z"})],-1),e("span",{style:{"padding-left":"5px"}},"答案解析",-1)]),10,vl)]),_:2},1032,["childItem"])],10,fl))),128))])])):(s(),d("div",gl,h[5]||(h[5]=[e("img",{style:{width:"200px"},src:"https://oss.hhypt.com/resources/mini-parent-helper/err.png"},null,-1),e("span",{style:{color:"#999"}},"暂无类题",-1)])))])),[[a,l(C)]])]),_:1},8,["modelValue"])}}}),xl=Ce(kl,[["__scopeId","data-v-6f029bbd"]]),bl={class:"pmm2_flex_between"},wl={class:"quesRoblem-box"},$l={class:"pmm2_flex_between"},Il={class:"dialog-footer pmm2_flex_center"},Vl=_e({__name:"quesRoblem",props:{falg:{},falgModifiers:{},uuid:{},uuidModifiers:{}},emits:["update:falg","update:uuid"],setup(ee){const le=Fe(ee,"falg"),S=Fe(ee,"uuid"),f=k(""),g=k([]),C=[{id:1,name:"题干错误"},{id:2,name:"答案错误"},{id:3,name:"解析错误"},{id:4,name:"考点错误"}],c=()=>{g.value=[],f.value=""},q=()=>{if(!f.value)return Y({message:"请输入问题描述",type:"warning"});let m={uuid:S.value,content:f.value,types:g.value};dt(m).then(x=>{x.code==0?(Y({message:"反馈成功",type:"success"}),le.value=!1):Y({message:x.msg,type:"warning"})})};return(m,x)=>{const Q=pe("CloseBold"),F=ge,B=ke,P=Te,_=Je,h=$e,U=fe,v=ye;return s(),X(v,{"append-to-body":"","close-on-click-modal":!1,class:"hhypt-dialog-boxs","show-close":!1,modelValue:le.value,"onUpdate:modelValue":x[3]||(x[3]=p=>le.value=p),title:"纠错",width:"500",onOpen:c,"align-center":""},{header:n(({close:p,titleClass:M})=>[e("div",bl,[e("span",{class:j(M)},"纠错",2),o(B,{onClick:p,underline:!1},{default:n(()=>[o(F,{size:"20"},{default:n(()=>[o(Q)]),_:1})]),_:2},1032,["onClick"])])]),footer:n(()=>[e("div",$l,[x[8]||(x[8]=e("div",null,null,-1)),e("div",Il,[o(U,{style:{width:"70px"},onClick:x[2]||(x[2]=p=>le.value=!1)},{default:n(()=>x[6]||(x[6]=[z("取消")])),_:1,__:[6]}),o(U,{style:{width:"70px"},type:"primary",onClick:q},{default:n(()=>x[7]||(x[7]=[z(" 确定 ")])),_:1,__:[7]})])])]),default:n(()=>[e("div",wl,[x[4]||(x[4]=e("div",{class:"bt"},"错误类型：",-1)),e("div",null,[o(_,{modelValue:l(g),"onUpdate:modelValue":x[0]||(x[0]=p=>ae(g)?g.value=p:null)},{default:n(()=>[(s(),d(ue,null,de(C,p=>o(P,{key:p.id,label:p.name,value:p.name},{default:n(()=>[z(L(p.name),1)]),_:2},1032,["label","value"])),64))]),_:1},8,["modelValue"])]),x[5]||(x[5]=e("div",{class:"bt"}," 问题描述： ",-1)),e("div",null,[o(h,{autosize:{minRows:2,maxRows:4},modelValue:l(f),"onUpdate:modelValue":x[1]||(x[1]=p=>ae(f)?f.value=p:null),maxlength:"500",type:"textarea",placeholder:"请输入..."},null,8,["modelValue"])])])]),_:1},8,["modelValue"])}}}),Ll=Ce(Vl,[["__scopeId","data-v-6edcc81f"]]),Sl={class:"pmm2_flex_between"},Fl={key:0,class:"questionView"},ql={key:0,id:"topicMainItem-box",class:"topicMainItem-box scrollBar"},Bl={class:"pmm2_flex_between"},Ul={key:0,style:{color:"#999"}},Tl={class:"dialog-footer"},Pl=_e({__name:"addEditor",props:["isUserPaper"],emits:["confirm"],setup(ee,{expose:le,emit:S}){const f=ee,g=k({}),C=k(!1),c=k("");let q={},m="";const x=k(!1),Q=async(P,_,h)=>{if(q=h,x.value=!1,m=P,P==1)c.value="人工录题",g.value=JSON.parse(JSON.stringify(rt));else{let U=JSON.parse(JSON.stringify(_));g.value=await ct(U),c.value="编辑题目"}C.value=!0},F=S,B=async()=>{x.value=!0;let P=JSON.parse(JSON.stringify(g.value));try{let _=await _t(P);if(_.question.userEditable||(_.uuid="",_.id="",_.content="",_.question.uuid="",_.question.action||(_.question.action="Adapted")),!f.isUserPaper){const h=await ft(_.question);if(h.code==0)Y({message:"操作成功",type:"success"});else{Y({message:h.msg,type:"warning"}),x.value=!1;return}}_.question.userEditable=!0,C.value=!1,x.value=!1,F("confirm",{items:_,...q,types:m})}catch(_){return x.value=!1,Y({message:(_==null?void 0:_.msg)||"请重试",type:"warning"})}};return le({opens:Q}),(P,_)=>{const h=pe("CloseBold"),U=ge,v=ke,p=pt,M=mt,K=fe,Z=ye;return s(),X(Z,{"append-to-body":"","close-on-click-modal":!1,class:"hhypt-dialog-boxs","show-close":!1,modelValue:l(C),"onUpdate:modelValue":_[1]||(_[1]=a=>ae(C)?C.value=a:null),title:l(c),width:"1000",zIndex:9,"align-center":""},{header:n(({close:a,titleClass:r})=>[e("div",Sl,[e("span",{class:j(r)},L(l(c)),3),l(x)?R("",!0):(s(),X(v,{key:0,onClick:a,underline:!1},{default:n(()=>[o(U,{size:"20"},{default:n(()=>[o(h)]),_:1})]),_:2},1032,["onClick"]))])]),footer:n(()=>[e("div",Bl,[e("div",null,[l(c)=="编辑题目"?(s(),d("span",Ul,"编辑题目自动保存至“我的试题”")):R("",!0)]),e("div",Tl,[o(K,{style:{width:"70px"},loading:l(x),onClick:_[0]||(_[0]=a=>C.value=!1)},{default:n(()=>_[2]||(_[2]=[z("取消")])),_:1,__:[2]},8,["loading"]),o(K,{style:{width:"70px"},type:"primary",onClick:B,loading:l(x)},{default:n(()=>_[3]||(_[3]=[z(" 确定 ")])),_:1,__:[3]},8,["loading"])])])]),default:n(()=>[l(C)?(s(),d("div",Fl,[o(M,{question:l(g).question,isbottom:!0},{default:n(()=>[l(g).question.categoryId?(s(),d("div",ql,[o(p,{childItem:l(g),addFalg:!0,childItemClass:"testPaperCenterckeditor"},null,8,["childItem"])])):R("",!0)]),_:1},8,["question"])])):R("",!0)]),_:1},8,["modelValue","title"])}}}),Nl=Ce(Pl,[["__scopeId","data-v-09134c72"]]),El={key:0,class:"topic-item-com ons"},Al={key:0,class:"hover-boxs"},Hl=["onClick"],Ml=["onClick"],zl={class:"part_break_text"},Rl=["onMouseout","onMouseover"],Ol=["onClick"],Dl=["onClick"],Zl=["onClick"],Jl=["onClick"],Ql=["onMouseout","onMouseover"],Kl={key:1},Gl=["id"],Wl={key:0,class:"bottom-box"},Xl=["onClick"],Yl=["onClick"],jl=["onClick"],es=["onClick"],ts=["onClick"],ls=["onClick"],ss=["onClick"],os=["onClick"],ns=["onClick"],as={key:1,class:"bottom-box"},is=["onClick"],us=["onClick"],ds={key:2,class:"childrenLen0 pmm2_flex_center"},rs=_e({__name:"topicItem",props:["paperLists","isUserPaper"],emits:["dellQues","addH1on"],setup(ee,{emit:le}){k(!1);const S=ee,f=k(!1),g=k(""),C=a=>{g.value=a,f.value=!0},c=a=>{a.question.answerKeyFalg=!a.question.answerKeyFalg},q=le,m=(a,r)=>{Pe.confirm("是否确认删除当前题目?","提示",{confirmButtonText:"确认删除",cancelButtonText:"取消",type:"warning"}).then(()=>{q("dellQues",a,r)}).catch(()=>{})},x=a=>{q("addH1on",a)},Q=k(null),F=(a,r)=>{Q.value.opens(a,r)},B=(a,r)=>{a.j!==void 0&&a.j!==null?S.paperLists[a.idx].children.splice(a.j+1,0,...r):(S.paperLists[a.idx].children||(S.paperLists[a.idx].children=[]),S.paperLists[a.idx].children.push(...r)),O().paperChangeFalg=!0},P=k(null),_=(a,r)=>{P.value.opens(r,a)},h=k(null),U=(a,r,V={})=>{h.value.opens(r,V,a)},v=async a=>{a.j===void 0?S.paperLists[a.idx].children=[a.items]:a.types==1?S.paperLists[a.idx].children.splice(a.j+1,0,a.items):S.paperLists[a.idx].children.splice(a.j,1,a.items),O().paperChangeFalg=!0};(()=>{let a=sessionStorage.getItem("basicsToken");if(a){let V=JSON.parse(a);O().basicInfo=V}else vt().then(V=>{let I=V.data||{};O().basicInfo=I,sessionStorage.setItem("basicsToken",JSON.stringify(I))});const{paperInfo:r}=O();gt({stageId:r.stageId,courseCode:r.courseCode}).then(V=>{O().categoriesInfo=V.data||[]})})();const M=(a,r,V)=>{a==="up"?K(r,V):Z(r,V)};function K(a,r){if(r===void 0){if(a===0){Y({message:"当前题已经是第一题了",type:"warning"});return}[S.paperLists[a],S.paperLists[a-1]]=[S.paperLists[a-1],S.paperLists[a]]}else if(r===0&&a!==0){const V=S.paperLists[a-1],I=S.paperLists[a].children.shift();V.children.push(I)}else{const V=S.paperLists[a].children;if(r>0)[V[r],V[r-1]]=[V[r-1],V[r]];else{Y({message:"已经是第一个小题了",type:"warning"});return}}O().paperChangeFalg=!0}function Z(a,r){const V=S.paperLists.length;if(r===void 0){if(a===V-1){Y({message:"当前题已经是最后一题了",type:"warning"});return}[S.paperLists[a],S.paperLists[a+1]]=[S.paperLists[a+1],S.paperLists[a]]}else{const I=S.paperLists[a].children,w=I.length;if(r===w-1&&a!==V-1){const N=S.paperLists[a+1],G=I.pop();N.children.unshift(G)}else if(r<w-1)[I[r],I[r+1]]=[I[r+1],I[r]];else{Y({message:"已经是最后一个小题了",type:"warning"});return}}O().paperChangeFalg=!0}return(a,r)=>{const V=fe;return s(),d(ue,null,[(s(!0),d(ue,null,de(ee.paperLists,(I,w)=>(s(),d("div",{class:"topic-item-box",key:w},[I.itemTypeId==l(ne).Subsection&&I.showPartBreakText?(s(),d("div",El,[ee.isUserPaper?(s(),d("div",Al,[e("ul",null,[e("li",{class:j(["huiA",{on:w==0}]),onClick:N=>M("up",w)},"上移",10,Hl),e("li",{class:j(["huiA",{on:ee.paperLists.length-1==w}]),onClick:N=>M("dom",w)},"下移",10,Ml)])])):R("",!0),e("div",zl,L(I.content),1)])):I.itemTypeId!=l(ne).Subsection?(s(),d("div",{key:1,class:j(["topic-item-com",{on:I.topicItemComOn}])},[ee.isUserPaper?(s(),d("div",{key:0,class:"hover-boxs",onMouseout:N=>I.topicItemComOn=!1,onMouseover:N=>I.topicItemComOn=!0},[e("ul",null,[e("li",{class:"huiA",onClick:N=>x({idx:w})},"添加大题",8,Ol),e("li",{class:j(["huiA",{on:w==0}]),onClick:N=>M("up",w)},"上移",10,Dl),e("li",{class:j(["huiA",{on:ee.paperLists.length-1==w}]),onClick:N=>M("dom",w)},"下移",10,Zl),e("li",{class:"huiA",onClick:N=>m(w)},"删除",8,Jl)])],40,Rl)):R("",!0),e("div",{class:"H1-box",onMouseout:N=>I.topicItemComOn=!1,onMouseover:N=>I.topicItemComOn=!0},L(I.ChineseNum)+" 、"+L(I.content)+"（共"+L(I.children.length)+"题） ",41,Ql),I.children.length>0?(s(),d("div",Kl,[(s(!0),d(ue,null,de(I.children,(N,G)=>(s(),d("div",{class:"topic-items",key:G,id:"topicId"+w+"c"+G},[o(Se,{childItem:N.question},null,8,["childItem"]),ee.isUserPaper?(s(),d("div",Wl,[e("ul",null,[N.question.userEditable?R("",!0):(s(),d("li",{key:0,class:"huiA",onClick:se=>_({idx:w,j:G},N)},"类题推送",8,Xl)),e("li",{class:"huiA",onClick:se=>U({idx:w,j:G},1)},"人工录题",8,Yl),e("li",{class:"huiA",onClick:se=>F(w,G)},"题库选题",8,jl),e("li",{class:"huiA",onClick:se=>c(N)},"答案解析",8,es),e("li",{class:"huiA",onClick:se=>U({idx:w,j:G},2,N)},L(N.question.userEditable?"编辑":"改编"),9,ts),N.question.userEditable?R("",!0):(s(),d("li",{key:1,class:"huiA",onClick:se=>C(N.content)},"纠错",8,ls)),e("li",{class:"huiA",onClick:se=>M("up",w,G)},"上移",8,ss),e("li",{class:"huiA",onClick:se=>M("dom",w,G)},"下移",8,os),e("li",{class:"huiA",onClick:se=>m(w,G)},"删除",8,ns)])])):(s(),d("div",as,[e("ul",null,[e("li",{class:"huiA",onClick:se=>c(N)},"答案解析",8,is),e("li",{class:"huiA",onClick:se=>U({idx:w,j:G},2,N)},L(N.question.userEditable?"编辑":"改编"),9,us)])]))],8,Gl))),128))])):ee.isUserPaper?(s(),d("div",ds,[o(V,{type:"primary",onClick:N=>U({idx:w},1)},{default:n(()=>r[2]||(r[2]=[z("人工录题")])),_:2,__:[2]},1032,["onClick"]),o(V,{type:"primary",onClick:N=>F(w)},{default:n(()=>r[3]||(r[3]=[z("题库选题")])),_:2,__:[3]},1032,["onClick"])])):R("",!0)],2)):R("",!0)]))),128)),o(cl,{ref_key:"quesBankRef",ref:Q,onConfirm:B},null,512),o(xl,{ref_key:"quesPushRef",ref:P,onConfirm:B},null,512),o(Ll,{uuid:l(g),"onUpdate:uuid":r[0]||(r[0]=I=>ae(g)?g.value=I:null),falg:l(f),"onUpdate:falg":r[1]||(r[1]=I=>ae(f)?f.value=I:null)},null,8,["uuid","falg"]),o(Nl,{ref_key:"addEditorRef",ref:h,onConfirm:v,isUserPaper:ee.isUserPaper},null,8,["isUserPaper"])],64)}}}),cs=Ce(rs,[["__scopeId","data-v-5de9629f"]]),ps={class:"pmm2_flex_between"},ms={key:0,class:"pmm2_flex",style:{padding:"20px"}},_s={key:1,class:"pmm2_flex",style:{padding:"20px"}},fs={class:"pmm2_flex_between"},vs={class:"dialog-footer"},gs=_e({__name:"addH1",emits:["confirm"],setup(ee,{expose:le,emit:S}){const f=k(!1),g=k("");let C={};const c=k(""),q=Q=>{C=Q,Q.content?(Q.text?c.value="编辑分卷名称":c.value="编辑大题名称",g.value=Q.content):(C.add=!0,c.value="添加大题",g.value=""),f.value=!0},m=S,x=()=>{if(!g.value||!g.value.trim())return Y({message:"请输入大题标题",type:"warning"});C.content=g.value,m("confirm",C),f.value=!1};return le({opens:q}),(Q,F)=>{const B=pe("CloseBold"),P=ge,_=ke,h=$e,U=fe,v=ye;return s(),X(v,{"append-to-body":"","close-on-click-modal":!1,class:"hhypt-dialog-boxs","show-close":!1,modelValue:l(f),"onUpdate:modelValue":F[3]||(F[3]=p=>ae(f)?f.value=p:null),title:l(c),width:"500",zIndex:9,"align-center":""},{header:n(({close:p,titleClass:M})=>[e("div",ps,[e("span",{class:j(M)},L(l(c)),3),o(_,{onClick:p,underline:!1},{default:n(()=>[o(P,{size:"20"},{default:n(()=>[o(B)]),_:1})]),_:2},1032,["onClick"])])]),footer:n(()=>[e("div",fs,[F[8]||(F[8]=e("div",null,null,-1)),e("div",vs,[o(U,{style:{width:"70px"},onClick:F[2]||(F[2]=p=>f.value=!1)},{default:n(()=>F[6]||(F[6]=[z("取消")])),_:1,__:[6]}),o(U,{style:{width:"70px"},type:"primary",onClick:x},{default:n(()=>F[7]||(F[7]=[z(" 确定 ")])),_:1,__:[7]})])])]),default:n(()=>[l(c)=="编辑分卷名称"?(s(),d("div",ms,[F[4]||(F[4]=e("div",{style:{width:"120px"}},"分卷名称：",-1)),o(h,{modelValue:l(g),"onUpdate:modelValue":F[0]||(F[0]=p=>ae(g)?g.value=p:null),size:"large",placeholder:"请输入分卷名称"},null,8,["modelValue"])])):(s(),d("div",_s,[F[5]||(F[5]=e("div",{style:{width:"120px"}},"大题名称：",-1)),o(h,{modelValue:l(g),"onUpdate:modelValue":F[1]||(F[1]=p=>ae(g)?g.value=p:null),size:"large",placeholder:"请输入大题名称"},null,8,["modelValue"])]))]),_:1},8,["modelValue","title"])}}}),Cs={class:"pmm2_flex_between"},hs={class:"downloads-box"},ys={class:"downloads-item"},ks={class:"nr"},xs={key:0,class:"downloads-item"},bs={class:"nr"},ws={class:"pmm2_flex_between"},$s={class:"dialog-footer"},Is=_e({__name:"downloads",emits:["setFilename","downloadSuccess"],setup(ee,{expose:le,emit:S}){const f=k(!1),g=k(1),C=k("");let c="",q="";const m=(U,v,p)=>{c=p,C.value=U,q=v,g.value=1,f.value=!0};let x=null;const Q=S;le({opens:m});const F=()=>{f.value=!1,Q("downloadSuccess")};let B=0;const P=k(!1),_=()=>{if(!C.value)return Y({message:"请输入试卷标题",type:"warning"});if(!c){f.value=!1,Q("setFilename",C.value,q);return}B=0,P.value=!0,Ct({uuid:c,filename:C.value,contentType:g.value}).then(U=>{if(P.value=!1,U.code!=0)return Y({message:U.msg,type:"warning"});{let v=U.data.taskId;h(v),x=setInterval(()=>{h(v)},3e3),f.value=!1}})},h=async U=>{B++;let v=await ht({uuid:c,taskId:U});v.data.url&&(await $t(v.data.url,C.value+".docx"),clearInterval(x),x=null,Q("downloadSuccess"),Y({type:"success",message:"下载成功"})),B>20&&(clearInterval(x),x=null,Q("downloadSuccess"),Y({type:"warning",message:"下载失败"}))};return(U,v)=>{const p=pe("CloseBold"),M=ge,K=ke,Z=$e,a=Qe,r=Ke,V=fe,I=ye;return s(),X(I,{"append-to-body":"","close-on-click-modal":!1,class:"hhypt-dialog-boxs","show-close":!1,modelValue:l(f),"onUpdate:modelValue":v[4]||(v[4]=w=>ae(f)?f.value=w:null),title:l(c)?"下载试卷":"保存试卷",width:"500",zIndex:9,"align-center":""},{header:n(({titleClass:w})=>[e("div",Cs,[e("span",{class:j(w)},L(l(c)?"下载试卷":"保存试卷"),3),o(K,{onClick:v[0]||(v[0]=N=>F()),underline:!1},{default:n(()=>[o(M,{size:"20"},{default:n(()=>[o(p)]),_:1})]),_:1})])]),footer:n(()=>[e("div",ws,[v[11]||(v[11]=e("div",null,null,-1)),e("div",$s,[o(V,{style:{width:"70px"},onClick:v[3]||(v[3]=w=>F())},{default:n(()=>v[9]||(v[9]=[z("取消")])),_:1,__:[9]}),o(V,{style:{width:"70px"},type:"primary",onClick:_,loading:l(P)},{default:n(()=>v[10]||(v[10]=[z(" 确定 ")])),_:1,__:[10]},8,["loading"])])])]),default:n(()=>[e("div",hs,[e("div",ys,[v[5]||(v[5]=e("div",{class:"bt"},"试卷标题：",-1)),e("div",ks,[o(Z,{modelValue:l(C),"onUpdate:modelValue":v[1]||(v[1]=w=>ae(C)?C.value=w:null),placeholder:"请输入试卷标题"},null,8,["modelValue"])])]),l(c)?(s(),d("div",xs,[v[8]||(v[8]=e("div",{class:"bt"},"答案位置：",-1)),e("div",bs,[o(r,{modelValue:l(g),"onUpdate:modelValue":v[2]||(v[2]=w=>ae(g)?g.value=w:null)},{default:n(()=>[o(a,{value:1},{default:n(()=>v[6]||(v[6]=[z("卷尾")])),_:1,__:[6]}),o(a,{value:2},{default:n(()=>v[7]||(v[7]=[z("题后")])),_:1,__:[7]})]),_:1},8,["modelValue"])])])):R("",!0)])]),_:1},8,["modelValue","title"])}}}),Vs=Ce(Is,[["__scopeId","data-v-7ac60433"]]),Ls={key:0,class:"pmm2_flex_center",style:{"flex-direction":"column"}},Ss={style:{color:"#999"}},Fs={key:1,class:"testPaperCenter-box","element-loading-background":"rgba(122, 122, 122, 0.8)"},qs={class:"header-box"},Bs={key:0,class:"container pmm2_flex_between"},Us={class:"left-box pmm2_flex_acenter"},Ts={href:"/"},Ps=["src"],Ns={class:"right-box"},Es={key:1,class:"container pmm2_flex_between"},As={class:"left-box pmm2_flex_acenter"},Hs={href:"/"},Ms=["src"],zs={class:"main-box scrollBar"},Rs={class:"container"},Os={class:"topic-box scrollBar"},Ds={key:0,class:"topic-title",id:"topic-titleBoxss"},Zs={key:1,class:"topic-title",id:"topic-titleBoxss",style:{"text-align":"center"}},Js={class:"page-title"},Qs={key:0,style:{"font-size":"18px","margin-top":"4px"}},Ks={key:1,style:{"font-size":"14px",margin:"4px 0"}},Gs={key:2,style:{"font-size":"14px"}},Ws={key:2,class:"topic-main"},Xs={class:"setup-box scrollBar"},Ys={key:0,class:"setup-com exampaperStyle-box"},js={class:"title-box pmm2_flex_between"},eo={style:{"padding-top":"15px"}},to={class:"setup-com exampaperStyle-box"},lo={class:"title-box pmm2_flex_between"},so={class:"title pmm2_flex_acenter"},oo={key:0,class:"tig"},no={class:"exampaperstructure-box",style:{"padding-top":"15px"}},ao={key:0,class:"part_break_text pmm2_flex_center"},io=["onClick"],uo={key:1,class:"paper-box"},ro={key:0,class:"H1-box pmm2_flex_between"},co={key:0},po=["onClick"],mo={key:1,class:"pmm2_flex_acenter"},_o=["onClick"],fo={key:2,class:"pmm2_flex_acenter"},vo=["onClick"],go={key:0},Co={key:1},ho={class:"setup-com exampaperStyle-box"},yo={class:"title-box pmm2_flex_between"},ko={style:{"padding-top":"15px"}},xo={class:"com"},bo={class:"com"},wo={class:"pmm2_flex_between",style:{width:"80%",margin:"0 auto"}},$o=_e({__name:"index",setup(ee){const le=lt(),S=Ge();let f="";S.params.uuid?f=S.params.uuid:f=new URLSearchParams(location.search).get("uuid");const g=k(""),C=k({exampaperStyleFalg:!1,exampaperstructureFalg:!0,exampaperAnalysisFalg:!0}),c=k(!1),q=k([]),m=k({}),x=k([]),Q=[];let F=!1,B={};const P=k(!1),_=async()=>{if(!f){g.value="非法请求";return}c.value=!1;let i=await yt({uuid:f});if(i.code!=0)return c.value=!0,g.value=i.msg;let t=i.data||{};B=t.paper||{},m.value=B.fullSetting,P.value=B.isUserPaper,m.value.title=B.title;let b={stageId:B.stageId,courseCode:B.courseCode,stageLabel:B.stageLabel,courseName:B.courseName};O().bloomLevelEnabled=t==null?void 0:t.bloomLevelEnabled;let oe={stageId:b.stageId,courseCode:b.courseCode};kt(oe).then(H=>{O().textbookLists=H.data||[]}),O().knowledgePointsFalg=t.knowledgeEnabled,t.knowledgeEnabled&&xt(oe).then(H=>{O().knowledgePointsList=H.data||[]}),O().paperInfo=b;let te=[];t.items.forEach(H=>{var J;H.itemTypeId===ne.Subsection?(H.showPartBreakText=B.fullSetting.showPartBreakText,te.push({...H,children:[]}),F=!0):H.itemTypeId===ne.H1headline?te.push({...H,children:[]}):((J=H==null?void 0:H.question)!=null&&J.uuid&&Q.push(H.question.uuid),te[te.length-1].children.push(H))}),O().paperUuidList=Q,q.value=te,c.value=!0},h=(i,t)=>{(t.match(/\n/g)||[]).length>=1&&i.preventDefault()},U=i=>{if(i&&!F){const t=JSON.parse(JSON.stringify(Ve)),b=JSON.parse(JSON.stringify(Ve));t.content="第Ⅰ卷",t.itemTypeId=ne.Subsection,t.showPartBreakText=i,b.content="第Ⅱ卷",b.itemTypeId=ne.Subsection,b.showPartBreakText=i,q.value.unshift(t,b),F=!0;return}q.value.forEach(t=>{t.itemTypeId==ne.Subsection&&(t.showPartBreakText=i)})},v=We(()=>{let i=new Map,t=new Map,b=0,oe=1;q.value.forEach(J=>{J.itemTypeId===ne.H1headline&&(J.ChineseNum=nt(oe),oe++,J.children.forEach(D=>{var y;if([ne.Complex,ne.ReadingCloze,ne.MultipleX2Multiple].includes(D.question.logicTypeId)){let ie=D.question.detailData.children.length;for(let xe=0;xe<ie;xe++)b++,D.question.detailData.children[xe].detailData.sortNum=b;D.sortNum=D.question.detailData.children[0].detailData.sortNum+"~"+D.question.detailData.children[ie-1].detailData.sortNum}else b++,D.question.detailData.sortNum=b,D.sortNum=b;let W=D.question.categoryName||"";i.set(W,(i.get(W)||0)+1);let re=D.question.testPoints||[];for(let E of re){let ie=(E.name||"").trim();t.set(ie,{name:ie,num:(((y=t.get(ie))==null?void 0:y.num)||0)+1,uuid:E.uuid})}}))}),x.value=Array.from(t.values());let te=Array.from(i,([J,D])=>({categoryName:J,num:D})),H=te.reduce((J,D)=>J+D.num,0);return te.map(J=>({...J,proportion:(J.num/H*100).toFixed(2)}))});_();const p=k(!1),M=k(!1),K=k(!1),Z=k(!1);function a(i,t,b){if(i&&(!t||!t.trim()))return p.value=!1,M.value=!1,K.value=!1,A("topic-titleBoxss"),Y({message:b,type:"warning"})}const r=k(null),V=()=>{p.value=!1,M.value=!1,K.value=!1,Z.value=!1},I=async i=>{if(M.value=!0,i!="save")if(i=="maker")Z.value=!0;else{r.value.opens(m.value.title,i,f),K.value=!0,M.value=!1;return}p.value=!0;const{title:t,showSubHeading:b,subHeading:oe,paperInfo:te,showPaperInfo:H,showStudentInfo:J,studentInfo:D}=m.value;if(a(!0,t,"请输入试卷标题")||a(b,oe,"请输入副标题")||a(H,te,"请输入试卷信息")||a(J,D,"请输入考试信息"))return;const W=await bt({uuid:f});if(W.code!=0)return Y({message:W.msg,type:"warning"});if((W.data||{}).allow)N(i);else{r.value.opens(m.value.title,i);return}},w=(i,t)=>{m.value.title=i,N(t,i)},N=async(i,t)=>{var D;let b=JSON.parse(JSON.stringify(q.value)),oe=b.length,te=[];for(let W=0;W<oe;W++){const{children:re,...y}=b[W];te.push(y,...re)}let H={items:te};O().paperChangeFalg&&(H.questionChanged=!0),t?(B.title=t,H.sourceUuid=B.uuid,delete B.uuid,B.fullSetting=m.value):(H.uuid=f,B.title=m.value.title,B.fullSetting=m.value),H.paper=B;const J=await wt(H);if(J.code!=0){M.value=!1,i=="save"||(i=="maker"?Z.value=!1:K.value=!1),p.value=!1,Y({message:J.msg,type:"warning"});return}else{let W=J.data||{};(D=W==null?void 0:W.paper)!=null&&D.uuid&&t&&(f=W.paper.uuid,B.uuid=f,le.replace({path:"/testPaperCenter/"+f})),M.value=!1,_(),i=="save"?(p.value=!1,t?Pe.confirm("试卷已修改，要需要重新制作答题卡，是否去制作?","保存成功",{confirmButtonText:"制作答题卡",cancelButtonText:"取消"}).then(()=>{p.value=!1,Z.value=!1,window.location.href=W.cardMakerUrl}).catch(()=>{}):Y({message:"保存成功",type:"success"})):i=="maker"?(p.value=!1,Z.value=!1,window.location.href=W.cardMakerUrl):(c.value=!0,r.value.opens(m.value.title,i,f))}},G=()=>(O().paperChangeFalg=!0,!0),se=(i=null,t=null)=>{setTimeout(()=>{i===null&&(q.value=[]),t===null?q.value.splice(i,1):q.value[i].children.splice(t,1)},250),O().paperChangeFalg=!0},be=k(null),T=i=>{be.value.opens(i)},u=i=>{if(i.add){const t=JSON.parse(JSON.stringify(Ve));t.content=i.content,t.uuid="",q.value.splice(i.idx+1,0,t)}else q.value[i.idx].content=i.content;O().paperChangeFalg=!0},A=(i,t)=>{const b=document.getElementById(i);b&&(t==1?(b.scrollIntoView({behavior:"smooth",block:"center",inline:"nearest"}),b.classList.add("on"),setTimeout(()=>{b.classList.remove("on")},1500)):b.scrollIntoView({behavior:"smooth"}))};return(i,t)=>{const b=fe,oe=$e,te=pe("ArrowDown"),H=pe("ArrowUp"),J=ge,D=Te,W=Ye,re=je,y=pe("CircleCloseFilled"),E=tt,ie=et,xe=Le;return l(g)?(s(),d("div",Ls,[t[18]||(t[18]=e("img",{style:{width:"200px"},src:"https://oss.hhypt.com/resources/mini-parent-helper/err.png"},null,-1)),e("span",Ss,L(l(g)),1)])):he((s(),d("div",Fs,[e("header",qs,[l(P)?(s(),d("div",Bs,[e("div",Us,[e("a",Ts,[e("img",{src:l(Be)(),alt:"Logo",width:"218",height:"38"},null,8,Ps)])]),t[22]||(t[22]=e("div",{class:"title"},"组卷中心",-1)),e("div",Ns,[t[21]||(t[21]=e("span",{class:"tig"},"试卷保存后，可在“个人题库-我的试卷”中查看",-1)),o(b,{disabled:l(p),loading:l(M),onClick:t[0]||(t[0]=$=>I("save"))},{default:n(()=>[z(L(l(M)?"试卷保存中...":"保存至我的试卷"),1)]),_:1},8,["disabled","loading"]),o(b,{disabled:l(p),loading:l(K),onClick:t[1]||(t[1]=$=>I("down")),type:"primary"},{default:n(()=>[z(L(l(K)?"下载中...":"下载试卷"),1)]),_:1},8,["disabled","loading"]),l(B).hasCard?(s(),X(b,{key:0,disabled:l(p),loading:l(Z),onClick:t[2]||(t[2]=$=>I("maker")),type:"primary"},{default:n(()=>t[19]||(t[19]=[z("编辑答题卡")])),_:1,__:[19]},8,["disabled","loading"])):(s(),X(b,{key:1,disabled:l(p),loading:l(Z),onClick:t[3]||(t[3]=$=>I("maker")),type:"primary"},{default:n(()=>t[20]||(t[20]=[z("制作答题卡")])),_:1,__:[20]},8,["disabled","loading"]))])])):(s(),d("div",Es,[e("div",As,[e("a",Hs,[e("img",{src:l(Be)(),alt:"Logo",width:"218",height:"38"},null,8,Ms)])]),t[23]||(t[23]=e("div",{class:"title"},"试卷详情",-1)),t[24]||(t[24]=e("div",{class:"right-box",style:{width:"300px"}},null,-1))]))]),e("main",zs,[e("div",Rs,[e("div",Os,[l(P)?(s(),d("div",Ds,[o(oe,{modelValue:l(m).title,"onUpdate:modelValue":t[4]||(t[4]=$=>l(m).title=$),autosize:"",type:"text",maxlength:"50",class:"page-title el-textareas",placeholder:"请输入标题"},null,8,["modelValue"]),l(m).showSubHeading?(s(),X(oe,{key:0,modelValue:l(m).subHeading,"onUpdate:modelValue":t[5]||(t[5]=$=>l(m).subHeading=$),autosize:"",type:"textarea",onKeydown:t[6]||(t[6]=Xe($=>h($,l(m).subHeading),["enter"])),style:{"font-size":"18px"},maxlength:"50",class:"el-textareas",placeholder:"请输入副标题"},null,8,["modelValue"])):R("",!0),l(m).showPaperInfo?(s(),X(oe,{key:1,modelValue:l(m).paperInfo,"onUpdate:modelValue":t[7]||(t[7]=$=>l(m).paperInfo=$),autosize:"",type:"textarea",style:{"font-size":"14px",margin:"4px 0"},maxlength:"50",class:"el-textareas",placeholder:"请输入试卷信息"},null,8,["modelValue"])):R("",!0),l(m).showStudentInfo?(s(),X(oe,{key:2,modelValue:l(m).studentInfo,"onUpdate:modelValue":t[8]||(t[8]=$=>l(m).studentInfo=$),autosize:"",type:"textarea",style:{"font-size":"14px"},maxlength:"100",class:"el-textareas",placeholder:"请输入考试信息"},null,8,["modelValue"])):R("",!0)])):(s(),d("div",Zs,[e("div",Js,L(l(m).title),1),l(m).showSubHeading?(s(),d("div",Qs,L(l(m).subHeading),1)):R("",!0),l(m).showPaperInfo?(s(),d("div",Ks,L(l(m).paperInfo),1)):R("",!0),l(m).showStudentInfo?(s(),d("div",Gs,L(l(m).studentInfo),1)):R("",!0)])),l(c)?(s(),d("div",Ws,[o(cs,{isUserPaper:l(P),paperLists:l(q),onDellQues:se,onAddH1on:T},null,8,["isUserPaper","paperLists"])])):R("",!0)]),e("div",Xs,[l(P)?(s(),d("div",Ys,[e("div",js,[t[25]||(t[25]=e("div",{class:"title"},[e("span",{class:"bt"},"试卷样式")],-1)),e("div",{class:"collapse pmm2_flex_acenter huiA2",onClick:t[9]||(t[9]=$=>l(C).exampaperStyleFalg=!l(C).exampaperStyleFalg)},[e("span",null,L(l(C).exampaperStyleFalg?"收起":"展开"),1),o(J,{size:"14"},{default:n(()=>[l(C).exampaperStyleFalg?(s(),X(te,{key:0})):(s(),X(H,{key:1}))]),_:1})])]),o(W,null,{default:n(()=>[he(e("div",eo,[o(D,{modelValue:l(m).showSubHeading,"onUpdate:modelValue":t[10]||(t[10]=$=>l(m).showSubHeading=$),label:"副标题"},null,8,["modelValue"]),o(D,{modelValue:l(m).showPaperInfo,"onUpdate:modelValue":t[11]||(t[11]=$=>l(m).showPaperInfo=$),label:"试卷信息"},null,8,["modelValue"]),o(D,{modelValue:l(m).showStudentInfo,"onUpdate:modelValue":t[12]||(t[12]=$=>l(m).showStudentInfo=$),label:"考试信息"},null,8,["modelValue"]),o(D,{onChange:U,modelValue:l(m).showPartBreakText,"onUpdate:modelValue":t[13]||(t[13]=$=>l(m).showPartBreakText=$),label:"分卷及注释"},null,8,["modelValue"])],512),[[Ie,l(C).exampaperStyleFalg]])]),_:1})])):R("",!0),e("div",to,[e("div",lo,[e("div",so,[t[26]||(t[26]=e("span",{class:"bt"},"试卷结构",-1)),l(P)?(s(),d("div",oo,"*拖拽题号可调整顺序")):R("",!0)]),e("div",{class:"collapse pmm2_flex_acenter huiA2",onClick:t[14]||(t[14]=$=>l(C).exampaperstructureFalg=!l(C).exampaperstructureFalg)},[e("span",null,L(l(C).exampaperstructureFalg?"收起":"展开"),1),o(J,{size:"14"},{default:n(()=>[l(C).exampaperstructureFalg?(s(),X(te,{key:0})):(s(),X(H,{key:1}))]),_:1})])]),o(W,null,{default:n(()=>[he(e("div",no,[o(l(qe),{modelValue:l(q),"onUpdate:modelValue":t[15]||(t[15]=$=>ae(q)?q.value=$:null),"item-key":"uuid",move:G},{item:n(({element:$,index:me})=>[e("div",null,[$.itemTypeId==l(ne).Subsection&&$.showPartBreakText?(s(),d("div",ao,[e("span",null,L($.content),1),e("img",{class:"icon huiA",onClick:ce=>T({content:$.content,idx:me,text:!0}),src:Ue,alt:""},null,8,io)])):$.itemTypeId!=l(ne).Subsection?(s(),d("div",uo,[$.itemTypeId==l(ne).H1headline?(s(),d("div",ro,[e("span",null,L($.ChineseNum)+"、"+L($.content),1),l(P)?(s(),d("div",co,[e("img",{class:"icon huiA",onClick:ce=>T({content:$.content,idx:me}),src:Ue,alt:""},null,8,po),o(re,{"popper-style":"z-index: 1;","confirm-button-text":"确定","cancel-button-text":"取消",width:"300",icon:"WarningFilled",onConfirm:ce=>se(me),title:"确定此大题下的所有试题吗?"},{actions:n(({confirm:ce,cancel:ve})=>[o(b,{size:"small",onClick:ve},{default:n(()=>t[27]||(t[27]=[z("取消")])),_:2,__:[27]},1032,["onClick"]),o(b,{type:"primary",size:"small",onClick:ce},{default:n(()=>t[28]||(t[28]=[z(" 确认 ")])),_:2,__:[28]},1032,["onClick"])]),reference:n(()=>t[29]||(t[29]=[e("img",{class:"huiA",src:It,alt:""},null,-1)])),_:2},1032,["onConfirm"])])):R("",!0)])):R("",!0),l(P)?(s(),d("ul",mo,[o(l(qe),{group:"children",modelValue:$.children,"onUpdate:modelValue":ce=>$.children=ce,"item-key":"index",move:G,class:"children pmm2_flex_acenter"},{item:n(({element:ce,index:ve})=>[e("li",{class:"huiA",onClick:we=>A("topicId"+me+"c"+ve,1)},[e("span",null,L(ce.sortNum||""),1),o(re,{"popper-style":"z-index: 1;","confirm-button-text":"确定","cancel-button-text":"取消",width:"250",icon:"WarningFilled",onConfirm:we=>se(me,ve),title:"确定删除此试题吗?"},{actions:n(({confirm:we,cancel:Ae})=>[o(b,{size:"small",onClick:Ae},{default:n(()=>t[30]||(t[30]=[z("取消")])),_:2,__:[30]},1032,["onClick"]),o(b,{type:"primary",size:"small",onClick:we},{default:n(()=>t[31]||(t[31]=[z(" 确认 ")])),_:2,__:[31]},1032,["onClick"])]),reference:n(()=>[o(J,{class:"icon"},{default:n(()=>[o(y)]),_:1})]),_:2},1032,["onConfirm"]),t[32]||(t[32]=e("img",{src:st,class:"FrameSvg"},null,-1))],8,_o)]),_:2},1032,["modelValue","onUpdate:modelValue"])])):(s(),d("ul",fo,[(s(!0),d(ue,null,de($.children,(ce,ve)=>(s(),d("div",{key:ve,class:"children pmm2_flex_acenter"},[e("li",{class:"huiA",onClick:we=>A("topicId"+me+"c"+ve,1)},[e("span",null,L(ce.sortNum||""),1)],8,vo)]))),128))]))])):R("",!0)])]),_:1},8,["modelValue"]),l(q).length>0?(s(),d("div",go,[l(P)?(s(),X(re,{key:0,"popper-style":"z-index: 1;","confirm-button-text":"确定","cancel-button-text":"取消",width:"300",icon:"WarningFilled",onConfirm:t[16]||(t[16]=$=>se()),title:"确定全部清空所有试题吗?"},{actions:n(({confirm:$,cancel:me})=>[o(b,{size:"small",onClick:me},{default:n(()=>t[33]||(t[33]=[z("取消")])),_:2,__:[33]},1032,["onClick"]),o(b,{type:"primary",size:"small",onClick:$},{default:n(()=>t[34]||(t[34]=[z(" 确认 ")])),_:2,__:[34]},1032,["onClick"])]),reference:n(()=>t[35]||(t[35]=[e("div",{class:"huiA allDel"},"全部清空",-1)])),_:1})):R("",!0)])):(s(),d("div",Co,t[36]||(t[36]=[e("div",{class:"allDel"},"暂无试题",-1)])))],512),[[Ie,l(C).exampaperstructureFalg]])]),_:1})]),e("div",ho,[e("div",yo,[t[37]||(t[37]=e("div",{class:"title"},[e("span",{class:"bt"},"试卷分析")],-1)),e("div",{class:"collapse pmm2_flex_acenter huiA2",onClick:t[17]||(t[17]=$=>l(C).exampaperAnalysisFalg=!l(C).exampaperAnalysisFalg)},[e("span",null,L(l(C).exampaperAnalysisFalg?"收起":"展开"),1),o(J,{size:"14"},{default:n(()=>[l(C).exampaperAnalysisFalg?(s(),X(te,{key:0})):(s(),X(H,{key:1}))]),_:1})])]),o(W,null,{default:n(()=>[he(e("div",ko,[e("div",xo,[t[38]||(t[38]=e("div",{class:"tm"},"考点/题点分布",-1)),o(ie,{data:l(x),border:"",style:{width:"100%"},"header-cell-style":{backgroundColor:"#f5f7fa",textAlign:"center"}},{default:n(()=>[o(E,{prop:"name",label:"考点/题点",align:"left"}),o(E,{prop:"num",label:"题目数量",align:"center",width:"120"})]),_:1},8,["data"])]),e("div",bo,[t[39]||(t[39]=e("div",{class:"tm"},"题型分布",-1)),o(ie,{data:l(v),border:"","header-cell-style":{backgroundColor:"#f5f7fa"}},{default:n(()=>[o(E,{prop:"categoryName",label:"题型",align:"center"}),o(E,{prop:"num",label:"题目数量",align:"center"},{default:n($=>[e("div",wo,[e("span",null,L($.row.num),1),e("span",null,L($.row.proportion)+"%",1)])]),_:1})]),_:1},8,["data"])])],512),[[Ie,l(C).exampaperAnalysisFalg]])]),_:1})])])])]),l(P)?(s(),X(gs,{key:0,ref_key:"addH1Ref",ref:be,onConfirm:u},null,512)):R("",!0),l(P)?(s(),X(Vs,{key:1,ref_key:"downloadsRef",ref:r,onSetFilename:w,onDownloadSuccess:V},null,512)):R("",!0)])),[[xe,!l(c)]])}}}),Yo=Ce($o,[["__scopeId","data-v-cdce3714"]]);export{Yo as default};

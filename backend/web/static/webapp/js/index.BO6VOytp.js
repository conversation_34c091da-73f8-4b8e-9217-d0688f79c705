import{d as K,r as E,c as N,F as se,q as oe,u as f,o as g,n as J,a as s,e as R,t as z,B as be,b as H,w as v,E as Ce,f as y,h as T,k as le,l as j,j as ie,m as Te,at as Ne,i as ee,aJ as ke,K as te,a1 as Se,J as ne,p as q,M as P,V as Ee,aC as Ae}from"./index.ZZ6UQeaF.js";/* empty css                  *//* empty css                  */import{_ as Q}from"./_plugin-vue_export-helper.DlAUqK2U.js";import{a as Re}from"./index.6W4rOsHv.js";import{s as ae}from"./request.CsKOOJzG.js";/* empty css                *//* empty css                   */const Me={class:"stepsbox-box"},De=["onClick"],Le={class:"num"},$e={class:"t1"},He={key:0,class:"xian"},Ve=K({__name:"index",props:["pageData","types"],emits:["setStep"],setup(h,{emit:M}){new URLSearchParams(location.search).get("step");const m=h,o=E(1),w=E([]);o.value=(m.pageData.step||1)-1;let k=m.pageData.url;m.pageData.content_split_type_id=="PdfManual"?w.value=["内容确认","智能切题","录入确认"]:m.pageData.content_split_type_id=="WordManual"&&(w.value=["智能切题","录入确认"]);const b=M,C=c=>{if(!(o.value<c)){if(m.pageData.content_split_type_id=="PdfManual"){if(m.types=="intelligenttopiccutting"&&c==0||m.types=="divide"&&c==1||m.types=="analysis"&&c==2)return;c==2?window.location.href=k:m.types=="analysis"?window.location.href=k+"&step="+(c+1)+"#/intelligenttopiccutting":b("setStep")}else if(m.pageData.content_split_type_id=="WordManual"){if(m.types=="divide"&&c==0||m.types=="analysis"&&c==1)return;c==0?window.location.href=k+"#/divide":window.location.href=k}}};return(c,x)=>(g(),N("div",Me,[(g(!0),N(se,null,oe(f(w),(S,_)=>(g(),N("div",{class:J(["items",{"last ":_<f(o),on:f(o)==_,aa:f(w).length-1==_}]),key:_},[s("div",{class:J(["wz",{huiA:f(o)>=_}]),onClick:U=>C(_)},[s("div",Le,z(_+1),1),s("div",$e,z(S),1)],10,De),f(w).length-1!=_?(g(),N("div",He)):R("",!0)],2))),128))]))}}),Be=Q(Ve,[["__scopeId","data-v-9868199f"]]),re="api/";function Oe(h){return ae({url:re+"v1/paper-question/paper-content",method:"post",data:h})}function Ie(h){return ae({url:re+"v1/paper-question/split",method:"post",data:h})}const Fe={class:"pmm2_flex_between"},Je={class:"rule-com"},Ue={style:{padding:"10px 0"}},We=K({__name:"rule",props:{modelValue:!1,modelModifiers:{}},emits:["update:modelValue"],setup(h){const M=be(h,"modelValue",!1),V=()=>{var m="width=925,height=500,left=100,top=100";window.open("https://yuejuan-img.yuandingbang.cn/resources/help/paper-split-20250605.mp4","newWindow",m)};return(m,o)=>{const w=j("CloseBold"),k=le,b=Te,C=j("CaretRight"),c=ie,x=Ce;return g(),H(x,{"append-to-body":"",class:"hhypt-dialog-boxs hhypt-dialog-boxs-rule","show-close":!1,modelValue:M.value,"onUpdate:modelValue":o[0]||(o[0]=S=>M.value=S),title:"操作说明",width:"930"},{header:v(({close:S,titleClass:_})=>[s("div",Fe,[s("span",{class:J(_)},"操作说明",2),y(b,{onClick:S,underline:!1},{default:v(()=>[y(k,{size:"20"},{default:v(()=>[y(w)]),_:1})]),_:2},1032,["onClick"])])]),default:v(()=>[s("div",Je,[o[4]||(o[4]=s("h3",null,"智能切题操作说明",-1)),o[5]||(o[5]=s("h4",null,"1、自动识别并切割题目",-1)),o[6]||(o[6]=s("p",null,"系统通过AI检测算法自动识别图像中的题目。会识别题号、版面分布等特征，用户无需手动操作，AI即可快速且准确地完成题目的初步切割。",-1)),o[7]||(o[7]=s("h4",{style:{"padding-top":"20px"}},"2、组合题（复合题）等特殊情况需手动调整切割区域",-1)),o[8]||(o[8]=s("p",null,"尽管AI能够自动识别并切割题目，但在某些情况下，用户可能需要对切割区域进行微调以确保准确性。以下是手动调整切割区域的方法：",-1)),o[9]||(o[9]=s("p",null,"(1) 直接选中：直接选中所需文本，即可完成题目重新圈定。",-1)),o[10]||(o[10]=s("p",null,"(2) 快捷键操作：将鼠标光标置于题目文本起始处，按住Shift键，同时点击文本末尾，即可实现题目重新圈定。",-1)),o[11]||(o[11]=s("p",null,"(3) 预览效果：在调整过程中，可随时在右侧检查区预览切割效果。确认预览效果符合要求后，再继续后续操作。",-1)),o[12]||(o[12]=s("p",{class:"red"},"注：若需撤回操作，可点击右侧检查区的“取消分割”按钮，或界面中心的“撤回”按钮。",-1)),s("p",Ue,[o[2]||(o[2]=s("span",null,"视频展示：",-1)),y(c,{onClick:V,type:"primary"},{default:v(()=>[y(k,null,{default:v(()=>[y(C)]),_:1}),o[1]||(o[1]=T("播放视频"))]),_:1,__:[1]}),o[3]||(o[3]=s("span",{style:{color:"blueviolet","padding-left":"5px"}},"视频将在新的页面打开",-1))]),o[13]||(o[13]=s("h4",null,"3、更改题目内容",-1)),o[14]||(o[14]=s("p",null,"若发现题目内容存在文字错误，可在左侧输入区进行修改。编辑完成后，需点击中间的“转换”按钮。",-1)),o[15]||(o[15]=s("p",null,[T("如发生误操作或需撤回，请点击中间的“撤回”按钮"),s("span",{class:"red"},"（请注意：“撤回”按钮仅在执行“转换”操作或“手动调整切割区域”后才会显示）。")],-1))])]),_:1},8,["modelValue"])}}}),qe=Q(We,[["__scopeId","data-v-d8d0da39"]]),Pe={key:0,class:"segmentation-box"},ze={key:0,class:"segmentation-top"},je={class:"header-box"},Ke={class:"containers pmm2_flex_between"},Qe={class:"left-box pmm2_flex_acenter"},Xe={href:"/"},Ye=["src"],Ge={class:"right-box"},Ze={class:"right-com pmm2_flex_acenter"},et={class:"segmentation-com ckeditor-box"},tt={class:"box",style:{float:"left"}},nt={class:"top-box pmm2_flex_between"},st={class:"bt-box"},ot=["innerHTML"],lt={class:"box",style:{float:"right"}},it={class:"com"},at={class:"ul"},rt=["innerHTML"],dt={key:0,class:"btns"},pt={key:1,class:"empty-box"},ct={class:"pmm2_flex_center",style:{"flex-direction":"column"}},ut={style:{"font-size":"16px","margin-bottom":"20px"}},mt=K({__name:"index",props:["divideType","domHtmls","url","step","propsUuid"],emits:["upStep"],setup(h,{expose:M,emit:V}){Ee();const m=Se();let w=new URLSearchParams(location.search).get("uuid");const k=/<div class="my-wrapper">([\s\S]*?)<\/div>/,b=h;b.propsUuid&&(w=b.propsUuid);let C="";const c=E("111"),x=E([]),S=E(!0),_=E(1),U=()=>{window.history.replaceState(null,"",window.location.pathname),m.go(-1)},B=E(!1),X=()=>new Promise((t,e)=>{let n=document.querySelector("#wordHtml"),l=(n==null?void 0:n.innerHTML)||"";const a=/<span class="math-tex">[\s\S]*?<\/span>/g,r=C.match(a);if(r){let i=0;l=l.replace(a,function(p){return i<r.length?r[i++]:p})}let d=$(l);d.forEach(i=>{if(i.type==2){const p=i.html.match(k);p&&p[1]&&(i.html=p[1])}}),t({questions:d,html:l})}),de=V,Y=()=>{de("upStep")};M({getQuestions:X});const pe=async()=>{const t=ne.service({lock:!0,text:"保存中..",background:"rgba(0, 0, 0, 0.7)"});try{const{questions:e,html:n}=await X();let l=await Ie({uuid:w,questions:e,html:n});if(l.code!=0){t.close(),q({message:l.msg,type:"warning"});return}window.location.replace(l.data),t.close()}catch{q({message:"处理异常",type:"warning"}),t.close()}};let O="",I=[];const L=E(!1),ce=async()=>{c.value="",await P(),c.value=O,O="",x.value=I,I=[],_.value++,L.value=!1},ue=async()=>{L.value=!1,c.value=JSON.parse(JSON.stringify(C));const t=c.value;x.value=$(t),_.value++,O="",I=[],L.value=!1},G=E(""),F=E({}),me=async()=>{const t=ne.service({lock:!0,text:"加载中..",background:"rgba(0, 0, 0, 0.7)"});try{let e=await Oe({uuid:w}),n=e.data||{};if(C=n.html||"",e.code!=0||!C){G.value=e.msg,c.value="",t.close(),q({message:e.msg,type:"warning"});return}c.value=JSON.parse(JSON.stringify(C));const l=c.value;x.value=$(l),b.divideType=="intelligenttopiccutting"?F.value={step:b.step,content_split_type_id:"PdfManual",url:b.url}:n!=null&&n.paperData.content_split_type_id&&(F.value={content_split_type_id:n.paperData.content_split_type_id,step:n.paperData.step,url:n.paperData.url}),t.close()}catch{c.value="",t.close()}},Z=/<div class="my-wrapper">([\s\S]*?)<\/div>/g,W=async()=>{let t=document.querySelector("#wordHtml");const e=(t==null?void 0:t.innerHTML)||"";x.value=$(e)},fe=/^(?:<.*?>)?\n?\s*(?:<.*?>)?[一二三四五六七八九十百]{1,10}(?:<.*?>)?[.|、|．](?:<.*?>)?\s*(.*?)\s*(?:\n|$)/g,ge=/^(?:<(?:b|span|i|p|strong)[^>]*?>)?\d+(?:\.\s?|、|．)/,ve=/(?:<[^>]*>\s*)?\[\[\[/g,$=t=>{if(!t)return[];const e=[];let n=0,l;for(;(l=Z.exec(t))!==null;){const i=t.slice(n,l.index);i&&e.push(...i.split("<br>").filter(Boolean).map(p=>({content:p,source:"br"}))),e.push({content:l[0],source:"regex"}),n=Z.lastIndex}const a=t.slice(n);a&&e.push(...a.split("<br>").filter(i=>i&&i!=="<strong></strong>"&&i!=="<span></span>"&&i!=="<p></p>").map(i=>({content:i,source:"br"})));const r=new Map;let d=0;return e.forEach(i=>{const p=i.content.trim();if(!p)return;let u=1;i.source==="regex"?(u=2,d++):fe.test(p)?(u=3,d++):ge.test(p)?(u=4,d++):ve.test(p)&&d++,r.has(d)||r.set(d,{html:"",type:u});const D=r.get(d);i.source==="regex"?(D.html+=i.content,D.type=2,d++):(D.html+=i.content+"<br/>",u===3&&d++)}),Array.from(r.values()).filter(i=>ye(i.html))};function ye(t){return t.replace(/<[^>]*>/g,"").trim()}Ne(()=>{document.querySelector("#wordHtml").addEventListener("keydown",function(t){if(t.key==="Enter"&&t.target.isContentEditable){t.preventDefault();const e=window.getSelection();if(!e.rangeCount)return;const n=e.getRangeAt(0),l=document.createElement("br");n.insertNode(l),n.setStartAfter(l),n.collapse(!0),e.removeAllRanges(),e.addRange(n)}})});const A={isTargetNode(t){var e;return((e=t==null?void 0:t.classList)==null?void 0:e.contains("my-wrapper"))&&t.tagName==="DIV"},isInForbiddenWrapper(t){for(;t;){if(this.isTargetNode(t))return!0;t=t.parentNode}return!1},findAncestor(t,e={}){const{table:n=!1,mathTex:l=!1,type:a}=e;for(;t&&t.tagName!=="BODY";){if(n&&t.tagName==="TABLE"||l&&t.className==="math-tex"&&(!a||a==="end"))return t;t=t.parentNode}return null},getNextSiblingAfterRange(t){const{endContainer:e,endOffset:n}=t;return e.nodeType===Node.TEXT_NODE?n<e.length?null:e.nextSibling:n<e.childNodes.length?e.childNodes[n]:e.nextSibling},removeAdjacentBrNodes(t){for(;t&&t.tagName==="BR";){const e=t.nextSibling;t.parentNode.removeChild(t),t=e}},removeNodesByType(t,e){const n=document.createTreeWalker(t,NodeFilter.SHOW_ELEMENT,{acceptNode:a=>a.tagName===e?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}),l=[];for(;n.nextNode();)l.push(n.currentNode);l.forEach(a=>a.parentNode.removeChild(a))},completeHtmlTags(t){const e=document.createElement("div");e.appendChild(t.cloneContents());const n=e.innerHTML,l=this.findUnclosedTags(n);if(l.length===0)return t;t.startContainer,t.startOffset;const a=t.endContainer,r=t.endOffset;l.forEach(i=>{const p=document.createElement(i);t.insertNode(p)});const d=document.createRange();return d.setStart(a,r),d.setEnd(a,r),t},findUnclosedTags(t){const e=[],n=["img","br","input","hr","area","base","col","command","embed","keygen","link","meta","param","source","track","wbr"],l=/<([a-z]+)(?:\s[^>]*)?>/gi;let a;for(;(a=l.exec(t))!==null;){const p=a[1].toLowerCase();n.includes(p)||e.push(p)}const r=[],d=/<\/([a-z]+)>/gi;for(;(a=d.exec(t))!==null;)r.push(a[1].toLowerCase());const i=[];return e.forEach(p=>{const u=r.indexOf(p);u!==-1?r.splice(u,1):i.push(p)}),i}};function _e(t){t=A.completeHtmlTags(t);const e=A.findAncestor(t.startContainer,{table:!0}),n=A.findAncestor(t.endContainer,{table:!0}),l=A.findAncestor(t.endContainer,{mathTex:!0,type:"end"}),a=document.createElement("div");a.classList.add("my-wrapper"),e&&a.appendChild(e);const r=document.createDocumentFragment(),d=t.cloneContents();if(l||n){A.removeNodesByType(d,"TABLE");const p=Array.from(d.querySelectorAll(".math-tex"));p.length&&p[p.length-1].remove()}r.appendChild(d),a.appendChild(r),n&&n!==e&&a.appendChild(n),l&&a.appendChild(l),t.deleteContents(),t.insertNode(a);const i=A.getNextSiblingAfterRange(t);return A.removeAdjacentBrNodes(i),a}async function he(t){if(!S.value)return;const e=window.getSelection();if(e.rangeCount===0)return;const n=e.getRangeAt(0);if(!e.toString().trim())return;const a=n.cloneContents();if(!(Array.from(a.childNodes).some(A.isTargetNode)||A.isInForbiddenWrapper(n.startContainer)))try{const d=document.querySelector("#wordHtml"),i=(d==null?void 0:d.innerHTML)||"";O=JSON.parse(JSON.stringify(i)),I=JSON.parse(JSON.stringify(x.value)),L.value=!0,_e(n),e.removeAllRanges(),await P(),W()}catch(d){console.error("包裹选区时出错:",d)}}const xe=t=>{t.preventDefault()},we=async(t,e)=>{const n=t.html.match(k);if(n&&n[1]){const l=n[1];x.value[e].html=l+"<br/>";let a=x.value.map(r=>r.html).join("");c.value=a,await P(),W()}};if(b.divideType=="uploadanswers"){C=b.domHtmls||"",c.value=JSON.parse(JSON.stringify(C));const t=c.value;x.value=$(t)}else me();return(t,e)=>{var p;const n=Be,l=j("Warning"),a=le,r=ie,d=ke,i=Ae("latexs");return f(c)?(g(),N("div",Pe,[h.divideType!="uploadanswers"?(g(),N("div",ze,[s("header",je,[s("div",Ke,[s("div",Qe,[s("a",Xe,[s("img",{src:f(Re)(),alt:"Logo",width:"218",height:"38"},null,8,Ye)])]),(p=f(F))!=null&&p.step?(g(),H(n,{key:0,onSetStep:Y,types:"divide",pageData:f(F)},null,8,["pageData"])):R("",!0),s("div",Ge,[s("div",Ze,[s("div",{class:"rule-box huiA2",onClick:e[0]||(e[0]=u=>B.value=!0)},[y(a,null,{default:v(()=>[y(l)]),_:1}),e[10]||(e[10]=s("span",null,"操作说明",-1))]),h.divideType=="intelligenttopiccutting"?(g(),H(r,{key:0,onClick:e[1]||(e[1]=u=>Y())},{default:v(()=>e[11]||(e[11]=[T("上一步")])),_:1,__:[11]})):R("",!0),y(r,{onClick:e[2]||(e[2]=u=>pe()),type:"primary"},{default:v(()=>e[12]||(e[12]=[T("下一步")])),_:1,__:[12]}),h.divideType!="intelligenttopiccutting"?(g(),H(r,{key:1,onClick:e[3]||(e[3]=u=>U())},{default:v(()=>e[13]||(e[13]=[T("取消")])),_:1,__:[13]})):R("",!0)])])])])])):R("",!0),s("div",et,[s("div",tt,[s("div",nt,[s("div",st,[e[15]||(e[15]=s("div",{class:"bt"},"输入区",-1)),s("div",null,[e[14]||(e[14]=T(" 手动切题 ")),y(d,{modelValue:f(S),"onUpdate:modelValue":e[4]||(e[4]=u=>ee(S)?S.value=u:null)},null,8,["modelValue"])])]),s("div",null,[y(r,{onClick:e[5]||(e[5]=u=>ue()),type:"danger",size:"small"},{default:v(()=>e[16]||(e[16]=[T("重置")])),_:1,__:[16]})])]),(g(),N("div",{class:"com",onDblclick:xe,onMouseup:he,id:"contentId",key:f(_)},[te(s("div",{innerHTML:f(c),id:"wordHtml",contenteditable:"true"},null,8,ot),[[i]])],32))]),s("div",{class:J(["btn-list",{on:h.divideType=="uploadanswers"}])},[y(r,{style:{"max-width":"100%"},onClick:e[6]||(e[6]=u=>W()),type:"warning"},{default:v(()=>e[17]||(e[17]=[T("转换")])),_:1,__:[17]}),f(L)?(g(),H(r,{key:0,style:{"max-width":"100%","margin-top":"10px","margin-left":"0"},onClick:e[7]||(e[7]=u=>ce()),type:"danger"},{default:v(()=>e[18]||(e[18]=[T("撤回")])),_:1,__:[18]})):R("",!0)],2),s("div",lt,[e[20]||(e[20]=s("div",{class:"top-box"},[s("div",{class:"bt"},"检查区")],-1)),s("div",it,[s("div",at,[(g(!0),N(se,null,oe(f(x),(u,D)=>(g(),N("div",{class:"li",key:D},[te(s("div",{innerHTML:u.html},null,8,rt),[[i]]),u.type==2?(g(),N("div",dt,[y(r,{type:"primary",onClick:gt=>we(u,D),size:"small"},{default:v(()=>e[19]||(e[19]=[T("取消分割")])),_:2,__:[19]},1032,["onClick"])])):R("",!0)]))),128))])])])]),y(qe,{modelValue:f(B),"onUpdate:modelValue":e[8]||(e[8]=u=>ee(B)?B.value=u:null)},null,8,["modelValue"])])):(g(),N("view",pt,[s("div",ct,[e[22]||(e[22]=s("img",{style:{width:"200px"},src:"https://oss.hhypt.com/resources/mini-parent-helper/err.png"},null,-1)),s("span",ut,z(f(G)||"暂无数据"),1),y(r,{type:"primary",style:{width:"120px"},onClick:e[9]||(e[9]=u=>U())},{default:v(()=>e[21]||(e[21]=[T("返回")])),_:1,__:[21]})])]))}}}),ft=Q(mt,[["__scopeId","data-v-d8e1e161"]]),Tt=Object.freeze(Object.defineProperty({__proto__:null,default:ft},Symbol.toStringTag,{value:"Module"}));export{Be as _,ft as d,Tt as i};

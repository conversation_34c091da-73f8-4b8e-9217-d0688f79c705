import{s as be}from"./request.CsKOOJzG.js";import{_ as Ue,u as Ge,b as _e,a as Qe}from"./index.pTwlcQaF.js";import{c as $e}from"./index.6W4rOsHv.js";import{q as P}from"./question.IyuOoK5G.js";import{d as he,aK as I,r as ne,c as h,o as d,F as ie,e as j,f as W,u as s,aM as Ie,a as _,U as Xe,w as Ce,T as Ke,h as Be,q as ue,au as ee,n as Le,t as de,W as Oe,K as Me,p as Ze,aC as Pe,b as se,g as Ye,aN as et}from"./index.ZZ6UQeaF.js";import{_ as xe}from"./index.vue_vue_type_style_index_0_lang.Cq6RDaj7.js";/* empty css                 *//* empty css                       */import{_ as me}from"./_plugin-vue_export-helper.DlAUqK2U.js";import{_ as He,a as tt,b as it,c as We,d as lt,H as ot,e as st}from"./index.CCoS0DfY.js";/* empty css                   */const ke="api/";function di(i){return be({url:ke+"v1/paper-card/get-detail",method:"post",data:i})}function ai(i){return be({url:ke+"v1/paper-card/preview",method:"post",data:i})}function pi(i){return be({url:ke+"v1/paper-card/staging",method:"post",data:i})}function ci(i){return be({url:ke+"v1/paper-card/save",method:"post",data:i})}function yi(i){return be({url:ke+"v1/paper-card/download",method:"post",data:i})}function hi(i){return be({url:ke+"v1/paper-card/answer",method:"post",data:i})}const nt={key:0,class:"clearfix"},ut={class:"edit-box pmm2_flex_acenter"},rt={key:0,class:"answerVal pmm2_flex_between",style:{width:"50px"}},dt={key:2,style:{"font-size":"16px"}},at=he({__name:"hhypt-radio",props:["paperItem","logicTypeId","uuid","index"],emits:["onEditorBlur"],setup(i,{emit:le}){let g=I().pageStyles;const F=i,q=Ie("CkeditorStyles"),o=ne(null),$=J=>{if(J=="ONE")return 100;if(J=="TWO")return 50;if(J=="FOUR")return 25},E=J=>{V({h:!0,key:"optionLayoutId",optionLayoutIdLable:J})};F.logicTypeId==13&&(F.paperItem.optionLayoutId||(F.paperItem.optionLayoutId="FOUR"));const G=le,V=J=>{G("onEditorBlur",J)};return(J,T)=>{const u=xe,R=Ke,U=Xe;return d(),h(ie,null,[i.logicTypeId!=13&&!i.paperItem.toOptiondSplit?(d(),h("div",nt,[W(u,{ckeditorType:"card",styles:s(q),onOnEditorBlur:T[0]||(T[0]=b=>V({key:"bodys",...b})),ckeditorCalss:"ckeditorTitle",modelValue:i.paperItem.bodys,"onUpdate:modelValue":T[1]||(T[1]=b=>i.paperItem.bodys=b),uuid:i.uuid},null,8,["styles","modelValue","uuid"])])):j("",!0),!i.paperItem.toBodySplit&&i.paperItem.options.length>0?(d(),h("div",{key:1,class:"options-list",ref_key:"listDom",ref:o},[_("div",ut,[T[6]||(T[6]=_("span",null,"每行选项个数设置",-1)),W(U,{modelValue:i.paperItem.optionLayoutId,"onUpdate:modelValue":T[2]||(T[2]=b=>i.paperItem.optionLayoutId=b),onChange:E},{default:Ce(()=>[W(R,{value:"ONE"},{default:Ce(()=>T[3]||(T[3]=[Be("1")])),_:1,__:[3]}),W(R,{value:"TWO"},{default:Ce(()=>T[4]||(T[4]=[Be("2")])),_:1,__:[4]}),W(R,{value:"FOUR"},{default:Ce(()=>T[5]||(T[5]=[Be("4")])),_:1,__:[5]})]),_:1},8,["modelValue"])]),(d(!0),h(ie,null,ue(i.paperItem.options,(b,B)=>{var p,Q,A,ge;return d(),h("div",{key:B,class:"options-item",style:ee(`width:${$(i.paperItem.optionLayoutId)}%`)},[B==0&&i.logicTypeId==13&&!i.paperItem.optionsOne?(d(),h("span",{key:0,class:Le(["topicSortNum",{on:s(I)().setting.objectiveAnswerTypeId!="FILL"}]),style:{}},[Be(de(i.paperItem.topicSortNum)+". ",1),s(I)().setting.objectiveAnswerTypeId!="FILL"?(d(),h("strong",rt,T[7]||(T[7]=[_("span",null,"(",-1),_("span",null,")",-1)]))):j("",!0)],2)):j("",!0),s(I)().setting.objectiveAnswerTypeId=="FILL"?(d(),h("span",{key:1,style:ee(`min-width: ${(p=s(g))==null?void 0:p.objectiveFillBoxSize[0]}px;width:${(Q=s(g))==null?void 0:Q.objectiveFillBoxSize[0]}px;line-height:${(A=s(g))==null?void 0:A.objectiveFillBoxSize[1]}px;height:${(ge=s(g))==null?void 0:ge.objectiveFillBoxSize[1]}px;`),class:"hhypt-label-item"},de(b.label),5)):(d(),h("span",dt,de(b.label)+".",1)),W(u,{ckeditorType:"card",style:{flex:"1"},styles:s(q),uuid:b.uuid,onOnEditorBlur:z=>V({key:"options",i:B,...z,content:b.content,id:b.uuid}),modelValue:b.content,"onUpdate:modelValue":z=>b.content=z},null,8,["styles","uuid","onOnEditorBlur","modelValue","onUpdate:modelValue"])],4)}),128))],512)):j("",!0)],64)}}}),ze=me(at,[["__scopeId","data-v-c0fb3d8e"]]),pt={key:0,class:"splitChildren-box"},ct={key:1,style:{"flex-wrap":"wrap"},class:"pmm2_flex_acenter splitChildren-box"},yt={key:2},ht={key:0,class:"scoringbox-box scoringbox-boxs"},mt={key:3,class:"mouse-box",id:"resizableId"},gt={key:4},ft=he({__name:"hhypt-blanks",props:["paperItem","uuid","item","cuuid"],emits:["onEditorBlur"],setup(i,{emit:le}){const g=i,F=Ie("CkeditorStyles"),q=g.cuuid||g.uuid||"";let o,$;q&&(o=I().scoreSetting.findIndex(O=>O.uuid==g.paperItem.H1uuid),$=I().scoreSetting[o].children.findIndex(O=>O.uuid==q));let E=ne(-1);const G=Oe(()=>{let O=0;if(!q)return 0;if(E.value=I().questionsScoreList.findIndex(L=>L.uuid==q),E.value!=-1){let L=I().questionsScoreList[E.value];L.children?O=L.children.reduce((M,te)=>M+te.score,0):O=L.full_score}return O}),V=O=>{g.paperItem.step_score=O,I().scoreSetting[o].children[$].step_score=O,z({key:"stepScore",step_score:O,h:!1})},J=O=>{var M;let L=I().questionsScoreList.find(te=>te.uuid==q)||{};return((M=L==null?void 0:L.children[O])==null?void 0:M.score)||0},T=ne(null),u=()=>{I().setting.scoringResultTypeId=="right_wrong"||G.value>0?T.value.opens():Ze({message:"请先设置题目分数！",type:"warning"})},R=Oe(()=>{var L,M;let O=g.paperItem.blankH;return g.paperItem.toBodySplit||(O+=g.paperItem.bodysH),((M=(L=g.paperItem)==null?void 0:L.splitChildren)==null?void 0:M.length)>0&&(O+=g.paperItem.splitH),O+"px"}),U=O=>{for(let L in O)g.paperItem[L]=O[L];I().questionsScoreList[E.value].children=O.splitChildren,I().scoreSetting[o].children[$].children=O.splitChildren,z({key:"split",splitFalg:!0,obj:O,h:!0})},b=()=>{g.paperItem.splitH=0,delete g.paperItem.splitNum,delete g.paperItem.splitChildren,delete g.paperItem.displayline,delete g.paperItem.splitType,delete I().scoreSetting[o].children[$].children,delete I().questionsScoreList[E.value].children,z({key:"split",splitFalg:!1,h:!0})},B=ne(null),p=ne(null),Q=O=>{var te,pe;let L=0;(te=B.value)!=null&&te.offsetHeight&&(L=B.value.offsetHeight),(pe=p==null?void 0:p.value)!=null&&pe.offsetHeight&&(L+=p.value.offsetHeight);let M=O-L;g.paperItem.blankH!=M&&(g.paperItem.blankH=M,z({key:"blankH",blankH:M,h:!0}))},A=ne(null),ge=le,z=O=>{O.key&&O.key!="bodys"&&A.value&&A.value.isEditingFun(),ge("onEditorBlur",O)};return(O,L)=>{var pe,ve;const M=xe,te=Pe("resizable");return Me((d(),h("div",{class:"resizable-box",style:ee({minHeight:s(R)})},[i.paperItem.bodys?(d(),h("div",{key:0,ref_key:"ckeditorRef",ref:B},[W(M,{ckeditorType:"card",ref_key:"CkeditorDomRef",ref:A,styles:s(F),modelValue:i.paperItem.bodys,"onUpdate:modelValue":L[0]||(L[0]=Y=>i.paperItem.bodys=Y),onOnEditorBlur:L[1]||(L[1]=Y=>z({key:"bodys",...Y})),uuid:i.cuuid||i.uuid},null,8,["styles","modelValue","uuid"])],512)):j("",!0),((ve=(pe=i.paperItem)==null?void 0:pe.splitChildren)==null?void 0:ve.length)>0?(d(),h("div",{key:1,ref_key:"splitChildrenRef",ref:p},[i.paperItem.splitType==6?(d(),h("div",pt,[(d(!0),h(ie,null,ue(i.paperItem.splitChildren,(Y,ye)=>(d(),h("div",{key:ye,class:"splitChildrenId"},[W(M,{ckeditorType:"card",styles:s(F),modelValue:Y.bodys,"onUpdate:modelValue":ae=>Y.bodys=ae,onOnEditorBlur:ae=>z({key:"splitbodys",...ae,id:Y.uuid,sbodys:Y.bodys})},null,8,["styles","modelValue","onUpdate:modelValue","onOnEditorBlur"]),W(He,{uuid:s(q),step_score:i.paperItem.step_score,class:"scoringbox-boxs",onSetStepScore:V,full_score:J(ye)},null,8,["uuid","step_score","full_score"])]))),128))])):(d(),h("div",ct,[(d(!0),h(ie,null,ue(i.paperItem.splitChildren,(Y,ye)=>(d(),h("div",{key:ye,style:ee([{position:"relative"},`min-width: ${100/i.paperItem.displayline}%;`]),class:"splitChildrenId"},[W(M,{ckeditorType:"card",styles:s(F),modelValue:Y.bodys,"onUpdate:modelValue":ae=>Y.bodys=ae,onOnEditorBlur:ae=>z({key:"splitbodys",...ae,id:Y.uuid,sbodys:Y.bodys})},null,8,["styles","modelValue","onUpdate:modelValue","onOnEditorBlur"]),W(He,{splitType:i.paperItem.splitType,uuid:s(q),step_score:i.paperItem.step_score,onSetStepScore:V,full_score:J(ye),class:"scoringbox-boxs"},null,8,["splitType","uuid","step_score","full_score"])],4))),128))]))],512)):j("",!0),i.paperItem.Noresizable?j("",!0):(d(),h("div",yt,[(!i.paperItem.splitChildren||i.paperItem.splitChildren.length==0)&&!i.paperItem.splitFalg?(d(),h("div",ht,[W(He,{uuid:s(q),step_score:i.paperItem.step_score,onSetStepScore:V,full_score:s(G)},null,8,["uuid","step_score","full_score"])])):j("",!0)])),_("div",{class:Le(["balnks-edit",{on:i.paperItem.splitNum}])},[_("div",{class:"btn1 huiA pmm2_flex_center",onClick:L[2]||(L[2]=Y=>u())},L[3]||(L[3]=[_("img",{src:tt,alt:""},null,-1),_("span",{style:{"padding-left":"5px"}},"拆分题目",-1)])),i.paperItem.splitNum?(d(),h("div",{key:0,class:"btn huiA pmm2_flex_center",onClick:b},L[4]||(L[4]=[_("img",{src:it,alt:""},null,-1),_("span",{style:{"padding-left":"5px"}},"取消拆分",-1)]))):j("",!0)],2),i.paperItem.toBodySplits?j("",!0):(d(),h("div",mt,L[5]||(L[5]=[_("img",{src:We,style:{width:"20px",height:"25px"},alt:"",srcset:""},null,-1)]))),s(q)?(d(),h("div",gt,[W(lt,{ref_key:"splitQuestionsRef",ref:T,index:s(E),full_score:s(G),onSetSplit:U,paperItem:i.paperItem},null,8,["index","full_score","paperItem"])])):j("",!0)],4)),[[te,{fun:Q}]])}}}),je=me(ft,[["__scopeId","data-v-80ca2c70"]]),bt={class:"hhypt-multiple"},It={class:"multiple-top"},xt={key:0,class:"multiple-com"},kt=["id"],vt={key:1,class:"multiple-com pmm2_flex_acenter",style:{"flex-wrap":"wrap","font-size":"16px"}},St=["id"],Nt=he({__name:"hhypt-multiple",props:["paperItem","uuid"],emits:["onEditorBlur"],setup(i,{emit:le}){let g=I().pageStyles;const F=i,q=Ie("CkeditorStyles");let o=["A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"],$=ne([]);for(let V=0;V<F.paperItem.optionsCount;V++)$.value.push(o[V]);const E=le,G=V=>{E("onEditorBlur",V)};return(V,J)=>{const T=xe;return d(),h("div",bt,[_("ul",It,[(d(!0),h(ie,null,ue(i.paperItem.options,(u,R)=>(d(),h("li",{key:u.uuid},[_("div",{class:"label-box",style:ee(s(q))},de(u.label)+". ",5),W(T,{ckeditorType:"card",style:{flex:"1"},styles:s(q),onOnEditorBlur:U=>G({key:"options",i:R,...U}),modelValue:u.content,"onUpdate:modelValue":U=>u.content=U,uuid:i.uuid},null,8,["styles","onOnEditorBlur","modelValue","onUpdate:modelValue","uuid"])]))),128))]),s(I)().setting.objectiveAnswerTypeId=="FILL"?(d(),h("div",xt,[(d(!0),h(ie,null,ue(i.paperItem.children,(u,R)=>(d(),h("div",{key:R,style:{padding:"5px 0"},id:"childrenId"+i.uuid},[_("span",null,de(u.detailData.topicSortNum)+".",1),(d(!0),h(ie,null,ue(s($),U=>{var b,B,p,Q;return d(),h("span",{style:ee([{"margin-right":"10px"},`min-width: ${(b=s(g))==null?void 0:b.objectiveFillBoxSize[0]}px;width:${(B=s(g))==null?void 0:B.objectiveFillBoxSize[0]}px;line-height:${(p=s(g))==null?void 0:p.objectiveFillBoxSize[1]}px;height:${(Q=s(g))==null?void 0:Q.objectiveFillBoxSize[1]}px;`]),key:U,class:"hhypt-label-item"},de(U),5)}),128))],8,kt))),128))])):(d(),h("div",vt,[(d(!0),h(ie,null,ue(i.paperItem.children,(u,R)=>(d(),h("div",{key:R,class:"pmm2_flex_acenter",style:{padding:"15px 0px",width:"80px"},id:"childrenId"+i.uuid},[_("span",null,de(u.detailData.topicSortNum)+".",1),J[0]||(J[0]=_("strong",{class:"answerVal pmm2_flex_between",style:{width:"50px",float:"none","margin-top":"-2px"}},[_("span",null,"("),_("span",null,")")],-1))],8,St))),128))]))])}}}),qt=me(Nt,[["__scopeId","data-v-662ce89a"]]),wt={class:"resizable-box"},Ct={key:0,class:"pmm2_flex_acenter",style:{padding:"10px 0"}},Bt={key:1,class:"pmm2_flex_acenter",style:{padding:"10px 0","font-size":"16px"}},Ht=he({__name:"hhypt-trueFalse",props:["paperItem","uuid"],emits:["onEditorBlur"],setup(i,{emit:le}){let g=I().pageStyles;const F=Ie("CkeditorStyles"),q=le,o=$=>{q("onEditorBlur",$)};return($,E)=>{var V,J,T,u,R,U,b,B;const G=xe;return d(),h("div",wt,[W(G,{ckeditorType:"card",uuid:i.uuid,styles:s(F),modelValue:i.paperItem.bodys,"onUpdate:modelValue":E[0]||(E[0]=p=>i.paperItem.bodys=p),onOnEditorBlur:E[1]||(E[1]=p=>o({key:"bodys",...p}))},null,8,["uuid","styles","modelValue"]),!i.paperItem.toBodySplit&&s(I)().setting.objectiveAnswerTypeId=="FILL"?(d(),h("div",Ct,[_("div",{style:ee([`min-width: ${(V=s(g))==null?void 0:V.objectiveFillBoxSize[0]}px;width:${(J=s(g))==null?void 0:J.objectiveFillBoxSize[0]}px;line-height:${(T=s(g))==null?void 0:T.objectiveFillBoxSize[1]}px;height:${(u=s(g))==null?void 0:u.objectiveFillBoxSize[1]}px;`,{"margin-right":"30px"}]),class:"hhypt-label-item"},"T",4),_("div",{style:ee(`min-width: ${(R=s(g))==null?void 0:R.objectiveFillBoxSize[0]}px;width:${(U=s(g))==null?void 0:U.objectiveFillBoxSize[0]}px;line-height:${(b=s(g))==null?void 0:b.objectiveFillBoxSize[1]}px;height:${(B=s(g))==null?void 0:B.objectiveFillBoxSize[1]}px;`),class:"hhypt-label-item"},"F",4)])):i.paperItem.toBodySplit?j("",!0):(d(),h("div",Bt,E[2]||(E[2]=[_("div",{style:{"margin-right":"30px"}},"T",-1),_("div",null,"F",-1)])))])}}}),Ve=me(Ht,[["__scopeId","data-v-42ef6af3"]]),Lt={key:0},Ot={class:"scoringbox-box"},Et={key:1,class:"mouse-box",id:"resizableId"},Tt=he({__name:"hhypt-alls",props:["paperItem","uuid","cuuid"],emits:["onEditorBlur"],setup(i,{emit:le}){const g=i,F=Ie("CkeditorStyles");let q=-1,o=g.cuuid||g.uuid||"",$,E;o&&($=I().scoreSetting.findIndex(b=>b.uuid==g.paperItem.H1uuid),E=I().scoreSetting[$].children.findIndex(b=>b.uuid==o));const G=Oe(()=>{let b=0;return o?(q==-1&&(q=I().questionsScoreList.findIndex(B=>B.uuid==o)),b=I().questionsScoreList[q].full_score,b):0}),V=b=>{g.paperItem.step_score=b,I().scoreSetting[$].children[E].step_score=b,U({key:"stepScore",step_score:b,h:!1})},J=ne(null),T=b=>{var Q;let B=0;(Q=J.value)!=null&&Q.offsetHeight&&(B=J.value.offsetHeight);let p=b-B;g.paperItem.blankH!=p&&(g.paperItem.blankH=p,U({key:"blankH",blankH:p,h:!0}))},u=ne(null),R=le,U=b=>{b.key&&b.key!="bodys"&&u.value&&u.value.isEditingFun(),R("onEditorBlur",b)};return(b,B)=>{const p=xe,Q=Pe("resizable");return Me((d(),h("div",{class:"resizable-box",style:ee({minHeight:(i.paperItem.toBodySplit?0:i.paperItem.bodysH)+i.paperItem.blankH+"px"})},[_("div",{ref_key:"ckeditorRef",ref:J},[i.paperItem.bodys?(d(),se(p,{key:0,ckeditorType:"card",styles:s(F),modelValue:i.paperItem.bodys,"onUpdate:modelValue":B[0]||(B[0]=A=>i.paperItem.bodys=A),ref_key:"CkeditorDomRef",ref:u,uuid:i.cuuid||i.uuid,onOnEditorBlur:B[1]||(B[1]=A=>U({key:"bodys",...A}))},null,8,["styles","modelValue","uuid"])):j("",!0)],512),i.paperItem.Noresizable?j("",!0):(d(),h("div",Lt,[_("div",Ot,[W(He,{uuid:i.cuuid||i.uuid,step_score:i.paperItem.step_score,onSetStepScore:V,full_score:s(G)},null,8,["uuid","step_score","full_score"])])])),i.paperItem.toBodySplits?j("",!0):(d(),h("div",Et,B[2]||(B[2]=[_("img",{src:We,style:{width:"20px",height:"25px"},alt:"",srcset:""},null,-1)])))],4)),[[Q,{fun:T}]])}}}),Ae=me(Tt,[["__scopeId","data-v-0eec1d19"]]),_t={class:"topic-boxs"},At={class:"page-content"},Ft=["id"],Dt={key:0,class:"question-name",style:{"font-size":"17px"}},$t={key:1},zt={key:2},jt={key:1},Vt={key:2},Jt=["id"],Ut={key:3},Mt={key:1},Pt=he({__name:"topic-item",props:{list:Object,index:Number,pageLayoutName:String,examNumberLength:Number,layout:{type:Object}},emits:["onEditorBlur"],setup(i,{emit:le}){let g=I().pageStyles;const F=Ie("CkeditorStyles"),q=le,o=(E,G,V)=>{q("onEditorBlur",E,G,V)},$=(E,G)=>{E&&q("onEditorBlur",{key:"H1",title:E,uuid:G})};return(E,G)=>{const V=Ue,J=Ye,T=xe;return d(),h("div",_t,[i.index%i.layout.headNum==0&&i.pageLayoutName!="A4_2"?(d(),se(V,{key:0,pageLayoutName:i.pageLayoutName,layout:i.layout,index:i.index,examNumberLength:i.examNumberLength},null,8,["pageLayoutName","layout","index","examNumberLength"])):j("",!0),_("div",At,[(d(!0),h(ie,null,ue(i.list,(u,R)=>{var U,b,B;return d(),h("div",{key:(u.uuid||u.content)+"-"+R,class:"page-box",id:"paperId"+i.index},[u.itemTypeId==s(P).H1headline?(d(),h("div",Dt,[W(J,{modelValue:u.title,"onUpdate:modelValue":p=>u.title=p,type:"textarea",onChange:p=>$(p,u.uuid),class:"el-textareas break-word",placeholder:"请输入标题",autosize:""},null,8,["modelValue","onUpdate:modelValue","onChange"])])):u.question.logicTypeId==s(P).EssayCorrectError||u.question.logicTypeId==s(P).GrammarBlankFilling?(d(),h("div",$t,[W(Ae,{onOnEditorBlur:p=>o({...p},u),uuid:u.question.uuid,paperItem:u.question.detailData},null,8,["onOnEditorBlur","uuid","paperItem"])])):u.question.isComplex?(d(),h("div",zt,[u.question.detailData.body?(d(),se(T,{key:0,ckeditorType:"card",onOnEditorBlur:p=>o({key:"body",...p},u),styles:s(F),modelValue:u.question.detailData.body,"onUpdate:modelValue":p=>u.question.detailData.body=p,uuid:u.question.uuid},null,8,["onOnEditorBlur","styles","modelValue","onUpdate:modelValue","uuid"])):j("",!0),u.question.logicTypeId==10?(d(),h("div",jt,[W(qt,{onOnEditorBlur:p=>o({...p},u),paperItem:u.question.detailData,uuid:u.question.uuid},null,8,["onOnEditorBlur","paperItem","uuid"])])):((B=(b=(U=u.question)==null?void 0:U.detailData)==null?void 0:b.children)==null?void 0:B.length)>0?(d(),h("div",Vt,[(d(!0),h(ie,null,ue(u.question.detailData.children,(p,Q)=>(d(),h("div",{key:p.uuid+Q,id:"childrenId"+u.question.uuid},[p.logicTypeId==s(P).SingleChoice||p.logicTypeId==s(P).MultipleChoice?(d(),se(ze,{key:0,onOnEditorBlur:A=>o({...A,cindex:Q},u,p.uuid),paperItem:p.detailData,logicTypeId:u.question.logicTypeId,uuid:u.question.uuid},null,8,["onOnEditorBlur","paperItem","logicTypeId","uuid"])):p.logicTypeId==s(P).TrueFalse?(d(),se(Ve,{key:1,onOnEditorBlur:A=>o({...A,cindex:Q},u,p.uuid),uuid:u.question.uuid,paperItem:p.detailData},null,8,["onOnEditorBlur","uuid","paperItem"])):p.logicTypeId==s(P).FreeAnswer||p.logicTypeId==s(P).BlankFilling?(d(),se(je,{key:2,onOnEditorBlur:A=>o({...A,cindex:Q},u,p.uuid),cuuid:p.uuid,uuid:u.question.uuid,paperItem:p.detailData},null,8,["onOnEditorBlur","cuuid","uuid","paperItem"])):(d(),se(Ae,{key:3,onOnEditorBlur:A=>o({...A,cindex:Q},u,p.uuid),cuuid:p.uuid,uuid:u.question.uuid,paperItem:p.detailData},null,8,["onOnEditorBlur","cuuid","uuid","paperItem"]))],8,Jt))),128))])):j("",!0)])):(d(),h("div",Ut,[u.question.logicTypeId==s(P).MultipleChoice||u.question.logicTypeId==s(P).SingleChoice?(d(),se(ze,{key:0,onOnEditorBlur:p=>o(p,u),paperItem:u.question.detailData,logicTypeId:u.question.logicTypeId,uuid:u.content},null,8,["onOnEditorBlur","paperItem","logicTypeId","uuid"])):u.question.logicTypeId==s(P).TrueFalse?(d(),h("div",Mt,[W(Ve,{onOnEditorBlur:p=>o(p,u),uuid:u.content,paperItem:u.question.detailData},null,8,["onOnEditorBlur","uuid","paperItem"])])):u.question.logicTypeId==s(P).FreeAnswer||u.question.logicTypeId==s(P).BlankFilling?(d(),se(je,{key:2,onOnEditorBlur:p=>o(p,u),uuid:u.content,paperItem:u.question.detailData},null,8,["onOnEditorBlur","uuid","paperItem"])):u.question.logicTypeId==s(P).EnglishWriting?(d(),se(ot,{key:3,onOnEditorBlur:p=>o(p,u),uuid:u.content,paperItem:u.question.detailData,HhyptType:"topics"},null,8,["onOnEditorBlur","uuid","paperItem"])):u.question.logicTypeId==s(P).ChineseWriting?(d(),se(st,{onOnEditorBlur:p=>o(p,u),uuid:u.content,paperItem:u.question.detailData,key:u.question.detailData.wordNum,HhyptType:"topics"},null,8,["onOnEditorBlur","uuid","paperItem"])):(d(),se(Ae,{key:5,onOnEditorBlur:p=>o(p,u),uuid:u.content,paperItem:u.question.detailData},null,8,["onOnEditorBlur","uuid","paperItem"]))]))],8,Ft)}),128))]),i.pageLayoutName!="A4_2"?(d(),h("div",{key:1,class:"page-number",style:ee(`height: ${s(g).pageNumberHeight}px;line-height: ${s(g).pageNumberHeight}px;`)},[_("span",null,"第"+de(i.index+1)+"页 共"+de(i.layout.paperListArrLen)+"页",1)],4)):j("",!0)])}}}),Je=me(Pt,[["__scopeId","data-v-b0aff41a"]]),Wt=["id"],Rt={key:0,style:{width:"100%"}},Gt={key:4,class:"newline-box"},Qt={key:5,class:"newline-preview-box",style:{height:"0"}},Xt=he({__name:"index",props:{questionsArr:Object,paperListList:Object,isPreview:Boolean,layout:{type:Object}},setup(i,{expose:le}){const g=i;let F=I().pageStyles,q=I().setting;const o=ne([]),$=ne([]),E=Oe(()=>{var t;return((t=$.value)==null?void 0:t.length)||0});let G=-1;const V=()=>(G++,G),{CkeditorStyles:J,shouldApplyClass:T,topicBoxItemStyle:u,topicComStyle:R,pageWidth:U,splitContentBodys:b,domFun:B,setHeight:p,headHeight:Q,pageHeight:A,questionNumberingTypeIdChanges:ge,getMarke:z,getZBFun:O,getEnspWidth:L,toFixeds:M,getHtmlTopicSortNum:te}=Ge(g);et("CkeditorStyles",J);function pe(t,X,x){var Z,K;let H=[],a=[],f=[],c=[P.SingleChoice,P.MultipleChoice,P.MultipleX2Multiple,P.TrueFalse,P.ReadingCloze],n=((Z=x.question)==null?void 0:Z.logicTypeId)||x.logicTypeId,k=((K=x.question)==null?void 0:K.uuid)||x.uuid;if(c.includes(Number(n)))if(I().setting.objectiveAnswerTypeId=="FILL"){let D=z(t,X,".hhypt-label-item");H.push({uuid:k,bounds:D})}else{let D=z(t,X,".answerVal","obj");D.x&&H.push({uuid:k,bounds:D})}else{let D=t.getBoundingClientRect();if((D==null?void 0:D.height)>0&&f.push({uuid:k,x:M(D.x-X.x),y:M(D.y-X.y),h:M(D.height),w:M(D.width)}),I().setting.scoringResultTypeId=="right_wrong"||I().setting.subjectiveScoreTypeId=="LINE"){let w=z(t,X,".score-row .score-item"),N=z(t,X,".score-row","obj");w.length>0&&a.push({uuid:k,bounds:w,boundsAll:N})}else{let w=z(t,X,".scorehandwritten-val","obj");w.x&&a.push({uuid:k,boundsAll:w})}}return{scoreBoxInfo:a,answerAreaInfo:f,optionInfo:H}}const ve=ne(null),Y=()=>O(ve,ye),ye=(t,X,x,H={})=>{let a=H.optionInfo||[],f=H.scoreBoxInfo||[],c=H.answerAreaInfo||[];return X.querySelectorAll("#paperId"+x).forEach((k,Z)=>{var K,D;if(k){let w=De.value[x][Z]||{};const N=((K=w==null?void 0:w.question)==null?void 0:K.uuid)||w.uuid;if(w.itemTypeId!="H1"){let v=w.question.detailData||{};if([15,16,12,14].includes(Number(w.question.logicTypeId))){let y=k.getBoundingClientRect();if((y==null?void 0:y.height)>0&&c.push({uuid:N,x:M(y.x-t.x),y:M(y.y-t.y),h:M(y.height),w:M(y.width)}),I().setting.scoringResultTypeId=="right_wrong"||I().setting.subjectiveScoreTypeId=="LINE"){let e=z(k,t,".score-row .score-item"),l=z(k,t,".score-row","obj");f.push({uuid:N,bounds:e,boundsAll:l})}else{let e=z(k,t,".scorehandwritten-val","obj");e.x&&f.push({uuid:N,boundsAll:e})}}else if(w.question.isComplex)k.querySelectorAll("#childrenId"+N).forEach((e,l)=>{var m,C,oe,qe;let r=v.children[l]||{};if(((C=(m=r==null?void 0:r.detailData)==null?void 0:m.splitChildren)==null?void 0:C.length)>0){let ce=e.querySelectorAll(".splitChildrenId"),Te=(oe=r==null?void 0:r.detailData)==null?void 0:oe.splitChildren;ce.forEach((Se,we)=>{let re=Se.getBoundingClientRect();(re==null?void 0:re.height)>0&&c.push({uuid:Te[we].uuid,x:re.x-t.x,y:re.y-t.y,h:re.height,w:re.width})}),e.querySelectorAll(".scoringbox-boxs").forEach((Se,we)=>{if(I().setting.scoringResultTypeId=="right_wrong"||I().setting.subjectiveScoreTypeId=="LINE"){let re=z(Se,t,".score-row .score-item"),Re=z(Se,t,".score-row","obj");f.push({uuid:Te[we].uuid,bounds:re,boundsAll:Re})}else{let re=z(Se,t,".scorehandwritten-val","obj");re.x&&f.push({uuid:Te[we].uuid,boundsAll:re})}})}else if(!((qe=r==null?void 0:r.detailData)!=null&&qe.splitFalg)){v.logicTypeId==P.MultipleX2Multiple&&(r.logicTypeId=P.MultipleX2Multiple);let ce=pe(e,t,r);ce.scoreBoxInfo.length>0&&f.push(...ce.scoreBoxInfo),ce.optionInfo.length>0&&a.push(...ce.optionInfo),ce.answerAreaInfo.length>0&&c.push(...ce.answerAreaInfo)}});else if(((D=v==null?void 0:v.splitChildren)==null?void 0:D.length)>0){let y=k.querySelectorAll(".splitChildrenId"),e=v.splitChildren;y.forEach((r,m)=>{let C=r.getBoundingClientRect();(C==null?void 0:C.height)>0&&c.push({uuid:e[m].uuid,x:C.x-t.x,y:C.y-t.y,h:C.height,w:C.width})}),k.querySelectorAll(".scoringbox-boxs").forEach((r,m)=>{if(I().setting.scoringResultTypeId=="right_wrong"||I().setting.subjectiveScoreTypeId=="LINE"){let C=z(r,t,".score-row .score-item"),oe=z(r,t,".score-row","obj");f.push({uuid:e[m].uuid,bounds:C,boundsAll:oe})}else{let C=z(r,t,".scorehandwritten-val","obj");C.x&&f.push({uuid:e[m].uuid,boundsAll:C})}})}else if(!v.splitFalg){let y=pe(k,t,w);y.scoreBoxInfo.length>0&&f.push(...y.scoreBoxInfo),y.optionInfo.length>0&&a.push(...y.optionInfo),y.answerAreaInfo.length>0&&c.push(...y.answerAreaInfo)}}}}),{optionInfo:a,scoreBoxInfo:f,answerAreaInfo:c}},ae=t=>{o.value=ge(t,o.value),Ne(o.value)};L();function Ee(t){return Qe.test(t)}const Fe=(t,X,x)=>{var n,k,Z,K,D,w,N,v,S,y;if(t.key=="H1"){let e=o.value.findIndex(l=>l.uuid==t.uuid);e!=-1&&(o.value[e].title=t.title,Ne(o.value));return}let H=JSON.parse(JSON.stringify($.value)).flat();q.pageLayout.value=="A4_2"&&(H=H.flat());const a=H.filter(e=>e.content==X.content);let f=a.length,c=o.value.findIndex(e=>e.content==X.content);if(t.key=="stepScore")if(t.cindex!==void 0){let e=o.value[c].question.detailData.children.findIndex(l=>l.uuid==x);(n=o.value[c].question.detailData.children[e].detailData)!=null&&n.splitChildren&&o.value[c].question.detailData.children[e].detailData.splitChildren.forEach(l=>{l.step_score=X.step_score}),o.value[c].question.detailData.children[e].detailData.step_score=t.step_score}else(k=o.value[c].question.detailData)!=null&&k.splitChildren&&o.value[c].question.detailData.splitChildren.forEach(e=>{e.step_score=X.step_score}),o.value[c].question.detailData.step_score=t.step_score;else if(f==1){let{detailData:e}=X.question;if(c!=-1){if(t.key=="body")e.body||(e.body=te(e.topicSortNum)),o.value[c].question.detailData.body=e.body;else if(t.cindex!==void 0){let l=e.children[t.cindex].detailData;if(t.key=="bodys")l.bodys||(l.bodys=te(l.topicSortNum)),o.value[c].question.detailData.children[t.cindex].detailData.bodys=l.bodys;else if(t.key=="options")o.value[c].question.detailData.children[t.cindex].detailData.options[t.i].content=t.content;else if(t.key=="optionLayoutId")o.value[c].question.detailData.children[t.cindex].detailData.optionLayoutId=t.optionLayoutIdLable;else if(t.key=="blankH")o.value[c].question.detailData.children[t.cindex].detailData.blankH=t.blankH+(l.splitH||0);else if(t.key=="split")o.value[c].question.detailData.children[t.cindex].detailData=l;else if(t.key=="splitbodys"){let m=(l.splitChildren||[]).findIndex(C=>C.uuid==t.id);m!=-1&&(o.value[c].question.detailData.children[t.cindex].detailData.splitChildren[m].bodys=t.sbodys)}}else if(t.key=="bodys")e.bodys||(e.bodys=te(e.topicSortNum)),I().setting.objectiveAnswerTypeId!="FILL"&&((Z=e==null?void 0:e.options)==null?void 0:Z.length)>0&&!Ee(e.bodys)&&(e.bodys+=_e),o.value[c].question.detailData.bodys=e.bodys;else if(t.key=="options")o.value[c].question.detailData.options[t.i]=e.options[t.i];else if(t.key=="optionLayoutId")o.value[c].question.detailData.optionLayoutId=t.optionLayoutIdLable;else if(t.key=="blankH")o.value[c].question.detailData.blankH=t.blankH+(e.splitH||0);else if(t.key=="split")o.value[c].question.detailData=e;else if(t.key=="wordNum")o.value[c].question.detailData.wordNum=t.wordNum,o.value[c].question.detailData.compositionSize=t.compositionSize;else if(t.key=="splitbodys"){let r=(e.splitChildren||[]).findIndex(m=>m.uuid==t.id);r!=-1&&(o.value[c].question.detailData.splitChildren[r].bodys=t.sbodys)}}}else if(t.key=="body"){let e="";for(let l=0;l<a.length;l++)a[l].question.detailData.body&&(e+=a[l].question.detailData.body);e||(e=te(a[0].question.detailData.topicSortNum)),o.value[c].question.detailData.body=e}else if(![P.EssayCorrectError,P.GrammarBlankFilling].includes(o.value[c].question.logicTypeId)&&((D=(K=o.value[c].question.detailData)==null?void 0:K.children)==null?void 0:D.length)>0){let e=o.value[c].question.detailData.children.findIndex(l=>l.uuid==x);if(t.key=="bodys"){const l=a.map(m=>m.question.detailData.children).flat().filter(m=>m.uuid==x);let r="";for(let m=0;m<l.length;m++)l[m].detailData.bodys&&(r+=l[m].detailData.bodys);r||(r=te(l[0].detailData.topicSortNum)),I().setting.objectiveAnswerTypeId!="FILL"&&((v=(N=(w=l[0])==null?void 0:w.detailData)==null?void 0:N.options)==null?void 0:v.length)>0&&!Ee(r)&&(r+=_e),o.value[c].question.detailData.children[e].detailData.bodys=r}else if(t.key=="options"){let r=o.value[c].question.detailData.children[e].detailData.options.findIndex(m=>m.uuid==t.id);r!=-1&&(o.value[c].question.detailData.children[e].detailData.options[r].content=t.content)}else if(t.key=="optionLayoutId")o.value[c].question.detailData.children[e].detailData.optionLayoutId=t.optionLayoutIdLable;else if(t.key=="blankH"){let l=[],r=o.value[c].question.detailData.children[e].uuid;for(let oe=0;oe<f;oe++)l.push(...a[oe].question.detailData.children.filter(qe=>qe.uuid==r));let m=l.length,C=0;if(m>0)for(let oe=0;oe<m;oe++)C+=l[oe].detailData.blankH,C+=l[oe].detailData.splitH||0;o.value[c].question.detailData.children[e].detailData.blankH=C}else if(t.key=="split")if(t.splitFalg)for(let l in t.obj)l!="splitFalg"&&(o.value[c].question.detailData.children[e].detailData[l]=t.obj[l]);else o.value[c].question.detailData.children[e].detailData.splitH=0,delete o.value[c].question.detailData.children[e].detailData.splitNum,delete o.value[c].question.detailData.children[e].detailData.splitChildren,delete o.value[c].question.detailData.children[e].detailData.displayline,delete o.value[c].question.detailData.children[e].detailData.splitType;else if(t.key=="splitbodys"){let r=o.value[c].question.detailData.children[e].detailData.splitChildren.findIndex(m=>m.uuid==t.id);r!=-1&&(o.value[c].question.detailData.children[e].detailData.splitChildren[r].bodys=t.sbodys)}}else if(t.key=="blankH"){let e=0;for(let l=0;l<a.length;l++)e+=a[l].question.detailData.blankH,e+=a[l].question.detailData.splitH||0;o.value[c].question.detailData.blankH=e}else if(t.key=="bodys"){let e="";for(let l=0;l<a.length;l++)a[l].question.detailData.bodys&&(e+=a[l].question.detailData.bodys);e||(e=te(a[0].question.detailData.topicSortNum)),I().setting.objectiveAnswerTypeId!="FILL"&&((y=(S=a[0].question.detailData)==null?void 0:S.options)==null?void 0:y.length)>0&&!Ee(e)&&(e+=_e),o.value[c].question.detailData.bodys=e}else if(t.key=="options"){let e=a.map(l=>l.question.detailData.options).flat();o.value[c].question.detailData.options=e}else if(t.key=="optionLayoutId")o.value[c].question.detailData.optionLayoutId=t.optionLayoutIdLable;else if(t.key=="wordNum")o.value[c].question.detailData.wordNum=t.wordNum,o.value[c].question.detailData.compositionSize=t.compositionSize;else if(t.key=="split")if(t.splitFalg)for(let e in t.obj)e!="splitFalg"&&(o.value[c].question.detailData[e]=t.obj[e]);else o.value[c].question.detailData.splitH=0,delete o.value[c].question.detailData.splitNum,delete o.value[c].question.detailData.splitChildren,delete o.value[c].question.detailData.displayline,delete o.value[c].question.detailData.splitType;else if(t.key=="splitbodys"){let e=a.map(l=>l.question.detailData.splitChildren).flat();o.value[c].question.detailData.splitChildren=e}t.h&&Ne(o.value)};let fe=I().setting.subjectiveScoreTypeId=="LINE"?30:14;I().setting.scoringResultTypeId=="right_wrong"&&(fe=10);const De=ne([]);async function Ne(t){G=-1;let X=JSON.parse(JSON.stringify(t)),x=0,H=0,a=Q,f=[[]];async function c(n,k,Z){return new Promise(async(K,D)=>{let w=[];if(n.bodys&&Z!=13){let N=n.bodys,v=B(N,{});n.bodysH=v;let S=a;if(a+=v,n.Noresizable=!1,n.toBodySplits=!1,n.toBodySplit=!1,a>A){H=x;const{ExceedingTheHeight:y,bodyList:e}=await b(N,{H:a,h:v,Hcopy:S,pages:H,detailData:n,logicTypeId:k});let l=e.length;H=e[0].pages;for(let r=0;r<l;r++)if(l!=r+1||l==1){let m=JSON.parse(JSON.stringify(n));[3,6,12,14].includes(k)?(m.blankH=0,m.Noresizable=!0,m.splitChildren&&(m.splitChildren=[])):[16,15].includes(k)&&(m.wordNumCopy=JSON.parse(JSON.stringify(n.wordNum)),m.wordNum=0),m.toBodySplit=!0,m.toBodySplits=!0,m.bodys=e[r].txt,m.options=[],w.push(m),l==1&&(n.bodys=e[r].txt,a=y+e[r].headHeightNum,n.bodysH=y)}else n.bodys=e[r].txt,a=y+e[r].headHeightNum,n.bodysH=y}}if([1,2,10].includes(k)){a+=2;let N=JSON.parse(JSON.stringify(n.options||[])),v=-1;if(n.optionLayoutId=="ONE"||k==10)for(let S=0;S<N.length;S++){let y=U-(F.objectiveFillBoxSize[0]||20)-12,e=B(N[S].content,{width:y})+6;e<30&&(e=30),a+=e,k==10&&(a-=4),a>A&&v==-1&&(v=S,a=p(H,e),H++)}else{let S=n.optionLayoutId=="TWO"?2:4,y=[[]],e=0;for(let l=0;l<N.length;l++){let r=U/S-34;Z==13&&l==0&&(r-=I().setting.subjectiveScoreTypeId=="LINE"?24:80);let m=B(N[l].content,{width:r})+6;m<30&&(m=30),l%S==0&&l!=0&&(e++,y[e]=[]),y[e].push({h:m,i:l})}e=0;for(let l=0;l<y.length;l++){const r=y[l].reduce((m,C)=>C.h>m.h?C:m,y[l][0]);a+=r.h,a>A&&(v=e,a=p(H,r.h),H++),e+=y[l].length}}if(v!=-1){const S=N.slice(0,v),y=N.slice(v);let e=JSON.parse(JSON.stringify(n));k==10&&(e.children=[],e.optionsFalg=!0,n.body=""),Z==13&&(n.optionsOne=y.length!=N.length),e.options=S,n.options=y,w.push(e),n.bodys="",n.toOptiondSplit=!0}if(k==10){let S=n.children.length*31;if(a+=I().setting.objectiveAnswerTypeId=="FILL"?S:54,a>A){let y=JSON.parse(JSON.stringify(n));y.optionsFalg=!0,y.children=[],w.push(y),n.options=[],n.body="",a=p(H,S),H++}}}else if(k==4)a+=33;else if([3,6,12,14].includes(k)){let N=!1;if(n.splitChildren){N=!0,n.splitH=0;let S=0,y=0,e=-1,l=n.splitChildren.length;for(let r=0;r<l;r++){let m=n.splitChildren[r],C=B(m.bodys,{})+28;a+=C,y+=C,a>A?(S+=C,e==-1&&(e==-1&&(e=r),a=p(H,C),H++)):n.splitH+=C}if(n.blankH&&(n.blankH-=y),e!=-1){const r=n.splitChildren.slice(0,e),m=n.splitChildren.slice(e);let C=JSON.parse(JSON.stringify(n));C.splitChildren=r,C.Noresizable=r.length>0,C.splitFalg=!0,C.blankH=0,C.splitH=S,w.push(C),n.bodys="",n.toBodySplit=!0,n.splitChildren=m}}n.blankH?n.blankH<fe&&(n.blankH=fe):n.blankH=fe;let v=a;if(a+=n.blankH,a>A){let S=JSON.parse(JSON.stringify(n));S.blankH=A-v,n.blankH=n.blankH-S.blankH,n.blankH<fe&&(n.blankH=fe),n.splitH=0,n.splitChildren=[],n.splitFalg=N,n.bodys="",n.toBodySplit=!0,S.Noresizable=!0,w.push(S),a=p(H,n.blankH),H++}}else if([16,15].includes(k)){let N=I().setting.subjectiveScoreTypeId=="LINE"?26:30,v=n.wordNum,S=50,y=1;k==16&&(n.compositionSize||(n.compositionSize=20),y=n.compositionSize,n.compositionWidth=Math.floor(U/y),v=Math.ceil(v/y),S=n.compositionWidth+10);let e=JSON.parse(JSON.stringify(n.wordNum));if(v>0)for(let l=0;l<v;l++){let r=S;if(l==0&&(r=S+N),a+=r,a>A){n.wordNumCopy=e;let m=JSON.parse(JSON.stringify(n));m.wordNumI?m.wordNum=(l-m.wordNumI)*y:m.wordNum=l*y,n.wordNumTo=(m.wordNumTo||0)+m.wordNum,n.wordNumI=l,n.wordNum=e-l*y,n.bodys="",n.toBodySplit=!0,n.wordNumFalg=!0,w.push(m),a=p(H,r),H++}}else a+=N}w.push(n),K(w)})}for(let n=0;n<X.length;n++){let k=X[n];if(k.itemTypeId=="H1")k.h=B(k.content,{fontSize:"17",minHeight:"40px",lineHeight:"32"}),a+=k.h,a>A&&(a=p(x,k.h),x++,f[x]||(f[x]=[])),f[x].push(k);else{const{logicTypeId:Z,detailData:K}=k.question;if([100,13,10].includes(Z)){let D=K.body,w=[];if(D){let N=B(D,{}),v=a;if(K.bodysH=N,a+=N,a>A){H=x;const{ExceedingTheHeight:S,bodyList:y}=await b(D,{H:a,h:N,Hcopy:v,pages:H});let e=y.length;for(let l=0;l<e;l++)if(e!=l+1||e==1){let r=JSON.parse(JSON.stringify(K));r.toBodySplit=!0,r.toBodySplits=!0,r.body=y[l].txt,Z==10&&(r.options=[]),r.children=[],w.push(r),e==1&&(K.body="",a=p(y[l].pages+2,0))}else K.body=y[l].txt,a=S+y[l].headHeightNum;for(let l=0;l<w.length;l++){l!=0&&x++,f[x]||(f[x]=[]);let r=JSON.parse(JSON.stringify(k));r.question.detailData=w[l],f[x].push(r)}w.length>0&&(x++,f[x]||(f[x]=[]))}}if(Z==10){H=x;let N=await c(K,Z);for(let v=0;v<N.length;v++){v!=0&&x++,f[x]||(f[x]=[]);let S=JSON.parse(JSON.stringify(k));S.question.detailData=N[v],f[x].push(S)}}else{let N=K.children,v=[],S=0;H=x;for(let y=0;y<N.length;y++){let e=N[y],l=await c(e.detailData,Number(e.logicTypeId),Z);for(let r=0;r<l.length;r++)S+=r,v[S]||(v[S]=[]),l[r]&&v[S].push({...e,detailData:l[r]})}for(let y=0;y<v.length;y++)if(v[y]){let e=JSON.parse(JSON.stringify(k));y>0&&(e.question.detailData.body="",x++,f[x]||(f[x]=[])),e.question.detailData.children=v[y],f[x].push(e)}}}else{let D=[];H=x,D=await c(K,Z);for(let w=0;w<D.length;w++){w!=0&&x++,f[x]||(f[x]=[]);let N=JSON.parse(JSON.stringify(k));N.question.detailData=D[w],f[x].push(N)}}}}if(g.layout.paperListArrLen=f.length,De.value=JSON.parse(JSON.stringify(f)),g.isPreview&&g.layout.column==2||q.pageLayout.value=="A4_2"){let n=$e(f,2,!0);$.value=JSON.parse(JSON.stringify(n))}else if(g.isPreview&&g.layout.column==3){let n=$e(f,3,!0);$.value=JSON.parse(JSON.stringify(n))}else $.value=JSON.parse(JSON.stringify(f))}return o.value=JSON.parse(JSON.stringify(g.questionsArr)),Ne(g.questionsArr),le({paperListArr:$,getZB:Y,questionNumberingTypeIdChange:ae,questionsArrCopy:o}),(t,X)=>{const x=Ue;return d(!0),h(ie,null,ue(s($),(H,a)=>(d(),h("div",{class:Le(["topic-com",{"newline-preview-box":s(E)>1}]),ref_for:!0,ref_key:"topicComRef",ref:ve,id:"print-page"+a,key:a},[g.isPreview&&(i.layout.column==2||i.layout.column==3)||s(q).pageLayout.value=="A4_2"?(d(),h("div",{key:0,class:"topic-box",style:ee([{"flex-wrap":"wrap"},s(R)(a)])},[s(q).pageLayout.value=="A4_2"?(d(),h("div",Rt,[a%2==0?(d(),se(x,{key:0,pageLayoutName:s(q).pageLayout.name,layout:i.layout,index:a,examNumberLength:s(q).examNumberLength},null,8,["pageLayoutName","layout","index","examNumberLength"])):j("",!0)])):j("",!0),(d(!0),h(ie,null,ue(H,(f,c)=>(d(),h("div",{class:Le(["topic-box-item",{on:s(T)(c,s(q).pageLayout.value)}]),key:c,style:ee(s(u)(c,a))},[W(Je,{list:f,onOnEditorBlur:Fe,index:V(),layout:i.layout,pageLayoutName:s(q).pageLayout.name,examNumberLength:s(q).examNumberLength},null,8,["list","index","layout","pageLayoutName","examNumberLength"])],6))),128)),s(q).pageLayout.name=="A4_2"?(d(),h("div",{key:1,class:"page-number",style:ee(`height: ${s(F).pageNumberHeight}px;line-height: ${s(F).pageNumberHeight}px;bottom: ${s(F).pagePadding[0]}px;`)},[_("span",null,"第"+de(a+1)+"页 共"+de(s(E))+"页",1)],4)):j("",!0)],4)):(d(),h("div",{key:1,class:"topic-box",style:ee(s(R)(a))},[W(Je,{list:H,onOnEditorBlur:Fe,index:a,layout:i.layout,pageLayoutName:s(q).pageLayout.name,examNumberLength:s(q).examNumberLength},null,8,["list","index","layout","pageLayoutName","examNumberLength"])],4)),i.isPreview?(d(),h(ie,{key:2},ue(["left","right"],f=>_("div",{id:"marker-boxs",key:f,style:ee(`position: absolute; height: ${s(F).marker.height}px; width: ${s(F).marker.width}px; ${f}: ${s(F).marker.left}px; top: ${s(F).marker.top}px;`)},null,4)),64)):j("",!0),i.isPreview?(d(),h(ie,{key:3},ue(["left","right"],f=>_("div",{id:"marker-boxs",key:f,style:ee(`position: absolute; height: ${s(F).marker.height}px; width: ${s(F).marker.width}px; ${f}: ${s(F).marker.left}px; bottom: ${s(F).marker.top}px;`)},null,4)),64)):j("",!0),!i.isPreview&&a+1!=s(E)?(d(),h("div",Gt)):j("",!0),i.isPreview&&s(E)>1?(d(),h("p",Qt)):j("",!0)],10,Wt))),128)}}}),mi=me(Xt,[["__scopeId","data-v-4aa5540b"]]);export{pi as a,hi as b,yi as d,di as g,ai as p,ci as s,mi as t};

import{d as S,r as i,at as I,aF as C,aK as e,c as g,K as b,L as x,u as d,e as F,a as h,au as B,f as V,p as k,o as f}from"./index.ZZ6UQeaF.js";import{g as D}from"./writing.OS0S12Z4.js";import{g as q}from"./index.pTwlcQaF.js";import{s as P}from"./index.CCoS0DfY.js";/* empty css                   */import{_ as R}from"./_plugin-vue_export-helper.DlAUqK2U.js";import"./request.CsKOOJzG.js";import"./index.xsH4HHeE.js";/* empty css                 */import"./index.6W4rOsHv.js";import"./question.IyuOoK5G.js";/* empty css                *//* empty css                  */import"./itemDom.vue_vue_type_script_setup_true_lang.CbGURBLf.js";/* empty css                          *//* empty css                       */import"./index.vue_vue_type_style_index_0_lang.Cq6RDaj7.js";/* empty css                        */const T={class:"cards-boxs",id:"targetDivs"},N={class:"main-boxs scrollBar"},W={key:0,class:"containers"},E={class:"topic-boxs scrollBar",id:"family-box"},M=S({__name:"print",setup($){function L(a){return new URLSearchParams(window.location.search).get(a)}let y=L("uuid"),t={};const o=i({});let s=i({HhyptType:"writing",layoutOpen:!0,step_score:1,familysStyle:{}});i([]);const v=i(null),p=i(!0);(async()=>{var c,u,_;const a=await D({uuid:y});if(a.code!=0)return k.error(a.msg);let r=a.data||{};t=r.cardInfo;let{printLayout:n}=t.setting.pageLayout;n.page[0]&&(t.setting.pageLayout.columnWidth.width=n.page[0],t.setting.pageLayout.columnWidth.height=n.page[1]),t.setting.contentTypeId=t.contentTypeId||1,o.value=t.setting,o.scoringResultTypeId===void 0&&(o.scoringResultTypeId="score"),o.value.contentFontFamily&&(s.value.familysStyle={"--font-ckeditor-familys":o.value.contentFontFamily}),e().setting=t.setting;let l=((u=(c=o.value.pageLayout)==null?void 0:c.printLayout)==null?void 0:u.column)||2;s.value={...s.value,headNum:l*2,column:l,title:t.title,id:t.id,created_by_name:t.created_by_name||"-",createdAt:t.createdAt,showCreatorInfoVal:t.showCreatorInfoVal||""},(_=t.cardList[0])!=null&&_.title||(t.cardList[0].isFalg=!0),v.value=t.cardList,e().questionsScoreList=r.questionsScoreList,e().scoreSetting=r.scoreSetting,e().pageStyles=t.pageStyle,q(e(),!0,s.value.column),p.value=!1})(),I(()=>{window.$previewSaveFun=w}),C(()=>{delete window.$previewSaveFun}),i(null);const m=i(null),w=()=>{const a=m.value.getZB();return t.cardList=m.value.quesListCopy,{uuid:y,coordinateInfo:a,cardInfo:{...t,title:s.value.title,showCreatorInfoVal:s.value.showCreatorInfoVal,setting:e().setting,pageStyle:e().pageStyles},scoreSetting:e().scoreSetting,questionsScoreList:e().questionsScoreList}};return(a,r)=>{var l,c,u;const n=x;return f(),g("div",T,[b((f(),g("main",N,[d(p)?F("",!0):(f(),g("div",W,[h("div",E,[h("div",{style:B(`margin: 0 auto;width: ${(u=(c=(l=d(o))==null?void 0:l.pageLayout)==null?void 0:c.columnWidth)==null?void 0:u.width}px;`)},[V(P,{cardList:d(v),ref_key:"singleCardRef",ref:m,layout:d(s),isPreview:!0},null,8,["cardList","layout"])],4)])]))])),[[n,d(p)]])])}}}),rt=R(M,[["__scopeId","data-v-04fd7a57"]]);export{rt as default};

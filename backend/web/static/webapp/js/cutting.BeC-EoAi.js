import{_ as fe}from"./index.vue_vue_type_style_index_0_lang.Cq6RDaj7.js";import{d as Q,r as l,b as H,E as ae,u as n,i as ie,w,o as I,a as d,c as L,e as F,q as ce,n as K,f as m,l as O,R as pe,k as re,F as J,aA as ve,h as G,j as ue,m as de,p as Z,K as _e,L as we,C as ge,au as W,M as le}from"./index.ZZ6UQeaF.js";/* empty css                        *//* empty css                *//* empty css                  */import{s as me}from"./index.6W4rOsHv.js";/* empty css                   */import{_ as ee}from"./_plugin-vue_export-helper.DlAUqK2U.js";import{s as te}from"./request.CsKOOJzG.js";const xe={class:"pmm2_flex_between"},ye={class:"historImgs-box pmm2_flex scrollBar-C"},Ie=["onClick"],ke=["preview-src-list","src"],Ce={class:"hover pmm2_flex_center"},Me={key:0,class:"on-box"},be={class:"pmm2_flex_between"},De={class:"dialog-footer"},$e=Q({__name:"historImgs",setup(b,{expose:P}){const g=l(!1),c=l([]),v=l(!1),U=l([]),x=l(-1),$=k=>{U.value=k,x.value=-1,v.value=!0},q=k=>{x.value==k?x.value=-1:x.value=k},E=()=>{x.value!=-1?(me(U.value[x.value]),v.value=!1):Z({message:"请选择图片",type:"warning"})};return P({show:$}),(k,r)=>{const V=O("CloseBold"),R=re,a=de,T=O("View"),X=O("Check"),N=ve,z=ue,i=ae;return I(),H(i,{"append-to-body":"","close-on-click-modal":!1,class:"hhypt-dialog-boxs","show-close":!1,modelValue:n(v),"onUpdate:modelValue":r[2]||(r[2]=e=>ie(v)?v.value=e:null),width:800,zIndex:9,"align-center":""},{header:w(({close:e,titleClass:s})=>[d("div",xe,[d("span",{class:K(s)},"历史图片",2),m(a,{onClick:e,underline:!1},{default:w(()=>[m(R,{size:"20"},{default:w(()=>[m(V)]),_:1})]),_:2},1032,["onClick"])])]),footer:w(()=>[d("div",be,[r[5]||(r[5]=d("div",null,null,-1)),d("div",De,[m(z,{style:{width:"70px"},onClick:r[1]||(r[1]=e=>v.value=!1)},{default:w(()=>r[3]||(r[3]=[G("取消")])),_:1,__:[3]}),m(z,{style:{width:"70px"},type:"primary",onClick:E},{default:w(()=>r[4]||(r[4]=[G(" 复制 ")])),_:1,__:[4]})])])]),default:w(()=>[d("div",ye,[(I(!0),L(J,null,ce(n(U),(e,s)=>(I(),L("div",{class:"item-box",key:s},[d("div",{class:K(["item-com huiA pmm2_flex_center on",{on:n(x)==s}]),onClick:h=>q(s)},[d("img",{"preview-src-list":[e],src:e,fit:"fill"},null,8,ke),d("div",Ce,[m(R,{style:{"font-size":"50px"},onClick:pe(h=>(g.value=!0,c.value=[e]),["stop"])},{default:w(()=>[m(T)]),_:2},1032,["onClick"])]),n(x)==s?(I(),L("div",Me)):F("",!0),n(x)==s?(I(),H(R,{key:1,class:"icon"},{default:w(()=>[m(X)]),_:1})):F("",!0)],10,Ie)]))),128)),n(g)?(I(),H(N,{key:0,"url-list":n(c),"show-progress":"",onClose:r[0]||(r[0]=e=>g.value=!1)},null,8,["url-list"])):F("",!0)])]),_:1},8,["modelValue"])}}}),Be=ee($e,[["__scopeId","data-v-37432235"]]),oe="api/v1/";function Qe(b){return te({url:oe+"paper-conversion/detail",method:"post",data:b})}function Le(b){return te({url:oe+"paper-conversion/upload-image",method:"post",data:b})}function Ze(b){return te({url:oe+"paper-conversion/save-content",method:"post",data:b})}const Re={class:"pmm2_flex_between"},Ee={class:"scrollBar-C",style:{"max-height":"calc(100vh - 150px)","overflow-y":"auto"}},Ve=Q({__name:"cuttingDivideImg",emits:["getimg"],setup(b,{expose:P,emit:g}){const c=l(!1),v=l({});P({show:o=>{v.value=o,a.value={},h(),c.value=!0}});const x=l({}),$=l({}),q=async o=>{try{const t=o.target,y=t.naturalWidth,f=t.naturalHeight,p=x.value.$el.querySelector("img");if(p){const B=p.getBoundingClientRect(),M=Math.ceil(B.width*100)/100,S=Math.ceil(B.height*100)/100;$.value={dwidth:M,dheight:S,wDig:y/M,hDig:f/S}}}catch{}},E=l(null),k=l(!1),r=l(!1),V=l(0),R=l(0);l([[]]);const a=l({}),T=o=>{if(u!=null&&u.isDragging)return;const t=E.value.getBoundingClientRect();V.value=o.clientX-t.left,R.value=o.clientY-t.top,k.value=!0,r.value=!0,document.addEventListener("mousemove",X),document.addEventListener("mouseup",N)},X=o=>{if(k.value){const t=E.value.getBoundingClientRect(),y=o.clientX-t.left,f=o.clientY-t.top;if(y>2||f>2){let p=Math.abs(y-V.value),B=Math.abs(f-R.value),M=Math.min(R.value,f),S=Math.min(V.value,y);S+p>t.width||M+B>t.height||M<0||S<0||(a.value={top:Math.floor(M),left:Math.floor(S),width:Math.ceil(p),height:Math.ceil(B)})}}},N=o=>{k.value=!1,document.removeEventListener("mousemove",X),document.removeEventListener("mouseup",N)},z=l(!1),i=g,e=(o=0,t)=>t=="w"?Math.ceil(o*$.value.wDig):Math.ceil(o*$.value.hDig),s=()=>{z.value=!0;let o=[{left:e(a.value.left,"w"),top:e(a.value.top,"h"),width:e(a.value.width,"w"),height:e(a.value.height,"h")}];Le({url:v.value.src,uuid:v.value.uuid,images:o}).then(async t=>{if(t.code==0){let y=t.data[0];i("getimg",y),me(y)}else t.msg&&Z({message:t.msg,type:"warning"});z.value=!1,c.value=!1}),h()},h=()=>{r.value=!1,a.value=null},_=l(null),u={isDragging:!1,startX:0,startY:0,left:0,top:0},D=o=>{u.isDragging=!0,u.startX=o.clientX,u.startY=o.clientY,u.left=_.value.offsetLeft,u.top=_.value.offsetTop,document.addEventListener("mousemove",C),document.addEventListener("mouseup",Y)},C=o=>{if(u.isDragging){const t=o.clientX-u.startX,y=o.clientY-u.startY;let f=u.left+t,p=u.top+y;const B=E.value.getBoundingClientRect(),M=_.value.getBoundingClientRect();f=Math.max(0,f),p=Math.max(0,p),f=Math.min(B.width-M.width,f),p=Math.min(B.height-M.height,p),f=Math.floor(f),p=Math.floor(p),_.value.style.left=f+"px",_.value.style.top=p+"px",a.value.left=f,a.value.top=p}},Y=()=>{u.isDragging=!1,document.removeEventListener("mousemove",C),document.removeEventListener("mouseup",Y)};return(o,t)=>{const y=O("CloseBold"),f=re,p=de,B=ge,M=ue,S=ae,he=we;return I(),H(S,{"append-to-body":"","close-on-click-modal":!1,class:"hhypt-dialog-boxs","show-close":!1,modelValue:n(c),"onUpdate:modelValue":t[0]||(t[0]=A=>ie(c)?c.value=A:null),width:"80%",zIndex:9,"align-center":""},{header:w(({close:A,titleClass:j})=>[d("div",Re,[d("span",{class:K(j)},"添加图片",2),m(p,{onClick:A,underline:!1},{default:w(()=>[m(f,{size:"20"},{default:w(()=>[m(y)]),_:1})]),_:2},1032,["onClick"])])]),default:w(()=>{var A,j,se,ne;return[_e((I(),L("div",Ee,[d("div",{ref_key:"parentRef",ref:E,class:"topic-main",onMousedown:T},[m(B,{src:n(v).src,class:"bg-img",ref_key:"boxImgRef",ref:x,onLoad:q},null,8,["src"]),t[3]||(t[3]=d("div",{class:"cropper-crop"},null,-1)),n(k)&&n(r)?(I(),L("div",{key:0,style:W({left:((A=n(a))==null?void 0:A.left)+"px",top:((j=n(a))==null?void 0:j.top)+"px",width:((se=n(a))==null?void 0:se.width)+"px",height:((ne=n(a))==null?void 0:ne.height)+"px"}),class:"drag-square"},null,4)):n(r)&&!n(k)&&n(a)?(I(),L("div",{key:1,style:W([{left:n(a).left+"px",top:n(a).top+"px",width:n(a).width+"px",height:n(a).height+"px"},{"z-index":"3"}]),class:"drag-square draggable",ref_key:"draggableRef",ref:_,onMousedown:D},[d("div",{class:"bottom-box",style:W({top:n(a).height+"px"})},[m(M,{onClick:h},{default:w(()=>t[1]||(t[1]=[G("取消")])),_:1,__:[1]}),m(M,{type:"primary",onClick:s},{default:w(()=>t[2]||(t[2]=[G("确认")])),_:1,__:[2]})],4)],36)):F("",!0)],544)])),[[he,n(z)]])]}),_:1},8,["modelValue"])}}}),ze=ee(Ve,[["__scopeId","data-v-33239745"]]),Se={class:"main-box scrollBar"},Ue={class:"containers"},Xe={class:"left-box scrollBar"},Ye={class:"right-box scrollBar"},qe={key:0,class:"ul"},Ne=["onClick"],Ae={class:"li-com"},Pe=Q({__name:"cutting",props:["getDataBody","pagesInfo","uuid"],setup(b,{expose:P}){const g=b,c=l({}),v=l({}),U=l({}),x=async i=>{v.value={left:$(i.region.left,"w")+12+"px",top:$(i.region.top,"h")+12+"px",width:$(i.region.width,"w")+"px",height:$(i.region.height,"h")+"px"},await le(),U.value.scrollIntoView({behavior:"smooth",block:"center"})},$=(i=0,e)=>e=="w"?Math.ceil(i*c.value.wDig):Math.ceil(i*c.value.hDig),q=l(null),E=/w_(\d+),h_(\d+)/i;function k(i){return i.replace(/<img([^>]*)(\/?>)/gi,function(e,s,h){if(/(\s|^)width\s*=/i.test(s)||/(\s|^)height\s*=/i.test(s))return e;const _=s.match(/src\s*=\s*["']([^"']+)["']/i);if(!_)return e;const u=_[1],D=new URL(u,"https://ydb-test.oss-cn-hangzhou.aliyuncs.com"),C=D.searchParams.get("width"),Y=D.searchParams.get("height");if(C&&Y){let o=Math.ceil(c.value.wDig*C),t=Math.ceil(c.value.hDig*Y);return`<img width="${o}" height="${t}"${s}${h}`}else{const o=D.search.match(E);if(o&&o.length>=3){let t=Math.ceil(c.value.wDig*o[1]),y=Math.ceil(c.value.hDig*o[2]);return`<img width="${t}" height="${y}"${s}${h}`}}return e})}const r=l(!1);function V(i,e){let s=i/e;return i>e&&(s=e/i),s}const R=async i=>{if(g.pagesInfo.imgInfo){c.value=g.pagesInfo.imgInfo,r.value=!0;return}r.value=!1;try{const e=i.target,s=e.naturalWidth,h=e.naturalHeight;c.value={width:s,height:h,src:g.pagesInfo.url,uuid:g.uuid};const _=q.value.$el.querySelector("img");if(_){const u=_.getBoundingClientRect(),D=Math.ceil(u.width*100)/100;c.value.dwidth=D;const C=Math.ceil(u.height*100)/100;c.value.dheight=C,c.value.wDig=V(s,D),c.value.hDig=V(h,C);let Y=g.pagesInfo.paragraphList.length;for(let o=0;o<Y;o++){let t=g.pagesInfo.paragraphList[o];/<img\s/i.test(t.text)&&(g.pagesInfo.paragraphList[o].text=k(t.text))}le(()=>{g.pagesInfo.imgInfo=c.value,r.value=!0})}}catch{r.value=!0}},a=l(null),T=()=>{var e,s;if((((s=(e=g.pagesInfo)==null?void 0:e.images)==null?void 0:s.length)||0)==0){Z({message:"暂无历史图片",type:"warning"});return}a.value.show(g.pagesInfo.images)},X=l(null),N=()=>{X.value.show(c.value)},z=i=>{var s,h;((h=(s=g.pagesInfo)==null?void 0:s.images)==null?void 0:h.length)||0?g.pagesInfo.images.push(i):g.pagesInfo.images=[i]};return P({divideImgShow:N,historImgsShow:T}),(i,e)=>{var _;const s=ge,h=fe;return I(),L(J,null,[d("main",Se,[d("div",Ue,[d("div",Xe,[m(s,{src:(_=b.pagesInfo)==null?void 0:_.url,ref_key:"leftBoxImgRef",ref:q,onLoad:R,style:{width:"100%"}},null,8,["src"]),d("div",{class:"box",style:W(n(v)),ref_key:"leftBoxRef",ref:U},null,4)]),e[1]||(e[1]=d("div",{class:"center-box pmm2_flex_center"},null,-1)),d("div",Ye,[n(r)?(I(),L("div",qe,[(I(!0),L(J,null,ce(b.pagesInfo.paragraphList,(u,D)=>(I(),L("div",{class:"li",onClick:C=>x(u),key:D},[d("div",Ae,[m(h,{tapNum:"2",modelValue:u.text,"onUpdate:modelValue":C=>u.text=C,onOnEditorBlur:e[0]||(e[0]=C=>v.value={}),uuid:D+"intelligenttopiccutting"},null,8,["modelValue","onUpdate:modelValue","uuid"])])],8,Ne))),128))])):F("",!0)])])]),m(ze,{ref_key:"divideImgRef",ref:X,onGetimg:z},null,512),m(Be,{ref_key:"historImgsRef",ref:a},null,512)],64)}}}),et=ee(Pe,[["__scopeId","data-v-0f192618"]]);export{et as c,Qe as g,Ze as s};

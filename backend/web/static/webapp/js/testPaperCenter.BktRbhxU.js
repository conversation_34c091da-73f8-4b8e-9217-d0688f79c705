import{ar as Oe,r as q,d as X,as as Te,b as J,E as Ae,u as a,i as H,w as m,o as l,a as t,c as i,K as ce,f as o,g as Ce,L as Pe,P as Le,N as pe,S as me,t as R,F as N,q as O,e as z,v as he,s as fe,l as Z,k as ie,h as B,j as ve,n as re,m as Ue,p as ne,W as we,at as Fe,au as ue,av as Ee,aw as ze,M as qe,U as ye,T as be,y as xe,z as Be,ax as Re,ay as je,O as Me}from"./index.ZZ6UQeaF.js";/* empty css                *//* empty css                  *//* empty css                     *//* empty css                  *//* empty css                  *//* empty css                *//* empty css                          *//* empty css                 */import{s as U}from"./request.CsKOOJzG.js";/* empty css                   */import{_ as ge}from"./_plugin-vue_export-helper.DlAUqK2U.js";/* empty css                       */import{q as x}from"./question.IyuOoK5G.js";import{_ as ae}from"./index.vue_vue_type_style_index_0_lang.Cq6RDaj7.js";import{u as se}from"./index.6W4rOsHv.js";/* empty css                       */const L=Oe("analysis",()=>{const e=q({}),s=q([]),c=q({}),r=q({}),f=q([]),w=q([]),p=q([]),n=q([]),D=q(!1),V=q(!1),b=new Date().getFullYear(),d=[];for(let k=b-5;k<=b+2;k++)d.push(String(k));d.reverse();const $=d,P=q([]),I=q(!1),A=q(!1);return{bloomLevelEnabled:D,bloomledgePointsList:n,paperChangeFalg:A,basicInfo:e,knowledgePointsFalg:V,knowledgePointsList:p,categoriesInfo:s,scenesInfo:f,cityList:w,currentYearList:$,paperInfo:c,textbookLists:r,paperUuidList:P,isUserPaper:I}}),Je={isQuestion:!0,itemTypeId:x.QUESTION,itemTypeLabel:"试题",question:{scenes:[],be_from:"",categoryId:void 0,year:"",provinceCode:"",cityCode:"",areaCode:"",difficultyId:"",testPoints:[],knowledges:[],isNew:!0,action:"new",detailData:{body:"",answers:[],answer:"",analysis:[{nr:""}],children:[{detailData:{body:"",answers:[],answer:"",analysis:[{nr:""}]}}],options:[]}}},Ro={isQuestion:!1,itemTypeId:x.H1headline,itemTypeLabel:"标题1",content:"",children:[]},Ne=(e,s,c,r,f=!1)=>{var w;if(!s)return"请选择题型";if(c==x.SingleChoice){if(!r.body&&!f)return"请输入题干";if(!r.answer)return"请选择答案";let p=r.options.findIndex(D=>!D.content);if(p!=-1)return(r.options[p].label||"")+".请输入选项";if(r.options.findIndex(D=>D.label==r.answer)==-1)return"请选择答案"}else if(c==x.MultipleChoice){if(!r.body&&!f)return"请输入题干";let p=r.options.find(V=>!V.content);if(p)return p.label+".请输入选项";if(!((w=r.answers)!=null&&w.length))return"请选择答案";let n=r.options.map(V=>V.label)||[];if(!r.answers.filter(V=>n.includes(V)).length)return"请选择答案"}else if(c==x.TrueFalse){if(!r.body&&!f)return"请输入题干";if(!r.answer||r.answer!="T"&&r.answer!="F")return"请选择答案"}else if(c==x.BlankFilling||c==x.FreeAnswer){if(!r.body&&!f)return"请输入题干";if(!L().isUserPaper&&!r.answer)return"请输入答案"}else if(c==x.EnglishWriting||c==x.ChineseWriting){if(!r.body&&!f)return"请输入题干"}else if(c==x.MultipleX2Multiple){if(!r.body)return"请输入材料";let p=r.options.find(D=>!D.content);if(p)return"选项"+p.label+"不能为空";let n=r.children.findIndex(D=>!D.detailData.answer);if(n!=-1)return"第"+(n+1)+"个答案不能为空"}else if(r.children&&r.children.length>0)for(let p=0;p<r.children.length;p++){let n=r.children[p];if(!n.logicTypeId)return"第"+(p+1)+"小题请选择题目题型";if(!r.childLogicTypeLabels[String(n.logicTypeId)])return"第"+(p+1)+"小题选择了不支持的题型，请重新选择";let D=Ne(e,s,n.logicTypeId,n.detailData,f);if(D)return"第"+(p+1)+"小题"+D}},Ve=(e,s)=>{if(e.answers){if(s==2){let c=e.options.map(f=>f.label)||[];const r=e.answers.filter((f,w)=>e.answers.indexOf(f)===w&&c.includes(f));e.answer=r.sort().join("")}delete e.answers}return e.answer=[e.answer],e.analysis=e.analysis.map(c=>c.nr),e},jo=(e,s=2)=>new Promise((c,r)=>{let f=L().basicInfo.logicTypeList.find(p=>p.value==e.question.logicTypeId)||{childrenNoBody:!1};if(s==2){const p=Ne(e.question.difficultyId,e.question.categoryId,e.question.logicTypeId,e.question.detailData,f.childrenNoBody);if(p){r({msg:p});return}}let w=e.question.logicTypeId;[x.Complex,x.MultipleX2Multiple,x.GrammarBlankFilling,x.ReadingCloze,x.EssayCorrectError].includes(w)?(e.question.detailData.answer=[],e.question.detailData.analysis=[],e.question.detailData.answers&&delete e.question.detailData.answers,e.question.detailData.children.forEach(p=>{w==x.MultipleX2Multiple&&(p.logicTypeId=x.SingleChoice),[x.SingleChoice,x.MultipleChoice].includes(p.logicTypeId)||(p.detailData.options=[],delete p.detailData.options),p.detailData=Ve(p.detailData,p.logicTypeId)}),e.question.detailData.options&&w!=x.MultipleX2Multiple&&(e.question.detailData.options=[])):(e.question.detailData.children&&delete e.question.detailData.children,[x.SingleChoice,x.MultipleChoice].includes(w)||(e.question.detailData.options=[],delete e.question.detailData.options),e.question.detailData=Ve(e.question.detailData,w)),e.question.canComposite="",delete e.question.canComposite,e.question.childrenNoBody="",delete e.question.childrenNoBody,c(e)}),$e=(e,s)=>(e.answer=Array.isArray(e.answer)?e.answer.join(""):e.answer||"",(s==x.MultipleChoice||s==x.SingleChoice)&&(e.answers=e.answer.split(""),e.answer=e.answers[0]||""),e.analysis=e.analysis?e.analysis.map(c=>c!=null&&c.nr?c:{nr:c||""}):"",e),Mo=e=>new Promise(s=>{let c=e.question.logicTypeId;c!=null&&(e.question.logicTypeId=Number(c));let r=L().basicInfo.logicTypeList.find(f=>f.value==c)||{canComposite:!1};if(e.question.canComposite=r.canComposite,e.question.childrenNoBody=r.childrenNoBody,c==x.Complex&&(e.question.canComposite=!0),(e.question.categoryId==x.RecognitionError||!e.question.categoryId)&&(e.question.categoryId="",e.question.canComposite=!1),e.question.difficultyId||(e.question.difficultyId=""),[x.Complex,x.MultipleX2Multiple,x.GrammarBlankFilling,x.ReadingCloze,x.EssayCorrectError].includes(c)){let f=e.question.detailData.children,w=f.length;for(let p=0;p<w;p++){let n=f[p].detailData;n.logicTypeId||(n.logicTypeId=f[p].logicTypeId),n.logicTypeId!==void 0&&n.logicTypeId!==null&&(e.question.detailData.children[p].logicTypeId=Number(n.logicTypeId)),e.question.detailData.children[p].detailData=$e(n,n.logicTypeId)}}else e.question.detailData=$e(e.question.detailData,c);s(e)}),Se="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADgAAAA2CAYAAACSjFpuAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAF8SURBVHgB7di/bYNAFAbwB6ZyxQhu/AeJwt4g3iCeICOEbJAN7GwQTxJnAigQCBoYIRUdkHeIRIkjg0ku6E76fhKN78B63Hc8bCIAAAAAAABQVZqm6ziOZ/QPTFJAWZa2aZp+FEUeSaZEgS17MpnseTX3vu/bJIlKBTbquvam06kvK7LKFdiayYqsqgUKn5GlPzAuDfCF70VcaDyzjrG8qqqt4zg5DWRdGuDi7J4vHVMT2SRJvOVyeRxyosoRPSdu+PPQyOpUYENsG17J7NqnrHYFtj4ie9c3UdcCBdswjEMYhuuuSToXmPOxdV036JqkZYG8Dw9FUWwWi0XQN9cijXAk37gfPq5Wq6drz9GpwIB/deyGNnstItpGUu6bDEfhaFnWiUbAK7Ph+P1o4CKSou9xJAe9vXy7BimAm/YN97WXs48Dvsm736zaV0ruQRFJXrUHkkCpAkUk+bidz+evJIkyDxletZPYizKLU0aWZdL+gwEAAAAAAAAQ3gGU6Y+QrCzq5gAAAABJRU5ErkJggg==",Y="api/";function Jo(e){return U({url:Y+"v1/basic/get-config",method:"post",data:e})}function Wo(e){return U({url:Y+"v1/question/detail",method:"get",params:e})}function Yo(e){return U({url:Y+"v1/question/save",method:"post",data:e})}function Qo(e){return U({url:Y+"v1/paper-editing/detail",method:"post",data:e})}function Zo(e){return U({url:Y+"v1/paper-editing/save",method:"post",data:e})}function Xo(e){return U({url:Y+"v1/question/get-categories",method:"post",data:e})}function Go(){return U({url:Y+"v1/basic",method:"get"})}function We(e){return U({url:Y+"v1/basic/bloom-levels",method:"post",data:e})}function Ko(e){return U({url:Y+"v1/basic/get-city-areas",method:"post",data:e})}function Ho(e){return U({url:Y+"v1/question/get-scenes",method:"post",data:e})}function es(e){return U({url:Y+"v1/textbook/list",method:"post",data:e})}function Ye(e){return U({url:Y+"v1/textbook/catalog",method:"post",data:e})}function ts(e){return U({url:Y+"v1/paper-answer/analysis",method:"post",headers:{"Content-Type":"multipart/form-data"},data:e})}function ls(e){return U({url:Y+"v1/paper-answer/detail",method:"post",data:e})}function os(e){return U({url:Y+"v1/paper-answer/save-pages",method:"post",data:e})}function ss(e){return U({url:Y+"v1/paper-answer/page-content",method:"post",data:e})}function ns(e){return U({url:Y+"v1/paper-answer/split",method:"post",data:e})}const Qe={class:"pmm2_flex_between"},Ze={key:0},Xe={class:"cardSetting-box"},Ge={class:"cardSetting-com pmm2_flex_between"},Ke={key:0,class:"left-box scrollBar"},He={class:"filterText-box"},et={key:0},tt=["title"],lt={key:1,class:"left-box",style:{"flex-direction":"column",display:"flex","align-items":"center"}},ot={class:"right-box"},st={key:0,class:"right-com scrollBar"},nt={class:"name-box pmm2_flex_acenter"},it={class:"name"},at=["onClick"],dt={key:1,class:"right-com",style:{"flex-direction":"column",display:"flex","align-items":"center"}},ut={class:"pmm2_flex_between"},ct={class:"dialog-footer"},rt=X({__name:"knowledgePoints",emits:["setTestPointsList"],setup(e,{expose:s,emit:c}){const r=q(!1),f=L().paperInfo;L().bloomledgePointsList.length==0&&We({}).then(g=>{L().bloomledgePointsList=g.data||[]});const w=q([]),p=q(""),n=q(null);Te(p,g=>{n.value.filter(g)});const D=(g,v)=>g?v.name.includes(g):!0,V=g=>{let v=d.value.findIndex(T=>T.knowledge_uuid==g.uuid);if(v==-1){let T={knowledge_name:g.name,knowledge_uuid:g.uuid};d.value.push(T)}else d.value.splice(v,1)},b=(g="",v="")=>{if(v){let T=$.value.findIndex(C=>C==v);T!=-1&&$.value.splice(T,1),d.value.splice(g,1)}else d.value=[],$.value=[]},d=q([]),$=q([]);q({});const P=q(!0);s({opens:(g=[])=>{P.value=!0,r.value=!0,d.value=JSON.parse(JSON.stringify(g)),$.value=d.value.map(v=>v.knowledge_uuid),setTimeout(()=>{w.value=L().knowledgePointsList,P.value=!1},500)}});const A=c,k=()=>{let g={testPointsList:d.value,type:"confirm"};if(L().bloomLevelEnabled){for(let v=0;v<d.value.length;v++)if(!d.value[v].bloom_level){ne({message:`请选择【${d.value[v].knowledge_name}】的布鲁姆认知层级`,type:"warning"});return}}A("setTestPointsList",JSON.parse(JSON.stringify(g))),r.value=!1},F=()=>{A("setTestPointsList",{type:"cancel"}),r.value=!1};return(g,v)=>{const T=Z("CloseBold"),C=ie,u=Ue,_=Ce,h=pe,W=me,j=Le,K=fe,ee=he,te=Z("Close"),le=ve,de=Ae,oe=Pe;return l(),J(de,{"append-to-body":"","close-on-click-modal":!1,class:"hhypt-dialog-boxs","show-close":!1,modelValue:a(r),"onUpdate:modelValue":v[2]||(v[2]=M=>H(r)?r.value=M:null),title:"设置知识点","align-center":"",width:"1000"},{header:m(({close:M,titleClass:y})=>[t("div",Qe,[t("span",{class:re(y)},[v[3]||(v[3]=B("设置知识点")),a(f).stageLabel?(l(),i("span",Ze,"【"+R(a(f).stageLabel)+" - "+R(a(f).courseName)+"】",1)):z("",!0)],2),o(u,{onClick:M,underline:!1},{default:m(()=>[o(C,{size:"20"},{default:m(()=>[o(T)]),_:1})]),_:2},1032,["onClick"])])]),footer:m(()=>[t("div",ut,[v[11]||(v[11]=t("div",null,null,-1)),t("div",ct,[o(le,{style:{width:"70px"},onClick:F},{default:m(()=>v[9]||(v[9]=[B("取消")])),_:1,__:[9]}),o(le,{style:{width:"70px"},type:"primary",onClick:k},{default:m(()=>v[10]||(v[10]=[B(" 确定 ")])),_:1,__:[10]})])])]),default:m(()=>[t("div",Xe,[v[8]||(v[8]=t("div",{style:{width:"100%",height:"20px"}},null,-1)),t("div",Ge,[a(w).length>0?(l(),i("div",Ke,[t("div",He,[o(_,{clearable:"",modelValue:a(p),"onUpdate:modelValue":v[0]||(v[0]=M=>H(p)?p.value=M:null),style:{width:"240px"},placeholder:"请输入知识点"},null,8,["modelValue"])]),ce((l(),J(j,{data:a(w),props:{children:"children",label:"name"},"empty-text":"暂无知识点数据","check-strictly":!0,"default-expand-all":"","node-key":"id",class:"hhypt-el-tree","filter-node-method":D,ref_key:"treeRef",ref:n},{default:m(({node:M,data:y})=>[!y.children||y.children.length==0?(l(),i("span",et,[o(W,{modelValue:a($),"onUpdate:modelValue":v[1]||(v[1]=E=>H($)?$.value=E:null),onChange:E=>V(y)},{default:m(()=>[o(h,{title:M.label,label:M.label,value:y.uuid},null,8,["title","label","value"])]),_:2},1032,["modelValue","onChange"])])):(l(),i("span",{key:1,title:M.label},R(M.label),9,tt))]),_:1},8,["data"])),[[oe,a(P)]])])):ce((l(),i("div",lt,v[4]||(v[4]=[t("img",{style:{width:"200px"},src:"https://oss.hhypt.com/resources/mini-parent-helper/err.png"},null,-1),t("span",{style:{color:"#999"}},"暂无知识点数据",-1)]))),[[oe,a(P)]]),v[7]||(v[7]=t("div",{class:"jiantou"},[t("img",{src:Se})],-1)),t("div",ot,[t("div",{class:"top-tig pmm2_flex_between"},[v[5]||(v[5]=t("div",{class:"t1"},"已选项",-1)),t("div",{class:"huiA",onClick:b,style:{color:"#2672FF"}},"全部清除")]),a(d).length>0?(l(),i("div",st,[(l(!0),i(N,null,O(a(d),(M,y)=>(l(),i("div",{class:"item-box pmm2_flex_between",key:y},[t("div",nt,[a(L)().bloomLevelEnabled?(l(),J(ee,{key:0,modelValue:M.bloom_level,"onUpdate:modelValue":E=>M.bloom_level=E,placeholder:"请选择",size:"small",style:{width:"70px","margin-right":"5px"},clearable:""},{default:m(()=>[(l(!0),i(N,null,O(a(L)().bloomledgePointsList,E=>(l(),J(K,{key:E.value,label:E.label,size:"small",value:E.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])):z("",!0),t("div",it,R(M.knowledge_name),1)]),t("div",{style:{"text-align":"right",width:"50px","font-size":"16px"},class:"huiA2",onClick:E=>b(y,M.knowledge_uuid)},[o(C,null,{default:m(()=>[o(te)]),_:1})],8,at)]))),128))])):(l(),i("div",dt,v[6]||(v[6]=[t("img",{style:{width:"200px"},src:"https://oss.hhypt.com/resources/mini-parent-helper/err.png"},null,-1),t("span",{style:{color:"#999"}},"未设置知识点数据",-1)])))])])])]),_:1},8,["modelValue"])}}}),pt=ge(rt,[["__scopeId","data-v-e0637d5d"]]),mt={class:"pmm2_flex_between"},ht={class:"cardSetting-box"},ft={class:"cardSetting-top pmm2_flex_acenter"},vt={class:"cardSetting-com pmm2_flex_between"},yt={key:0,class:"left-box scrollBar"},bt={class:"filterText-box"},gt={key:0},_t=["title"],kt={key:1,class:"left-box",style:{"flex-direction":"column",display:"flex","align-items":"center"}},xt={class:"right-box"},Ct={key:0,class:"right-com scrollBar"},wt={class:"name"},Dt=["onClick"],It={key:1,class:"right-com",style:{"flex-direction":"column",display:"flex","align-items":"center"}},qt={class:"pmm2_flex_between"},Vt={class:"dialog-footer"},$t=X({__name:"examPoint",emits:["setTestPointsList"],setup(e,{expose:s,emit:c}){const r=q(!1),f=L().paperInfo,w=L().textbookLists,p=q([]),n=_=>{var W;let h=w.find(j=>j.uuid==_)||{};p.value=h.textbooks||[],g.value.textbookVal=(W=h.textbooks[0])==null?void 0:W.id,P(g.value.textbookVal)},D=q(""),V=q(null);Te(D,_=>{V.value.filter(_)});const b=(_,h)=>_?h.name.includes(_):!0,d=q(!1),$=q([]),P=_=>{d.value=!0,Ye({textbookId:_}).then(h=>{d.value=!1,$.value=h.data||[]})},I=_=>{let h=k.value.findIndex(W=>W.uuid==_.uuid);h==-1?k.value.push(_):k.value.splice(h,1)},A=(_="",h="")=>{if(h){let W=F.value.findIndex(j=>j==h);W!=-1&&F.value.splice(W,1),k.value.splice(_,1)}else k.value=[],F.value=[]},k=q([]),F=q([]),g=q({});s({opens:(_=[])=>{var W;if(r.value)return;r.value=!0,k.value=JSON.parse(JSON.stringify(_)),F.value=k.value.map(j=>j.uuid);let h={textbooks:[]};if(w.length>0){let j=w.findIndex(K=>K.is_default);j==-1&&(j=0),h=w[j]}g.value.subjectVal=h.uuid,p.value=h.textbooks||[],g.value.textbookVal=(W=h.textbooks[0])==null?void 0:W.id,P(g.value.textbookVal)}});const T=c,C=()=>{let _={testPointsList:k.value,type:"confirm"};T("setTestPointsList",JSON.parse(JSON.stringify(_))),r.value=!1},u=()=>{T("setTestPointsList",{type:"cancel"}),r.value=!1};return(_,h)=>{const W=Z("CloseBold"),j=ie,K=Ue,ee=fe,te=he,le=Ce,de=pe,oe=me,M=Le,y=Z("Close"),E=ve,_e=Ae,Ie=Pe;return l(),J(_e,{"append-to-body":"","close-on-click-modal":!1,class:"hhypt-dialog-boxs","show-close":!1,modelValue:a(r),"onUpdate:modelValue":h[4]||(h[4]=S=>H(r)?r.value=S:null),title:"设置考点/题点","align-center":"",width:"950"},{header:m(({close:S,titleClass:Q})=>[t("div",mt,[t("span",{class:re(Q)},"设置考点/题点【"+R(a(f).stageLabel)+" - "+R(a(f).courseName)+"】",3),o(K,{onClick:S,underline:!1},{default:m(()=>[o(j,{size:"20"},{default:m(()=>[o(W)]),_:1})]),_:2},1032,["onClick"])])]),footer:m(()=>[t("div",qt,[h[13]||(h[13]=t("div",null,null,-1)),t("div",Vt,[o(E,{style:{width:"70px"},onClick:u},{default:m(()=>h[11]||(h[11]=[B("取消")])),_:1,__:[11]}),o(E,{style:{width:"70px"},type:"primary",onClick:C},{default:m(()=>h[12]||(h[12]=[B(" 确定 ")])),_:1,__:[12]})])])]),default:m(()=>[t("div",ht,[t("div",ft,[h[5]||(h[5]=t("span",{class:"bt"},"版本：",-1)),o(te,{modelValue:a(g).subjectVal,"onUpdate:modelValue":h[0]||(h[0]=S=>a(g).subjectVal=S),onChange:n,placeholder:"请选择学科",style:{width:"200px"}},{default:m(()=>[(l(!0),i(N,null,O(a(w),(S,Q)=>(l(),J(ee,{key:Q,label:S.name,value:S.uuid},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),h[6]||(h[6]=t("span",{class:"bt",style:{"padding-left":"20px"}},"模块：",-1)),o(te,{modelValue:a(g).textbookVal,"onUpdate:modelValue":h[1]||(h[1]=S=>a(g).textbookVal=S),onChange:P,placeholder:"请选择模块",style:{width:"200px"}},{default:m(()=>[(l(!0),i(N,null,O(a(p),(S,Q)=>(l(),J(ee,{key:Q,label:S.name,value:S.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),t("div",vt,[a($).length>0?(l(),i("div",yt,[t("div",bt,[o(le,{clearable:"",modelValue:a(D),"onUpdate:modelValue":h[2]||(h[2]=S=>H(D)?D.value=S:null),style:{width:"240px"},placeholder:"请输入考点/题点"},null,8,["modelValue"])]),ce((l(),J(M,{data:a($),props:{children:"children",label:"name"},"empty-text":"暂无考点/题点数据","check-strictly":!0,"default-expand-all":"","node-key":"id",class:"hhypt-el-tree","filter-node-method":b,ref_key:"treeRef",ref:V},{default:m(({node:S,data:Q})=>[!Q.children||Q.children.length==0?(l(),i("span",gt,[o(oe,{modelValue:a(F),"onUpdate:modelValue":h[3]||(h[3]=ke=>H(F)?F.value=ke:null),onChange:ke=>I(Q)},{default:m(()=>[o(de,{title:S.label,label:S.label,value:Q.uuid},null,8,["title","label","value"])]),_:2},1032,["modelValue","onChange"])])):(l(),i("span",{key:1,title:S.label},R(S.label),9,_t))]),_:1},8,["data"])),[[Ie,a(d)]])])):ce((l(),i("div",kt,h[7]||(h[7]=[t("img",{style:{width:"200px"},src:"https://oss.hhypt.com/resources/mini-parent-helper/err.png"},null,-1),t("span",{style:{color:"#999"}},"暂无考点/题点数据",-1)]))),[[Ie,a(d)]]),h[10]||(h[10]=t("div",{class:"jiantou"},[t("img",{src:Se})],-1)),t("div",xt,[t("div",{class:"top-tig pmm2_flex_between"},[h[8]||(h[8]=t("div",{class:"t1"},"已选项",-1)),t("div",{class:"huiA",onClick:A,style:{color:"#2672FF"}},"全部清除")]),a(k).length>0?(l(),i("div",Ct,[(l(!0),i(N,null,O(a(k),(S,Q)=>(l(),i("div",{class:"item-box pmm2_flex_between",key:Q},[t("div",wt,R(S.name),1),t("div",{style:{"text-align":"right",width:"50px","font-size":"16px"},class:"huiA2",onClick:ke=>A(Q,S.uuid)},[o(j,null,{default:m(()=>[o(y)]),_:1})],8,Dt)]))),128))])):(l(),i("div",It,h[9]||(h[9]=[t("img",{style:{width:"200px"},src:"https://oss.hhypt.com/resources/mini-parent-helper/err.png"},null,-1),t("span",{style:{color:"#999"}},"未设置考点/题点数据",-1)])))])])])]),_:1},8,["modelValue"])}}}),Tt=ge($t,[["__scopeId","data-v-acebaa11"]]),At={class:"bottom-box"},Pt={class:"bottom-item bottom-item-tops pmm2_flex_acenter"},Lt={key:0,class:"bt"},Ut={key:0,class:"testPoints-select"},Bt={key:1,class:"testPoints-select on"},Nt={class:"t1 pmm2_ellipsis"},St={key:0,class:"t1 t2"},Ot={class:"icon-box"},Ft={key:0,class:"testPoints-option scrollBar"},Et={class:"pmm2_ellipsis"},zt={key:2,class:"bt"},Rt={key:3,class:"testPoints-boxs huiA"},jt={key:0,class:"testPoints-select"},Mt={key:1,class:"testPoints-select on"},Jt={class:"t1 pmm2_ellipsis"},Wt={key:0,class:"t1 t2"},Yt={class:"icon-box"},Qt={key:0,class:"testPoints-option testPoints-option2 scrollBar"},Zt={class:"pmm2_ellipsis",style:{width:"150px"}},Xt={key:0,class:"bloomLevel-box"},Gt={class:"itemBottomSele-boxs scrollBar",id:"topicMainItem-box"},Kt={key:0,class:"bottom-item pmm2_flex_acenter",style:{border:"none"}},Ht={key:1,class:"bottom-item pmm2_flex_acenter",style:{"align-items":"baseline"}},el=X({__name:"itemBottomSele",props:{question:Object,isbottom:Boolean},setup(e){const s=e;s.question.knowledges||(s.question.knowledges=[]);const c=L().isUserPaper,r=L().basicInfo,f=L().categoriesInfo,w=L().scenesInfo,p=L().cityList,n=L().currentYearList,D=q(null),V=q(null),b=q(-1),d=()=>{b.value=1,qe(()=>{D.value.opens(s.question.testPoints)})},$=()=>{b.value=2,qe(()=>{V.value.opens(s.question.knowledges)})},P=(C,u)=>{C.type=="confirm"&&(s.question[u]=C.testPointsList),b.value=-1},I=(C,u)=>{s.question[u].splice(C,1)},A=C=>{let u=JSON.parse(JSON.stringify(s.question.detailData.children||[]));if(C==x.Complex)if(u.length==0){let _=JSON.parse(JSON.stringify(s.question.detailData));_.logicTypeId||(_.logicTypeId=k.value),s.question.detailData.children=[{logicTypeId:_.logicTypeId,detailData:_}]}else s.question.detailData.children[0].logicTypeId||(s.question.detailData.children[0].logicTypeId=k.value);else if(u.length>0){let _={...s.question.detailData,...u[0].detailData};s.question.detailData.body&&(_.body=s.question.detailData.body),s.question.detailData=_}},k=we(()=>(f.find(u=>u.id==s.question.categoryId)||{logicTypeId:0}).logicTypeId),F=C=>{let u=f.find(h=>h.id==C);s.question.categoryName=u.name;let _=r.logicTypeList.find(h=>h.value==u.logicTypeId)||{};s.question.canComposite=_.canComposite,s.question.childrenNoBody=_.childrenNoBody,s.question.detailData.children||(s.question.detailData.children=JSON.parse(JSON.stringify(Je.question.detailData.children))),s.question.logicTypeId==100&&_.canComposite||(s.question.logicTypeId=Number(u.logicTypeId))};q(null),s.question.scenes?typeof s.question.scenes=="object"&&(s.question.scenes=Object.values(s.question.scenes)):s.question.scenes=[],Fe(()=>{});let g=[s.question.provinceCode,s.question.cityCode,s.question.areaCode].filter(C=>C);const v=q(g),T=C=>{C&&(s.question.provinceCode=C[0],s.question.cityCode=C[1]||"",s.question.areaCode=C[2]||"")};return(C,u)=>{const _=fe,h=he,W=Z("ArrowDown"),j=ie,K=Z("Close"),ee=ze,te=Ce,le=pe,de=me,oe=Tt,M=pt;return l(),i(N,null,[t("div",At,[t("div",Pt,[u[15]||(u[15]=t("span",{class:"bt"},"题型：",-1)),o(h,{modelValue:e.question.categoryId,"onUpdate:modelValue":u[0]||(u[0]=y=>e.question.categoryId=y),onChange:F,placeholder:"题型",style:ue(`width: ${a(L)().knowledgePointsFalg?120:180}px`)},{default:m(()=>[(l(!0),i(N,null,O(a(f),y=>(l(),J(_,{key:y.id,label:y.name,value:y.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","style"]),e.question.canComposite?(l(),i("span",Lt,"组合题：")):z("",!0),e.question.canComposite?(l(),J(h,{key:1,modelValue:e.question.logicTypeId,"onUpdate:modelValue":u[1]||(u[1]=y=>e.question.logicTypeId=y),onChange:A,placeholder:"组合题",style:ue(`width: ${a(L)().knowledgePointsFalg?100:120}px`)},{default:m(()=>[o(_,{value:100,label:"组合题"},{default:m(()=>u[11]||(u[11]=[B("组合题")])),_:1,__:[11]}),a(k)!=100?(l(),J(_,{key:0,value:a(k),label:"非组合题"},{default:m(()=>u[12]||(u[12]=[B("非组合题")])),_:1,__:[12]},8,["value"])):z("",!0)]),_:1},8,["modelValue","style"])):z("",!0),u[16]||(u[16]=t("span",{class:"bt"},"难度：",-1)),o(h,{modelValue:e.question.difficultyId,"onUpdate:modelValue":u[2]||(u[2]=y=>e.question.difficultyId=y),placeholder:"难度",style:ue(`width: ${a(L)().knowledgePointsFalg?80:100}px`)},{default:m(()=>[(l(!0),i(N,null,O(a(r).difficulties,(y,E)=>(l(),J(_,{key:E,label:y,value:Number(E)},null,8,["label","value"]))),128))]),_:1},8,["modelValue","style"]),u[17]||(u[17]=t("span",{class:"bt"},"考点/题点：",-1)),t("div",{class:re(["testPoints-boxs huiA",{on:!a(L)().knowledgePointsFalg}])},[t("div",{class:"testPoints-select-com",onClick:u[3]||(u[3]=y=>d())},[e.question.testPoints.length==0?(l(),i("div",Ut," 考点/题点 ")):(l(),i("div",Bt,[t("div",Nt,R(e.question.testPoints[0].name),1),e.question.testPoints.length>1?(l(),i("div",St,"+"+R(e.question.testPoints.length-1),1)):z("",!0)])),t("div",Ot,[o(j,null,{default:m(()=>[o(W)]),_:1})])]),u[13]||(u[13]=t("div",{class:"sanjiao"},null,-1)),e.question.testPoints.length>0?(l(),i("div",Ft,[t("ul",null,[(l(!0),i(N,null,O(e.question.testPoints,(y,E)=>(l(),i("li",{class:"pmm2_flex_between",key:y.uuid},[t("div",Et,R(y.name),1),o(j,{onClick:_e=>I(E,"testPoints"),class:"huiA2"},{default:m(()=>[o(K)]),_:2},1032,["onClick"])]))),128))])])):z("",!0)],2),a(L)().knowledgePointsFalg?(l(),i("span",zt,"知识点：")):z("",!0),a(L)().knowledgePointsFalg?(l(),i("div",Rt,[t("div",{class:"testPoints-select-com",onClick:u[4]||(u[4]=y=>$())},[e.question.knowledges.length==0?(l(),i("div",jt," 知识点 ")):(l(),i("div",Mt,[t("div",Jt,R(e.question.knowledges[0].knowledge_name),1),e.question.knowledges.length>1?(l(),i("div",Wt,"+"+R(e.question.knowledges.length-1),1)):z("",!0)])),t("div",Yt,[o(j,null,{default:m(()=>[o(W)]),_:1})])]),u[14]||(u[14]=t("div",{class:"sanjiao"},null,-1)),e.question.knowledges.length>0?(l(),i("div",Qt,[t("ul",null,[(l(!0),i(N,null,O(e.question.knowledges,(y,E)=>(l(),i("li",{class:re(["pmm2_flex_between",{on:y.bloom_level}]),key:y.uuid},[t("div",Zt,[y.bloom_level&&a(L)().bloomLevelEnabled?(l(),i("span",Xt,R(y.bloom_level),1)):z("",!0),B(" "+R(y.knowledge_name),1)]),o(j,{onClick:_e=>I(E,"knowledges"),class:"huiA2"},{default:m(()=>[o(K)]),_:2},1032,["onClick"])],2))),128))])])):z("",!0)])):z("",!0)]),t("div",Gt,[Ee(C.$slots,"default",{},void 0,!0),!e.isbottom&&!a(c)?(l(),i("div",Kt,[u[18]||(u[18]=t("span",{class:"bt"},"年份：",-1)),o(h,{modelValue:e.question.year,"onUpdate:modelValue":u[5]||(u[5]=y=>e.question.year=y),placeholder:"年份",style:{width:"120px"}},{default:m(()=>[(l(!0),i(N,null,O(a(n),(y,E)=>(l(),J(_,{key:E,label:y,value:y},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),u[19]||(u[19]=t("span",{class:"bt"},"地区：",-1)),o(ee,{options:a(p),onChange:T,modelValue:a(v),"onUpdate:modelValue":u[6]||(u[6]=y=>H(v)?v.value=y:null),placeholder:"地区",style:{width:"195px"},props:{checkStrictly:!0,children:"children",value:"code",label:"name"}},null,8,["options","modelValue"]),u[20]||(u[20]=t("span",{class:"bt"},"题源：",-1)),o(te,{modelValue:e.question.be_from,"onUpdate:modelValue":u[7]||(u[7]=y=>e.question.be_from=y),placeholder:"请输入题目来源",style:{width:"220px"}},null,8,["modelValue"])])):z("",!0),!e.isbottom&&a(w).length>0&&!a(c)?(l(),i("div",Ht,[u[21]||(u[21]=t("span",{class:"bt",style:{width:"65px",position:"relative",top:"-2px"}},"场景：",-1)),o(de,{modelValue:e.question.scenes,"onUpdate:modelValue":u[8]||(u[8]=y=>e.question.scenes=y),style:{flex:"1"}},{default:m(()=>[(l(!0),i(N,null,O(a(w),y=>(l(),J(le,{key:y.id,label:y.name,value:y.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])])):z("",!0)])]),a(b)==1?(l(),J(oe,{key:0,ref_key:"examPointRef",ref:D,onSetTestPointsList:u[9]||(u[9]=y=>P(y,"testPoints"))},null,512)):z("",!0),a(b)==2?(l(),J(M,{key:1,ref_key:"knowledgePointsRef",ref:V,onSetTestPointsList:u[10]||(u[10]=y=>P(y,"knowledges"))},null,512)):z("",!0)],64)}}}),is=ge(el,[["__scopeId","data-v-5046bf3f"]]),tl={class:"hhypt-radio-com"},ll={key:0,class:"lable-box"},ol={class:"options-box"},sl={class:"lable-box"},nl={class:"options-box"},il={class:"lable-box"},al={class:"options-box"},dl=X({__name:"hhypt-zj-blanks",props:["detailData","childrenNoBody","ckeditorCalss"],setup(e){return(s,c)=>{const r=ae;return l(),i("div",tl,[e.childrenNoBody?z("",!0):(l(),i("div",ll,[c[2]||(c[2]=t("div",{class:"bt"},"题干：",-1)),t("div",ol,[o(r,{ckeditorCalss:e.ckeditorCalss,placeholder:"请输入题干",modelValue:e.detailData.body,"onUpdate:modelValue":c[0]||(c[0]=f=>e.detailData.body=f)},null,8,["ckeditorCalss","modelValue"])])])),t("div",sl,[c[3]||(c[3]=t("div",{class:"bt"},"答案：",-1)),t("div",nl,[o(r,{ckeditorCalss:e.ckeditorCalss,placeholder:"请输入答案",modelValue:e.detailData.answer,"onUpdate:modelValue":c[1]||(c[1]=f=>e.detailData.answer=f)},null,8,["ckeditorCalss","modelValue"])])]),t("div",il,[c[4]||(c[4]=t("div",{class:"bt"},"解析：",-1)),t("div",al,[(l(!0),i(N,null,O(e.detailData.analysis,(f,w)=>(l(),i("div",{class:"lable-com",key:w},[o(r,{ckeditorCalss:e.ckeditorCalss+"2",placeholder:"请输入解析",modelValue:f.nr,"onUpdate:modelValue":p=>f.nr=p},null,8,["ckeditorCalss","modelValue","onUpdate:modelValue"])]))),128))])])])}}}),ul={class:"hhypt-radio-com"},cl={key:0,class:"lable-box"},rl={class:"options-box"},pl={class:"lable-box"},ml={class:"options-box"},hl={class:"lable-box"},fl={class:"options-box"},vl=X({__name:"hhypt-zj-trueFalse",props:["detailData","childrenNoBody","ckeditorCalss"],setup(e){return(s,c)=>{const r=ae,f=be,w=ye;return l(),i("div",ul,[e.childrenNoBody?z("",!0):(l(),i("div",cl,[c[2]||(c[2]=t("div",{class:"bt"},"题干：",-1)),t("div",rl,[o(r,{ckeditorCalss:e.ckeditorCalss,placeholder:"请输入题干",modelValue:e.detailData.body,"onUpdate:modelValue":c[0]||(c[0]=p=>e.detailData.body=p)},null,8,["ckeditorCalss","modelValue"])])])),t("div",pl,[c[5]||(c[5]=t("div",{class:"bt"},"选项：",-1)),t("div",ml,[o(w,{modelValue:e.detailData.answer,"onUpdate:modelValue":c[1]||(c[1]=p=>e.detailData.answer=p)},{default:m(()=>[o(f,{value:"T"},{default:m(()=>c[3]||(c[3]=[B("T")])),_:1,__:[3]}),o(f,{value:"F"},{default:m(()=>c[4]||(c[4]=[B("F")])),_:1,__:[4]})]),_:1},8,["modelValue"])])]),t("div",hl,[c[6]||(c[6]=t("div",{class:"bt"},"解析：",-1)),t("div",fl,[(l(!0),i(N,null,O(e.detailData.analysis,(p,n)=>(l(),i("div",{class:"lable-com",key:n},[o(r,{ckeditorCalss:e.ckeditorCalss+"2",placeholder:"请输入解析",modelValue:p.nr,"onUpdate:modelValue":D=>p.nr=D},null,8,["ckeditorCalss","modelValue","onUpdate:modelValue"])]))),128))])])])}}});function De(e){let s=["A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"];const c=n=>{if(n=="ONE")return 100;if(n=="TWO")return 50;if(n=="FOUR")return 25};e.detailData.optionLayoutId||(e.detailData.optionLayoutId="ONE");const r=()=>{(!e.detailData.options||e.detailData.options.length==0)&&(e.detailData.options=[{uuid:se(),label:"A",content:"",analysis:""},{uuid:se(),label:"B",content:"",analysis:""},{uuid:se(),label:"C",content:"",analysis:""},{uuid:se(),label:"D",content:"",analysis:""}])},f=()=>{let n=JSON.parse(JSON.stringify(e.detailData.options)),D=n.length;for(let V=0;V<D;V++)n[V].label=s[V];e.detailData.options=n};return{delOption:n=>{if(e.detailData.options.length==1)return ne({message:"至少需要保留一个选项",type:"warning"});e.detailData.options.splice(n,1),f()},addOption:n=>{e.detailData.options.splice(n+1,0,{uuid:se(),label:"",content:"",analysis:""}),f()},setWidth:c,setOptions:f,addOptions:r}}const yl={class:"hhypt-radio-com"},bl={key:0,class:"lable-box"},gl={class:"options-box"},_l={class:"lable-box"},kl={class:"options-box"},xl={class:"pmm2_flex",style:{width:"100%","flex-wrap":"wrap"}},Cl={style:{width:"32px"}},wl={style:{flex:"1",position:"relative",top:"-2px","padding-left":"5px"}},Dl={style:{width:"55px",padding:"0 8px"},class:"pmm2_flex_between options-radio-btns"},Il={class:"lable-box"},ql={class:"options-box"},Vl={class:"lable-box"},$l={class:"options-box"},Tl=X({__name:"hhypt-zj-checkbox",props:["detailData","childrenNoBody","ckeditorCalss"],setup(e){const s=e,{delOption:c,addOption:r,setWidth:f,addOptions:w}=De(s);return w(),(p,n)=>{const D=ae,V=pe,b=me,d=Z("CirclePlus"),$=ie,P=Z("Remove"),I=be,A=ye;return l(),i("div",yl,[e.childrenNoBody?z("",!0):(l(),i("div",bl,[n[3]||(n[3]=t("div",{class:"bt"},"题干：",-1)),t("div",gl,[o(D,{ckeditorCalss:e.ckeditorCalss,placeholder:"请输入题干",modelValue:e.detailData.body,"onUpdate:modelValue":n[0]||(n[0]=k=>e.detailData.body=k)},null,8,["ckeditorCalss","modelValue"])])])),t("div",_l,[n[4]||(n[4]=t("div",{class:"bt",style:{position:"relative",top:"-2px"}},"选项：",-1)),t("div",kl,[t("div",xl,[(l(!0),i(N,null,O(e.detailData.options,(k,F)=>(l(),i("div",{class:"options-com pmm2_flex options-radio-box",style:ue(`width:${a(f)(e.detailData.optionLayoutId)}%`),key:F},[t("div",Cl,[o(b,{modelValue:e.detailData.answers,"onUpdate:modelValue":n[1]||(n[1]=g=>e.detailData.answers=g)},{default:m(()=>[o(V,{value:k.label},{default:m(()=>[B(R(k.label)+".",1)]),_:2},1032,["value"])]),_:2},1032,["modelValue"])]),t("div",wl,[o(D,{ckeditorCalss:e.ckeditorCalss,placeholder:"请输入选项",modelValue:k.content,"onUpdate:modelValue":g=>k.content=g},null,8,["ckeditorCalss","modelValue","onUpdate:modelValue"])]),t("div",Dl,[o($,{size:"16",class:"huiA",onClick:g=>a(r)(F)},{default:m(()=>[o(d)]),_:2},1032,["onClick"]),o($,{size:"16",class:"huiA",onClick:g=>a(c)(F)},{default:m(()=>[o(P)]),_:2},1032,["onClick"])])],4))),128))])])]),t("div",Il,[n[8]||(n[8]=t("div",{class:"bt"},"每行选项个数设置：",-1)),t("div",ql,[o(A,{modelValue:e.detailData.optionLayoutId,"onUpdate:modelValue":n[2]||(n[2]=k=>e.detailData.optionLayoutId=k)},{default:m(()=>[o(I,{value:"ONE"},{default:m(()=>n[5]||(n[5]=[B("1")])),_:1,__:[5]}),o(I,{value:"TWO"},{default:m(()=>n[6]||(n[6]=[B("2")])),_:1,__:[6]}),o(I,{value:"FOUR"},{default:m(()=>n[7]||(n[7]=[B("4")])),_:1,__:[7]})]),_:1},8,["modelValue"])])]),t("div",Vl,[n[9]||(n[9]=t("div",{class:"bt"},"解析：",-1)),t("div",$l,[(l(!0),i(N,null,O(e.detailData.analysis,(k,F)=>(l(),i("div",{class:"lable-com",key:F},[o(D,{ckeditorCalss:e.ckeditorCalss+"2",placeholder:"请输入解析",modelValue:k.nr,"onUpdate:modelValue":g=>k.nr=g},null,8,["ckeditorCalss","modelValue","onUpdate:modelValue"])]))),128))])])])}}}),Al={class:"hhypt-radio-com"},Pl={key:0,class:"lable-box"},Ll={class:"options-box"},Ul={class:"lable-box"},Bl={class:"options-box"},Nl={class:"pmm2_flex",style:{width:"100%","flex-wrap":"wrap"}},Sl={style:{width:"32px"}},Ol={style:{flex:"1",position:"relative",top:"-2px","padding-left":"5px"}},Fl={style:{width:"55px",padding:"0 8px"},class:"pmm2_flex_between options-radio-btns"},El={class:"lable-box"},zl={class:"options-box"},Rl={class:"lable-box"},jl={class:"options-box"},Ml=X({__name:"hhypt-zj-radio",props:["detailData","childrenNoBody","ckeditorCalss"],setup(e){const s=e,{delOption:c,addOption:r,setWidth:f,addOptions:w}=De(s);return w(),(p,n)=>{const D=ae,V=be,b=ye,d=Z("CirclePlus"),$=ie,P=Z("Remove");return l(),i("div",Al,[e.childrenNoBody?z("",!0):(l(),i("div",Pl,[n[3]||(n[3]=t("div",{class:"bt"},"题干：",-1)),t("div",Ll,[o(D,{ckeditorCalss:e.ckeditorCalss,placeholder:"请输入题干",modelValue:e.detailData.body,"onUpdate:modelValue":n[0]||(n[0]=I=>e.detailData.body=I)},null,8,["ckeditorCalss","modelValue"])])])),t("div",Ul,[n[4]||(n[4]=t("div",{class:"bt",style:{position:"relative",top:"-2px"}},"选项：",-1)),t("div",Bl,[t("div",Nl,[(l(!0),i(N,null,O(e.detailData.options,(I,A)=>(l(),i("div",{class:"options-com pmm2_flex options-radio-box",style:ue(`width:${a(f)(e.detailData.optionLayoutId)}%`),key:A},[t("div",Sl,[o(b,{modelValue:e.detailData.answer,"onUpdate:modelValue":n[1]||(n[1]=k=>e.detailData.answer=k)},{default:m(()=>[o(V,{value:I.label},{default:m(()=>[B(R(I.label)+".",1)]),_:2},1032,["value"])]),_:2},1032,["modelValue"])]),t("div",Ol,[o(D,{ckeditorCalss:e.ckeditorCalss,placeholder:"请输入选项",modelValue:I.content,"onUpdate:modelValue":k=>I.content=k},null,8,["ckeditorCalss","modelValue","onUpdate:modelValue"])]),t("div",Fl,[o($,{size:"16",class:"huiA",onClick:k=>a(r)(A)},{default:m(()=>[o(d)]),_:2},1032,["onClick"]),o($,{size:"16",class:"huiA",onClick:k=>a(c)(A)},{default:m(()=>[o(P)]),_:2},1032,["onClick"])])],4))),128))])])]),t("div",El,[n[8]||(n[8]=t("div",{class:"bt"},"每行选项个数设置：",-1)),t("div",zl,[o(b,{modelValue:e.detailData.optionLayoutId,"onUpdate:modelValue":n[2]||(n[2]=I=>e.detailData.optionLayoutId=I)},{default:m(()=>[o(V,{value:"ONE"},{default:m(()=>n[5]||(n[5]=[B("1")])),_:1,__:[5]}),o(V,{value:"TWO"},{default:m(()=>n[6]||(n[6]=[B("2")])),_:1,__:[6]}),o(V,{value:"FOUR"},{default:m(()=>n[7]||(n[7]=[B("4")])),_:1,__:[7]})]),_:1},8,["modelValue"])])]),t("div",Rl,[n[9]||(n[9]=t("div",{class:"bt"},"解析：",-1)),t("div",jl,[(l(!0),i(N,null,O(e.detailData.analysis,(I,A)=>(l(),i("div",{class:"lable-com",key:A},[o(D,{ckeditorCalss:e.ckeditorCalss+"2",placeholder:"请输入解析",modelValue:I.nr,"onUpdate:modelValue":k=>I.nr=k},null,8,["ckeditorCalss","modelValue","onUpdate:modelValue"])]))),128))])])])}}}),Jl={class:"hhypt-radio-com"},Wl={class:"lable-box"},Yl={class:"options-box"},Ql={class:"lable-box"},Zl={class:"options-box"},Xl={class:"pmm2_flex",style:{width:"100%","flex-wrap":"wrap"}},Gl={style:{flex:"1",position:"relative",top:"-2px","padding-left":"5px"}},Kl={style:{width:"50px",padding:"0 8px"},class:"pmm2_flex_between options-radio-btns"},Hl={class:"lable-box"},eo={class:"bt",style:{position:"relative",top:"-2px","font-weight":"bold"}},to={class:"options-box"},lo={style:{padding:"0 8px",width:"200px"},class:"pmm2_flex_between options-radio-btns"},oo={class:"lable-box"},so={class:"options-box"},no=X({__name:"hhypt-zj-multiple",props:["detailData","ckeditorCalss"],setup(e){var D,V;const s=e,{delOption:c,addOption:r,setOptions:f}=De(s);let w=we(()=>s.detailData.options.map(b=>b.label));if(!s.detailData.options||s.detailData.options.length<7){let b=[],d=((V=(D=s.detailData)==null?void 0:D.options)==null?void 0:V.length)||0;for(let $=0;$<7-d;$++)b.push({uuid:se(),content:"",analysis:""});s.detailData.options=b,f()}if(!s.detailData.children||s.detailData.children.length<5){let b=s.detailData.children.length||0;for(let d=0;d<=4-b;d++)s.detailData.children.push({detailData:{body:"",answers:[],answer:"",analysis:[{nr:""}]}})}const p=b=>{s.detailData.children.splice(b+1,0,{detailData:{body:"",answers:[],answer:"",analysis:[{nr:""}]}})},n=b=>{if(s.detailData.children.length==1)return ne({message:"至少需要保留一个答案",type:"warning"});s.detailData.children.splice(b,1)};return(b,d)=>{const $=ae,P=ie,I=Z("Remove"),A=be,k=ye,F=ve;return l(),i("div",Jl,[t("div",Wl,[d[1]||(d[1]=t("div",{class:"bt"},"材料：",-1)),t("div",Yl,[o($,{ckeditorCalss:e.ckeditorCalss,placeholder:"请输入材料",modelValue:e.detailData.body,"onUpdate:modelValue":d[0]||(d[0]=g=>e.detailData.body=g)},null,8,["ckeditorCalss","modelValue"])])]),t("div",Ql,[d[2]||(d[2]=t("div",{class:"bt",style:{position:"relative",top:"-2px"}},"选项：",-1)),t("div",Zl,[t("div",Xl,[(l(!0),i(N,null,O(e.detailData.options,(g,v)=>(l(),i("div",{class:"options-com pmm2_flex options-radio-box",style:{width:"100%"},key:v},[t("div",null,R(g.label)+". ",1),t("div",Gl,[o($,{ckeditorCalss:e.ckeditorCalss,placeholder:"请输入选项",modelValue:g.content,"onUpdate:modelValue":T=>g.content=T},null,8,["ckeditorCalss","modelValue","onUpdate:modelValue"])]),t("div",Kl,[o(P,{size:"16",class:"huiA",onClick:T=>a(r)(v)},{default:m(()=>[o(a(xe))]),_:2},1032,["onClick"]),o(P,{size:"16",class:"huiA",onClick:T=>a(c)(v)},{default:m(()=>[o(I)]),_:2},1032,["onClick"])])]))),128))])])]),(l(!0),i(N,null,O(e.detailData.children,(g,v)=>(l(),i("div",{key:v,class:"detailData-children-box"},[t("div",Hl,[t("div",eo,"第"+R(v+1)+"题.",1),t("div",to,[o(k,{modelValue:g.detailData.answer,"onUpdate:modelValue":T=>g.detailData.answer=T},{default:m(()=>[(l(!0),i(N,null,O(a(w),(T,C)=>(l(),J(A,{value:T,key:C},{default:m(()=>[B(R(T),1)]),_:2},1032,["value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),t("div",lo,[o(F,{size:"small",icon:a(xe),onClick:T=>p(v)},{default:m(()=>d[3]||(d[3]=[B("新增小题")])),_:2,__:[3]},1032,["icon","onClick"]),o(F,{size:"small",icon:a(Be),onClick:T=>n(v)},{default:m(()=>d[4]||(d[4]=[B("删除小题")])),_:2,__:[4]},1032,["icon","onClick"])])]),t("div",oo,[d[5]||(d[5]=t("div",{class:"bt"},"解析：",-1)),t("div",so,[(l(!0),i(N,null,O(g.detailData.analysis,(T,C)=>(l(),i("div",{class:"lable-com",key:C},[o($,{ckeditorCalss:e.ckeditorCalss+"2",placeholder:"请输入解析",modelValue:T.nr,"onUpdate:modelValue":u=>T.nr=u},null,8,["ckeditorCalss","modelValue","onUpdate:modelValue"])]))),128))])])]))),128))])}}}),io={class:"main-com"},ao={key:0,class:"main-item"},uo={key:1,class:"main-item"},co={key:2,class:"main-item"},ro={key:3,class:"main-item"},po={key:4,class:"main-item"},mo={key:5,class:"main-item hhypt-radio-com"},ho={class:"lable-box",style:{"padding-bottom":"0"}},fo={class:"options-box"},vo=["id"],yo={class:"bt",style:{"font-size":"16px","font-weight":"bold"}},bo={class:"options-box"},go={style:{padding:"0 8px",width:"350px"},class:"pmm2_flex_between options-radio-btns"},_o={key:0,class:"main-item"},ko={key:1,class:"main-item"},xo={key:2,class:"main-item"},Co={key:3,class:"main-item"},wo=X({__name:"topic-main-item",props:["childItem","childItemClass","addFalg"],setup(e){const s=e,{logicTypeList:c}=L().basicInfo;let r=s.childItem.question.detailData.children||[];const f=we(()=>{let d=(c.find(I=>I.value==s.childItem.question.logicTypeId)||{}).childrenTypes||[];const $=d.reduce((I,A)=>(I[A.value]=A.label,I),{});s.childItem.question.detailData.childLogicTypeLabels=$;let P=d.length;return P>0&&s.childItem.question.detailData.children.forEach(I=>{P==1?I.logicTypeId=d[0].value:d.some(A=>A.value===I.logicTypeId)||(I.logicTypeId=void 0)}),d}),w=b=>{if(r.length==1)return ne({message:"至少需要保留一个题目",type:"warning"});Me.alert(`<p>
			<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_523_1139)"><path d="M20 10C20 11.9778 19.4135 13.9112 18.3147 15.5557C17.2159 17.2002 15.6541 18.4819 13.8268 19.2388C11.9996 19.9957 9.98891 20.1937 8.0491 19.8079C6.10929 19.422 4.32746 18.4696 2.92894 17.0711C1.53041 15.6725 0.578004 13.8907 0.192152 11.9509C-0.193701 10.0111 0.00433281 8.00043 0.761209 6.17317C1.51809 4.3459 2.79981 2.78412 4.4443 1.6853C6.08879 0.58649 8.02219 0 10 0C12.6522 0 15.1957 1.05357 17.0711 2.92893C18.9464 4.8043 20 7.34784 20 10ZM10.5818 12.2318L10.8773 4.79091C10.8773 4.55824 10.7849 4.3351 10.6203 4.17058C10.4558 4.00606 10.2327 3.91364 10 3.91364C9.76734 3.91364 9.5442 4.00606 9.37968 4.17058C9.21516 4.3351 9.12273 4.55824 9.12273 4.79091L9.41819 12.2318C9.41819 12.4364 9.68637 12.5955 10 12.5955C10.3136 12.5955 10.5727 12.4364 10.5818 12.2318ZM10 13.9091C9.8202 13.9091 9.64444 13.9624 9.49494 14.0623C9.34544 14.1622 9.22892 14.3042 9.16011 14.4703C9.09131 14.6364 9.0733 14.8192 9.10838 14.9955C9.14346 15.1719 9.23004 15.3339 9.35718 15.461C9.48432 15.5881 9.6463 15.6747 9.82265 15.7098C9.999 15.7449 10.1818 15.7269 10.3479 15.6581C10.514 15.5893 10.656 15.4727 10.7559 15.3232C10.8558 15.1737 10.9091 14.998 10.9091 14.8182C10.9091 14.5771 10.8133 14.3458 10.6428 14.1754C10.4723 14.0049 10.2411 13.9091 10 13.9091Z" fill="#FB871D"/></g><defs><clipPath id="clip0_523_1139"><rect width="20" height="20" fill="white"/></clipPath></defs></svg>
			确定删除该题目吗？<p><p style=" font-size:12px; color: #999;padding-left: 24px;margin-top: 5px;">删除后将不可恢复<p>
			`,{confirmButtonText:"确认",cancelButtonText:"取消",showCancelButton:!0,dangerouslyUseHTMLString:!0}).then(()=>{s.childItem.question.detailData.children.splice(b,1)}).catch(()=>{})},p=async b=>{let d="";f.value.length==1&&(d=f.value[0].value),s.childItem.question.detailData.children.splice(b+1,0,{logicTypeId:d,detailData:{body:"",answers:[],answer:"",analysis:[{nr:""}]}})},n=(b,d)=>{b==="up"?D(d):V(d)};function D(b){if(b===0)return ne({message:"当前题已经是第一题了",type:"warning"});[s.childItem.question.detailData.children[b],s.childItem.question.detailData.children[b-1]]=[s.childItem.question.detailData.children[b-1],s.childItem.question.detailData.children[b]]}function V(b){const d=s.childItem.question.detailData.children.length;if(b===d-1){ne({message:"当前题已经是最后一题了",type:"warning"});return}[s.childItem.question.detailData.children[b],s.childItem.question.detailData.children[b+1]]=[s.childItem.question.detailData.children[b+1],s.childItem.question.detailData.children[b]]}return(b,d)=>{const $=no,P=Ml,I=Tl,A=vl,k=dl,F=ae,g=fe,v=he,T=ve;return l(),i("div",io,[e.childItem.question.logicTypeId==a(x).MultipleX2Multiple?(l(),i("div",ao,[o($,{ckeditorCalss:e.childItemClass,detailData:e.childItem.question.detailData},null,8,["ckeditorCalss","detailData"])])):e.childItem.question.logicTypeId==a(x).SingleChoice?(l(),i("div",uo,[o(P,{ckeditorCalss:e.childItemClass,detailData:e.childItem.question.detailData},null,8,["ckeditorCalss","detailData"])])):e.childItem.question.logicTypeId==a(x).MultipleChoice?(l(),i("div",co,[o(I,{ckeditorCalss:e.childItemClass,detailData:e.childItem.question.detailData},null,8,["ckeditorCalss","detailData"])])):e.childItem.question.logicTypeId==a(x).TrueFalse?(l(),i("div",ro,[o(A,{ckeditorCalss:e.childItemClass,detailData:e.childItem.question.detailData},null,8,["ckeditorCalss","detailData"])])):[a(x).BlankFilling,a(x).FreeAnswer,a(x).EnglishWriting,a(x).ChineseWriting].includes(e.childItem.question.logicTypeId)?(l(),i("div",po,[o(k,{ckeditorCalss:e.childItemClass,detailData:e.childItem.question.detailData},null,8,["ckeditorCalss","detailData"])])):(l(),i("div",mo,[t("div",ho,[d[1]||(d[1]=t("div",{class:"bt"},"材料：",-1)),t("div",fo,[o(F,{ckeditorCalss:e.childItemClass+"2",placeholder:"请输入材料",modelValue:e.childItem.question.detailData.body,"onUpdate:modelValue":d[0]||(d[0]=C=>e.childItem.question.detailData.body=C)},null,8,["ckeditorCalss","modelValue"])])]),(l(!0),i(N,null,O(e.childItem.question.detailData.children,(C,u)=>(l(),i("div",{key:u,class:"main-item-boxs"},[t("div",{class:"lable-box lable-box-bt",id:"childrenTitle"+u},[t("div",yo,"第"+R(u+1)+"题：",1),t("div",bo,[o(v,{modelValue:C.logicTypeId,"onUpdate:modelValue":_=>C.logicTypeId=_,placeholder:"题型",style:{width:"180px"}},{default:m(()=>[(l(!0),i(N,null,O(a(f),(_,h)=>(l(),J(g,{key:h,label:_.label,value:_.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),t("div",go,[o(T,{class:"el-button-small-hhypt",size:"small",icon:a(xe),onClick:_=>p(u)},{default:m(()=>d[2]||(d[2]=[B("新增题目")])),_:2,__:[2]},1032,["icon","onClick"]),o(T,{class:"el-button-small-hhypt",size:"small",icon:a(Be),onClick:_=>w(u)},{default:m(()=>d[3]||(d[3]=[B("删除题目")])),_:2,__:[3]},1032,["icon","onClick"]),o(T,{class:"el-button-small-hhypt",size:"small",disabled:u==0,icon:a(Re),onClick:_=>n("up",u)},{default:m(()=>d[4]||(d[4]=[B("上移")])),_:2,__:[4]},1032,["disabled","icon","onClick"]),o(T,{class:"el-button-small-hhypt",size:"small",disabled:u==e.childItem.question.detailData.children.length-1,icon:a(je),onClick:_=>n("down",u)},{default:m(()=>d[5]||(d[5]=[B("下移")])),_:2,__:[5]},1032,["disabled","icon","onClick"])])],8,vo),C.logicTypeId==a(x).SingleChoice?(l(),i("div",_o,[o(P,{ckeditorCalss:e.childItemClass,detailData:C.detailData,childrenNoBody:e.childItem.question.childrenNoBody},null,8,["ckeditorCalss","detailData","childrenNoBody"])])):C.logicTypeId==a(x).MultipleChoice?(l(),i("div",ko,[o(I,{ckeditorCalss:e.childItemClass,detailData:C.detailData,childrenNoBody:e.childItem.question.childrenNoBody},null,8,["ckeditorCalss","detailData","childrenNoBody"])])):C.logicTypeId==a(x).TrueFalse?(l(),i("div",xo,[o(A,{ckeditorCalss:e.childItemClass,detailData:C.detailData,childrenNoBody:e.childItem.question.childrenNoBody},null,8,["ckeditorCalss","detailData","childrenNoBody"])])):C.logicTypeId==a(x).BlankFilling||C.logicTypeId==a(x).FreeAnswer?(l(),i("div",Co,[o(k,{ckeditorCalss:e.childItemClass,detailData:C.detailData,childrenNoBody:e.childItem.question.childrenNoBody},null,8,["ckeditorCalss","detailData","childrenNoBody"])])):z("",!0)]))),128))]))])}}}),as=ge(wo,[["__scopeId","data-v-1736146f"]]),G="api/";function ds(e){return U({url:G+"v1/knowledge/catalog",method:"post",data:e})}function us(e){return U({url:G+"v1/paper/get-elements",method:"post",data:e})}function cs(e){return U({url:G+"v1/question/search",method:"post",data:e})}function rs(e){return U({url:G+"v1/question/get-recommend",method:"post",data:e})}function ps(e){return U({url:G+"v1/question/report-problem",method:"post",data:e})}function ms(e){return U({url:G+"v1/paper/submit-download",method:"post",data:e})}function hs(e){return U({url:G+"v1/paper/check-download",method:"post",data:e})}function fs(e){return U({url:G+"v1/paper/check-savable",method:"post",data:e})}function vs(e){return U({url:G+"v1/paper/save",method:"post",data:e})}export{ms as A,hs as B,us as C,fs as D,vs as E,Wo as F,as as _,Ro as a,is as b,L as c,Ne as d,ss as e,os as f,ls as g,ns as h,Go as i,Qo as j,ds as k,es as l,Xo as m,Ho as n,Ko as o,ts as p,Je as q,Jo as r,Mo as s,jo as t,Zo as u,Ye as v,cs as w,rs as x,ps as y,Yo as z};

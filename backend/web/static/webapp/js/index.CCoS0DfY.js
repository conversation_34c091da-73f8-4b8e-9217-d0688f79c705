import{d as Ne,r as q,aK as N,b as Ce,e as A,u as t,w as J,i as Be,E as Ue,o as l,a as i,c as n,f as d,az as je,T as st,h as be,U as ot,N as nt,q as Se,t as ke,F as xe,l as fe,k as Ae,j as De,n as He,m as qe,W as $e,au as Ee,p as We,aM as Me,at as pt,S as ct,K as Ke,aC as Xe,g as yt,aN as mt}from"./index.ZZ6UQeaF.js";/* empty css                *//* empty css                  */import{_ as ft}from"./itemDom.vue_vue_type_script_setup_true_lang.CbGURBLf.js";import{_ as Le}from"./_plugin-vue_export-helper.DlAUqK2U.js";/* empty css                          */import{u as Ge,c as Ze}from"./index.6W4rOsHv.js";/* empty css                   */import{_ as gt,u as ht,q as vt}from"./index.pTwlcQaF.js";import{q as Je}from"./question.IyuOoK5G.js";/* empty css                 *//* empty css                       */import{_ as Qe}from"./index.vue_vue_type_style_index_0_lang.Cq6RDaj7.js";/* empty css                        */const rt="data:image/svg+xml,%3csvg%20width='12'%20height='12'%20viewBox='0%200%2012%2012'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cg%20clip-path='url(%23clip0_342_284)'%3e%3cpath%20d='M11.491%203.77927H0.453125V0H11.491V3.77927ZM1.29758%202.93482H10.6465V0.844453H1.29758V2.93482ZM11.491%2011.8131H0.453125V8.03615H11.491V11.8131ZM1.29758%2010.971H10.6465V8.8806H1.29758V10.971ZM0.487734%205.5697H0.903038V6.41415H0.487734V5.5697ZM10.2612%206.41415H9.48139V5.5697H10.2612V6.41415ZM8.70154%206.41415H7.92169V5.5697H8.70154V6.41415ZM7.14184%206.41415H6.36199V5.5697H7.14184V6.41415ZM5.58214%206.41415H4.80229V5.5697H5.58214V6.41415ZM4.02244%206.41415H3.24259V5.5697H4.02244V6.41415ZM2.46274%206.41415H1.68289V5.5697H2.46274V6.41415ZM11.0411%205.5697H11.4564V6.41415H11.0411V5.5697Z'%20fill='white'/%3e%3c/g%3e%3cdefs%3e%3cclipPath%20id='clip0_342_284'%3e%3crect%20width='12'%20height='12'%20fill='white'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e",kt=""+new URL("../img/icon3.DqazWU7J.svg",import.meta.url).href,Ye="data:image/svg+xml,%3csvg%20width='10'%20height='18'%20viewBox='0%200%2010%2018'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M4.80566%200.0610704C4.76594%200.0610705%204.72783%200.0768529%204.69974%200.104945L1.52209%203.2826C1.50818%203.29651%201.49714%203.31302%201.48962%203.3312C1.48209%203.34937%201.47821%203.36885%201.47821%203.38852C1.47821%203.40819%201.48209%203.42767%201.48962%203.44585C1.49714%203.46402%201.50818%203.48053%201.52209%203.49444L2.15762%204.12997C2.17153%204.14388%202.18804%204.15492%202.20622%204.16244C2.22439%204.16997%202.24387%204.17385%202.26354%204.17385C2.28321%204.17385%202.30269%204.16997%202.32087%204.16245C2.33904%204.15492%202.35555%204.14388%202.36946%204.12997L4.20652%202.29292L4.20647%207.94913C4.20647%207.98885%204.22225%208.02694%204.25034%208.05503C4.27843%208.08311%204.31652%208.09889%204.35624%208.0989L5.25509%208.0989C5.29481%208.09889%205.3329%208.08311%205.36099%208.05503C5.38908%208.02694%205.40486%207.98885%205.40486%207.94913L5.40481%202.29292L7.24187%204.12997C7.25577%204.14388%207.27229%204.15492%207.29046%204.16244C7.30864%204.16997%207.32812%204.17385%207.34779%204.17385C7.36746%204.17385%207.38694%204.16997%207.40511%204.16244C7.42329%204.15492%207.4398%204.14388%207.45371%204.12997L8.08924%203.49444C8.10315%203.48053%208.11418%203.46402%208.12171%203.44584C8.12924%203.42767%208.13311%203.40819%208.13311%203.38852C8.13311%203.36885%208.12924%203.34937%208.12171%203.3312C8.11418%203.31302%208.10315%203.29651%208.08924%203.2826L4.91159%200.104945C4.88349%200.0768528%204.84539%200.0610702%204.80566%200.0610704Z'%20fill='%232672FF'/%3e%3cpath%20d='M4.80566%2017.935C4.76594%2017.935%204.72783%2017.9192%204.69974%2017.8911L1.52209%2014.7135C1.50818%2014.6996%201.49714%2014.6831%201.48962%2014.6649C1.48209%2014.6467%201.47821%2014.6272%201.47821%2014.6076C1.47821%2014.5879%201.48209%2014.5684%201.48962%2014.5502C1.49714%2014.5321%201.50818%2014.5156%201.52209%2014.5017L2.15762%2013.8661C2.17153%2013.8522%202.18804%2013.8412%202.20622%2013.8336C2.22439%2013.8261%202.24387%2013.8222%202.26354%2013.8222C2.28321%2013.8222%202.30269%2013.8261%202.32087%2013.8336C2.33904%2013.8412%202.35555%2013.8522%202.36946%2013.8661L4.20652%2015.7032L4.20647%2010.047C4.20647%2010.0072%204.22225%209.96915%204.25034%209.94107C4.27843%209.91298%204.31652%209.8972%204.35624%209.89719L5.25509%209.89719C5.29481%209.8972%205.3329%209.91298%205.36099%209.94107C5.38908%209.96915%205.40486%2010.0072%205.40486%2010.047L5.40481%2015.7032L7.24187%2013.8661C7.25577%2013.8522%207.27229%2013.8412%207.29046%2013.8336C7.30864%2013.8261%207.32812%2013.8222%207.34779%2013.8222C7.36746%2013.8222%207.38694%2013.8261%207.40511%2013.8336C7.42329%2013.8412%207.4398%2013.8522%207.45371%2013.8661L8.08924%2014.5017C8.10315%2014.5156%208.11418%2014.5321%208.12171%2014.5502C8.12924%2014.5684%208.13311%2014.5879%208.13311%2014.6076C8.13311%2014.6272%208.12924%2014.6467%208.12171%2014.6649C8.11418%2014.6831%208.10315%2014.6996%208.08924%2014.7135L4.91159%2017.8911C4.88349%2017.9192%204.84539%2017.935%204.80566%2017.935Z'%20fill='%232672FF'/%3e%3c/svg%3e";function xt(){return{splitspaceChange:(Q,s)=>{var C;if(Q||(s.space=1),Q>1){s.children||(s.children=[]);let p=((C=s==null?void 0:s.children)==null?void 0:C.length)||0;if(p>Q){let y=p-Q;s.children.splice(Q,p-y)}else for(let y=0;y<Q-p;y++)s.children.push({score:s.score})}else delete s.children}}}const Oi=[{value:1,label:"一"},{value:2,label:"二"},{value:3,label:"三"},{value:4,label:"四"},{value:5,label:"五"},{value:6,label:"六"},{value:7,label:"七"},{value:8,label:"八"},{value:9,label:"九"},{value:10,label:"十"},{value:11,label:"十一"},{value:12,label:"十二"},{value:13,label:"十三"},{value:14,label:"十四"},{value:15,label:"十五"},{value:16,label:"十六"},{value:17,label:"十七"},{value:18,label:"十八"},{value:19,label:"十九"},{value:20,label:"二十"}],$i=["A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"],Ri={multiple:"选择题",FreeAnswer:"非选择题",BlankFilling:"填空题",EnglishWriting:"英语作文",ChineseWriting:"语文作文",Composition:"组合题"},Mi=[{id:1,name:"单选题"},{id:2,name:"多选题"},{id:4,name:"判断题"}],zi=(e=[],Q)=>new Promise((s,C)=>{e.forEach(async p=>{var w;let y="";if(p.logicTypeId==6){y=`<p>${p.topicSortNum}(${p.full_score}分).</p>`;let k=((w=p.splitChildren)==null?void 0:w.length)||0;if(k>0)p=await Ct(p,k,Q);else if(p.lineFalg)for(let $=0;$<p.lineNum;$++)y+="<hr>"}else if(p.mergeChecked){y="<p>";let k=p.spaceChline||1,$=k>1?(k-1)*4:0,H=Math.floor((Q-$)/k),W=0;p.mergeChildren=p.children,delete p.children,p.mergeChildren.forEach(async x=>{let b=x.spaceNum||1;for(let u=0;u<b;u++){let z=H;if(u==0){let c=`${x.topicSortNum}.`,E=c.length;y+=`<u>${c}`,z-=E}else y+="<u>";for(let c=0;c<z;c++)y+="&ensp;";W++,W%k==0?y+="</u></p><p>":y+="</u>&ensp;&ensp;&ensp;&ensp;"}}),p.bodys=y}else{y=`<p>${p.topicSortNum}(${p.full_score}分).</p>`;let k=p.spaceChline||1,$=await et(k,p.spaceNum,Q);y+=$}p.bodys=y}),s(e)}),Ct=(e,Q,s)=>new Promise(async(C,p)=>{if(e.splitType=="6")for(let y=0;y<Q;y++){let w=`<p>(${e.splitChildren[y].sort})</p>`;if(e.splitChildren[y].lineFalg)for(let k=0;k<e.lineNum;k++)w+="<hr>";e.splitChildren[y].bodys=w}else for(let y=0;y<Q;y++){let w=3,k=e.splitChildren[y].space,$=await et(w,k,s);e.splitChildren[y].bodys=$}C(e)}),et=(e,Q,s,C="")=>new Promise(async(p,y)=>{let w="",k=e>1?(e-1)*4:0,$=Math.floor((s-k)/e);for(let H=0;H<Q;H++){let W=$,x=H+1,b=C||`(${x})`,u=b.length;W-=u,H==0?w+=`<p><u>${b}`:w+=`<u>${b}`;for(let z=0;z<W;z++)w+="&ensp;";Q==x?w+="</u></p>":x%e==0?w+="</u></p><p>":w+="</u>&ensp;&ensp;&ensp;&ensp;"}p(w)}),St={class:"pmm2_flex_between"},bt={class:"splitQuestions-box"},It={class:"box"},Ht={class:"box pmm2_flex_between",style:{"margin-top":"10px"}},Nt={class:"com pmm2_flex_acenter"},wt={key:0},_t={key:0},Tt={key:1,class:"pmm2_flex_acenter"},Et={key:1},Lt={key:2},Bt={class:"scrollBar"},At={class:"pmm2_flex_acenter"},Jt={key:0,class:"pmm2_flex_acenter",style:{"padding-left":"10px"}},Vt={class:"pmm2_flex_acenter",style:{"align-items":"baseline"}},Ot={key:0},$t={key:1},Rt={key:0,class:"box"},Mt={key:0,class:"pmm2_flex_acenter",style:{color:"red",float:"right"}},zt={key:1,class:"pmm2_flex_acenter",style:{color:"red",float:"right"}},Ft={class:"dialog-footer"},Ut=Ne({__name:"splitQuestions",props:["paperItem","full_score","index","itemtype"],emits:["setSplit"],setup(e,{expose:Q,emit:s}){const C=q(!1),p=e;let y=p.itemtype=="answerSheet"?[]:N().questionsScoreList;const w=q(p.full_score||0),k=q({displayline:1});let $=q({space:1}),H=q([]);const{splitspaceChange:W}=xt();Q({opens:()=>{w.value=p.full_score||0,C.value=!0;let P=p.paperItem;k.value.splitType=(P==null?void 0:P.splitType)||"6";let a=(P==null?void 0:P.splitNum)||0;if(a>0)k.value.splitNum=a,p.itemtype=="answerSheet"?H.value=P.splitChildren:H.value=JSON.parse(JSON.stringify(y[p.index].children));else{k.value.splitNum=2,H.value=[];for(let h=0;h<2;h++)H.value.push({sort:h+1,space:1,step_score:P.step_score,uuid:Ge()})}}});const b=q(!1),u=q(""),z=s,c=P=>{b.value=!0,u.value=P,setTimeout(()=>{b.value=!1},5e3)};let E="<p>&nbsp&nbsp</p>";const G=async()=>{let P=0;for(let h=0;h<H.value.length;h++){let re=H.value[h];if(!re.score&&N().setting.scoringResultTypeId=="score")return c("请设置"+p.paperItem.topicSortNum+"("+re.sort+")题分数！");if(P+=re.score,k.value.splitType==6){let ue=p.itemtype=="single"?`<p class="topicSortNum">(${h+1})</p>${E}`:`<p class="topicSortNum">(${h+1}).</p>${E}`;if(p.itemtype=="answerSheet"&&re.lineFalg)for(let F=0;F<re.lineNum;F++)ue+="<hr>";re.bodys=ue}else{let ue=N().storeEnspNum,F=re.space,j=3,ie=`(${h+1})`+(p.itemtype=="single"?"":"."),me=await et(j,F,ue,ie);re.bodys=me}}if(p.itemtype=="answerSheet"&&(w.value=P),w.value!=P&&N().setting.scoringResultTypeId=="score")return c("拆分小题总分不等于当前题分数，请重新输入！");$.value={space:1};let a={splitChildren:H.value,...k.value};p.itemtype=="answerSheet"&&(a.full_score=P),z("setSplit",a),C.value=!1},f=P=>{let a=[];for(let h=0;h<P;h++){let re=H.value[h]||{},ue={sort:h+1,step_score:p.paperItem.step_score,...re};ue.uuid||(ue.uuid=Ge()),ue.space||(ue.space=1),a.push(ue)}H.value=a},S=P=>{H.value.forEach(a=>{a.space=P||1,W(P,a)})},_=P=>{H.value.forEach(a=>{a.score=P||0,V()})},V=(P,a)=>{p.itemtype=="answerSheet"&&(w.value=0,H.value.forEach(h=>{w.value+=h.score||0}))},m=P=>{H.value.forEach(a=>{a.lineFalg=P})},te=P=>{H.value.forEach(a=>{a.lineNum=P})},X=(P,a)=>{k.lineFalg=!1},ae=(P,a)=>{k.lineNum=void 0};return(P,a)=>{const h=fe("CloseBold"),re=Ae,ue=qe,F=je,j=st,ie=ot,me=nt,ge=fe("Warning"),we=De,ze=Ue;return t(C)?(l(),Ce(ze,{key:0,"append-to-body":"",class:"hhypt-dialog-boxs2","show-close":!1,modelValue:t(C),"onUpdate:modelValue":a[8]||(a[8]=O=>Be(C)?C.value=O:null),title:"拆分小题",width:"800"},{header:J(({close:O,titleClass:Z})=>[i("div",St,[i("span",{class:He(Z)},"拆分小题 (第"+ke(e.paperItem.topicSortNum)+"题，满分"+ke(t(w))+"分)",3),d(ue,{onClick:O,underline:!1},{default:J(()=>[d(re,{size:"20"},{default:J(()=>[d(h)]),_:1})]),_:2},1032,["onClick"])])]),footer:J(()=>[i("div",Ft,[d(we,{style:{width:"70px"},onClick:a[7]||(a[7]=O=>C.value=!1)},{default:J(()=>a[20]||(a[20]=[be("取消")])),_:1,__:[20]}),d(we,{style:{width:"70px"},type:"primary",onClick:G},{default:J(()=>a[21]||(a[21]=[be(" 确定 ")])),_:1,__:[21]})])]),default:J(()=>[i("div",bt,[i("div",It,[a[9]||(a[9]=i("span",{class:"name"}," 拆分数量 ",-1)),d(F,{onChange:f,step:1,"step-strictly":!0,min:2,modelValue:t(k).splitNum,"onUpdate:modelValue":a[0]||(a[0]=O=>t(k).splitNum=O),style:{width:"100px"},"controls-position":"right"},null,8,["modelValue"])]),i("div",Ht,[i("div",Nt,[a[12]||(a[12]=i("span",{class:"name"},"小题样式",-1)),d(ie,{modelValue:t(k).splitType,"onUpdate:modelValue":a[1]||(a[1]=O=>t(k).splitType=O)},{default:J(()=>[d(j,{value:"6"},{default:J(()=>a[10]||(a[10]=[be("解答")])),_:1,__:[10]}),d(j,{value:"3"},{default:J(()=>a[11]||(a[11]=[be("填空")])),_:1,__:[11]})]),_:1},8,["modelValue"])]),e.itemtype=="answerSheet"?(l(),n("div",wt,[t(k).splitType==3?(l(),n("div",_t,[a[13]||(a[13]=i("span",{class:"name"},"每题空格",-1)),d(F,{onChange:S,modelValue:t($).space,"onUpdate:modelValue":a[2]||(a[2]=O=>t($).space=O),min:1,style:{width:"100px"},step:1,"step-strictly":!0,"controls-position":"right"},null,8,["modelValue"]),a[14]||(a[14]=i("span",{style:{"padding-left":"5px"}},"个",-1))])):t(k).splitType==6?(l(),n("div",Tt,[d(me,{onChange:m,modelValue:t(k).lineFalg,"onUpdate:modelValue":a[3]||(a[3]=O=>t(k).lineFalg=O),label:"每题添加长横线："},null,8,["modelValue"]),d(F,{onChange:te,"controls-position":"right",style:{width:"80px"},"step-strictly":!0,modelValue:t(k).lineNum,"onUpdate:modelValue":a[4]||(a[4]=O=>t(k).lineNum=O),min:1,step:1},null,8,["modelValue"])])):A("",!0)])):t(k).splitType==3?(l(),n("div",Et,[a[15]||(a[15]=i("span",{class:"name"},"每题空格",-1)),d(F,{onChange:S,modelValue:t($).space,"onUpdate:modelValue":a[5]||(a[5]=O=>t($).space=O),min:1,style:{width:"100px"},step:1,"step-strictly":!0,"controls-position":"right"},null,8,["modelValue"]),a[16]||(a[16]=i("span",{style:{"padding-left":"5px"}},"个",-1))])):A("",!0),t(N)().setting.scoringResultTypeId=="score"?(l(),n("div",Lt,[a[17]||(a[17]=i("span",{class:"name"},"每题分值",-1)),d(F,{onChange:_,step:e.paperItem.step_score,"step-strictly":!0,modelValue:t($).score,"onUpdate:modelValue":a[6]||(a[6]=O=>t($).score=O),style:{width:"100px"},"controls-position":"right"},null,8,["step","modelValue"])])):A("",!0)]),i("ul",Bt,[(l(!0),n(xe,null,Se(t(H),(O,Z)=>(l(),n("li",{key:Z,class:"pmm2_flex_between",style:{"align-items":"baseline"}},[i("div",At,[i("div",null,ke(e.paperItem.topicSortNum)+"("+ke(O.sort)+")",1),t(k).splitType==6&&e.itemtype=="answerSheet"?(l(),n("div",Jt,[d(me,{onChange:he=>X(he,O),modelValue:O.lineFalg,"onUpdate:modelValue":he=>O.lineFalg=he,label:"每题添加长横线："},null,8,["onChange","modelValue","onUpdate:modelValue"]),d(F,{onChange:he=>ae(he,O),"controls-position":"right",style:{width:"80px"},"step-strictly":!0,modelValue:O.lineNum,"onUpdate:modelValue":he=>O.lineNum=he,min:1,step:1},null,8,["onChange","modelValue","onUpdate:modelValue"])])):A("",!0)]),i("div",Vt,[t(k).splitType==3?(l(),n("div",Ot,[d(F,{onChange:he=>t(W)(he,O),modelValue:O.space,"onUpdate:modelValue":he=>O.space=he,step:1,"step-strictly":!0,min:1,style:{width:"100px"},"controls-position":"right"},null,8,["onChange","modelValue","onUpdate:modelValue"]),a[18]||(a[18]=i("span",{style:{padding:"0 5px"}},"空,",-1))])):A("",!0),t(N)().setting.scoringResultTypeId=="score"?(l(),n("div",$t,[i("div",null,[d(F,{onChange:he=>V(he,O),step:e.paperItem.step_score,"step-strictly":!0,modelValue:O.score,"onUpdate:modelValue":he=>O.score=he,style:{width:"100px"},"controls-position":"right"},null,8,["onChange","step","modelValue","onUpdate:modelValue"]),a[19]||(a[19]=i("span",{style:{"padding-left":"5px"}},"分",-1))])])):A("",!0)])]))),128))]),t(k).splitType==3?(l(),n("div",Rt,[t(b)?(l(),n("div",Mt,[d(re,null,{default:J(()=>[d(ge)]),_:1}),be(ke(t(u)),1)])):A("",!0)])):t(b)?(l(),n("div",zt,[d(re,null,{default:J(()=>[d(ge)]),_:1}),be(ke(t(u)),1)])):A("",!0)])]),_:1},8,["modelValue"])):A("",!0)}}}),ut=Le(Ut,[["__scopeId","data-v-de4bc218"]]),qt="data:image/svg+xml,%3csvg%20width='14'%20height='14'%20viewBox='0%200%2014%2014'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M10.6423%203.26277C10.6423%203.02106%2010.4438%202.82078%2010.1883%202.82078H9.06259V2.86567C9.06259%203.10738%208.86404%203.30766%208.60852%203.30766H4.77393C4.52531%203.30766%204.31986%203.10911%204.31986%202.86567V2.82078H2.97145C2.72284%202.82078%202.51738%203.01933%202.51738%203.26277V11.6674C2.51738%2011.9091%202.71593%2012.1094%202.97145%2012.1094H6.58332L7.48456%2012.9917H2.52429C2.02705%2012.9917%201.62305%2012.5946%201.62305%2012.1094V2.82424C1.62305%202.33391%202.02705%201.94199%202.52429%201.94199H4.32676C4.32676%201.70028%204.52531%201.5%204.78084%201.5H8.61542C8.86404%201.5%209.0695%201.69855%209.0695%201.94199H10.6475C11.1448%201.94199%2011.5488%202.33909%2011.5488%202.82424V7.22684L10.6475%208.14535L10.6423%203.26277ZM9.14028%206.24273H4.01945C3.86233%206.24273%203.73285%206.11497%203.73285%205.96476V5.68679C3.73285%205.53659%203.86061%205.40882%204.01945%205.40882H9.14201C9.29912%205.40882%209.42861%205.53659%209.42861%205.68679V5.96476C9.42688%206.11497%209.29912%206.24273%209.14028%206.24273ZM9.42688%208.34908C9.42688%208.49928%209.29912%208.62704%209.14028%208.62704H4.01945C3.86233%208.62704%203.73285%208.49928%203.73285%208.34908V8.07111C3.73285%207.9209%203.86061%207.79314%204.01945%207.79314H9.14201C9.29912%207.79314%209.42861%207.9209%209.42861%208.07111V8.34908H9.42688ZM7.46729%2010.3415C7.61405%2010.3415%207.74699%2010.4054%207.84022%2010.5055L8.99008%2011.5621L11.5125%208.87566C11.6075%208.77552%2011.7387%208.71337%2011.8854%208.71337C12.1755%208.71337%2012.412%208.96544%2012.412%209.27621C12.412%209.42815%2012.3533%209.57145%2012.2601%209.67159L9.36991%2012.7569C9.27495%2012.857%209.14374%2012.9192%208.99698%2012.9192C8.85023%2012.9192%208.71729%2012.8553%208.62405%2012.7569L7.10472%2011.2997C7.00977%2011.1995%206.95279%2011.058%206.95279%2010.9043C6.94071%2010.5953%207.17206%2010.3415%207.46729%2010.3415Z'%20fill='white'/%3e%3c/svg%3e",Qt={class:"score-box pmm2_flex_acenter"},Wt={key:0,class:"score-row pmm2_flex_acenter"},Gt={key:0},jt={class:"pmm2_flex_between"},Dt={style:{"padding-top":"20px"}},Pt={class:"pmm2_flex_between"},Zt={class:"dialog-footer"},Kt=Ne({__name:"scoringbox",props:["step_score","uuid","full_score","splitType","HhyptType"],emits:["setStepScore"],setup(e,{emit:Q}){const s=e,C=q(!1),p=N().pageStyles,y=q(s.step_score),w=()=>{let W=parseFloat(s.full_score);if(W%1==0)C.value=!0;else return We.error(`当前分值：${W} 分数间隔为0.5 不允许修改分数间隔`)},k=$e(()=>{let W=[],x=parseFloat(s.full_score);if(x>0){if(x>9){let u=Math.floor(x/10);for(let z=1;z<=u;z++)W.push(z);W.reverse(),W.push("　"),x=9}let b=Math.floor(x);for(b;b>=0;b--)W.push(b);return s.step_score!=1&&(W.push("　"),W.push(.5)),W}else return[]}),$=Q,H=()=>{$("setStepScore",y.value),C.value=!1};return(W,x)=>{const b=fe("CloseBold"),u=Ae,z=qe,c=je,E=De,G=Ue;return l(),n("div",null,[i("div",Qt,[t(k).length>0?(l(),n("div",Wt,[e.splitType!=3?(l(),n("div",{key:0,class:"setting-btn huiA pmm2_flex_center",onClick:w},x[3]||(x[3]=[i("img",{src:qt,alt:""},null,-1),i("div",{style:{"padding-left":"5px"}},"设置打分框",-1)]))):A("",!0),(l(!0),n(xe,null,Se(t(k),(f,S)=>{var _,V,m,te;return l(),n("div",{class:He(["score-item",{"score-on":["answerSheet","singleCards","writing"].includes(e.HhyptType)}]),style:Ee(`min-width: ${(_=t(p))==null?void 0:_.subjectiveLineBoxSize[0]}px;width:${(V=t(p))==null?void 0:V.subjectiveLineBoxSize[0]}px;line-height:${(m=t(p))==null?void 0:m.subjectiveLineBoxSize[1]}px;height:${(te=t(p))==null?void 0:te.subjectiveLineBoxSize[1]}px;`),key:S},ke(f),7)}),128))])):A("",!0)]),e.uuid&&t(C)?(l(),n("div",Gt,[d(G,{"append-to-body":"",class:"hhypt-dialog-boxs","show-close":!1,modelValue:t(C),"onUpdate:modelValue":x[2]||(x[2]=f=>Be(C)?C.value=f:null),title:"打分框设置",width:"500"},{header:J(({close:f,titleClass:S})=>[i("div",jt,[i("span",{class:He(S)},"打分框设置(满分："+ke(e.full_score)+" 分)",3),d(z,{onClick:f,underline:!1},{default:J(()=>[d(u,{size:"20"},{default:J(()=>[d(b)]),_:1})]),_:2},1032,["onClick"])])]),footer:J(()=>[i("div",Pt,[x[8]||(x[8]=i("div",null,null,-1)),i("div",Zt,[d(E,{style:{width:"70px"},onClick:x[1]||(x[1]=f=>C.value=!1)},{default:J(()=>x[6]||(x[6]=[be("取消")])),_:1,__:[6]}),d(E,{style:{width:"70px"},type:"primary",onClick:H},{default:J(()=>x[7]||(x[7]=[be(" 确定 ")])),_:1,__:[7]})])])]),default:J(()=>[i("div",Dt,[i("div",null,[x[4]||(x[4]=i("span",null,"打分框分数间隔 ",-1)),d(c,{modelValue:t(y),"onUpdate:modelValue":x[0]||(x[0]=f=>Be(y)?y.value=f:null),min:.5,max:1,step:.5,"step-strictly":"","controls-position":"right"},null,8,["modelValue"])]),x[5]||(x[5]=i("div",{style:{"padding-top":"20px"}}," 说明：打分框分数间隔只允许0.5 、1。 ",-1))])]),_:1},8,["modelValue"])])):A("",!0)])}}}),Xt=Le(Kt,[["__scopeId","data-v-6ffaf6b7"]]),Yt="data:image/png;base64,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",el={class:"scoreNo-com pmm2_flex_acenter score-row"},tl={class:"scoreNo-item score-item"},ll={class:"scoreNo-item score-item",style:{"border-right":"none"}},il=Ne({__name:"scoreNo",props:["HhyptType"],setup(e){return(Q,s)=>{const C=fe("Select"),p=Ae,y=fe("CloseBold");return l(),n("div",{class:He(["scoreNo-box",{on:e.HhyptType=="singleCards"}])},[i("div",el,[i("div",tl,[s[0]||(s[0]=i("span",null,"2",-1)),d(p,null,{default:J(()=>[d(C)]),_:1})]),s[2]||(s[2]=i("div",{class:"scoreNo-item score-item"},[i("span",null,"1"),i("img",{src:Yt,style:{width:"20px",height:"25px"}})],-1)),i("div",ll,[s[1]||(s[1]=i("span",null,"0",-1)),d(p,null,{default:J(()=>[d(y)]),_:1})])])],2)}}}),sl=Le(il,[["__scopeId","data-v-18e9935e"]]),ol=Ne({__name:"scorehandwritten",props:["HhyptType"],setup(e){return(Q,s)=>(l(),n("div",{class:He(["scorehandwritten-box",{on:e.HhyptType=="singleCards"}])},s[0]||(s[0]=[i("div",{class:"scorehandwritten-com pmm2_flex_acenter"},[i("div",{class:"scorehandwritten-wz"},"得分"),i("div",{class:"scorehandwritten-val"})],-1)]),2))}}),nl=Le(ol,[["__scopeId","data-v-cac1d4b9"]]),Oe=Ne({__name:"index",props:["uuid","step_score","full_score","splitType","HhyptType"],emits:["setStepScore"],setup(e,{emit:Q}){const s=Q,C=p=>{s("setStepScore",p)};return(p,y)=>(l(),n("div",null,[t(N)().setting.scoringResultTypeId=="right_wrong"?(l(),Ce(sl,{key:0,HhyptType:e.HhyptType},null,8,["HhyptType"])):t(N)().setting.subjectiveScoreTypeId=="LINE"?(l(),Ce(Xt,{key:1,uuid:e.uuid,step_score:e.step_score,splitType:e.splitType,onSetStepScore:C,full_score:e.full_score},null,8,["uuid","step_score","splitType","full_score"])):(l(),Ce(nl,{key:2,HhyptType:e.HhyptType},null,8,["HhyptType"]))]))}}),rl={class:"resizable-box",style:{"padding-bottom":"10px"}},ul={key:0,ref:"ckeditorRef"},dl={key:1},al={key:2,class:"balnks-edit balnks-edit2"},pl={key:4,class:"wordNum-box",id:"writingId"},cl={key:0,class:"wordNum-com pmm2_flex_center"},yl=Ne({__name:"hhypt-writinge",props:["paperItem","uuid","HhyptType"],emits:["onEditorBlur","topicDialogOpens"],setup(e,{emit:Q}){const s=e,C=Me("CkeditorStyles");let p=s.uuid||"",y,w;p&&(y=N().scoreSetting.findIndex(f=>f.uuid==s.paperItem.H1uuid),w=N().scoreSetting[y].children.findIndex(f=>f.uuid==p));const k=s.HhyptType=="singleCards"?1:0,$=f=>{s.paperItem.step_score=f,N().scoreSetting[y].children[w].step_score=f,E({key:"stepScore",step_score:f,h:!1})};let H=-1;const W=$e(()=>{let f=0;return s.uuid?(H==-1&&(H=N().questionsScoreList.findIndex(S=>S.uuid==s.uuid)),f=N().questionsScoreList[H].full_score,f):0}),x=q(null),b=q(s.paperItem.wordNumCopy||s.paperItem.wordNum),u=f=>{E({key:"wordNum",wordNum:f,h:!0})},z=()=>{b.value=5,E({key:"wordNum",wordNum:5,h:!0})},c=Q,E=f=>{f.key&&f.key!="bodys"&&x.value&&x.value.isEditingFun(),c("onEditorBlur",f)},G=()=>{c("topicDialogOpens")};return(f,S)=>{const _=Qe,V=fe("MessageBox"),m=Ae,te=je;return l(),n("div",rl,[e.paperItem.bodys?(l(),n("div",ul,[d(_,{ckeditorType:"card",ref_key:"CkeditorDomRef",ref:x,styles:t(C),HhyptType:e.HhyptType,modelValue:e.paperItem.bodys,"onUpdate:modelValue":S[0]||(S[0]=X=>e.paperItem.bodys=X),onOnEditorBlur:S[1]||(S[1]=X=>E({key:"bodys",...X})),uuid:e.uuid},null,8,["styles","HhyptType","modelValue","uuid"])],512)):A("",!0),e.paperItem.toBodySplit?A("",!0):(l(),n("div",dl,[i("div",{class:He(["scoringbox-box",{"scoringbox-boxs-on":e.HhyptType=="writing"}])},[d(Oe,{uuid:e.uuid,step_score:e.paperItem.step_score,onSetStepScore:$,full_score:t(W),HhyptType:e.HhyptType,class:"scoringbox-boxs"},null,8,["uuid","step_score","full_score","HhyptType"])],2)])),e.HhyptType=="singleCards"?(l(),n("div",al,[i("div",{class:"btn huiA pmm2_flex_center",onClick:S[2]||(S[2]=X=>G())},[d(m,null,{default:J(()=>[d(V)]),_:1}),S[4]||(S[4]=i("span",{style:{"padding-left":"5px"}},"查看原题",-1))])])):A("",!0),t(b)==0?(l(),n("div",{key:3,class:"balnks-edit",onClick:z},S[5]||(S[5]=[i("div",{class:"btn1 huiA pmm2_flex_center"},[i("img",{src:rt,alt:""}),i("span",{style:{"padding-left":"5px"}},"添加答题区域")],-1)]))):A("",!0),t(b)!=0?(l(),n("div",pl,[(l(!0),n(xe,null,Se(e.paperItem.wordNum,X=>(l(),n("div",{key:X,class:"p"}))),128)),!e.paperItem.toBodySplit&&e.HhyptType!="writing"?(l(),n("div",cl,[d(te,{size:"small",style:{width:"80px",margin:"0 5px"},modelValue:t(b),"onUpdate:modelValue":S[3]||(S[3]=X=>Be(b)?b.value=X:null),step:1,max:25,min:t(k),"step-strictly":"",onChange:u},null,8,["modelValue","min"]),S[6]||(S[6]=i("span",null,"行",-1))])):A("",!0)])):A("",!0)])}}}),ml=Le(yl,[["__scopeId","data-v-ff3a641f"]]),fl={key:2,class:"writingc-box",id:"writingId"},gl={key:3,class:"balnks-edit"},hl={class:"pmm2_flex_between"},vl={style:{"padding-top":"20px"}},kl={style:{"margin-top":"20px"}},xl={class:"pmm2_flex_between"},Cl={class:"dialog-footer"},Sl=Ne({__name:"hhypt-writingc",props:["paperItem","uuid","HhyptType"],emits:["onEditorBlur","topicDialogOpens"],setup(e,{emit:Q}){const s=e,C=Me("CkeditorStyles");let p=s.uuid||"",y,w;p&&s.HhyptType!="writing"&&(y=N().scoreSetting.findIndex(a=>a.uuid==s.paperItem.H1uuid),w=N().scoreSetting[y].children.findIndex(a=>a.uuid==p));const k=q(null),$=a=>{s.paperItem.step_score=a,N().scoreSetting[y].children[w].step_score=a,ae({key:"stepScore",step_score:a,h:!1})},H=s.paperItem.wordKing||1,W=s.paperItem.wordNumTo||0;s.paperItem.minWordNum||s.paperItem.wordNum;const x=a=>{let h=a+W;if(H==1){if(h%100==0)return h}else if(H==3){if(h%200==0)return h}else if(h==s.paperItem.minWordNum)return h};let b=-1;const u=$e(()=>{let a=0;return s.uuid?(b==-1&&(b=N().questionsScoreList.findIndex(h=>h.uuid==s.uuid)),a=N().questionsScoreList[b].full_score,a):0}),z=q(!1),c=q(s.paperItem.wordNum),E=q(0),G=q(null),f=q(!1),S=s.paperItem.compositionWidth+10;pt(()=>{if(c.value>0){let a=G.value.offsetWidth;E.value=Math.floor(a/s.paperItem.compositionWidth),c.value%E.value>0&&(c.value+=E.value-c.value%E.value),f.value=!0}});const _=q(0),V=q(20),m=()=>{V.value=s.paperItem.compositionSize||20,_.value=s.paperItem.wordNumCopy||s.paperItem.wordNum,z.value=!0},te=()=>{z.value=!1,ae({key:"wordNum",wordNum:_.value,compositionSize:V.value,h:!0})},X=Q,ae=a=>{a.key&&a.key!="bodys"&&k.value&&k.value.isEditingFun(),X("onEditorBlur",a)},P=()=>{X("topicDialogOpens")};return(a,h)=>{const re=Qe,ue=fe("Setting"),F=Ae,j=fe("MessageBox"),ie=fe("CloseBold"),me=qe,ge=je,we=De,ze=Ue;return l(),n("div",{class:"resizable-box",ref_key:"writingcBoxref",ref:G},[e.paperItem.bodys?(l(),Ce(re,{key:0,ckeditorType:"card",ref_key:"CkeditorDomRef",ref:k,styles:t(C),modelValue:e.paperItem.bodys,"onUpdate:modelValue":h[0]||(h[0]=O=>e.paperItem.bodys=O),HhyptType:e.HhyptType,onOnEditorBlur:h[1]||(h[1]=O=>ae({key:"bodys",...O})),uuid:e.uuid},null,8,["styles","modelValue","HhyptType","uuid"])):A("",!0),e.paperItem.toBodySplit?A("",!0):(l(),n("div",{key:1,class:He({"scoringbox-boxs-on":e.HhyptType=="writing"})},[d(Oe,{uuid:e.uuid,step_score:e.paperItem.step_score,onSetStepScore:$,full_score:t(u),HhyptType:e.HhyptType,class:"scoringbox-boxs"},null,8,["uuid","step_score","full_score","HhyptType"])],2)),t(f)?(l(),n("div",fl,[(l(!0),n(xe,null,Se(t(c),O=>(l(),n("div",{class:"rows",key:O,style:Ee(`width: ${e.paperItem.compositionWidth}px;height: ${S}px;`)},[i("div",{class:He(["words",{on:O%t(E)==0}])},[i("b",null,ke(x(O)),1)],2)],4))),128))])):A("",!0),e.HhyptType!="writing"?(l(),n("div",gl,[i("div",{class:"btn huiA pmm2_flex_center",onClick:h[2]||(h[2]=O=>m())},[d(F,null,{default:J(()=>[d(ue)]),_:1}),h[8]||(h[8]=i("span",{style:{"padding-left":"5px"}},"字数设置",-1))]),e.HhyptType=="singleCards"?(l(),n("div",{key:0,class:"btn huiA pmm2_flex_center",onClick:h[3]||(h[3]=O=>P())},[d(F,null,{default:J(()=>[d(j)]),_:1}),h[9]||(h[9]=i("span",{style:{"padding-left":"5px"}},"查看原题",-1))])):A("",!0)])):A("",!0),d(ze,{"append-to-body":"",class:"hhypt-dialog-boxs","show-close":!1,modelValue:t(z),"onUpdate:modelValue":h[7]||(h[7]=O=>Be(z)?z.value=O:null),title:"字数设置",width:"500"},{header:J(({close:O,titleClass:Z})=>[i("div",hl,[i("span",{class:He(Z)},[h[10]||(h[10]=be("字数设置")),i("span",null,"(满分："+ke(t(u))+" 分)",1)],2),d(me,{onClick:O,underline:!1},{default:J(()=>[d(F,{size:"20"},{default:J(()=>[d(ie)]),_:1})]),_:2},1032,["onClick"])])]),footer:J(()=>[i("div",xl,[h[15]||(h[15]=i("div",null,null,-1)),i("div",Cl,[d(we,{style:{width:"70px"},onClick:h[6]||(h[6]=O=>z.value=!1)},{default:J(()=>h[13]||(h[13]=[be("取消")])),_:1,__:[13]}),d(we,{style:{width:"70px"},type:"primary",onClick:te},{default:J(()=>h[14]||(h[14]=[be(" 确定 ")])),_:1,__:[14]})])])]),default:J(()=>[i("div",vl,[i("div",null,[h[11]||(h[11]=i("span",null,"字数设置 ",-1)),d(ge,{modelValue:t(_),"onUpdate:modelValue":h[4]||(h[4]=O=>Be(_)?_.value=O:null),min:100,step:1,"step-strictly":"","controls-position":"right"},null,8,["modelValue"])]),i("div",kl,[h[12]||(h[12]=i("span",null,"每行字数 ",-1)),d(ge,{modelValue:t(V),"onUpdate:modelValue":h[5]||(h[5]=O=>Be(V)?V.value=O:null),min:10,step:1,"step-strictly":"","controls-position":"right"},null,8,["modelValue"])])])]),_:1},8,["modelValue"])],512)}}}),bl=Le(Sl,[["__scopeId","data-v-b8f368c3"]]),Il={class:"pmm2_flex_between"},Hl={class:"topicDialog-box scrollBar"},Nl=Ne({__name:"topicDialog",setup(e,{expose:Q}){const s=q(!1),C=q([]);return Q({opens:(y=[])=>{C.value=y,s.value=!0}}),(y,w)=>{const k=fe("CloseBold"),$=Ae,H=qe,W=Ue;return l(),Ce(W,{"append-to-body":"",class:"hhypt-dialog-boxs hhypt-dialog-boxs2","show-close":!1,modelValue:t(s),"onUpdate:modelValue":w[0]||(w[0]=x=>Be(s)?s.value=x:null),title:"查看原题",width:"800"},{header:J(({close:x,titleClass:b})=>[i("div",Il,[i("span",{class:He(b)},"查看原题",2),d(H,{onClick:x,underline:!1},{default:J(()=>[d($,{size:"20"},{default:J(()=>[d(k)]),_:1})]),_:2},1032,["onClick"])])]),default:J(()=>[i("div",Hl,[(l(!0),n(xe,null,Se(t(C),(x,b)=>(l(),n("div",{key:b},[d(ft,{childItem:x,class:"ckeditorBoxs"},null,8,["childItem"])]))),128))])]),_:1},8,["modelValue"])}}}),wl=Le(Nl,[["__scopeId","data-v-885eeaba"]]),_l={class:"pmm2_flex_between"},Tl={class:"topicDialog-box scrollBar"},El={class:"pmm2_flex_between"},Ll={class:"dialog-footer"},Bl=Ne({__name:"mergeTapDialog",emits:["confirm"],setup(e,{expose:Q,emit:s}){const C=q(!1),p=q({}),y=q({}),w=q([]),k=q([]);let $=0;Q({opens:(b,u)=>{$=(N().storeEnspNum-30)/3,p.value=JSON.parse(JSON.stringify(b));let c=b.freeList;u.fuuid?c=b.freeList.filter(E=>!E.mergeFlag&&E.fuuid==u.fuuid&&E.logicTypeId==3):c=b.freeList.filter(E=>!E.mergeFlag&&!E.fuuid&&E.logicTypeId==3),w.value=c,y.value=u,k.value=[u.uuid],C.value=!0}});const W=s,x=()=>{let b=k.value.length;if(b<2)return We.error("至少选择两道题进行合并");const{questionsScoreList:u}=N();let z=JSON.parse(JSON.stringify(p.value)),c=[],E=[];for(let j=0;j<b;j++){let ie=k.value[j],me=z.freeList.findIndex(we=>we.uuid==ie),ge=z.freeList[me];ge.type=="merge"?(c.push(...ge.mergeChildren),z.freeList.splice(me,1),E.push(ge.uuid)):c.push(JSON.parse(JSON.stringify(ge))),ge.mergeFlag=!0}c.sort((j,ie)=>j.topicSortNum-ie.topicSortNum);let G=c[0],f=G.topicSortNum,S=c.length,V=(u.find(j=>j.uuid==G.uuid)||{}).full_score||0;const m=JSON.parse(JSON.stringify(N().scoreSetting));let te={},X=m.findIndex(j=>j.uuid==G.H1uuid),ae=m[X].children.findIndex(j=>j.uuid==G.uuid);te=m[X].children[ae]||{},m[X].children[ae].mergeFlag=!0;let P=[{...te}],a="";for(let j=0;j<$;j++)a+="&ensp;";let h=`<span class="topicSortNum">${G.topicSortNum}-${c[S-1].topicSortNum}.</span>`,re=`<u>(${G.topicSortNum}).${a}</u>`;h+=`${re}&ensp;&ensp;&ensp;&ensp;&ensp;`;for(let j=1;j<S;j++){if(f++,f!=c[j].topicSortNum)return We.error("只能合并连续的题号");let ie=u.find(we=>we.uuid==c[j].uuid)||{};V+=ie.full_score||0;let me=m[X].children.findIndex(we=>we.uuid==c[j].uuid);me!=-1&&(P.push(JSON.parse(JSON.stringify(m[X].children[me]))),m[X].children[me].mergeFlag=!0);let ge=`<u>(${c[j].topicSortNum}).${a}</u>`;h+=`${ge}&ensp;&ensp;&ensp;&ensp;&ensp;`}let ue={...G,type:"merge",uuid:Ge(),topicSort:`${G.topicSort}-${c[S-1].topicSort}`,topicSortH1:`${G.topicSortH1}-${c[S-1].topicSortH1}`,topicSortNum:`${G.topicSortNum}-${c[S-1].topicSortNum}`,mergeChildren:c,bodys:h,blankH:40};m[X].children.splice(ae,0,{...ue,mergeChildren:P,questionUuid:te.questionUuid,full_score:V});for(let j=0;j<E.length;j++){let ie=m[X].children.findIndex(ge=>ge.uuid==E[j]);m[X].children.splice(ie,1);let me=N().questionsScoreList.findIndex(ge=>ge.uuid==E[j]);N().questionsScoreList.splice(me,1)}N().scoreSetting=m,N().questionsScoreList.push({uuid:ue.uuid,H1uuid:ue.H1uuid,mergeChildren:P,full_score:V});let F=z.freeList.findIndex(j=>j.uuid==G.uuid);z.freeList.splice(F,0,ue),W("confirm",z,ue.uuid),C.value=!1};return(b,u)=>{const z=fe("CloseBold"),c=Ae,E=qe,G=nt,f=ct,S=De,_=Ue;return l(),Ce(_,{"append-to-body":"",class:"hhypt-dialog-boxs hhypt-dialog-boxs2","show-close":!1,modelValue:t(C),"onUpdate:modelValue":u[2]||(u[2]=V=>Be(C)?C.value=V:null),title:"题目合并",width:"800"},{header:J(({close:V,titleClass:m})=>[i("div",_l,[i("span",{class:He(m)},"题目合并",2),d(E,{onClick:V,underline:!1},{default:J(()=>[d(c,{size:"20"},{default:J(()=>[d(z)]),_:1})]),_:2},1032,["onClick"])])]),footer:J(()=>[i("div",El,[u[5]||(u[5]=i("div",null,null,-1)),i("div",Ll,[d(S,{style:{width:"70px"},onClick:u[1]||(u[1]=V=>C.value=!1)},{default:J(()=>u[3]||(u[3]=[be("取消")])),_:1,__:[3]}),d(S,{style:{width:"70px"},type:"primary",onClick:x},{default:J(()=>u[4]||(u[4]=[be(" 确定 ")])),_:1,__:[4]})])])]),default:J(()=>[i("div",Tl,[d(f,{modelValue:t(k),"onUpdate:modelValue":u[0]||(u[0]=V=>Be(k)?k.value=V:null)},{default:J(()=>[(l(!0),n(xe,null,Se(t(w),(V,m)=>(l(),Ce(G,{border:"",disabled:V.logicTypeId!=3||V.splitNum,key:m,value:V.uuid,style:{"margin-top":"15px"}},{default:J(()=>[be(ke(`第${V.topicSortNum}题`),1)]),_:2},1032,["disabled","value"]))),128))]),_:1},8,["modelValue"])])]),_:1},8,["modelValue"])}}}),Al=Le(Bl,[["__scopeId","data-v-2a0b2c71"]]),Jl={class:"btnList pmm2_flex_acenter"},Vl={class:"options-list-box"},Ol={class:"options-item"},$l=["title"],Rl={class:"options pmm2_flex_acenter"},Ml=Ne({__name:"single-radio",props:["itemInfo","pageWidth","radioMaxNum"],emits:["onEditorBlur"],setup(e,{emit:Q}){const s=e,C=[1,2,3,4,5],p=q([]),y=s.itemInfo.optionList||[],w=q(null),k=q(N().setting.pageLayout.printLayout.column==3?"33%":"25%"),$=s.itemInfo.maxOptionsLen;$>s.radioMaxNum-1?k.value="100%":$>(s.radioMaxNum-2)/2?k.value="50%":$>=7&&(k.value="33%");const H=Q,W=(x,b)=>{p.value=Ze(y,x,!0),b!=2&&H("onEditorBlur",{key:"groupSize",groupSize:x,h:!0})};return W(s.itemInfo.groupSize,2),(x,b)=>{const u=st,z=ot;return l(),n("div",{class:"singleRadio-box pmm2_flex_between",ref_key:"singleRadioRef",ref:w},[i("div",Jl,[b[1]||(b[1]=i("span",null,"每组题数：",-1)),d(z,{modelValue:e.itemInfo.groupSize,"onUpdate:modelValue":b[0]||(b[0]=c=>e.itemInfo.groupSize=c),onChange:W},{default:J(()=>[(l(),n(xe,null,Se(C,c=>d(u,{key:c,value:c},{default:J(()=>[be(ke(c),1)]),_:2},1032,["value"])),64))]),_:1},8,["modelValue"])]),i("div",Vl,[(l(!0),n(xe,null,Se(t(p),(c,E)=>(l(),n("div",{class:"options-list-com",style:Ee("width:"+t(k)),key:E},[(l(!0),n(xe,null,Se(c,(G,f)=>(l(),n("div",{class:"options-list",key:f},[i("div",Ol,[i("div",{class:"topicSort",style:{cursor:"default"},title:G.full_score?G.full_score+"分":""},ke(G.topicSortNum),9,$l),i("div",Rl,[(l(!0),n(xe,null,Se(G.options,(S,_)=>(l(),n("div",{class:"hhypt-label-item",key:_},ke(S),1))),128))])])]))),128))],4))),128))])],512)}}}),zl=Le(Ml,[["__scopeId","data-v-576dfe84"]]);function tt(e,Q,s,C){let y="";const w=()=>{if(!y){const f=N().storeEnspNum;for(let S=0;S<f;S++)S==0&&(y="<p><u>"),y+="&ensp;";y+="</u></p>"}e.itemInfo.bodys+=y,H({key:"bodys",h:!0})},k=f=>{var V;let S=0;(V=s.value)!=null&&V.offsetHeight&&(S=s.value.offsetHeight);let _=f-S;e.itemInfo.blankH!=_&&(e.itemInfo.blankH=_,H({key:"blankH",blankH:_,h:!0}))},$=q(null),H=f=>{f.key&&f.key!="bodys"&&$.value&&$.value.isEditingFun(),Q("onEditorBlur",f)},W=$e(()=>{var S,_;let f=e.itemInfo.blankH;return e.itemInfo.toBodySplit||(f+=e.itemInfo.bodysH),((_=(S=e.itemInfo)==null?void 0:S.splitChildren)==null?void 0:_.length)>0&&(f+=e.itemInfo.splitH),f});let x,b;const u=e.itemInfo.uuid;x=N().scoreSetting.findIndex(f=>f.uuid==e.itemInfo.H1uuid),b=N().scoreSetting[x].children.findIndex(f=>f.uuid==u);const z=$e(()=>{let f=0;if(!u)return 0;if(C.value=N().questionsScoreList.findIndex(S=>S.uuid==u),C.value!=-1){let S=N().questionsScoreList[C.value];S?S.children?f=S.children.reduce((_,V)=>_+V.score,0):f=S.full_score:C.value=-1}return f});return{uuids:u,full_scores:z,setStepScore:f=>{e.itemInfo.step_score=f,N().scoreSetting[x].children[b].step_score=f,H({key:"stepScore",step_score:f,h:!1})},MI:4.75,minHeight:W,onEditorBlur:H,resizableFun:k,addXian:w,setSplit:f=>{if(e.HhyptType=="answerSheet"){let _=N().scoreSetting[x].children;if(_[b].full_score!=f.full_score){N().scoreSetting[x].children[b].full_score=f.full_score;let V=0;_.forEach(m=>{V+=m.full_score}),N().scoreSetting[x].full_score=V,f.bodys=`<p>${e.itemInfo.topicSortNum}(${f.full_score}分).</p>`}}for(let S in f)e.itemInfo[S]=f[S];console.log(e.itemInfo),N().questionsScoreList[C.value].children=f.splitChildren,N().scoreSetting[x].children[b].children=f.splitChildren,H({key:"split",splitFalg:!0,obj:f,h:!0})},splitCancel:()=>{e.itemInfo.splitH=0,delete e.itemInfo.splitNum,delete e.itemInfo.splitChildren,delete e.itemInfo.displayline,delete e.itemInfo.splitType,delete N().scoreSetting[x].children[b].children,delete N().questionsScoreList[C.value].children,H({key:"split",splitFalg:!1,h:!0})}}}const Fl={key:1,ref:"splitChildrenRef"},Ul={key:0,class:"splitChildren-box"},ql={key:1,style:{"flex-wrap":"wrap"},class:"pmm2_flex_acenter splitChildren-box"},Ql={key:2},Wl={key:0,class:"scoringbox-box scoringbox-boxs"},Gl={key:3,class:"mouse-box",id:"resizableId"},jl=Ne({__name:"single-free",props:["itemInfo","pageWidth","isPreview","HhyptType"],emits:["onEditorBlur","topicDialogOpens","mergeTap"],setup(e,{emit:Q}){const s=e,C=Me("CkeditorStyles"),p=Q,y=q(null);let w=q(-1);const k=()=>{p("topicDialogOpens")},{uuids:$,full_scores:H,minHeight:W,onEditorBlur:x,resizableFun:b,addXian:u,setSplit:z,splitCancel:c}=tt(s,p,y,w),E=V=>{var te;if(w.value=N().questionsScoreList.findIndex(X=>X.uuid==$),w.value==-1)return 0;let m=N().questionsScoreList[w.value]||{};if(m!=null&&m.children){let X=m.children.findIndex(ae=>ae.uuid==V.uuid);return X==-1?0:((te=m.children[X])==null?void 0:te.score)||0}else return 0},G=q(null),f=()=>{N().setting.scoringResultTypeId=="right_wrong"||H.value>0?G.value.opens():We({message:"请先设置题目分数！",type:"warning"})},S=()=>{p("mergeTap",s.itemInfo)},_=(V,m)=>{let te=N().scoreSetting.findIndex(ae=>ae.uuid==s.itemInfo.H1uuid),X=N().scoreSetting[te].children.findIndex(ae=>ae.uuid==$);m||m===0?(s.itemInfo.splitChildren[m].step_score=V,N().scoreSetting[te].children[X].children[m].step_score=V):N().scoreSetting[te].children[X].step_score=V,s.itemInfo.step_score=V,x({key:"stepScore",step_score:V,idx:m,h:!1})};return(V,m)=>{var re,ue;const te=Qe,X=fe("SemiSelect"),ae=Ae,P=fe("MessageBox"),a=fe("CopyDocument"),h=Xe("resizable");return Ke((l(),n("div",{class:"resizable-box",style:Ee({minHeight:t(W)+"px"})},[e.itemInfo.toBodySplit?A("",!0):(l(),n("div",{key:0,ref_key:"ckeditorRef",ref:y,class:"ckeditorBoxs"},[d(te,{ckeditorType:"card",styles:t(C),modelValue:e.itemInfo.bodys,"onUpdate:modelValue":m[0]||(m[0]=F=>e.itemInfo.bodys=F),uuid:e.itemInfo.uuid,onOnEditorBlur:m[1]||(m[1]=F=>t(x)({key:"bodys",...F})),ref:"CkeditorDomRef"},null,8,["styles","modelValue","uuid"])],512)),((ue=(re=e.itemInfo)==null?void 0:re.splitChildren)==null?void 0:ue.length)>0?(l(),n("div",Fl,[e.itemInfo.splitType==6?(l(),n("div",Ul,[(l(!0),n(xe,null,Se(e.itemInfo.splitChildren,(F,j)=>(l(),n("div",{key:j,class:"splitChildrenId"},[d(te,{ckeditorType:"card",styles:t(C),modelValue:F.bodys,"onUpdate:modelValue":ie=>F.bodys=ie,uuid:F.uuid,onOnEditorBlur:ie=>t(x)({key:"splitbodys",...ie,id:F.uuid,sbodys:F.bodys})},null,8,["styles","modelValue","onUpdate:modelValue","uuid","onOnEditorBlur"]),t(N)().setting.subjectiveScoreTypeId=="LINE"?(l(),Ce(Oe,{key:0,uuid:t($),step_score:F.step_score,class:"scoringbox-boxs on",onSetStepScore:ie=>_(ie,j),full_score:E(F),HhyptType:e.HhyptType},null,8,["uuid","step_score","onSetStepScore","full_score","HhyptType"])):A("",!0)]))),128))])):(l(),n("div",ql,[(l(!0),n(xe,null,Se(e.itemInfo.splitChildren,(F,j)=>(l(),n("div",{key:j,style:Ee([{position:"relative"},`min-width: ${100/e.itemInfo.displayline}%;`]),class:"splitChildrenId"},[d(te,{ckeditorType:"card",styles:t(C),modelValue:F.bodys,"onUpdate:modelValue":ie=>F.bodys=ie,uuid:F.uuid,onOnEditorBlur:ie=>t(x)({key:"splitbodys",...ie,id:F.uuid,sbodys:F.bodys})},null,8,["styles","modelValue","onUpdate:modelValue","uuid","onOnEditorBlur"]),d(Oe,{class:"scoringbox-boxs on",splitType:e.itemInfo.splitType,uuid:t($),HhyptType:e.HhyptType,step_score:F.step_score,onSetStepScore:ie=>_(ie,j),full_score:E(F)},null,8,["splitType","uuid","HhyptType","step_score","onSetStepScore","full_score"])],4))),128))]))],512)):A("",!0),e.itemInfo.Noresizable?A("",!0):(l(),n("div",Ql,[(!e.itemInfo.splitChildren||e.itemInfo.splitChildren.length==0)&&!e.itemInfo.splitFalg?(l(),n("div",Wl,[d(Oe,{uuid:t($),step_score:e.itemInfo.step_score,onSetStepScore:_,full_score:t(H),HhyptType:e.HhyptType},null,8,["uuid","step_score","full_score","HhyptType"])])):A("",!0)])),e.itemInfo.toBodySplits?A("",!0):(l(),n("div",Gl,m[7]||(m[7]=[i("img",{src:Ye,style:{width:"20px",height:"25px"},alt:"",srcset:""},null,-1)]))),e.HhyptType=="singleCards"||e.itemInfo.logicTypeId==6?(l(),n("div",{key:4,class:He(["balnks-edit",{on:e.itemInfo.splitNum}])},[e.HhyptType=="singleCards"?(l(),n("div",{key:0,class:"btn huiA pmm2_flex_center",onClick:m[2]||(m[2]=F=>t(u)())},[d(ae,null,{default:J(()=>[d(X)]),_:1}),m[8]||(m[8]=i("span",{style:{"padding-left":"5px"}},"添加横线",-1))])):A("",!0),e.HhyptType=="singleCards"||e.itemInfo.logicTypeId==6?(l(),n("div",{key:1,class:"btn1 huiA pmm2_flex_center",onClick:m[3]||(m[3]=F=>f())},m[9]||(m[9]=[i("img",{src:rt,alt:""},null,-1),i("span",{style:{"padding-left":"5px"}},"拆分题目",-1)]))):A("",!0),e.itemInfo.splitNum?(l(),n("div",{key:2,class:"btn huiA pmm2_flex_center",onClick:m[4]||(m[4]=(...F)=>t(c)&&t(c)(...F))},m[10]||(m[10]=[i("img",{src:kt,alt:""},null,-1),i("span",{style:{"padding-left":"5px"}},"取消拆分",-1)]))):A("",!0),e.HhyptType=="singleCards"?(l(),n("div",{key:3,class:"btn huiA pmm2_flex_center",onClick:m[5]||(m[5]=F=>k())},[d(ae,null,{default:J(()=>[d(P)]),_:1}),m[11]||(m[11]=i("span",{style:{"padding-left":"5px"}},"查看原题",-1))])):A("",!0),e.itemInfo.logicTypeId==3&&!e.itemInfo.splitNum&&e.HhyptType=="singleCards"?(l(),n("div",{key:4,class:"btn huiA pmm2_flex_center",onClick:m[6]||(m[6]=F=>S())},[d(ae,null,{default:J(()=>[d(a)]),_:1}),m[12]||(m[12]=i("span",{style:{"padding-left":"5px"}},"合并题目",-1))])):A("",!0)],2)):A("",!0),d(ut,{ref_key:"splitQuestionsRef",ref:G,index:t(w),full_score:t(H),onSetSplit:t(z),paperItem:e.itemInfo,itemtype:e.HhyptType},null,8,["index","full_score","onSetSplit","paperItem","itemtype"])],4)),[[h,{fun:t(b)}]])}}}),Dl=Le(jl,[["__scopeId","data-v-e61948dd"]]),Pl={key:1},Zl={key:0,class:"scoringbox-box scoringbox-boxs"},Kl={key:2,class:"mouse-box",id:"resizableId"},Xl=Ne({__name:"single-merge",props:["itemInfo","pageWidth","isPreview","HhyptType"],emits:["onEditorBlur","topicDialogOpens","mergeTap","cancelMergeTap"],setup(e,{emit:Q}){const s=e,C=Me("CkeditorStyles"),p=Q,y=q(null);let w=q(-1);const k=()=>{p("topicDialogOpens")},{uuids:$,full_scores:H,setStepScore:W,minHeight:x,onEditorBlur:b,resizableFun:u,addXian:z,setSplit:c}=tt(s,p,y,w),E=q(null),G=()=>{p("mergeTap",s.itemInfo)},f=()=>{p("cancelMergeTap")};return(S,_)=>{const V=Qe,m=fe("SemiSelect"),te=Ae,X=fe("MessageBox"),ae=fe("CopyDocument"),P=fe("Notification"),a=Xe("resizable");return Ke((l(),n("div",{class:"resizable-box",style:Ee({minHeight:t(x)+"px"})},[e.itemInfo.bodys?(l(),n("div",{key:0,ref_key:"ckeditorRef",ref:y,class:"ckeditorBoxs"},[d(V,{ckeditorType:"card",styles:t(C),modelValue:e.itemInfo.bodys,"onUpdate:modelValue":_[0]||(_[0]=h=>e.itemInfo.bodys=h),uuid:e.itemInfo.uuid,onOnEditorBlur:_[1]||(_[1]=h=>t(b)({key:"bodys",...h})),ref:"CkeditorDomRef"},null,8,["styles","modelValue","uuid"])],512)):A("",!0),e.itemInfo.Noresizable?A("",!0):(l(),n("div",Pl,[(!e.itemInfo.splitChildren||e.itemInfo.splitChildren.length==0)&&!e.itemInfo.splitFalg?(l(),n("div",Zl,[d(Oe,{HhyptType:e.HhyptType,uuid:t($),step_score:e.itemInfo.step_score,onSetStepScore:t(W),full_score:t(H)},null,8,["HhyptType","uuid","step_score","onSetStepScore","full_score"])])):A("",!0)])),e.itemInfo.toBodySplits?A("",!0):(l(),n("div",Kl,_[6]||(_[6]=[i("img",{src:Ye,style:{width:"20px",height:"25px"},alt:"",srcset:""},null,-1)]))),i("div",{class:He(["balnks-edit",{on:e.itemInfo.splitNum}])},[i("div",{class:"btn huiA pmm2_flex_center",onClick:_[2]||(_[2]=h=>t(z)())},[d(te,null,{default:J(()=>[d(m)]),_:1}),_[7]||(_[7]=i("span",{style:{"padding-left":"5px"}},"添加横线",-1))]),e.HhyptType=="singleCards"?(l(),n("div",{key:0,class:"btn huiA pmm2_flex_center",onClick:_[3]||(_[3]=h=>k())},[d(te,null,{default:J(()=>[d(X)]),_:1}),_[8]||(_[8]=i("span",{style:{"padding-left":"5px"}},"查看原题",-1))])):A("",!0),i("div",{class:"btn huiA pmm2_flex_center",onClick:_[4]||(_[4]=h=>G())},[d(te,null,{default:J(()=>[d(ae)]),_:1}),_[9]||(_[9]=i("span",{style:{"padding-left":"5px"}},"合并题目",-1))]),i("div",{class:"btn huiA pmm2_flex_center",onClick:_[5]||(_[5]=h=>f())},[d(te,null,{default:J(()=>[d(P)]),_:1}),_[10]||(_[10]=i("span",{style:{"padding-left":"5px"}},"取消合并",-1))])],2),d(ut,{ref_key:"splitQuestionsRef",ref:E,index:t(w),full_score:t(H),onSetSplit:t(c),paperItem:e.itemInfo,itemtype:"single"},null,8,["index","full_score","onSetSplit","paperItem"])],4)),[[a,{fun:t(u)}]])}}}),Yl=Le(Xl,[["__scopeId","data-v-346d5367"]]),ei={key:0},ti={class:"scoringbox-box scoringbox-boxs"},li={key:1,class:"mouse-box",id:"resizableId"},ii={class:"balnks-edit"},si=Ne({__name:"single-all",props:["itemInfo","pageWidth","isPreview","HhyptType"],emits:["onEditorBlur","topicDialogOpens"],setup(e,{emit:Q}){const s=e,C=Me("CkeditorStyles"),p=Q,y=q(null);let w=q(-1);const k=()=>{p("topicDialogOpens")},{uuids:$,full_scores:H,setStepScore:W,minHeight:x,onEditorBlur:b,resizableFun:u,addXian:z}=tt(s,p,y,w);return(c,E)=>{const G=Qe,f=fe("SemiSelect"),S=Ae,_=fe("MessageBox"),V=Xe("resizable");return Ke((l(),n("div",{class:"resizable-box",style:Ee({minHeight:t(x)+"px"})},[i("div",{ref_key:"ckeditorRef",ref:y,class:"ckeditorBoxs"},[e.itemInfo.bodys?(l(),Ce(G,{key:0,ckeditorType:"card",styles:t(C),modelValue:e.itemInfo.bodys,"onUpdate:modelValue":E[0]||(E[0]=m=>e.itemInfo.bodys=m),onOnEditorBlur:E[1]||(E[1]=m=>t(b)({key:"bodys",...m})),ref:"CkeditorDomRef",uuid:e.itemInfo.uuid},null,8,["styles","modelValue","uuid"])):A("",!0)],512),e.itemInfo.Noresizable?A("",!0):(l(),n("div",ei,[i("div",ti,[d(Oe,{uuid:t($),step_score:e.itemInfo.step_score,onSetStepScore:t(W),full_score:t(H),HhyptType:e.HhyptType},null,8,["uuid","step_score","onSetStepScore","full_score","HhyptType"])])])),e.itemInfo.toBodySplits?A("",!0):(l(),n("div",li,E[4]||(E[4]=[i("img",{src:Ye,style:{width:"20px",height:"25px"},alt:"",srcset:""},null,-1)]))),i("div",ii,[e.HhyptType=="singleCards"?(l(),n("div",{key:0,class:"btn huiA pmm2_flex_center",onClick:E[2]||(E[2]=m=>t(z)())},[d(S,null,{default:J(()=>[d(f)]),_:1}),E[5]||(E[5]=i("span",{style:{"padding-left":"5px"}},"添加横线",-1))])):A("",!0),e.HhyptType=="singleCards"?(l(),n("div",{key:1,class:"btn huiA pmm2_flex_center",onClick:E[3]||(E[3]=m=>k())},[d(S,null,{default:J(()=>[d(_)]),_:1}),E[6]||(E[6]=i("span",{style:{"padding-left":"5px"}},"查看原题",-1))])):A("",!0)])],4)),[[V,{fun:t(u)}]])}}}),oi=Le(si,[["__scopeId","data-v-c5537e16"]]),ni={class:"topic-boxs singleCards-boxs"},ri={class:"page-content"},ui=["id"],di={key:0,class:"question-name",style:{"font-size":"17px"}},ai={key:1},pi={key:0},ci={key:1},yi={key:2},mi={key:3},fi=["id"],gi={key:5,class:"writings-boxs"},hi=Ne({__name:"item",props:{list:Object,index:Number,pageLayoutName:String,examNumberLength:Number,pageWidth:Number,radioMaxNum:Number,isPreview:Boolean,layout:{type:Object}},emits:["onEditorBlur","topicDialogOpens","mergeTap","cancelMergeTap"],setup(e,{emit:Q}){let s=N().pageStyles;Me("CkeditorStyles");const C=Q,p=(H,W,x)=>{C("onEditorBlur",H,W,x)},y=H=>{C("topicDialogOpens",H)},w=H=>{C("mergeTap",H)},k=H=>{C("cancelMergeTap",H)},$=(H,W)=>{H&&C("onEditorBlur",{key:"H1",title:H,uuid:W,h:!0})};return(H,W)=>{const x=gt,b=yt;return l(),n("div",ni,[e.index%e.layout.headNum==0?(l(),Ce(x,{key:0,pageLayoutName:e.pageLayoutName,layout:e.layout,index:e.index,examNumberLength:e.examNumberLength},null,8,["pageLayoutName","layout","index","examNumberLength"])):A("",!0),i("div",ri,[(l(!0),n(xe,null,Se(e.list,(u,z)=>(l(),n("div",{key:(u.uuid||"-")+(u.type||"-")+z,class:"page-box",id:"paperId"+e.index},[u.itemTypeId==t(Je).H1headline&&!u.isFalg?(l(),n("div",di,[e.layout.HhyptType=="singleCards"?(l(),Ce(b,{key:0,modelValue:u.title,"onUpdate:modelValue":c=>u.title=c,type:"textarea",onChange:c=>$(c,u.uuid),class:"el-textareas break-word",placeholder:"请输入标题",autosize:""},null,8,["modelValue","onUpdate:modelValue","onChange"])):(l(),n("span",ai,[be(ke(u.title)+" ",1),u.full_score!==void 0?(l(),n("span",pi,"("+ke(u.full_score)+"分)",1)):A("",!0)]))])):u.type=="radio"&&!u.isFalg?(l(),n("div",ci,[d(zl,{pageWidth:e.pageWidth,onOnEditorBlur:c=>p(c,u),radioMaxNum:e.radioMaxNum,itemInfo:u,HhyptType:e.layout.HhyptType},null,8,["pageWidth","onOnEditorBlur","radioMaxNum","itemInfo","HhyptType"])])):u.logicTypeId==12||u.logicTypeId==14?(l(),n("div",yi,[d(oi,{pageWidth:e.pageWidth,isPreview:e.isPreview,onOnEditorBlur:c=>p(c,u),itemInfo:u,onTopicDialogOpens:c=>y(u),HhyptType:e.layout.HhyptType},null,8,["pageWidth","isPreview","onOnEditorBlur","itemInfo","onTopicDialogOpens","HhyptType"])])):u.type=="free"&&!u.mergeFlag?(l(),n("div",mi,[d(Dl,{pageWidth:e.pageWidth,isPreview:e.isPreview,onTopicDialogOpens:c=>y(u),onMergeTap:w,onOnEditorBlur:c=>p(c,u),itemInfo:u,HhyptType:e.layout.HhyptType},null,8,["pageWidth","isPreview","onTopicDialogOpens","onOnEditorBlur","itemInfo","HhyptType"])])):u.type=="merge"?(l(),n("div",{key:4,id:"merge-"+u.uuid},[d(Yl,{pageWidth:e.pageWidth,isPreview:e.isPreview,onTopicDialogOpens:c=>y(u),onMergeTap:w,onCancelMergeTap:c=>k(u),onOnEditorBlur:c=>p(c,u),itemInfo:u,HhyptType:e.layout.HhyptType},null,8,["pageWidth","isPreview","onTopicDialogOpens","onCancelMergeTap","onOnEditorBlur","itemInfo","HhyptType"])],8,fi)):u.type=="writing"?(l(),n("div",gi,[u.logicTypeId==t(Je).EnglishWriting?(l(),Ce(ml,{key:0,onOnEditorBlur:c=>p(c,u),uuid:u.uuid,paperItem:u,HhyptType:e.layout.HhyptType,onTopicDialogOpens:c=>y(u)},null,8,["onOnEditorBlur","uuid","paperItem","HhyptType","onTopicDialogOpens"])):u.logicTypeId==t(Je).ChineseWriting?(l(),Ce(bl,{onOnEditorBlur:c=>p(c,u),uuid:u.uuid,paperItem:u,onTopicDialogOpens:c=>y(u),HhyptType:e.layout.HhyptType,key:u.wordNum},null,8,["onOnEditorBlur","uuid","paperItem","onTopicDialogOpens","HhyptType"])):A("",!0)])):A("",!0)],8,ui))),128))]),e.pageLayoutName!="A4_2"?(l(),n("div",{key:1,class:"page-number",style:Ee(`height: ${t(s).pageNumberHeight}px;line-height: ${t(s).pageNumberHeight}px;`)},[i("span",null,"第"+ke(e.index+1)+"页 共"+ke(e.layout.paperListArrLen)+"页",1)],4)):A("",!0)])}}}),it=Le(hi,[["__scopeId","data-v-6cffbd77"]]),vi=["id"],ki={key:4,class:"newline-box"},xi={key:5,class:"newline-preview-box",style:{height:"0"}},Ci=Ne({__name:"index",props:{questionsArr:{type:Array,default:()=>[]},cardList:Object,isPreview:Boolean,layout:{type:Object}},setup(e,{expose:Q}){const s=e;let C=N().pageStyles,p=N().setting;const y=q([]),w=q([]),k=$e(()=>{var v;return((v=w.value)==null?void 0:v.length)||0}),{CkeditorStyles:$,shouldApplyClass:H,topicBoxItemStyle:W,topicComStyle:x,pageWidth:b,splitContentBodys:u,domFun:z,setHeight:c,headHeight:E,pageHeight:G,questionNumberingTypeIdChanges:f,getMarke:S,getZBFun:_,container:V,toFixeds:m,getContainerHeightAfterImagesLoad:te}=ht(s);mt("CkeditorStyles",$);const X=q(null),ae=v=>{if(v.type=="merge"&&!v.fuuid){let I=[];v.mergeChildren.forEach(R=>{let o=y.value.find(r=>r.content==R.uuid)||{};o!=null&&o.question&&I.push(o.question)}),X.value.opens(I)}else{let I=y.value.find(R=>R.content==(v.fuuid||v.uuid))||{};I!=null&&I.question&&X.value.opens([I.question])}},P=$e(()=>({"--template-line":N().pageStyles.DashedLine?"dashed":"solid"})),a=v=>{var o;let I=JSON.parse(JSON.stringify(Z.value)),R=I.length;if(v)for(let r=0;r<R;r++)r==0||r==1?I[r].isFalg=!1:(I[r].type=="radio"||((o=I[r+1])==null?void 0:o.type)=="radio"&&I[r].itemTypeId=="H1"&&(!I[r+2]||I[r+2].itemTypeId=="H1"))&&(I[r].isFalg=!0);else for(let r=0;r<R;r++)r==0||r==1?I[r].isFalg=!0:I[r].isFalg=!1;Ve(I,!0),setTimeout(()=>{me.value++},200)},h=q(null);let re=-1;const ue=v=>{let I=JSON.parse(JSON.stringify(Z.value));re=I.findIndex(R=>R.H1uuid==v.H1uuid&&R.type=="free"),h.value.opens(I[re],v)},F=(v,I)=>{let R=JSON.parse(JSON.stringify(Z.value));R.splice(re,1,v),Ve(R,!0),setTimeout(()=>{me.value++,setTimeout(()=>{ie("merge-"+I)},500)},200)},j=v=>{let I=JSON.parse(JSON.stringify(Z.value)),R=I.findIndex(B=>B.H1uuid==v.H1uuid&&B.type=="free"),o=I[R].freeList.findIndex(B=>B.uuid==v.uuid),r=I[R].freeList[o].uuid,T=I[R].freeList[o].H1uuid;I[R].freeList[o].mergeChildren.forEach(B=>{let de=I[R].freeList.findIndex(ye=>ye.uuid==B.uuid);de!=-1&&(I[R].freeList[de].mergeFlag=!1)}),I[R].freeList.splice(o,1);let ee=N().questionsScoreList.findIndex(B=>B.uuid==r);N().questionsScoreList.splice(ee,1);const se=N().scoreSetting;let M=se.findIndex(B=>B.uuid==T),oe=se[M].children.findIndex(B=>B.uuid==r);se[M].children[oe].mergeChildren.forEach(B=>{let de=se[M].children.findIndex(ye=>ye.uuid==B.uuid);N().scoreSetting[M].children[de].mergeFlag=!1}),N().scoreSetting[M].children.splice(oe,1),N().scoreSetting[M].full_score=N().scoreSetting[M].children.reduce((B,de)=>B+de.full_score,0),Ve(I,!0),setTimeout(()=>{me.value++},200)},ie=v=>{const I=document.getElementById(v);I&&I.scrollIntoView({behavior:"smooth"})};let me=q(1);const ge=v=>{y.value=f(v,y.value);let I=JSON.parse(JSON.stringify(Z.value)),R=/<u>\((\d+)\)/g;const o=/<span class="topicSortNum">([\d-]+)\.<\/span>/g;I.forEach(r=>{if(r.type=="radio")r.optionList.forEach(T=>{v==1?T.topicSortNum=T.topicSort:T.topicSortNum=T.topicSortH1});else if(r.type=="free"||r.type=="writing"){let T=r.type=="free"?"freeList":"writingList";r[T].forEach(ee=>{ee.topicSortNum=v===1?ee.topicSort:ee.topicSortH1;let se=ee.bodys.replace(o,()=>`<span class="topicSortNum">${ee.topicSortNum}.</span>`);if(ee.logicTypeId==Je.BlankFilling&&ee.mergeChildren){let M=parseInt(ee.topicSortNum[0]);se=se.replace(R,()=>{const oe=M;return M++,`<u>(${oe})`})}ee.bodys=se})}}),Ve(I,!0),setTimeout(()=>{me.value++},200)},we=q(null),ze=()=>_(we,O),O=(v,I,R,o={})=>{let r=o.optionInfo||[],T=o.scoreBoxInfo||[],ee=o.answerAreaInfo||[];return I.querySelectorAll("#paperId"+R).forEach((M,oe)=>{var L;if(M){let B=lt.value[R][oe]||{};if(B.itemTypeId!="H1"){if(B.type=="radio"){let de=M.querySelectorAll(".options-list-com .options-list");de.length==0&&(de=M.querySelectorAll(".options-list-com"));for(let ye=0;ye<de.length;ye++){let U=B.optionList[ye],g=S(de[ye],v,".hhypt-label-item"),Y={uuid:U.uuid,bounds:g};U.fuuid&&(Y.fuuid=U.fuuid),r.push(Y)}}else if(((L=B==null?void 0:B.splitChildren)==null?void 0:L.length)>0){let de=M.querySelectorAll(".splitChildrenId"),ye=B.splitChildren,U={uuids:B.uuid};B.fuuid&&(U.fuuid=B.fuuid),de.forEach((Y,pe)=>{let D=Y.getBoundingClientRect();(D==null?void 0:D.height)>0&&ee.push({...U,uuid:ye[pe].uuid,x:D.x-v.x,y:D.y-v.y,h:D.height,w:D.width})}),M.querySelectorAll(".scoringbox-boxs").forEach((Y,pe)=>{if(N().setting.subjectiveScoreTypeId=="LINE"){let D=S(Y,v,".score-row .score-item"),K=S(Y,v,".score-row","obj");T.push({...U,uuid:ye[pe].uuid,bounds:D,boundsAll:K})}else{let D=S(Y,v,".scorehandwritten-val","obj");D.x&&T.push({...U,uuid:ye[pe].uuid,boundsAll:D})}})}else if(!(B!=null&&B.splitFalg)){let de={uuid:B.uuid};B.fuuid&&(de.fuuid=B.fuuid);let ye=!0;if(s.layout.HhyptType=="writing"&&B.type=="writing"){let U=M.querySelector("#writingId").getBoundingClientRect();(U==null?void 0:U.height)>0&&(ye=!1,ee.push({...de,x:m(U.x-v.x),y:m(U.y-v.y),h:m(U.height),w:m(U.width)}))}if(ye){let U=M.getBoundingClientRect();(U==null?void 0:U.height)>0&&ee.push({...de,x:m(U.x-v.x),y:m(U.y-v.y),h:m(U.height),w:m(U.width)})}if(N().setting.subjectiveScoreTypeId=="LINE"){let U=S(M,v,".score-row .score-item"),g=S(M,v,".score-row","obj");U.length>0&&T.push({...de,bounds:U,boundsAll:g})}else{let U=S(M,v,".scorehandwritten-val","obj");U.x&&T.push({...de,boundsAll:U})}}}}}),{optionInfo:r,scoreBoxInfo:T,answerAreaInfo:ee}};let Z=q([]);const he=(v,I,R)=>{if(v.key!="bodys"&&v.key!="splitbodys"&&(N().ckeditorUuid=""),v.key=="H1"){let o=Z.value.findIndex(r=>r.uuid==(v==null?void 0:v.uuid));o!=-1&&(Z.value[o].title=v.title,Z.value[o].content=v.title)}else if(v.key=="groupSize"){let o=Z.value.findIndex(r=>r.H1uuid==I.H1uuid);o!=-1&&(Z.value[o].groupSize=v.groupSize)}else{let o=Z.value.findIndex(T=>T.H1uuid==I.H1uuid&&T.type!="radio");if(o==-1)return;let r=-1;if(I.type=="writing"?r=Z.value[o].writingList.findIndex(T=>T.uuid==I.uuid):r=Z.value[o].freeList.findIndex(T=>T.uuid==I.uuid),v.key=="stepScore")I.type=="writing"?Z.value[o].writingList[r].step_score=I.step_score:v.idx||v.idx===0?Z.value[o].freeList[r].splitChildren[v.idx].step_score=I.step_score:Z.value[o].freeList[r].step_score=I.step_score;else if(v.key=="wordNum")r!=-1&&(Z.value[o].writingList[r].wordNum=v.wordNum,Z.value[o].writingList[r].compositionSize=v.compositionSize);else{const ee=JSON.parse(JSON.stringify(w.value)).flat().filter(M=>M.uuid==I.uuid);if(ee.length==1){if(r!=-1){if(v.key=="bodys")I.type=="writing"?Z.value[o].writingList[r].bodys=I.bodys:Z.value[o].freeList[r].bodys=I.bodys;else if(v.key=="blankH")Z.value[o].freeList[r].blankH=v.blankH;else if(v.key=="split")Z.value[o].freeList[r]=I;else if(v.key=="splitbodys"){let oe=(I.splitChildren||[]).findIndex(L=>L.uuid==v.id);oe!=-1&&(Z.value[o].freeList[r].splitChildren[oe].bodys=v.sbodys)}}}else if(v.key=="bodys"){let M="";for(let oe=0;oe<ee.length;oe++)ee[oe].bodys&&(M+=ee[oe].bodys);I.type=="writing"?Z.value[o].writingList[r].bodys=M:Z.value[o].freeList[r].bodys=M}else if(v.key=="blankH"){let M=0;for(let oe=0;oe<ee.length;oe++)M+=ee[oe].blankH;Z.value[o].freeList[r].blankH=M}else if(v.key=="split")v.splitFalg?Z.value[o].freeList[r]=I:(Z.value[o].freeList[r].splitH=0,delete Z.value[o].freeList[r].splitNum,delete Z.value[o].freeList[r].splitChildren,delete Z.value[o].freeList[r].displayline,delete Z.value[o].freeList[r].splitType);else if(v.key=="splitbodys"){let M=ee.map(oe=>oe.splitChildren).flat();Z.value[o].freeList[r].splitChildren=M}}}v.h&&Ve(Z.value,!0)},dt=v=>{let I=JSON.parse(JSON.stringify(v)),R=[],o=[],r=[],T=[],ee=[],se=[],M="";const oe={type:"radio",itemTypeId:Je.QUESTION,groupSize:1},L={type:"blank",itemTypeId:Je.QUESTION},B={type:"free",itemTypeId:Je.QUESTION},de={type:"writing",itemTypeId:Je.QUESTION};let ye=(N().storeEnspNum-30)/3,U=30*4.75+20;function g(D,K,le,ve){let ne={uuid:le,logicTypeId:D,topicSort:K.topicSort,topicSortNum:K.topicSortNum,topicSortH1:K.topicSortH1,step_score:K.step_score,H1uuid:M,fuuid:ve};if(N().setting.questionNumberingTypeId==1?ne.topicSortNum=K.topicSort:ne.topicSortNum=K.topicSortH1,D==4)o.push({options:["T","F"],optionsLen:2,...ne});else if([1,2].includes(D)){let ce=(K.options||[]).map(Ie=>Ie.label);o.push({options:ce,optionsLen:ce.length,...ne})}else if([3,6].includes(D))se.push({...ne,...B,bodys:`<span class="topicSortNum">${ne.topicSortNum}.</span>`,blankH:U});else if([12,14].includes(D)){let ce=`<span class="topicSortNum">${ne.topicSortNum}.</span>`;for(let Ie=0;Ie<K.children.length;Ie++){let _e=`<u>(${Ie+1}).`,Re=ye;Ie+1>=10&&(Re=ye-1);for(let Te=0;Te<Re;Te++)_e+="&ensp;";_e+="</u>",ce+=`${_e}&ensp;&ensp;&ensp;&ensp;&ensp;`,Ie+1<=10&&(ce+="&ensp;")}se.push({...ne,...B,bodys:ce,blankH:20})}else[16,15].includes(D)&&ee.push({...ne,...de,wordNum:K.wordNum||5,wordNumI:K.wordNumI,compositionSize:K.compositionSize||20,bodys:`<span class="topicSortNum">${ne.topicSortNum}.</span>`})}for(let D=0;D<I.length;D++){const K=I[D];if(K.itemTypeId==Je.H1headline)o.length>0&&(r.push(...o),R.push({...oe,optionList:o,H1uuid:M}),o=[]),T.length>0&&(R.push({...L,blankList:T,H1uuid:M}),T=[]),se.length>0&&(R.push({...B,freeList:se,H1uuid:M}),se=[]),ee.length>0&&(R.push({...de,writingList:ee,H1uuid:M}),ee=[]),R.push(K),M=K.uuid;else{const{logicTypeId:le,detailData:ve}=K.question;le==10?ve.children.forEach(async ce=>{g(1,{...ce.detailData,options:ve.options},ce.uuid,K.content)}):[100,13].includes(le)?ve.children.forEach(async ce=>{g(Number(ce.logicTypeId),ce.detailData,ce.uuid,K.content)}):g(Number(le),ve,K.content)}}o.length>0&&(r.push(...o),R.push({...oe,optionList:o,H1uuid:M}),o=[]),T.length>0&&(R.push({...L,blankList:T,H1uuid:M}),T=[]),se.length>0&&(R.push({...B,freeList:se,H1uuid:M}),se=[]),ee.length>0&&(R.push({...de,writingList:ee,H1uuid:M}),ee=[]);let Y=JSON.parse(JSON.stringify(vt));Y.title="选择题",Y.isFalg=!0;let pe={...oe,optionList:r,H1uuid:Ge(),isFalg:!0};return Y.uuid=pe.H1uuid,R.unshift(pe),R.unshift(Y),R};let Fe=q(Math.floor((b-40)/48));p.pageLayout.value=="B4_3"&&(Fe.value=6);let Pe=-1;const at=v=>(Pe++,Pe),lt=q([]);async function Ve(v,I){try{Pe=-1;let R=I?JSON.parse(JSON.stringify(v)):dt(v);Z.value=JSON.parse(JSON.stringify(R));let o=0,r=E,T=[[]],ee=R.length,se={};const M=L=>new Promise(async(B,de)=>{let ye=await te(L.bodys,{});L.bodysH=ye;let U=r;if(r+=ye,r+=5,L.Noresizable=!1,L.toBodySplits=!1,L.toBodySplit=!1,r>G){let{ExceedingTheHeight:g,bodyList:Y}=await u(L.bodys,{Hcopy:U,pages:o}),pe=Y.length;for(let D=0;D<pe;D++)if(pe!=D+1||pe==1){if(Y[D].txt){let K=JSON.parse(JSON.stringify(L));[3,6,12,14].includes(L.logicTypeId)?(K.blankH=0,K.bodysH=Y[D].h,K.Noresizable=!0,K.splitChildren&&(K.splitChildren=[])):[16,15].includes(L.logicTypeId)&&(K.wordNumCopy=JSON.parse(JSON.stringify(L.wordNum)),K.wordNum=0),K.toBodySplits=!0,K.bodys=Y[D].txt,K.options=[],T[o].push(K)}o++,T[o]||(T[o]=[]),pe==1&&(L.bodys="",L.toBodySplit=!0,g=0,r=g+Y[D].headHeightNum,L.bodysH=g),r+=5}else L.bodys=Y[D].txt,r=g+Y[D].headHeightNum,L.bodysH=g}B(L)});async function oe(L){return new Promise(async(B,de)=>{let ye=[];if(L.type=="radio"){let U=17,g=s.layout.column==3?3:4,Y=L.optionList.length;se[L.H1uuid]=L.optionList.reduce((ce,Ie)=>ce+(Ie.full_score||0),0);const pe=L.optionList.reduce((ce,Ie)=>{var _e;return Math.max(ce,((_e=Ie==null?void 0:Ie.options)==null?void 0:_e.length)||0)},-1);pe>=Fe.value-1?g=1:pe>(Fe.value-2)/2?g=2:pe>=7&&(g=3),L.maxOptionsLen=pe;let K=(Y>L.groupSize?L.groupSize:Y)*21,le=0,ve=L.groupSize*g;ve<Y&&L.groupSize!==1&&(le=Y%ve);let ne=Math.ceil(Math.floor(Y/L.groupSize)/g);ne==0&&(ne=1),U+=ne*(K+10),le!==0&&le<L.groupSize&&(U+=le*21+10),r+=U,r>G&&(r=c(o,U),o++),T[o]||(T[o]=[]),T[o].push(L)}else if(L.type=="free"){V.classList.add("singleCards-ckeditor-box");for(let U=0;U<L.freeList.length;U++){let g=L.freeList[U];if(se[g.H1uuid]+=g.full_score,g.mergeFlag)continue;let Y=N().setting.subjectiveScoreTypeId=="LINE"?30:13;g=await M(g);let pe=!1;if(g.splitChildren){Y=6,pe=!0;let le=0,ve=0,ne=0,ce=-1,Ie=g.splitChildren.length;for(let _e=0;_e<Ie;_e++){let Re=g.splitChildren[_e],Te=z(Re.bodys,{})+28;r+=Te,r>G&&(le=ve,ve=0,ce==-1&&(ce==-1&&(ce=_e),r=c(o,Te))),ne+=Te,ve+=Te}if(g.blankH&&(g.blankH-=ve),ce!=-1){const _e=g.splitChildren.slice(0,ce),Re=g.splitChildren.slice(ce);let Te=JSON.parse(JSON.stringify(g));Te.splitChildren=_e,Te.Noresizable=_e.length>0,Te.splitFalg=!0,Te.blankH=0,Te.splitH=le,T[o].push(Te),o++,T[o]||(T[o]=[]),g.bodys="",g.splitH=ve,g.toBodySplit=!0,g.splitChildren=Re}}g.blankH?g.blankH<Y&&(g.blankH=Y):g.blankH=Y;let D=g.blankH,K=r;if(r+=D,r>G){let le=JSON.parse(JSON.stringify(g));le.blankH=G-K,g.blankH=g.blankH-le.blankH,g.blankH<Y&&(g.blankH=Y),g.splitFalg=pe,g.toBodySplit=!0,le.Noresizable=!0,g.bodys="",g.splitChildren=[],T[o].push(le),r=c(o,g.blankH)+5,o++,T[o]||(T[o]=[])}T[o].push(g)}V.classList.remove("singleCards-ckeditor-box")}else if(L.type=="blank")console.log(L);else if(L.type=="writing")for(let U=0;U<L.writingList.length;U++){let g=L.writingList[U];se[g.H1uuid]+=g.full_score,g.H1uuid=L.H1uuid,s.layout.HhyptType=="writing"?g.bodys&&(g=await M(g)):g=await M(g);let Y=g.wordNum,pe=50,D=1;if(g.logicTypeId==16){g.compositionSize||(g.compositionSize=20),D=g.compositionSize;let le=Math.floor((b-4)/D);g.compositionWidth=le,Y=Math.ceil(Y/D),pe=le+10}let K=JSON.parse(JSON.stringify(g.wordNum||""));for(let le=0;le<Y;le++){let ve=pe;if(le==0&&(ve+=38),r+=pe,r>G){g.wordNumCopy=K;let ne=JSON.parse(JSON.stringify(g));ne.wordNumI?ne.wordNum=(le-ne.wordNumI)*D:ne.wordNum=le*D,g.wordNumI=le,g.wordNumTo=(ne.wordNumTo||0)+ne.wordNum,g.wordNum=K-le*D,g.bodys="",g.toBodySplit=!0,g.wordNumFalg=!0,T[o].push(ne),r=c(o,ve)+5,o++,T[o]||(T[o]=[])}}T[o].push(g)}B(ye)})}for(let L=0;L<ee;L++){let B=R[L];B.isFalg||(B.itemTypeId=="H1"?(B.h=z(B.content||B.title,{fontSize:"17",minHeight:"40px",lineHeight:"32"})+5,r+=B.h+5,r>G&&(r=c(o,B.h),o++,T[o]||(T[o]=[])),se[B.uuid]=0,T[o].push(B)):await oe(B))}if(T.forEach(L=>{L.forEach(B=>{se[B.uuid]&&(B.full_score=se[B.uuid])})}),s.layout.paperListArrLen=T.length,lt.value=JSON.parse(JSON.stringify(T)),s.isPreview&&s.layout.column==2){let L=Ze(T,2,!0);w.value=JSON.parse(JSON.stringify(L))}else if(s.isPreview&&s.layout.column==3){let L=Ze(T,3,!0);w.value=JSON.parse(JSON.stringify(L))}else w.value=JSON.parse(JSON.stringify(T))}catch(R){console.log(R)}}return y.value=JSON.parse(JSON.stringify(s.questionsArr)),s.cardList?Ve(s.cardList,!0):Ve(s.questionsArr),Q({paperListArr:w,questionNumberingTypeIdChange:ge,questionsArrCopy:y,quesListCopy:Z,getZB:ze,objectiveQuestionMerging:a}),(v,I)=>(l(),n(xe,null,[(l(!0),n(xe,null,Se(t(w),(R,o)=>(l(),n("div",{class:He(["topic-com singleCards-ckeditor-box",{"newline-preview-box":t(k)>1}]),ref_for:!0,ref_key:"topicComRef",ref:we,id:"print-page"+o,key:o,style:Ee(t(P))},[s.isPreview&&(e.layout.column==2||e.layout.column==3)?(l(),n("div",{key:0,class:"topic-box",style:Ee([{"flex-wrap":"wrap"},t(x)(o)])},[(l(!0),n(xe,null,Se(R,(r,T)=>(l(),n("div",{class:He(["topic-box-item",{on:t(H)(T,t(p).pageLayout.value)}]),key:T,style:Ee(t(W)(T,o))},[d(it,{list:r,onOnEditorBlur:he,index:at(),isPreview:e.isPreview,onTopicDialogOpens:ae,pageLayoutName:t(p).pageLayout.name,layout:e.layout,pageWidth:t(b),radioMaxNum:t(Fe),examNumberLength:t(p).examNumberLength},null,8,["list","index","isPreview","pageLayoutName","layout","pageWidth","radioMaxNum","examNumberLength"])],6))),128))],4)):(l(),n("div",{key:1,class:"topic-box",style:Ee(t(x)(o))},[(l(),Ce(it,{list:R,pageLayoutName:t(p).pageLayout.name,index:o,layout:e.layout,pageWidth:t(b),onMergeTap:ue,onCancelMergeTap:j,onOnEditorBlur:he,radioMaxNum:t(Fe),key:t(me),examNumberLength:t(p).examNumberLength,onTopicDialogOpens:ae,isPreview:e.isPreview},null,8,["list","pageLayoutName","index","layout","pageWidth","radioMaxNum","examNumberLength","isPreview"]))],4)),e.isPreview?(l(),n(xe,{key:2},Se(["left","right"],r=>i("div",{id:"marker-boxs",key:r,style:Ee(`position: absolute; height: ${t(C).marker.height}px; width: ${t(C).marker.width}px; ${r}: ${t(C).marker.left}px; top: ${t(C).marker.top}px;`)},null,4)),64)):A("",!0),e.isPreview?(l(),n(xe,{key:3},Se(["left","right"],r=>i("div",{id:"marker-boxs",key:r,style:Ee(`position: absolute; height: ${t(C).marker.height}px; width: ${t(C).marker.width}px; ${r}: ${t(C).marker.left}px; bottom: ${t(C).marker.top}px;`)},null,4)),64)):A("",!0),!e.isPreview&&o+1!=t(k)?(l(),n("div",ki)):A("",!0),e.isPreview&&t(k)>1?(l(),n("p",xi)):A("",!0)],14,vi))),128)),e.isPreview?A("",!0):(l(),Ce(wl,{key:0,ref_key:"topicDialogRef",ref:X},null,512)),e.isPreview?A("",!0):(l(),Ce(Al,{key:1,ref_key:"mergeTapDialogRef",ref:h,onConfirm:F},null,512))],64))}}),Fi=Le(Ci,[["__scopeId","data-v-a10315f4"]]);export{Ri as C,ml as H,$i as L,Oe as _,rt as a,kt as b,Ye as c,ut as d,bl as e,zi as f,Oi as n,Mi as r,Fi as s,xt as u};

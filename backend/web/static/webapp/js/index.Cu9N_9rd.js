import{d as qe,r as L,b as P,w as v,i as ze,u as n,E as et,o as m,a as l,f as a,c as h,aP as pt,v as tt,q as Z,s as lt,F as D,g as ot,az as De,e as ne,aQ as ct,h as se,j as Re,n as Me,k as Je,l as ye,m as st,aK as _,W as nt,N as it,U as at,t as R,T as ut,S as mt,aR as gt,aS as ft,M as rt,p as fe,K as Ae,L as yt,au as We,Y as _t,J as Ge,Z as Ze,O as Ye}from"./index.ZZ6UQeaF.js";import"./el-collapse-transition.l0sNRNKZ.js";/* empty css                          *//* empty css                 *//* empty css                        *//* empty css                       *//* empty css                  */import{n as dt,d as vt,r as Be,u as ht,f as Pe,L as Fe,s as Ct,C as Xe}from"./index.CCoS0DfY.js";/* empty css                *//* empty css                     *//* empty css                     *//* empty css                  *//* empty css                  */import{q as ue}from"./question.IyuOoK5G.js";import{u as Se,a as bt}from"./index.6W4rOsHv.js";import{_ as Qe}from"./_plugin-vue_export-helper.DlAUqK2U.js";/* empty css                   */import{p as Vt,_ as xt}from"./previewDom.vue_vue_type_style_index_0_lang.BzEnMuYY.js";import{c as St}from"./cardSetting.BS28GH8Z.js";import{a as wt}from"./downloads.B3gkFdce.js";import{g as je,q as Nt}from"./index.pTwlcQaF.js";import{g as kt,p as Tt,s as It,d as Ut,a as Lt}from"./answerSheet.XzQSe7IO.js";/* empty css                       */import"./itemDom.vue_vue_type_script_setup_true_lang.CbGURBLf.js";import"./index.vue_vue_type_style_index_0_lang.Cq6RDaj7.js";/* empty css                        */import"./index.xsH4HHeE.js";import"./request.CsKOOJzG.js";const $t={class:"pmm2_flex_between"},Ft={class:"addTopic-box scrollBar-C"},Mt={key:0,style:{width:"100%"}},Et={key:1,style:{width:"100%"}},Ht={class:"pmm2_flex_between"},Ot={class:"dialog-footer"},At=qe({__name:"addWriting",props:["step_score","chineseWritingTypes"],emits:["addSubmit"],setup(Ie,{expose:Ue,emit:_e}){const de=Ie,A=L(!1),f=L({}),o=L(null);L({});let $="",j=L(!1);de.chineseWritingTypes&&de.chineseWritingTypes[0].value;const me=F=>{var O,ee;$=Se(),j.value=!0;let d={uuid:Se(),logicType:((O=F==null?void 0:F.list[0])==null?void 0:O.id)||"",wordNum:F.type=="ChineseWriting"?800:10,compositionSize:20,wordKing:1,topicSortNum:F.topicSortNum||1,full_score:50,step_score:de.step_score||1,type:F.type,list:F.list,name:"作文"};f.value=d,A.value=!0,(ee=o==null?void 0:o.value)==null||ee.resetFields()},ve=F=>{var re,Q;j.value=!1,$=F.uuid;let d=F.children[0],O=F.type=="ChineseWriting"?800:10,ee={minWordNum:F.minWordNum||d.minWordNum,wordNum:d.wordNum||O,compositionSize:d.compositionSize||20,wordKing:d.wordKing||1,topicSortNum:d.topicSortNum||1,full_score:d.full_score||50,step_score:d.step_score||de.step_score||1,type:F.type,list:F.list,uuid:d.uuid,chineseNum:d.chineseNum,name:d.name||F.title,idx:F.idx};ee.logicType=F.logicType||((re=F==null?void 0:F.list[0])==null?void 0:re.id)||"",f.value=ee,A.value=!0,(Q=o==null?void 0:o.value)==null||Q.resetFields()},J=_e,we=()=>{o.value.validate(F=>{if(F){let{bodyFlag:d,type:O,minWordNum:ee,list:re,topicSortNum:Q,full_score:K,chineseNum:G,name:W,wordNum:te}=f.value,Ce=O=="ChineseWriting"?ue.ChineseWriting:ue.EnglishWriting;const ie={type:"writing",itemTypeId:"QUESTION",H1uuid:$,writingList:[]};O=="ChineseWriting"&&ee>te&&(ee=te),K%1!==0&&(f.value.step_score=.5),ie.writingList[0]={...f.value,logicTypeId:Ce,type:"writing",H1uuid:$,questionUuid:"",topicSort:Q,topicSortH1:Q};let V=[{title:G?G+"、"+W:W,itemTypeId:"H1",uuid:$,full_score:K}],q={...V[0],minWordNum:ee,wordNum:te,type:O,list:re,topicSortNum:Q,children:[...JSON.parse(JSON.stringify(ie.writingList))]};if(j.value)_().scoreSetting.push(q),_().questionsScoreList.push({uuid:f.value.uuid,bodyFlag:d,H1uuid:$,full_score:K});else{_().scoreSetting.splice(f.value.idx||0,1,q);let ke=_().questionsScoreList.findIndex(be=>be.uuid==f.value.uuid);ke!=-1&&(_().questionsScoreList[ke].full_score=K)}let Ne=`<span class="topicSortNum">${Q}.</span>`;ie.writingList[0].bodys=Ne,V.push(ie),J("addSubmit",V,j.value),A.value=!1}else console.log("error submit!")})};return Ue({opens:me,editOpens:ve}),(F,d)=>{const O=ye("CloseBold"),ee=Je,re=st,Q=lt,K=tt,G=pt,W=ot,te=De,Ce=ct,ie=Re,oe=et;return m(),P(oe,{"append-to-body":"",class:"hhypt-dialog-boxs hhypt-dialog-boxs-addTopicDom","show-close":!1,modelValue:n(A),"onUpdate:modelValue":d[11]||(d[11]=V=>ze(A)?A.value=V:null),title:"添加试题",width:"500","align-center":""},{header:v(({close:V,titleClass:q})=>[l("div",$t,[l("span",{class:Me(q)},"作文设置",2),a(re,{onClick:V,underline:!1},{default:v(()=>[a(ee,{size:"20"},{default:v(()=>[a(O)]),_:1})]),_:2},1032,["onClick"])])]),footer:v(()=>[l("div",Ht,[d[25]||(d[25]=l("div",null,null,-1)),l("div",Ot,[a(ie,{style:{width:"70px"},onClick:d[10]||(d[10]=V=>A.value=!1)},{default:v(()=>d[23]||(d[23]=[se("取消")])),_:1,__:[23]}),a(ie,{style:{width:"70px"},type:"primary",onClick:we},{default:v(()=>d[24]||(d[24]=[se(" 确定 ")])),_:1,__:[24]})])])]),default:v(()=>[l("div",Ft,[a(Ce,{ref_key:"ruleFormRef",ref:o,style:{width:"100%"},model:n(f),"inline-message":""},{default:v(()=>[d[22]||(d[22]=l("br",null,null,-1)),a(G,{prop:"chineseNum",class:"item-box"},{default:v(()=>[d[12]||(d[12]=l("span",{class:"bt"},"大题题号：",-1)),a(K,{modelValue:n(f).chineseNum,"onUpdate:modelValue":d[0]||(d[0]=V=>n(f).chineseNum=V),placeholder:"题号",class:"item-w"},{default:v(()=>[(m(!0),h(D,null,Z(n(dt),V=>(m(),P(Q,{key:V.value,label:V.label,value:V.label},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1,__:[12]}),a(G,{prop:"name",class:"item-box"},{default:v(()=>[d[13]||(d[13]=l("span",{class:"bt"},"大题题目：",-1)),a(W,{modelValue:n(f).name,"onUpdate:modelValue":d[1]||(d[1]=V=>n(f).name=V),style:{width:"300px"},placeholder:"请输入题目"},null,8,["modelValue"])]),_:1,__:[13]}),a(G,{prop:"logicType",class:"item-box"},{default:v(()=>[d[14]||(d[14]=l("span",{class:"bt"},"题型：",-1)),a(K,{modelValue:n(f).logicType,"onUpdate:modelValue":d[2]||(d[2]=V=>n(f).logicType=V),placeholder:"题型",class:"item-w"},{default:v(()=>[(m(!0),h(D,null,Z(n(f).list,V=>(m(),P(Q,{key:V.id,label:V.name,value:V.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1,__:[14]}),a(G,{prop:"topicSortNum",class:"item-box",rules:[{required:!0,message:"请输入小题题号",trigger:"blur"}]},{default:v(()=>[d[15]||(d[15]=l("span",{class:"bt"},"小题题号：",-1)),a(te,{"controls-position":"right","step-strictly":!0,modelValue:n(f).topicSortNum,"onUpdate:modelValue":d[3]||(d[3]=V=>n(f).topicSortNum=V),min:1,class:"item-w",step:1},null,8,["modelValue"])]),_:1,__:[15]}),a(G,{prop:"full_score",class:"item-box",rules:[{required:!0,message:"请输入题目分值",trigger:"blur"}]},{default:v(()=>[d[16]||(d[16]=l("span",{class:"bt"},"题目分值：",-1)),a(te,{"controls-position":"right","step-strictly":!0,modelValue:n(f).full_score,"onUpdate:modelValue":d[4]||(d[4]=V=>n(f).full_score=V),min:.5,class:"item-w",step:.5},null,8,["modelValue"])]),_:1,__:[16]}),n(f).type=="ChineseWriting"?(m(),h("div",Mt,[a(G,{prop:"wordNum",class:"item-box",rules:[{required:!0,message:"请输入方格数量",trigger:"blur"}]},{default:v(()=>[d[17]||(d[17]=l("span",{class:"bt"},"方格数量：",-1)),a(te,{"controls-position":"right",class:"item-w","step-strictly":!0,modelValue:n(f).wordNum,"onUpdate:modelValue":d[5]||(d[5]=V=>n(f).wordNum=V),min:1,step:1},null,8,["modelValue"])]),_:1,__:[17]}),a(G,{prop:"wordKing",class:"item-box"},{default:v(()=>[d[18]||(d[18]=l("span",{class:"bt"},"字数标记：",-1)),a(K,{modelValue:n(f).wordKing,"onUpdate:modelValue":d[6]||(d[6]=V=>n(f).wordKing=V),class:"item-w"},{default:v(()=>[a(Q,{label:"每隔100字显示",value:1}),a(Q,{label:"每隔200字显示",value:3}),a(Q,{label:"仅最少字数处显示",value:2})]),_:1},8,["modelValue"])]),_:1,__:[18]}),n(f).wordKing==2?(m(),P(G,{key:0,prop:"minWordNum",class:"item-box",rules:[{required:!0,message:"请输入最少字数",trigger:"blur"}]},{default:v(()=>[d[19]||(d[19]=l("span",{class:"bt"},"最少字数：",-1)),a(te,{"controls-position":"right","step-strictly":!0,class:"item-w",modelValue:n(f).minWordNum,"onUpdate:modelValue":d[7]||(d[7]=V=>n(f).minWordNum=V),min:1,max:n(f).wordNum,step:1},null,8,["modelValue","max"])]),_:1,__:[19]})):ne("",!0),a(G,{prop:"compositionSize",class:"item-box",rules:[{required:!0,message:"请输入每行字数",trigger:"blur"}]},{default:v(()=>[d[20]||(d[20]=l("span",{class:"bt"},"每行字数：",-1)),a(te,{"controls-position":"right","step-strictly":!0,class:"item-w",modelValue:n(f).compositionSize,"onUpdate:modelValue":d[8]||(d[8]=V=>n(f).compositionSize=V),min:10,step:1},null,8,["modelValue"])]),_:1,__:[20]})])):(m(),h("div",Et,[a(G,{prop:"wordNum",class:"item-box",rules:[{required:!0,message:"请输入小题行数",trigger:"blur"}]},{default:v(()=>[d[21]||(d[21]=l("span",{class:"bt"},"小题行数：",-1)),a(te,{"controls-position":"right","step-strictly":!0,class:"item-w",modelValue:n(f).wordNum,"onUpdate:modelValue":d[9]||(d[9]=V=>n(f).wordNum=V),min:1,max:20,step:1},null,8,["modelValue"])]),_:1,__:[21]})]))]),_:1,__:[22]},8,["model"])])]),_:1},8,["modelValue"])}}}),Wt=Qe(At,[["__scopeId","data-v-d9e1f563"]]),Bt={class:"pmm2_flex_between"},Pt={class:"addTopic-box"},qt={class:"top-box"},zt={class:"main-boxs scrollBar"},Dt={class:"ul-box"},Rt={class:"li-com pmm2_flex_between"},Jt={class:"li-left-box pmm2_flex_acenter"},Qt={class:"li-left-com"},Kt={class:"li-left-com"},Gt={class:"li-left-com"},Zt={key:0,class:"li-left-com"},Yt={key:1,class:"li-left-com pmm2_flex_acenter"},Xt={class:"bt"},jt={key:2,class:"li-left-com pmm2_flex_acenter"},el={class:"li-right pmm2_flex_between"},tl={key:0,class:"tig-box pmm2_flex_acenter"},ll={class:"list-box"},ol={key:0,class:"item-main pmm2_flex_between"},sl={class:"left-box pmm2_flex_acenter"},nl={class:"topicSortNum"},il={key:0,class:"left-com"},al={key:1,class:"left-com"},ul={class:"right-box"},rl={class:"right-com"},dl={key:1,class:"item-main"},pl={key:0,class:"item-main-box pmm2_flex_between"},cl={class:"left-box pmm2_flex_acenter"},ml={class:"topicSortNum"},gl={key:0,class:"left-com pmm2_flex_acenter",style:{"padding-left":"10px"}},fl={class:"right-box"},yl={class:"right-com"},_l={key:1,class:"item-main-box pmm2_flex_between"},vl={class:"left-box pmm2_flex_acenter"},hl={class:"topicSortNum"},Cl={style:{"padding-left":"10px"}},bl={key:0,class:"pmm2_flex_acenter"},Vl={key:0,class:"left-com"},xl={key:1,class:"left-com"},Sl={class:"right-box"},wl={class:"right-com pmm2_flex_acenter"},Nl={key:0},kl={key:2,class:"item-main-spilt"},Tl={class:"scrollBar",style:{"margin-top":"10px","padding-left":"10px"}},Il={class:"pmm2_flex_acenter"},Ul={key:0,class:"pmm2_flex_acenter",style:{"padding-left":"10px"}},Ll={class:"pmm2_flex_acenter",style:{"align-items":"baseline"}},$l={key:0},Fl={key:2,class:"item-main pmm2_flex_between"},Ml={class:"left-box pmm2_flex_acenter"},El={class:"topicSortNum"},Hl={class:"right-box"},Ol={key:0,class:"right-com"},Al={key:1,class:"right-com"},Wl={class:"pmm2_flex_between"},Bl={class:"dialog-footer"},Pl={key:0},ql=qe({__name:"addTopic",props:["partScoreTypes","step_score"],emits:["addSubmit"],setup(Ie,{expose:Ue,emit:_e}){const{splitspaceChange:de}=ht(),A=L(!1),f=L({}),o=L([]);let $="";const j=L(!1);let me=0;const ve=Ie,J=(p,e)=>{var u;me=_().storeEnspNum,j.value=!0,$=Se();let g={uuid:Se(),lineFalg:!1,frontNum:p.topicSortNum||1,optionsCount:4,logicTypeId:((u=p==null?void 0:p.list[0])==null?void 0:u.id)||"",mergeChecked:!1,children:[]};o.value=[g],f.value={...p},A.value=!0},we=p=>{o.value=[],f.value={},me=_().storeEnspNum,j.value=!1;let e=[];$=p.uuid,p.children.forEach(u=>{if(u.mergeChecked)u.logicTypeId=u.logicType,u.mergeChildren&&(u.children=u.mergeChildren,delete u.mergeChildren),e.push(u);else{let x=e.length;if((x==0?-1:e.findIndex(Y=>Y.uuid==u.fuuid))==-1){if(x!=0){let E=e[x-1].children.length;e[x-1].afterNum=e[x-1].children[E-1].topicSortNum}let Y=!1;u.logicTypeId==4&&(Y=!0),e.push({mergeChecked:!1,uuid:u.fuuid,logicTypeId:u.logicType,frontNum:u.topicSortNum,scoreMethod:u.scoreMethod,scoreTotal:u.scoreTotal,disabled:Y,children:[u]})}else e[x-1].children.push(u)}});let g=e.length;if(g!=0){let u=e[g-1].children.length;e[g-1].afterNum=e[g-1].children[u-1].topicSortNum}o.value=e,f.value=p,A.value=!0},F=nt(()=>o.value.reduce((e,g)=>e+(g.mergeChecked?g.full_score||0:g.children.reduce((u,x)=>u+(x.full_score||0),0)),0)),d=L(null),O=p=>{fe({message:p,type:"warning"}),setTimeout(()=>{d.value=null},2e3)},ee=_e,re=async()=>{d.value=null;let p={type:"radio",itemTypeId:"QUESTION",groupSize:1,optionList:[],H1uuid:$},e={type:"free",itemTypeId:"QUESTION",freeList:[],H1uuid:$},g=[],u=20;(f.value.type=="FreeAnswer"||f.value.type=="Composition")&&(u=10*4.75+20);const x=(I,U,b,c,i)=>{if(I==ue.MultipleChoice){if(U.answer.length==0)return d.value=b+"-"+c,{msg:"第"+U.topicSortNum+"题请选择答案"};U.answer=U.answer.sort()}else if(!U.answer)return d.value=b+"-"+c,{msg:"第"+U.topicSortNum+"题请选择答案"};return U.full_score?(i>U.full_score&&(i=U.full_score),{scoreMin:i,child:U}):(d.value=b+"-"+c,{msg:"第"+U.topicSortNum+"题请输入分值"})};for(let I=0;I<o.value.length;I++){let U=99990,b=o.value[I];if(!b.frontNum||!b.afterNum)return d.value=I,O("请将题号输入完整");if(f.value.type=="multiple"){for(let c=0;c<b.children.length;c++){let i=b.children[c];const t=x(b.logicTypeId,i,I,c,U);if(t.msg)return O(t.msg);i=t.child,b.logicTypeId==ue.MultipleChoice&&(i.scoreMethod=b.scoreMethod,i.scoreTotal=b.scoreTotal),i.logicTypeId=b.logicTypeId,i.questionUuid="",i.topicSortH1=i.topicSortNum,i.topicSort=i.topicSortNum,i.logicType=b.logicTypeId,p.optionList.push(i)}if(b.logicTypeId==ue.MultipleChoice){if(!b.scoreTotal&&b.scoreTotal!==0)return d.value=I,O("请输入多选题赋分规则");if(b.scoreMethod==1&&U<b.scoreTotal)return d.value=I,O("多选题赋分规则:根据选对个数赋分时选择分值不能大于当前分数")}}else if(f.value.type=="FreeAnswer")for(let c=0;c<b.children.length;c++){let i=b.children[c];if(!i.full_score)return d.value=I+"-"+c,O("第"+i.topicSortNum+"题请输入分值");i.full_score%1!==0&&(i.step_score=.5),i.logicType=b.logicTypeId,i.questionUuid="",i.topicSort=i.topicSortNum,i.topicSortH1=i.topicSortNum,i.type="free",i.blankH=u,e.freeList.push(i)}else if(f.value.type=="BlankFilling")if(b.mergeChecked){if(!b.full_score)return d.value=I,O("请输入分值");b.full_score%1!==0&&(b.step_score=.5);let c=b.frontNum+"~"+b.afterNum;b.topicSort=c,b.topicSortH1=c,b.topicSortNum=c;let i={...b,logicType:b.logicTypeId,logicTypeId:3,questionUuid:"",topicSort:"",topicSortH1:"",type:"free",blankH:u,fuuid:b.uuid,H1uuid:$,step_score:b.step_score||ve.step_score};e.freeList.push(i)}else for(let c=0;c<b.children.length;c++){let i=b.children[c];if(!i.full_score)return d.value=I+"-"+c,O("第"+i.topicSortNum+"题请输入分值");i.full_score%1!==0&&(i.step_score=.5),i.logicType=b.logicTypeId,i.questionUuid="",i.topicSort=i.topicSortNum,i.topicSortH1=i.topicSortNum,i.type="free",i.blankH=u,e.freeList.push(i)}else if(f.value.type=="Composition")for(let c=0;c<b.children.length;c++){let i=b.children[c];if(!i.full_score)return d.value=I+"-"+c,O("第"+i.topicSortNum+"题请输入分值");if(i.full_score%1!==0&&(i.step_score=.5),i.radioFalg){const t=x(i.logicTypeId,i,I,c,U);if(t.msg)return O(t.msg);i=t.child,p.optionList.push(i)}else i.type="free",i.blankH=u,i.logicTypeId=6,e.freeList.push(i);i.logicType=b.logicTypeId,i.questionUuid="",i.topicSort=i.topicSortNum,i.topicSortH1=i.topicSortNum,g.push(i)}}let k=0;function Y(){o.value.forEach(I=>{if(I.mergeChecked){let U={uuid:I.uuid,H1uuid:$,full_score:I.full_score};_().questionsScoreList.push(U)}else I.children.forEach(U=>{let b={uuid:U.uuid,H1uuid:$,full_score:U.full_score};U.children&&(b.children=U.children),U.splitChildren&&(b.children=U.splitChildren),_().questionsScoreList.push(b),k=U.topicSort})})}j.value||(_().questionsScoreList=_().questionsScoreList.filter(I=>I.H1uuid!==$)),Y();const{chineseNum:E,name:S,type:M,list:X}=f.value;let le=[{title:E?E+"、"+S:S,itemTypeId:"H1",uuid:$,full_score:F.value}],he={chineseNum:E,name:S,type:M,list:X};const pe={...le[0],...he,topicSortNum:k,chineseNum:E||"",children:[]};M=="multiple"?(le.push(p),pe.children=p.optionList):["BlankFilling","FreeAnswer"].includes(M)?(e.freeList=await Pe(e.freeList,me),le.push(e),pe.children=e.freeList):M=="Composition"&&(p.optionList.length>0&&le.push(p),e.freeList.length>0&&(e.freeList=await Pe(e.freeList,me),le.push(e)),pe.children=g);let ge=JSON.parse(JSON.stringify(pe));ge.children.forEach(I=>{I.splitChildren&&(I.children=I.splitChildren,delete I.splitChildren),I.bodys&&(I.bodys="")}),j.value?_().scoreSetting.push(ge):_().scoreSetting.splice(f.value.idx,1,ge),ee("addSubmit",JSON.parse(JSON.stringify(le)),j.value),A.value=!1},Q=(p,e)=>{p==1?e.scoreTotal=0:e.scoreTotal=50},K=(p,e)=>{p?(e.logicTypeId=Be[0].id,delete e.splitNum,delete e.splitChildren,delete e.splitType,delete e.displayline,G(e.logicTypeId,e)):(delete e.logicTypeId,delete e.disabled,delete e.optionsCount,delete e.options,delete e.answer)},G=(p,e)=>{if(p==ue.TrueFalse)e.disabled=!0,e.optionsCount=2,e.options=["T","F"],e.answer="";else{e.disabled=!1,e.optionsCount||(e.optionsCount=4);let g=[];for(let u=0;u<e.optionsCount;u++)g.push(Fe[u]);p==ue.MultipleChoice?(e.options=g,e.answer=[]):(e.options=g,e.answer="")}},W=(p,e)=>{let g=o.value[e].children.length;for(let u=0;u<g;u++)o.value[e].children[u].spaceNum=p},te=(p,e,g)=>{o.value[e].spaceNum=void 0},Ce=(p,e)=>{let g=o.value[e].children.length;for(let u=0;u<g;u++)o.value[e].children[u].spaceChline=p},ie=(p,e,g)=>{o.value[e].spaceChline=void 0},oe=L({}),V=L(null),q=(p,e)=>{let g=o.value[p].children[e];const u={split:{i:p,idx:e},splitFalg:!0,paperItem:{...g,step_score:ve.step_score},full_score:g.full_score};oe.value=JSON.parse(JSON.stringify(u)),rt(()=>{V.value.opens()})},Ne=p=>{let e=o.value[oe.value.split.i].children[oe.value.split.idx];o.value[oe.value.split.i].children[oe.value.split.idx]={...e,...p},oe.value={}},ke=(p,e,g)=>{g.full_score=g.splitChildren.reduce((u,x)=>u+(x.score||0),0)},be=(p,e)=>{p.splitChildren.splice(e,1),p.splitChildren.length==0&&(delete p.splitNum,delete p.splitChildren,delete p.displayline,delete p.splitType)},Ve=(p,e)=>{let g=o.value[e].children.length;for(let u=0;u<g;u++)o.value[e].children[u].lineNum=p},Ee=(p,e,g)=>{},He=(p,e)=>{o.value[e].lineNum||(o.value[e].lineNum=1);let g=o.value[e].children.length;for(let u=0;u<g;u++)o.value[e].children[u].lineNum||(o.value[e].children[u].lineNum=1),o.value[e].children[u].lineFalg=p},Oe=(p,e,g)=>{o.value[e].lineFalg=!1},Te=(p,e)=>{var g,u;if(p=="+"){let x=o.value[e].afterNum||0,k={uuid:Se(),optionsCount:4,logicTypeId:((u=(g=f.value)==null?void 0:g.list[0])==null?void 0:u.id)||"",frontNum:x+1,children:[]};o.value.splice(e+1,0,k)}else o.value.splice(e,1)},C=(p,e)=>{o.value[e].disabled=!1;let g=o.value[e].children.length;if(o.value.length==1){let u=f.value.list.find(x=>x.id==p);u&&(f.value.name=u.name)}if(f.value.type=="multiple")if(p==4){o.value[e].disabled=!0,o.value[e].optionsCount=2;for(let u=0;u<g;u++)o.value[e].children[u].optionsCount=2,o.value[e].children[u].options=["T","F"],o.value[e].children[u].answer=""}else{let u=4,x=["A","B","C","D"];if(o.value[e].optionsCount=u,p==2){o.value[e].scoreMethod||(o.value[e].scoreMethod=1),o.value[e].scoreTotal||(o.value[e].scoreTotal=0);for(let k=0;k<g;k++)o.value[e].children[k].optionsCount=u,o.value[e].children[k].options=x,o.value[e].children[k].answer=[]}else for(let k=0;k<g;k++)o.value[e].children[k].optionsCount=u,o.value[e].children[k].options=x,o.value[e].children[k].answer=""}},s=(p,e)=>{const{frontNum:g,afterNum:u}=o.value[e];if(!g||!u)return;let x=o.value[e].children.length,k=u+1-g;if(k>x){const E=k-x;let M={full_score:o.value[e].full_score||void 0,step_score:ve.step_score,H1uuid:$,fuuid:o.value[e].uuid};if(f.value.type=="multiple"){M.logicTypeId=o.value[e].logicTypeId;let X=[];if(M.logicTypeId==ue.TrueFalse)X=["T","F"];else{let xe=o.value[e].optionsCount||4;for(let le=0;le<xe;le++)X.push(Fe[le])}M.options=X,M.optionsCount=X.length}else f.value.type=="FreeAnswer"?(M.lineFalg=o.value[e].lineFalg,M.lineNum=o.value[e].lineNum,M.logicTypeId=6):f.value.type=="BlankFilling"&&(M.logicTypeId=3);for(let X=0;X<E;X++)M.uuid=Se(),o.value[e].children.push(JSON.parse(JSON.stringify(M)))}else{const E=x-k;for(let S=0;S<E;S++)o.value[e].children.pop()}o.value[e].children.forEach((E,S)=>{E.topicSortNum=g+S});let Y=o.value.length;if(Y>1){let E=e+1,S=JSON.parse(JSON.stringify(u));for(E;E<Y&&(S+=1,o.value[E].frontNum=S,o.value[E].afterNum);E++)o.value[E].children.forEach((M,X)=>{M.topicSortNum=S+X}),S+=o.value[E].children.length-1,o.value[E].afterNum=S}},y=(p,e)=>{let g=o.value[e].children.length;for(let u=0;u<g;u++){o.value[e].children[u].optionsCount=p;let x=[];for(let k=0;k<p;k++)x.push(Fe[k]);w(p,e,u,x)}},T=(p,e)=>{let g=o.value[e].children.length;for(let u=0;u<g;u++){let x=o.value[e].children[u];x.splitChildren&&x.splitChildren.length>0||(o.value[e].children[u].full_score=p)}},w=(p,e,g,u)=>{let x=u||[];if(!u){o.value[e].optionsCount=void 0;for(let k=0;k<p;k++)x.push(Fe[k])}o.value[e].logicTypeId==2?o.value[e].children[g].answer=[]:o.value[e].children[g].answer="",o.value[e].children[g].options=x},N=(p,e,g)=>{o.value[e].full_score=void 0};return Ue({opens:J,editOpens:we}),(p,e)=>{const g=ye("CloseBold"),u=Je,x=st,k=lt,Y=tt,E=ot,S=De,M=it,X=ye("CirclePlus"),xe=ye("Remove"),le=ut,he=at,pe=gt,ge=mt,I=ft,U=Re,b=et;return m(),h(D,null,[a(b,{"append-to-body":"",class:"hhypt-dialog-boxs hhypt-dialog-boxs-addTopicDom","show-close":!1,modelValue:n(A),"onUpdate:modelValue":e[3]||(e[3]=c=>ze(A)?A.value=c:null),title:"添加试题",width:"1000"},{header:v(({close:c,titleClass:i})=>[l("div",Bt,[l("span",{class:Me(i)},"添加试题",2),a(x,{onClick:c,underline:!1},{default:v(()=>[a(u,{size:"20"},{default:v(()=>[a(g)]),_:1})]),_:2},1032,["onClick"])])]),footer:v(()=>[l("div",Wl,[l("div",null,"当前分值："+R(n(F)),1),l("div",Bl,[a(U,{style:{width:"70px"},onClick:e[2]||(e[2]=c=>A.value=!1)},{default:v(()=>e[30]||(e[30]=[se("取消")])),_:1,__:[30]}),a(U,{style:{width:"70px"},type:"primary",onClick:re},{default:v(()=>e[31]||(e[31]=[se(" 确定 ")])),_:1,__:[31]})])])]),default:v(()=>[l("div",Pt,[l("div",qt,[e[4]||(e[4]=l("span",null,"题号：",-1)),a(Y,{modelValue:n(f).chineseNum,"onUpdate:modelValue":e[0]||(e[0]=c=>n(f).chineseNum=c),placeholder:"题号",style:{width:"100px"}},{default:v(()=>[(m(!0),h(D,null,Z(n(dt),c=>(m(),P(k,{key:c.value,label:c.label,value:c.label},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),e[5]||(e[5]=l("span",{style:{"padding-left":"20px"}},"大题题目：",-1)),a(E,{modelValue:n(f).name,"onUpdate:modelValue":e[1]||(e[1]=c=>n(f).name=c),style:{width:"500px"},placeholder:"请输入题目"},null,8,["modelValue"])]),l("div",zt,[l("div",Dt,[(m(!0),h(D,null,Z(n(o),(c,i)=>(m(),h("div",{class:Me(["li-box",{on:n(d)==i}]),key:i},[l("div",Rt,[l("div",Jt,[l("div",Qt,[e[6]||(e[6]=l("span",null,"题型：",-1)),a(Y,{onChange:t=>C(t,i),modelValue:c.logicTypeId,"onUpdate:modelValue":t=>c.logicTypeId=t,placeholder:"题型",style:{width:"130px"}},{default:v(()=>[(m(!0),h(D,null,Z(n(f).list,t=>(m(),P(k,{key:t.id,label:t.name,value:t.id},null,8,["label","value"]))),128))]),_:2},1032,["onChange","modelValue","onUpdate:modelValue"])]),l("div",Kt,[e[7]||(e[7]=l("span",{class:"bt"},"题号：",-1)),a(S,{onChange:t=>s(t,i),"controls-position":"right","step-strictly":!0,modelValue:c.frontNum,"onUpdate:modelValue":t=>c.frontNum=t,min:1,max:c.afterNum||9999,step:1},null,8,["onChange","modelValue","onUpdate:modelValue","max"]),e[8]||(e[8]=l("span",{style:{padding:"0 5px"}},"~",-1)),a(S,{onChange:t=>s(t,i),"controls-position":"right","step-strictly":!0,modelValue:c.afterNum,"onUpdate:modelValue":t=>c.afterNum=t,min:c.frontNum||1,step:1},null,8,["onChange","modelValue","onUpdate:modelValue","min"])]),l("div",Gt,[e[9]||(e[9]=l("span",{class:"bt"},"分值：",-1)),a(S,{onChange:t=>T(t,i),"controls-position":"right","step-strictly":!0,modelValue:c.full_score,"onUpdate:modelValue":t=>c.full_score=t,min:0,step:.5},null,8,["onChange","modelValue","onUpdate:modelValue"])]),n(f).type=="multiple"?(m(),h("div",Zt,[e[10]||(e[10]=l("span",{class:"bt"},"选项：",-1)),a(S,{disabled:c.disabled,onChange:t=>y(t,i),"controls-position":"right","step-strictly":!0,modelValue:c.optionsCount,"onUpdate:modelValue":t=>c.optionsCount=t,min:2,max:7,step:1},null,8,["disabled","onChange","modelValue","onUpdate:modelValue"])])):n(f).type=="FreeAnswer"?(m(),h("div",Yt,[l("div",Xt,[a(M,{onChange:t=>He(t,i),modelValue:c.lineFalg,"onUpdate:modelValue":t=>c.lineFalg=t,label:"每题添加长横线："},null,8,["onChange","modelValue","onUpdate:modelValue"])]),a(S,{onChange:t=>Ve(t,i),"controls-position":"right","step-strictly":!0,modelValue:c.lineNum,"onUpdate:modelValue":t=>c.lineNum=t,min:1,step:1},null,8,["onChange","modelValue","onUpdate:modelValue"])])):n(f).type=="BlankFilling"?(m(),h("div",jt,[e[11]||(e[11]=l("span",{class:"bt"},"每题空格：",-1)),a(S,{onChange:t=>W(t,i),"controls-position":"right","step-strictly":!0,modelValue:c.spaceNum,"onUpdate:modelValue":t=>c.spaceNum=t,min:1,step:1},null,8,["onChange","modelValue","onUpdate:modelValue"]),e[12]||(e[12]=l("span",{class:"bt"},"每行",-1)),a(S,{onChange:t=>Ce(t,i),"controls-position":"right","step-strictly":!0,modelValue:c.spaceChline,"onUpdate:modelValue":t=>c.spaceChline=t,min:1,step:1},null,8,["onChange","modelValue","onUpdate:modelValue"]),e[13]||(e[13]=l("span",{style:{"padding-right":"20px"}},"空",-1)),a(M,{title:"合并后使用一个打分框",modelValue:c.mergeChecked,"onUpdate:modelValue":t=>c.mergeChecked=t,label:"合并"},null,8,["modelValue","onUpdate:modelValue"])])):ne("",!0)]),l("div",el,[a(u,{class:"huiA2",onClick:t=>Te("+",i)},{default:v(()=>[a(X)]),_:2},1032,["onClick"]),n(o).length>1?(m(),P(u,{key:0,class:"huiA2",onClick:t=>Te("-",i)},{default:v(()=>[a(xe)]),_:2},1032,["onClick"])):ne("",!0)])]),c.logicTypeId==n(ue).MultipleChoice?(m(),h("div",tl,[e[14]||(e[14]=l("span",null,"多选题赋分规则 ：",-1)),a(he,{class:"group",onChange:t=>Q(t,c),modelValue:c.scoreMethod,"onUpdate:modelValue":t=>c.scoreMethod=t},{default:v(()=>[(m(!0),h(D,null,Z(Ie.partScoreTypes,t=>(m(),P(le,{key:t.value,value:t.value},{default:v(()=>[se(R(t.label),1)]),_:2},1032,["value"]))),128))]),_:2},1032,["onChange","modelValue","onUpdate:modelValue"]),a(S,{modelValue:c.scoreTotal,"onUpdate:modelValue":t=>c.scoreTotal=t,max:100,min:0,style:{width:"100px","margin-left":"10px"},"controls-position":"right"},null,8,["modelValue","onUpdate:modelValue"]),l("span",null,R(c.scoreMethod==1?"分":"%"),1)])):ne("",!0)],2))),128))]),l("div",ll,[(m(!0),h(D,null,Z(n(o),(c,i)=>(m(),h("div",{class:"item-box",key:i},[(m(!0),h(D,null,Z(c.children,(t,z)=>{var Le,B,H,$e,Ke;return m(),h("div",{class:Me(["item-com",{on:i+"-"+z==n(d)}]),key:z},[n(f).type=="multiple"?(m(),h("div",ol,[l("div",sl,[l("div",nl,R(t.topicSortNum)+". ",1),c.logicTypeId==n(ue).MultipleChoice?(m(),h("div",il,[a(ge,{modelValue:t.answer,"onUpdate:modelValue":r=>t.answer=r},{default:v(()=>[(m(!0),h(D,null,Z(t.options,(r,ce)=>(m(),P(pe,{class:"radio-button-boxs checkbox-button-boxs",key:ce,label:r,value:r},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])])):(m(),h("div",al,[a(he,{modelValue:t.answer,"onUpdate:modelValue":r=>t.answer=r},{default:v(()=>[(m(!0),h(D,null,Z(t.options,(r,ce)=>(m(),P(I,{class:"radio-button-boxs",key:ce,label:r,value:r},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]))]),l("div",ul,[l("div",rl,[a(S,{style:{width:"80px"},onChange:r=>N(r,i,z),"controls-position":"right","step-strictly":!0,modelValue:t.full_score,"onUpdate:modelValue":r=>t.full_score=r,min:0,step:.5},null,8,["onChange","modelValue","onUpdate:modelValue"]),e[15]||(e[15]=l("span",{style:{"padding-right":"10px"}},"分",-1)),a(S,{disabled:c.disabled,style:{width:"80px"},onChange:r=>w(r,i,z),"controls-position":"right","step-strictly":!0,modelValue:t.optionsCount,"onUpdate:modelValue":r=>t.optionsCount=r,min:2,max:7,step:1},null,8,["disabled","onChange","modelValue","onUpdate:modelValue"]),e[16]||(e[16]=l("span",null,"个选项，",-1))])])])):n(f).type=="FreeAnswer"||n(f).type=="Composition"?(m(),h("div",dl,[n(f).type=="FreeAnswer"?(m(),h("div",pl,[l("div",cl,[l("div",ml,R(t.topicSortNum)+". ",1),!(t!=null&&t.splitChildren)||((Le=t==null?void 0:t.splitChildren)==null?void 0:Le.length)==0?(m(),h("div",gl,[a(M,{onChange:r=>Oe(r,i,z),modelValue:t.lineFalg,"onUpdate:modelValue":r=>t.lineFalg=r,label:"每题添加长横线："},null,8,["onChange","modelValue","onUpdate:modelValue"]),a(S,{onChange:r=>Ee(r,i,z),"controls-position":"right",style:{width:"80px"},"step-strictly":!0,modelValue:t.lineNum,"onUpdate:modelValue":r=>t.lineNum=r,min:1,step:1},null,8,["onChange","modelValue","onUpdate:modelValue"])])):ne("",!0)]),l("div",fl,[l("div",yl,[a(S,{style:{width:"80px"},disabled:(t==null?void 0:t.splitChildren)&&((B=t==null?void 0:t.splitChildren)==null?void 0:B.length)!=0,onChange:r=>N(r,i,z),"controls-position":"right","step-strictly":!0,modelValue:t.full_score,"onUpdate:modelValue":r=>t.full_score=r,min:0,step:.5},null,8,["disabled","onChange","modelValue","onUpdate:modelValue"]),e[18]||(e[18]=l("span",{style:{"padding-right":"10px"}},"分",-1)),a(x,{underline:!1,type:"primary",onClick:r=>q(i,z)},{default:v(()=>e[17]||(e[17]=[se("添加小题")])),_:2,__:[17]},1032,["onClick"])])])])):(m(),h("div",_l,[l("div",vl,[l("div",hl,R(t.topicSortNum)+". ",1),l("div",Cl,[a(M,{onChange:r=>K(r,t),modelValue:t.radioFalg,"onUpdate:modelValue":r=>t.radioFalg=r,label:"是否是选择题"},null,8,["onChange","modelValue","onUpdate:modelValue"])]),t.radioFalg?(m(),h("div",bl,[a(Y,{onChange:r=>G(r,t),modelValue:t.logicTypeId,"onUpdate:modelValue":r=>t.logicTypeId=r,placeholder:"题型",style:{width:"100px"}},{default:v(()=>[(m(!0),h(D,null,Z(n(Be),r=>(m(),P(k,{key:r.id,label:r.name,value:r.id},null,8,["label","value"]))),128))]),_:2},1032,["onChange","modelValue","onUpdate:modelValue"]),t.logicTypeId==n(ue).MultipleChoice?(m(),h("div",Vl,[a(ge,{modelValue:t.answer,"onUpdate:modelValue":r=>t.answer=r},{default:v(()=>[(m(!0),h(D,null,Z(t.options,(r,ce)=>(m(),P(pe,{class:"radio-button-boxs checkbox-button-boxs",key:ce,label:r,value:r},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])])):(m(),h("div",xl,[a(he,{modelValue:t.answer,"onUpdate:modelValue":r=>t.answer=r},{default:v(()=>[(m(!0),h(D,null,Z(t.options,(r,ce)=>(m(),P(I,{class:"radio-button-boxs",key:ce,label:r,value:r},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]))])):ne("",!0)]),l("div",Sl,[l("div",wl,[a(S,{style:{width:"80px"},disabled:(t==null?void 0:t.splitChildren)&&((H=t==null?void 0:t.splitChildren)==null?void 0:H.length)!=0,onChange:r=>N(r,i,z),"controls-position":"right","step-strictly":!0,modelValue:t.full_score,"onUpdate:modelValue":r=>t.full_score=r,min:0,step:.5},null,8,["disabled","onChange","modelValue","onUpdate:modelValue"]),e[21]||(e[21]=l("span",{style:{"padding-right":"10px"}},"分",-1)),t.radioFalg?(m(),h("div",Nl,[a(S,{disabled:t.disabled,style:{width:"80px"},onChange:r=>w(r,i,z),"controls-position":"right","step-strictly":!0,modelValue:t.optionsCount,"onUpdate:modelValue":r=>t.optionsCount=r,min:2,max:7,step:1},null,8,["disabled","onChange","modelValue","onUpdate:modelValue"]),e[19]||(e[19]=l("span",null,"个选项",-1))])):(m(),P(x,{key:1,underline:!1,type:"primary",onClick:r=>q(i,z)},{default:v(()=>e[20]||(e[20]=[se("添加小题")])),_:2,__:[20]},1032,["onClick"]))])])])),(($e=t==null?void 0:t.splitChildren)==null?void 0:$e.length)>0?(m(),h("div",kl,[l("ul",Tl,[(m(!0),h(D,null,Z(t.splitChildren,(r,ce)=>(m(),h("li",{key:ce,class:"pmm2_flex_between",style:{"align-items":"baseline","margin-bottom":"5px"}},[l("div",Il,[l("div",null,R(t.topicSortNum)+"("+R(r.sort)+")",1),t.splitType==6?(m(),h("div",Ul,[a(M,{modelValue:r.lineFalg,"onUpdate:modelValue":ae=>r.lineFalg=ae,label:"每题添加长横线："},null,8,["modelValue","onUpdate:modelValue"]),a(S,{"controls-position":"right",style:{width:"80px"},"step-strictly":!0,modelValue:r.lineNum,"onUpdate:modelValue":ae=>r.lineNum=ae,min:1,step:1},null,8,["modelValue","onUpdate:modelValue"])])):ne("",!0)]),l("div",Ll,[t.splitType==3?(m(),h("div",$l,[a(S,{onChange:ae=>n(de)(ae,r),modelValue:r.space,"onUpdate:modelValue":ae=>r.space=ae,step:1,"step-strictly":!0,min:1,style:{width:"100px"},"controls-position":"right"},null,8,["onChange","modelValue","onUpdate:modelValue"]),e[22]||(e[22]=l("span",{style:{padding:"0 5px"}},"空,",-1))])):ne("",!0),l("div",null,[l("div",null,[a(S,{onChange:ae=>ke(ae,r,t),step:t.step_score,"step-strictly":!0,modelValue:r.score,"onUpdate:modelValue":ae=>r.score=ae,style:{width:"100px"},"controls-position":"right"},null,8,["onChange","step","modelValue","onUpdate:modelValue"]),e[23]||(e[23]=l("span",{style:{"padding-left":"5px"}},"分",-1))])]),a(x,{underline:!1,type:"primary",onClick:ae=>be(t,ce)},{default:v(()=>e[24]||(e[24]=[se("删除")])),_:2,__:[24]},1032,["onClick"])])]))),128))])])):ne("",!0)])):n(f).type=="BlankFilling"?(m(),h("div",Fl,[l("div",Ml,[l("div",El,R(t.topicSortNum)+". ",1)]),l("div",Hl,[c.mergeChecked?(m(),h("div",Ol,[e[25]||(e[25]=l("span",{class:"bt"},"每题空格：",-1)),a(S,{style:{width:"80px"},onChange:r=>te(r,i,z),"controls-position":"right","step-strictly":!0,modelValue:t.spaceNum,"onUpdate:modelValue":r=>t.spaceNum=r,min:1,step:1},null,8,["onChange","modelValue","onUpdate:modelValue"])])):(m(),h("div",Al,[a(S,{style:{width:"80px"},disabled:(t==null?void 0:t.splitChildren)&&((Ke=t==null?void 0:t.splitChildren)==null?void 0:Ke.length)!=0,onChange:r=>N(r,i,z),"controls-position":"right","step-strictly":!0,modelValue:t.full_score,"onUpdate:modelValue":r=>t.full_score=r,min:0,step:.5},null,8,["disabled","onChange","modelValue","onUpdate:modelValue"]),e[26]||(e[26]=l("span",{style:{"padding-right":"10px"}},"分",-1)),e[27]||(e[27]=l("span",{class:"bt"},"每题空格：",-1)),a(S,{style:{width:"80px"},onChange:r=>te(r,i,z),"controls-position":"right","step-strictly":!0,modelValue:t.spaceNum,"onUpdate:modelValue":r=>t.spaceNum=r,min:1,step:1},null,8,["onChange","modelValue","onUpdate:modelValue"]),e[28]||(e[28]=l("span",{style:{"padding-left":"10px"}},"每行",-1)),a(S,{style:{width:"80px"},onChange:r=>ie(r,i,z),"controls-position":"right","step-strictly":!0,modelValue:t.spaceChline,"onUpdate:modelValue":r=>t.spaceChline=r,min:1,step:1},null,8,["onChange","modelValue","onUpdate:modelValue"]),e[29]||(e[29]=l("span",null,"空",-1))]))])])):ne("",!0)],2)}),128))]))),128))])])])]),_:1},8,["modelValue"]),n(oe).splitFalg?(m(),h("div",Pl,[a(vt,{ref_key:"splitQuestionsRef",ref:V,full_score:n(oe).full_score,onSetSplit:Ne,paperItem:n(oe).paperItem,itemtype:"answerSheet"},null,8,["full_score","paperItem"])])):ne("",!0)],64)}}}),zl=Qe(ql,[["__scopeId","data-v-53f5326a"]]),Dl={key:0,class:"pmm2_flex_center",style:{"flex-direction":"column"},id:"targetDivs"},Rl={style:{color:"#999"}},Jl={key:1,class:"cards-box scrollBar-C",id:"targetDivs"},Ql={class:"header-box"},Kl={class:"containers pmm2_flex_between"},Gl={class:"left-box"},Zl={href:"/"},Yl=["src"],Xl={class:"right-box"},jl={class:"main-box"},eo={key:0,class:"containers"},to={class:"setup-com",style:{"margin-bottom":"20px"}},lo={class:"title-box pmm2_flex_between"},oo={class:"com"},so={class:"com"},no={class:"com"},io={class:"com"},ao={style:{"padding-top":"15px"}},uo={class:"setup-com",style:{"padding-bottom":"10px"}},ro={class:"buttom-list pmm2_flex_acenter"},po={class:"setup-com",style:{"padding-top":"0"}},co={class:"title-box pmm2_flex_between",style:{"padding-bottom":"12px"}},mo={class:"title"},go={style:{"font-size":"14px","padding-left":"14px",color:"#252b3a"}},fo={style:{"font-size":"16px",color:"#2672FF","margin-right":"5px"}},yo={class:"tipc-list-box"},_o={class:"title"},vo={class:"pmm2_ellipsis"},ho={class:"score"},Co={class:"btn-box pmm2_flex_between"},bo=qe({__name:"index",setup(Ie){function Ue(C){return new URLSearchParams(window.location.search).get(C)}const _e=L(""),de=L(null);let A=Ue("uuid");const f=L(!1);let o=L({fractionOpen:!0,layoutOpen:!0,headNum:4,HhyptType:"answerSheet",step_score:1,familysStyle:{}});const $=L([]),j=L(!0),me=L(-1),ve=L({});L({}),L({});let J={};const we=L([]),F=async C=>{let y=(await Lt(C)).data||{},T=[{name:"选择题",type:"multiple",list:Be}];for(let w in Xe)y[w]&&T.push({name:Xe[w],type:w,list:y[w]});we.value=T},d=L(null),O=L(null),ee=C=>{let s=_().scoreSetting.length;C.topicSortNum=1,s>0&&(C.topicSortNum=_().scoreSetting[s-1].topicSortNum+1),["EnglishWriting","ChineseWriting"].includes(C.type)?O.value.opens(C):d.value.opens(C)},re=(C,s)=>{var w;let y=((w=W.value)==null?void 0:w.quesListCopy)||[];if(s)y.push(...C);else{let N=C[0].uuid,p=y.findIndex(e=>e.uuid==N);y=y.filter(e=>e.uuid!==N&&N!==e.H1uuid),p!==-1?y.splice(p,0,...C):y.push(...C)}let T=_().pageStyles.objectiveQuestionMerging;T&&C.findIndex(p=>p.type=="radio")!=-1&&(y.shift(),y.shift(),y=V(T,y)),K.value=y,q()},Q=L([]),K=L([]);(async()=>{var N,p;if(!A){_e.value="非法请求";return}let C={uuid:A};F(C);const s=await kt(C);if(s.code!=0)return j.value=!1,_e.value=s.msg||"",fe.error(s.msg);const y=s.data;J=y.cardInfo;let T=J.setting;T.scoringResultTypeId===void 0&&(T.scoringResultTypeId="score"),ve.value=J.partScorePlan,T.contentTypeId=1,$.value=J.metadata,o.value.title=J.title,o.value.id=J.id,o.value.created_by_name=J.created_by_name||"-",o.value.createdAt=J.createdAt;let w=((p=(N=T.pageLayout)==null?void 0:N.printLayout)==null?void 0:p.column)||2;if(o.value.headNum=w*2,o.value.column=w,o.value.showCreatorInfoVal=J.showCreatorInfoVal||"",T.pageLayoutId||(T.pageLayoutId="A4_1"),_().setting=T,y.scoreSetting)K.value=y.cardInfo.cardList||y.cardInfo.cardLsit,_().questionsScoreList=y.questionsScoreList,_().scoreSetting=y.scoreSetting,_().pageStyles=y.cardInfo.pageStyle;else{let e=JSON.parse(JSON.stringify(J.metadata.cardPageStyles[T.pageLayoutId]));if(e.contentLineHeightMM||(e.contentLineHeightMM=Math.round((e==null?void 0:e.contentLineHeight)/4.75)),!e.contentLineHeightMMH){let g=e.contentLineHeightMM;g<=5?e.contentLineHeightMMH=1.5:e.contentLineHeightMMH=(1.5+(g-5)*.3).toFixed(1)}_().pageStyles=e,f.value=!0}je(_(),!1,o.value.column),j.value=!1})();const W=L(null),te=(C,s)=>{let y=JSON.parse(JSON.stringify(C));if(y.idx=s,y.type!="multiple"){let T=W.value.quesListCopy.filter(N=>N.H1uuid==C.uuid&&(N.type=="free"||N.type=="writing")),w="freeList";["EnglishWriting","ChineseWriting","writing"].includes(y.type)&&(w="writingList"),T.forEach(N=>{N[w].forEach(p=>{let e=y.children.findIndex(g=>g.uuid==p.uuid);e!==-1&&(y.children[e]=p)})})}["EnglishWriting","ChineseWriting"].includes(y.type)?O.value.editOpens(y):d.value.editOpens(y)},Ce=(C,s)=>{Ye.confirm("删除后不可恢复，是否确认删除?","确认删除",{confirmButtonText:"确认删除",cancelButtonText:"取消",type:"warning"}).then(()=>{_().scoreSetting.splice(s,1),_().questionsScoreList=_().questionsScoreList.filter(w=>w.H1uuid!=C);let y=W.value.quesListCopy.filter(w=>w.itemTypeId=="H1"?w.uuid!=C:w.H1uuid!=C),T=_().pageStyles.objectiveQuestionMerging;T&&(y.shift(),y.shift(),y=V(T,y)),K.value=y,q(),fe({type:"success",message:"删除成功！"})}).catch(()=>{})};let ie=nt(()=>_().scoreSetting.reduce((s,y)=>s+(y.full_score||0),0));const oe=C=>{let s=W.value.quesListCopy;K.value=V(C,s),q({type:"copy"})},V=(C,s)=>{var y;if(C){const T={type:"radio",itemTypeId:ue.QUESTION,groupSize:1},w=[];for(let e=0;e<s.length;e++)s[e].type=="radio"?(w.push(...s[e].optionList),s[e].isFalg=!0):((y=s[e+1])==null?void 0:y.type)=="radio"&&s[e].itemTypeId=="H1"&&(!s[e+2]||s[e+2].itemTypeId=="H1")&&(s[e].isFalg=!0);if(w.length==0)return _().pageStyles.objectiveQuestionMerging=!1,fe.error("请先添加选择题"),s;let N=JSON.parse(JSON.stringify(Nt));N.title="选择题",N.isFalg=!1;let p={...T,optionList:w,H1uuid:Se(),isFalg:!1};N.uuid=p.H1uuid,s.unshift(p),s.unshift(N)}else s.forEach(T=>{T.isFalg=!1}),s.shift(),s.shift();return s},q=C=>{var s;(C==null?void 0:C.type)=="copy"&&(K.value=((s=W==null?void 0:W.value)==null?void 0:s.quesListCopy)||[]),_().ckeditorUuid="",me.value--},Ne=(C={})=>{var y,T,w;let s=((T=(y=_().setting.pageLayout)==null?void 0:y.printLayout)==null?void 0:T.column)||2;if(o.value.headNum=s*2,C.ispageLayoutId){let N=((w=W==null?void 0:W.value)==null?void 0:w.quesListCopy)||[],p=N.findIndex(e=>e.type=="free");if(je(_(),!1,o.value.column),p==-1)return q({type:"copy"});Ye.confirm("检测到切换了纸张大小，是否重置答题卡内容?","提示",{confirmButtonText:"重置",cancelButtonText:"取消",type:"warning"}).then(async()=>{let e=_().storeEnspNum;for(let g=0;g<N.length;g++)N[g].type=="free"&&(N[g].freeList=await Pe(N[g].freeList,e));await rt(),q({type:"copy"})}).catch(()=>{q({type:"copy"})})}else q({type:"copy"})},ke=C=>{_().pageStyles.contentLineHeight=Math.round(C*4.75),C<=5?_().pageStyles.contentLineHeightMMH=1.5:_().pageStyles.contentLineHeightMMH=(1.5+(C-5)*.3).toFixed(1),q({type:"copy"})},be=L(null),Ve=L(!1),Ee=async()=>{Ve.value=!0,be.value.dialogVisible=!0;let C=Te(),s=await Tt({uuid:A,data:C});if(s.code==0)be.value.show(s.data),Ve.value=!1;else return fe.error(s.msg)},He=async()=>{const C=Ge.service({lock:!0,text:"保存中..",background:"rgba(0, 0, 0, 0.7)"});let s=Te(),y={};if(y=await It({uuid:A,data:s}),C.close(),y.code==0)fe({message:"保存成功",type:"success",plain:!0});else return fe.error("保存失败")},Oe=async()=>{var T;const C=Ge.service({lock:!0,text:"下载中..",background:"rgba(0, 0, 0, 0.7)"});let s=Te(),y=await Ut({uuid:A,data:s});try{if(y.code&&y.code!==0)return C.close(),fe.error(y.msg);let w=o.value.title+"-"+J.courseName;(T=y==null?void 0:y.data)!=null&&T.url&&wt(y.data.url,w+".pdf")}catch{}setTimeout(()=>{if(y.msg)return fe.error(y.msg);C.close()},1e3)},Te=()=>{const C=W.value.getZB();return J.cardList=W.value.quesListCopy,{uuid:A,coordinateInfo:C,cardInfo:{...J,full_score:ie.value,title:o.value.title,showCreatorInfoVal:o.value.showCreatorInfoVal,setting:_().setting,pageStyle:_().pageStyles,partScorePlan:ve.value},scoreSetting:_().scoreSetting,questionsScoreList:_().questionsScoreList}};return(C,s)=>{var M,X,xe,le,he,pe,ge,I,U,b,c,i,t,z,Le;const y=Re,T=ye("ArrowDown"),w=ye("ArrowUp"),N=Je,p=ut,e=at,g=De,u=it,x=_t,k=ye("Plus"),Y=ye("Edit"),E=ye("Delete"),S=yt;return n(_e)?(m(),h("div",Dl,[s[14]||(s[14]=l("img",{style:{width:"200px"},src:"https://oss.hhypt.com/resources/mini-parent-helper/err.png"},null,-1)),l("span",Rl,R(n(_e)),1)])):(m(),h("div",Jl,[l("header",Ql,[l("div",Kl,[l("div",Gl,[l("a",Zl,[l("img",{src:n(bt)(),alt:"Logo",width:"218",height:"38"},null,8,Yl)])]),s[18]||(s[18]=l("div",{class:"title",style:{flex:"1","text-align":"center"}},"制作答题卡",-1)),l("div",Xl,[a(y,{onClick:s[0]||(s[0]=B=>Ee())},{default:v(()=>s[15]||(s[15]=[se("预览")])),_:1,__:[15]}),a(y,{onClick:s[1]||(s[1]=B=>He()),type:"primary"},{default:v(()=>s[16]||(s[16]=[se("保存")])),_:1,__:[16]}),a(y,{onClick:s[2]||(s[2]=B=>Oe()),type:"primary"},{default:v(()=>s[17]||(s[17]=[se("下载题卡")])),_:1,__:[17]})])])]),Ae((m(),h("main",jl,[!n(j)&&n(_)().setting.pageLayout?(m(),h("div",eo,[(m(),h("div",{class:"topic-box scrollBar-C",style:We(`min-width: ${(xe=(X=(M=n(_)().setting)==null?void 0:M.pageLayout)==null?void 0:X.columnWidth)==null?void 0:xe.width}px;`),key:n(me),id:"family-box"},[l("div",{ref:"topicBoxs",style:We(`transform-origin: top left;transition: transform 0.2s; transform: translate(0, 0);margin: 0 auto;
						 width: ${(pe=(he=(le=n(_)().setting)==null?void 0:le.pageLayout)==null?void 0:he.columnWidth)==null?void 0:pe.width}px;
						 min-width: ${(U=(I=(ge=n(_)().setting)==null?void 0:ge.pageLayout)==null?void 0:I.columnWidth)==null?void 0:U.width}px;
						 max-width: ${(i=(c=(b=n(_)().setting)==null?void 0:b.pageLayout)==null?void 0:c.columnWidth)==null?void 0:i.width}px;
						`)},[a(Ct,{cardList:n(K),ref_key:"singleCardRef",ref:W,layout:n(o),questionsArr:n(Q)},null,8,["cardList","layout","questionsArr"])],4)],4)),l("div",{class:"setup-box scrollBar",style:We(`width: calc(100% - ${((Le=(z=(t=n(_)().setting)==null?void 0:t.pageLayout)==null?void 0:z.columnWidth)==null?void 0:Le.width)+4}px)`)},[l("div",to,[l("div",lo,[s[19]||(s[19]=l("div",{class:"title"},[l("span",{class:"bt"},"版面设计")],-1)),l("div",{class:"collapse pmm2_flex_acenter huiA2",onClick:s[3]||(s[3]=B=>n(o).layoutOpen=!n(o).layoutOpen)},[l("span",null,R(n(o).layoutOpen?"收起":"展开"),1),a(N,{size:"14"},{default:v(()=>[n(o).layoutOpen?(m(),P(T,{key:0})):(m(),P(w,{key:1}))]),_:1})])]),a(x,null,{default:v(()=>{var B;return[Ae(l("div",null,[l("ul",null,[l("li",null,[s[20]||(s[20]=l("div",{class:"name"},"考号识别",-1)),l("div",oo,[a(e,{onChange:s[4]||(s[4]=H=>q({type:"copy"})),modelValue:n(_)().setting.examNoTypeId,"onUpdate:modelValue":s[5]||(s[5]=H=>n(_)().setting.examNoTypeId=H)},{default:v(()=>[(m(!0),h(D,null,Z(n($).examNoTypes,H=>(m(),P(p,{value:H.value,size:"large"},{default:v(()=>[l("div",null,R(H.label),1)]),_:2},1032,["value"]))),256))]),_:1},8,["modelValue"])])]),l("li",null,[s[21]||(s[21]=l("div",{class:"name"},"页面布局",-1)),l("div",so,[l("span",null,R((B=n(_)().setting.pageLayout)==null?void 0:B.label),1),l("span",{class:"huiA",style:{"padding-left":"5px"},onClick:s[6]||(s[6]=(...H)=>n(de).opens&&n(de).opens(...H))},"修改")])]),l("li",null,[s[22]||(s[22]=l("div",{class:"name"},"内容字号",-1)),l("div",no,[a(g,{modelValue:n(_)().pageStyles.contentFontSize,"onUpdate:modelValue":s[7]||(s[7]=H=>n(_)().pageStyles.contentFontSize=H),min:14,max:28,style:{width:"80px"},size:"small","controls-position":"right",onChange:s[8]||(s[8]=H=>q({type:"copy"}))},null,8,["modelValue"])])]),l("li",null,[s[24]||(s[24]=l("div",{class:"name"},"行距",-1)),l("div",io,[a(g,{onChange:ke,modelValue:n(_)().pageStyles.contentLineHeightMM,"onUpdate:modelValue":s[9]||(s[9]=H=>n(_)().pageStyles.contentLineHeightMM=H),min:4,max:20,"controls-position":"right",style:{width:"80px"},size:"small"},null,8,["modelValue"]),s[23]||(s[23]=se(" mm "))])])]),l("div",ao,[a(u,{onChange:oe,modelValue:n(_)().pageStyles.objectiveQuestionMerging,"onUpdate:modelValue":s[10]||(s[10]=H=>n(_)().pageStyles.objectiveQuestionMerging=H),label:"客观题合并"},null,8,["modelValue"]),a(u,{modelValue:n(_)().pageStyles.DashedLine,"onUpdate:modelValue":s[11]||(s[11]=H=>n(_)().pageStyles.DashedLine=H),label:"虚线"},null,8,["modelValue"])])],512),[[Ze,n(o).layoutOpen]])]}),_:1})]),l("div",uo,[s[25]||(s[25]=l("div",{class:"title-box pmm2_flex_between"},[l("div",{class:"title"},[l("span",{class:"bt"},"添加试题")]),l("div",{class:"collapse pmm2_flex_acenter huiA2"})],-1)),l("div",null,[l("div",ro,[(m(!0),h(D,null,Z(n(we),(B,H)=>(m(),h("div",{class:"btn-item",key:H},[a(y,{onClick:$e=>ee(B)},{default:v(()=>[a(N,null,{default:v(()=>[a(k)]),_:1}),l("span",null,R(B.name),1)]),_:2},1032,["onClick"])]))),128))])])]),l("div",po,[l("div",co,[l("div",mo,[s[28]||(s[28]=l("span",{class:"bt"},"试题列表",-1)),l("span",go,[s[26]||(s[26]=se(" 目前总分：")),l("b",fo,R(n(ie)),1),s[27]||(s[27]=se("分 "))])]),l("div",{class:"collapse pmm2_flex_acenter huiA2",onClick:s[12]||(s[12]=B=>n(o).fractionOpen=!n(o).fractionOpen)},[l("span",null,R(n(o).fractionOpen?"收起":"展开"),1),a(N,{size:"14"},{default:v(()=>[n(o).fractionOpen?(m(),P(T,{key:0})):(m(),P(w,{key:1}))]),_:1})])]),a(x,null,{default:v(()=>[Ae(l("div",null,[l("div",yo,[s[29]||(s[29]=l("div",{class:"tipc-list-title"},null,-1)),(m(!0),h(D,null,Z(n(_)().scoreSetting,(B,H)=>(m(),h("div",{class:"item-box pmm2_flex_between",key:H},[l("div",_o,[l("div",vo,R(B.title),1)]),l("div",ho,R(B.full_score||0)+"分 ",1),l("div",Co,[a(N,{class:"huiA2",onClick:$e=>te(B,H)},{default:v(()=>[a(Y)]),_:2},1032,["onClick"]),a(N,{class:"huiA2",onClick:$e=>Ce(B.uuid,H)},{default:v(()=>[a(E)]),_:2},1032,["onClick"])])]))),128))])],512),[[Ze,n(o).fractionOpen]])]),_:1})])],4)])):ne("",!0)])),[[S,n(j)]]),a(Vt,{paperCardPageLayouts:n($).cardPageLayouts,paperCardPageLayoutsNo:!0,ref_key:"papeSizeRef",ref:de,onSetCardSetting:Ne,cardPageStyles:n($).cardPageStyles,cardContentTypes:n($).cardContentTypes},null,8,["paperCardPageLayouts","cardPageStyles","cardContentTypes"]),n(f)?(m(),P(St,{key:0,paperCardPageLayoutsNo:!0,paperCardPageLayouts:n($).cardPageLayouts,answerSheetPageLayouts:n($).answerSheetPageLayouts,cardPageStyles:n($).cardPageStyles,examNoTypes:n($).examNoTypes,onSetCardSetting:Ne,cardContentTypes:n($).cardContentTypes},null,8,["paperCardPageLayouts","answerSheetPageLayouts","cardPageStyles","examNoTypes","cardContentTypes"])):ne("",!0),a(xt,{ref_key:"previewDomRef",ref:be,modelValue:n(Ve),"onUpdate:modelValue":s[13]||(s[13]=B=>ze(Ve)?Ve.value=B:null)},null,8,["modelValue"]),a(zl,{ref_key:"addTopicRef",ref:d,step_score:n(o).step_score,partScoreTypes:n($).partScoreTypes,onAddSubmit:re},null,8,["step_score","partScoreTypes"]),a(Wt,{ref_key:"addWritingRef",ref:O,onAddSubmit:re,step_score:n(o).step_score},null,8,["step_score"])]))}}}),Zo=Qe(bo,[["__scopeId","data-v-0713b44d"]]);export{Zo as default};

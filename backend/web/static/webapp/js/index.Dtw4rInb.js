import{d as se,r as m,b as M,w as s,u as l,i as W,E as re,o as a,c as y,e as z,f as o,a as t,g as Ie,h as C,j as ie,n as oe,t as Z,k as pe,l as ce,m as ve,p as L,q as te,s as $e,F as le,v as Ve,x as Be,y as ye,z as Te,A as Oe,B as Ue,C as Ae,D as Ne,G as ze,H as Me,I as Je,J as Qe,K as Re,L as Fe,M as ge,N as He,O as Pe}from"./index.ZZ6UQeaF.js";/* empty css                *//* empty css                  *//* empty css                          *//* empty css                 */import{d as je,_ as Ke}from"./index.BO6VOytp.js";import{d as we,_ as Ze}from"./vuedraggable.umd.BJGhys5F.js";import{q as fe}from"./question.IyuOoK5G.js";import{q as Ee,a as De,_ as qe,b as Se,c as P,d as Ge,p as We,g as Xe,e as Ye,f as et,h as tt,i as lt,j as ot,k as st,l as nt,m as at,n as it,o as dt,r as ut,s as rt,t as pt,u as ct}from"./testPaperCenter.BktRbhxU.js";import{u as xe}from"./index.6W4rOsHv.js";/* empty css                   */import{_ as ne}from"./_plugin-vue_export-helper.DlAUqK2U.js";/* empty css                     *//* empty css                  *//* empty css                  *//* empty css                        *//* empty css                    *//* empty css                      */import{c as mt}from"./cutting.BeC-EoAi.js";/* empty css                       *//* empty css                  */import"./request.CsKOOJzG.js";import"./index.xsH4HHeE.js";/* empty css                *//* empty css                       */import"./index.vue_vue_type_style_index_0_lang.Cq6RDaj7.js";const _t={class:"pmm2_flex_between"},ft={key:0,class:"addQuestion-box"},vt={key:0,class:"pmm2_flex_acenter",style:{padding:"20px 0"}},gt={style:{flex:"1"}},yt={key:0,class:"topicMainItem-box"},bt={class:"pmm2_flex_between"},ht={class:"dialog-footer"},xt=se({__name:"addQuestion",emits:["addQuestion"],setup(T,{expose:j,emit:B}){const c=m(!1),k=m({}),i=m({}),v=m("添加大题");let x={};j({opens:(r,u)=>{x={idx:r,j:u},k.value=JSON.parse(JSON.stringify(Ee)),c.value=!0,u===null?(i.value=JSON.parse(JSON.stringify(De)),v.value="添加大题"):v.value="添加题目"}});const q=B,p=()=>{const{logicTypeList:r}=P().basicInfo;let{question:u}=k.value;if(v.value=="添加大题"){if(!i.value.content)return L({message:"请输入大题标题",type:"warning"});if(!u.categoryId){i.value.uuid=xe(),q("addQuestion",x,i.value),c.value=!1;return}}if(!u.categoryId)return L({message:"请选择题目题型",type:"warning"});let _=r.find(h=>h.value==u.logicTypeId);const d=Ge(u.difficultyId,u.categoryId,u.logicTypeId,u.detailData,_.childrenNoBody);if(d)return L({message:d,type:"warning"});v.value=="添加大题"?(i.value.children=[k.value],q("addQuestion",x,i.value)):q("addQuestion",x,k.value),c.value=!1};return(r,u)=>{const _=ce("CloseBold"),d=pe,h=ve,A=Ie,O=qe,J=Se,Q=ie,b=re;return a(),M(b,{"append-to-body":"","close-on-click-modal":!1,class:"hhypt-dialog-boxs","show-close":!1,modelValue:l(c),"onUpdate:modelValue":u[2]||(u[2]=n=>W(c)?c.value=n:null),title:l(v),width:"1000",zIndex:9,"align-center":""},{header:s(({close:n,titleClass:V})=>[t("div",_t,[t("span",{class:oe(V)},Z(l(v)),3),o(h,{onClick:n,underline:!1},{default:s(()=>[o(d,{size:"20"},{default:s(()=>[o(_)]),_:1})]),_:2},1032,["onClick"])])]),footer:s(()=>[t("div",bt,[u[6]||(u[6]=t("div",null,null,-1)),t("div",ht,[o(Q,{style:{width:"70px"},onClick:u[1]||(u[1]=n=>c.value=!1)},{default:s(()=>u[4]||(u[4]=[C("取消")])),_:1,__:[4]}),o(Q,{style:{width:"70px"},type:"primary",onClick:p},{default:s(()=>u[5]||(u[5]=[C(" 确定 ")])),_:1,__:[5]})])])]),default:s(()=>[l(c)?(a(),y("div",ft,[l(v)=="添加大题"?(a(),y("div",vt,[u[3]||(u[3]=t("div",{class:"bt"},"大题标题：",-1)),t("div",gt,[o(A,{modelValue:l(i).content,"onUpdate:modelValue":u[0]||(u[0]=n=>l(i).content=n),modelModifiers:{trim:!0},autosize:"",type:"text",maxlength:"50",placeholder:"请输入大题标题"},null,8,["modelValue"])])])):z("",!0),o(J,{question:l(k).question,isbottom:!0},{default:s(()=>[l(k).question.categoryId?(a(),y("div",yt,[o(O,{childItem:l(k),addFalg:!0,childItemClass:"testPaperCenterckeditor"},null,8,["childItem"])])):z("",!0)]),_:1},8,["question"])])):z("",!0)]),_:1},8,["modelValue","title"])}}}),kt=ne(xt,[["__scopeId","data-v-a335f038"]]),Ct={class:"pmm2_flex_between"},wt={class:"pmm2_flex_acenter"},It={class:"batchQueTypes-box"},$t={class:"batchQueTypes-top"},Vt={class:"ss"},Tt={class:"batchQueTypes-com scrollBar"},Qt={key:0,class:"list-box"},Et={class:"item"},Dt={class:"bt"},qt={class:"ss"},St={class:"pmm2_flex_between"},Lt={class:"dialog-footer"},Bt=se({__name:"batchQueTypes",emits:["logicTypeConfirm"],setup(T,{expose:j,emit:B}){const c=m(!1);m([]);let k="",i="";const v=m({}),x=m(""),$=m([]),q=(_,d,h)=>{v.value=JSON.parse(JSON.stringify(_));let A=P().basicInfo.logicTypeList.find(O=>O.value==_.question.logicTypeId)||{};$.value=A.childrenTypes||[],x.value="",k=d,i=h,c.value=!0},p=_=>{v.value.question.detailData.children.forEach(d=>{d.logicTypeId=_})},r=B,u=()=>{let _=v.value.question.detailData.children;for(let d=0;d<_.length;d++)if(!_[d].logicTypeId)return L({message:"请选择第"+(d+1)+"题题型",type:"warning"});r("logicTypeConfirm",v.value,k,i),c.value=!1};return j({opens:q}),(_,d)=>{const h=ce("CloseBold"),A=pe,O=ve,J=$e,Q=Ve,b=ie,n=re;return a(),M(n,{"append-to-body":"","close-on-click-modal":!1,class:"hhypt-dialog-boxs hhypt-dialog-lookVolume-boxs","show-close":!1,modelValue:l(c),"onUpdate:modelValue":d[3]||(d[3]=V=>W(c)?c.value=V:null),title:"批量选择题型",width:"600","align-center":""},{header:s(({close:V,titleClass:E})=>[t("div",Ct,[t("span",{class:oe(E)},"批量选择题型（第"+Z(l(i)+1)+"题）",3),t("div",wt,[o(O,{onClick:V,underline:!1},{default:s(()=>[o(A,{size:"20"},{default:s(()=>[o(h)]),_:1})]),_:2},1032,["onClick"])])])]),footer:s(()=>[t("div",St,[d[7]||(d[7]=t("div",null,null,-1)),t("div",Lt,[o(b,{style:{width:"70px"},onClick:d[2]||(d[2]=V=>c.value=!1)},{default:s(()=>d[5]||(d[5]=[C("取消")])),_:1,__:[5]}),o(b,{style:{width:"70px"},type:"primary",onClick:u},{default:s(()=>d[6]||(d[6]=[C(" 确定 ")])),_:1,__:[6]})])])]),default:s(()=>{var V,E,X;return[t("div",It,[t("div",$t,[d[4]||(d[4]=t("div",{class:"bt"},"选择题型：",-1)),t("div",Vt,[o(Q,{modelValue:l(x),"onUpdate:modelValue":d[0]||(d[0]=K=>W(x)?x.value=K:null),placeholder:"批量选择题型",onChange:p},{default:s(()=>[(a(!0),y(le,null,te(l($),(K,R)=>(a(),M(J,{key:R,label:K.label,value:K.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])])]),t("div",Tt,[(X=(E=(V=l(v))==null?void 0:V.question)==null?void 0:E.detailData)!=null&&X.children?(a(),y("div",Qt,[(a(!0),y(le,null,te(l(v).question.detailData.children,(K,R)=>(a(),y("div",Et,[t("div",Dt," 第"+Z(R+1)+"题 ",1),t("div",qt,[o(Q,{modelValue:K.logicTypeId,"onUpdate:modelValue":Y=>K.logicTypeId=Y,placeholder:"选择题型",onChange:d[1]||(d[1]=Y=>x.value="")},{default:s(()=>[(a(!0),y(le,null,te(l($),(Y,me)=>(a(),M(J,{key:me,label:Y.label,value:Y.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])])]))),256))])):z("",!0)])])]}),_:1},8,["modelValue"])}}}),Ot=ne(Bt,[["__scopeId","data-v-87481653"]]),Ut={class:"pmm2_flex_between"},At={class:"pmm2_flex_acenter"},Nt={class:"batchQueTypes-box"},zt={class:"batchQueTypes-top"},Mt={class:"ss"},Jt={class:"pmm2_flex_between"},Rt={class:"dialog-footer"},Ft=se({__name:"batchQueTypesAll",emits:["logicTypeAllConfirm"],setup(T,{expose:j,emit:B}){const c=m(!1);let k="";const i=m({}),v=m(""),x=(p,r)=>{i.value=JSON.parse(JSON.stringify(p)),v.value="",k=r,c.value=!0},$=B,q=()=>{if(!v.value)return L({message:"请选择题型",type:"warning"});const p=P().basicInfo;let r=P().categoriesInfo.find(_=>_.id==v.value),u=p.logicTypeList.find(_=>_.value==r.logicTypeId)||{};i.value.children.forEach(_=>{_.question.categoryId=v.value,_.question.categoryName=r.name,_.question.canComposite=u.canComposite,_.question.childrenNoBody=u.childrenNoBody,_.question.detailData.children||(_.question.detailData.children=JSON.parse(JSON.stringify(Ee.question.detailData.children))),_.question.logicTypeId==100&&u.canComposite||(_.question.logicTypeId=Number(r.logicTypeId))}),$("logicTypeAllConfirm",i.value,k),c.value=!1};return j({opens:x}),(p,r)=>{const u=ce("CloseBold"),_=pe,d=ve,h=$e,A=Ve,O=ie,J=re;return a(),M(J,{"append-to-body":"","close-on-click-modal":!1,class:"hhypt-dialog-boxs hhypt-dialog-lookVolume-boxs","show-close":!1,modelValue:l(c),"onUpdate:modelValue":r[2]||(r[2]=Q=>W(c)?c.value=Q:null),title:"批量选择题型",width:"450","align-center":""},{header:s(({close:Q,titleClass:b})=>[t("div",Ut,[t("span",{class:oe(b)},"批量选择题型（"+Z(l(i).content||"")+"）",3),t("div",At,[o(d,{onClick:Q,underline:!1},{default:s(()=>[o(_,{size:"20"},{default:s(()=>[o(u)]),_:1})]),_:2},1032,["onClick"])])])]),footer:s(()=>[t("div",Jt,[r[6]||(r[6]=t("div",null,null,-1)),t("div",Rt,[o(O,{style:{width:"70px"},onClick:r[1]||(r[1]=Q=>c.value=!1)},{default:s(()=>r[4]||(r[4]=[C("取消")])),_:1,__:[4]}),o(O,{style:{width:"70px"},type:"primary",onClick:q},{default:s(()=>r[5]||(r[5]=[C(" 确定 ")])),_:1,__:[5]})])])]),default:s(()=>[t("div",Nt,[t("div",zt,[r[3]||(r[3]=t("div",{class:"bt"},"选择题型：",-1)),t("div",Mt,[o(A,{modelValue:l(v),"onUpdate:modelValue":r[0]||(r[0]=Q=>W(v)?v.value=Q:null),placeholder:"批量选择题型"},{default:s(()=>[(a(!0),y(le,null,te(l(P)().categoriesInfo,Q=>(a(),M(h,{key:Q.id,label:Q.name,value:Q.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])])])])]),_:1},8,["modelValue"])}}}),Ht=ne(Ft,[["__scopeId","data-v-77175cc8"]]),Pt=["id"],jt={class:"top-box pmm2_flex_between"},Kt={class:"title"},Zt={class:"right-btn"},Gt={class:"topic-main-boxs"},Wt=se({__name:"topicItem",props:["childItem","jdx","idx","batchEditable"],emits:["dellQues","addQuestionOpen","batchQue"],setup(T,{emit:j}){const B=j,c=()=>{B("addQuestionOpen")},k=()=>{B("dellQues")},i=()=>{B("batchQue")};return(v,x)=>{const $=ie,q=qe,p=Se;return a(),y("div",{class:"topic-items",id:"topicId"+T.idx+"c"+T.jdx},[t("div",jt,[t("div",Kt,"第"+Z(T.childItem.pagesIndex||T.jdx+1)+"题",1),t("div",Zt,[T.childItem.question.logicTypeId==100||T.childItem.question.logicTypeId==12||T.childItem.question.logicTypeId==14||T.childItem.question.logicTypeId==13?(a(),M($,{key:0,class:"el-button-small-hhypt",size:"small",icon:l(Be),onClick:i},{default:s(()=>x[0]||(x[0]=[C("批量设置题型")])),_:1,__:[0]},8,["icon"])):z("",!0),o($,{class:"el-button-small-hhypt",size:"small",icon:l(ye),onClick:c},{default:s(()=>x[1]||(x[1]=[C("新增题目")])),_:1,__:[1]},8,["icon"]),o($,{class:"el-button-small-hhypt",size:"small",icon:l(Te),onClick:k},{default:s(()=>x[2]||(x[2]=[C("删除题目")])),_:1,__:[2]},8,["icon"])])]),t("div",Gt,[o(q,{childItem:T.childItem},null,8,["childItem"])]),o(p,{question:T.childItem.question},null,8,["question"])],8,Pt)}}}),Xt=ne(Wt,[["__scopeId","data-v-e7a4a1f7"]]),Yt={class:"pmm2_flex_between"},el={class:"pmm2_flex_acenter"},tl={class:"lookVolume-box scrollBar"},ll={class:"demo-image__lazy pmm2_flex_center",style:{width:"100%"}},ol=se({__name:"lookVolume",props:Oe(["paperData"],{modelValue:{},modelModifiers:{}}),emits:["update:modelValue"],setup(T){const j=Ue(T,"modelValue"),B=T,c=()=>{let k=B.paperData.word_url;const i=k.lastIndexOf("/"),v=k.substring(i+1);let x=document.createElement("a");x.href=k,x.download=v,x.style.display="none",document.body.appendChild(x),x.click(),x.remove()};return(k,i)=>{const v=ve,x=ce("CloseBold"),$=pe,q=Ae,p=re;return a(),M(p,{"append-to-body":"","close-on-click-modal":!1,class:"hhypt-dialog-boxs hhypt-dialog-lookVolume-boxs","show-close":!1,modelValue:j.value,"onUpdate:modelValue":i[0]||(i[0]=r=>j.value=r),title:"设置考点/题点",width:"1000","align-center":""},{header:s(({close:r,titleClass:u})=>[t("div",Yt,[t("span",{class:oe(u)},"查看原卷",2),t("div",el,[o(v,{type:"primary",style:{"padding-right":"20px"},underline:!1,icon:l(Ne),onClick:c},{default:s(()=>i[1]||(i[1]=[C("下载原卷")])),_:1,__:[1]},8,["icon"]),o(v,{onClick:r,underline:!1},{default:s(()=>[o($,{size:"20"},{default:s(()=>[o(x)]),_:1})]),_:2},1032,["onClick"])])])]),default:s(()=>[t("div",tl,[t("div",ll,[(a(!0),y(le,null,te(T.paperData.word_pics,(r,u)=>(a(),M(q,{class:"demo-image-box",key:u,src:r,"preview-src-list":T.paperData.word_pics,"initial-index":u},null,8,["src","preview-src-list","initial-index"]))),128))])])]),_:1},8,["modelValue"])}}}),sl=ne(ol,[["__scopeId","data-v-68f59a7f"]]),nl={class:"pmm2_flex_between"},al={class:"upload-demo-box"},il={key:0,class:"t1"},dl={key:1},ul={class:"t2"},rl={class:"bottom-box"},pl=se({__name:"dialog",props:["uuid"],emits:["confirm"],setup(T,{expose:j,emit:B}){const c=m(!1),k=T,i=m(!1),v=m(""),x=m([]);let $=m([]),q=null;j({opens:d=>{q=null,c.value=!1,v.value="",$.value=[".docx"],d.pdfAnalysisEnabled&&$.value.push(".pdf"),x.value=[],i.value=!0}});const r=B,u=()=>{if(!q.get("uuid"))return L.error("请先上传文件");c.value=!0,We(q).then(d=>{if(d.code!=0)return L.error(d.msg);i.value=!1,r("confirm",{type:q.get("type")})}).finally(()=>{c.value=!1})},_=d=>{let A=d.name.toLowerCase().split(".").pop();if(!$.value.includes("."+A))return L.error("上传格式不正确，仅支持"+$.value.join(",")+"格式");if(d.size/1024/1024>10)return L.error("上传文件大小不能超过10M");q=new FormData,v.value=d.name,q.append("file",d),q.append("uuid",k.uuid),q.append("type",A)};return(d,h)=>{const A=ce("CloseBold"),O=pe,J=ve,Q=ie,b=ze,n=re;return a(),M(n,{"append-to-body":"",class:"hhypt-dialog-boxs hhypt-dialog-boxs-upXlsx","show-close":!1,modelValue:l(i),"onUpdate:modelValue":h[2]||(h[2]=V=>W(i)?i.value=V:null),title:"上传单词",width:"500","align-center":"","close-on-click-modal":!1},{header:s(({close:V,titleClass:E})=>[t("div",nl,[t("span",{class:oe(E)},"上传答案",2),l(c)?z("",!0):(a(),M(J,{key:0,onClick:V,underline:!1},{default:s(()=>[o(O,{size:"20"},{default:s(()=>[o(A)]),_:1})]),_:2},1032,["onClick"]))])]),default:s(()=>[t("div",al,[o(b,{class:"upload-demo",disabled:l(c),drag:"",action:"#","http-request":()=>{},"before-upload":_,"show-file-list":!1,accept:l($).join(",")},{default:s(()=>[l(v)?(a(),y("div",il,Z(l(v)),1)):(a(),y("div",dl,[h[4]||(h[4]=t("div",{class:"t1"},"点击选择文件或将文件拖入框中",-1)),t("div",ul,[C("导入支持"+Z(l($).join(","))+"格式，大小",1),h[3]||(h[3]=t("span",{style:{color:"red"}},"不超过10M",-1))])])),o(Q,null,{default:s(()=>h[5]||(h[5]=[C("选择文件")])),_:1,__:[5]})]),_:1},8,["disabled","accept"])]),t("div",rl,[o(Q,{onClick:h[0]||(h[0]=V=>i.value=!1),loading:l(c)},{default:s(()=>h[6]||(h[6]=[C("取消")])),_:1,__:[6]},8,["loading"]),o(Q,{type:"primary",onClick:h[1]||(h[1]=V=>u()),loading:l(c)},{default:s(()=>h[7]||(h[7]=[C("确认")])),_:1,__:[7]},8,["loading"])])]),_:1},8,["modelValue"])}}}),cl=ne(pl,[["__scopeId","data-v-4550c11f"]]),ml={key:0,class:"footer-box pmm2_flex_between"},_l={class:"left-box pmm2_flex_acenter"},fl={key:1,class:"footer-box footer-box2"},vl={key:2,style:{width:"100%"}},gl={key:3,style:{width:"100%",position:"relative",height:"100%"}},yl={class:"questions-left-box scrollBar-C"},bl={class:"ul pmm2_flex_acenter"},hl=se({__name:"drawer",props:["uuid"],emits:["getLists"],setup(T,{expose:j,emit:B}){const c=T,k=m(!1),i=m(0),v=m(""),x=b=>{i.value=0,k.value=!0,v.value=b.type,b.type=="pdf"?u():A()},$=m(null),q=()=>{$.value.divideImgShow()},p=m(1),r=m([]),u=()=>{p.value=1,Xe({uuid:c.uuid}).then(b=>{if(i.value=1,(b==null?void 0:b.code)==0){let n=b.data||[];r.value=n.pages||[]}else b.msg&&L({message:b.msg,type:"warning"})})},_=m(!1),d=async()=>{_.value=!0;try{let b=await et({uuid:c.uuid,pageContents:r.value});b.code==0?A():(b.msg&&L({message:b.msg,type:"warning"}),_.value=!1)}catch{_.value=!1}},h=m({domHtmls:""}),A=()=>{h.value={domHtmls:""},Ye({uuid:c.uuid}).then(b=>{if((b==null?void 0:b.code)==0){i.value=2;let n=b.data||{questions:[]},V=[],E=-1;n.questions.forEach(X=>{X.itemTypeId=="H1"?(E++,V[E]={children:[],...X}):V[E].children.push(X)}),n.questionsArr=V,h.value=n}else L({message:b.msg,type:"warning"});_.value=!1})},O=m(null),J=B,Q=async()=>{const b=Qe.service({lock:!0,text:"提交中..",background:"rgba(0, 0, 0, 0.7)"});try{const{questions:n,html:V}=await O.value.getQuestions();let E=await tt({uuid:c.uuid,questions:n,html:V});if(E.code!=0){b.close(),L({message:E.msg,type:"warning"});return}J("getLists"),k.value=!1,b.close()}catch{L({message:"处理异常",type:"warning"}),b.close()}};return j({opens:x}),(b,n)=>{const V=Je,E=ie,X=Me;return a(),M(X,{modelValue:l(k),"onUpdate:modelValue":n[7]||(n[7]=K=>W(k)?k.value=K:null),"close-on-press-escape":!1,"close-on-click-modal":!1,size:"80%","with-header":!1,class:"uploadanswers-drawer-boxs",zIndex:9},{default:s(()=>{var K;return[l(i)==1?(a(),y("div",ml,[t("div",_l,[o(V,{"current-page":l(p),"onUpdate:currentPage":n[0]||(n[0]=R=>W(p)?p.value=R:null),"page-size":1,background:"",layout:"prev, pager, next","page-count":l(r).length},null,8,["current-page","page-count"])]),t("div",null,[o(E,{type:"danger",plain:"",loading:l(_),onClick:n[1]||(n[1]=R=>k.value=!1)},{default:s(()=>n[8]||(n[8]=[C("取消")])),_:1,__:[8]},8,["loading"]),o(E,{onClick:(K=l($))==null?void 0:K.historImgsShow},{default:s(()=>n[9]||(n[9]=[C("历史图片")])),_:1,__:[9]},8,["onClick"]),o(E,{type:"success",onClick:q},{default:s(()=>n[10]||(n[10]=[C("添加图片")])),_:1,__:[10]}),o(E,{type:"primary",loading:l(_),onClick:n[2]||(n[2]=R=>d())},{default:s(()=>n[11]||(n[11]=[C("下一步")])),_:1,__:[11]},8,["loading"])])])):l(i)==2?(a(),y("div",fl,[o(E,{onClick:n[3]||(n[3]=R=>k.value=!1),type:"danger",plain:""},{default:s(()=>n[12]||(n[12]=[C("取消")])),_:1,__:[12]}),l(v)=="pdf"?(a(),M(E,{key:0,type:"success",onClick:n[4]||(n[4]=R=>i.value=1)},{default:s(()=>n[13]||(n[13]=[C("上一步")])),_:1,__:[13]})):z("",!0),o(E,{type:"primary",onClick:n[5]||(n[5]=R=>Q())},{default:s(()=>n[14]||(n[14]=[C("完成")])),_:1,__:[14]})])):z("",!0),l(i)==1?(a(),y("div",vl,[l(r)[l(p)-1]?(a(),M(mt,{ref_key:"cuttingRef",ref:$,uuid:T.uuid,pagesInfo:l(r)[l(p)-1],key:l(p)},null,8,["uuid","pagesInfo"])):z("",!0)])):l(i)==2?(a(),y("div",gl,[t("div",yl,[n[15]||(n[15]=t("h2",{style:{"font-size":"18px"}},"试卷结构",-1)),t("ul",null,[(a(!0),y(le,null,te(l(h).questionsArr,(R,Y)=>(a(),y("li",{key:Y},[t("h3",null,Z(R.content),1),t("div",bl,[(a(!0),y(le,null,te(R.children,(me,be)=>(a(),y("div",{class:"li",key:be},Z(me.content),1))),128))])]))),128))])]),o(je,{onUpStep:n[6]||(n[6]=R=>i.value=1),style:{"background-color":"#fff"},propsUuid:T.uuid,divideType:"uploadanswers",ref_key:"divideComRef",ref:O,domHtmls:l(h).html},null,8,["propsUuid","domHtmls"])])):z("",!0)]}),_:1},8,["modelValue"])}}}),xl=ne(hl,[["__scopeId","data-v-5a00a43e"]]),kl={key:0,class:"header-box pmm2_flex_center"},Cl={class:"main-box scrollBar"},wl={class:"container"},Il={class:"topic-box scrollBar"},$l={class:"topic-title"},Vl={key:0,class:"topic-main"},Tl=["id"],Ql={key:0,class:"part_break_text"},El={key:1},Dl={class:"H1-box pmm2_flex_between"},ql={class:"pmm2_flex_acenter right-btns"},Sl={key:0},Ll={key:1,class:"childrenLen0 pmm2_flex_center"},Bl={class:"setup-box"},Ol={class:"setup-com scrollBar"},Ul={class:"exampaperstructure-box",style:{"padding-top":"15px"}},Al={key:0,class:"part_break_text pmm2_flex_center"},Nl={key:1,class:"paper-box"},zl={key:0,class:"H1-box pmm2_flex_between"},Ml=["onClick"],Jl={key:0},Rl={class:"exampaperstructure-bottom"},Fl={class:"btn-list pmm2_flex_center"},Hl={class:"pmm2_flex_between"},Pl={class:"pmm2_flex_between"},jl={class:"dialog-footer"},Kl={key:1,class:"pmm2_flex_center",style:{"flex-direction":"column"}},Zl={style:{color:"#999"}},Gl=se({__name:"index",setup(T){let B=new URLSearchParams(location.search).get("uuid"),c=m(!0);const k=()=>r.value.batchEditable,i=m({}),v=m(null),x=()=>{v.value.opens({pdfAnalysisEnabled:i.value.pdfAnalysisEnabled})},$=m(null),q=g=>{$.value.opens(g)},p=m([]),r=m({}),u=m({}),_=m(!1);let d={};const h=m("非法请求"),A=m(!1),O=m(!1),J=m({}),Q=async(g=1)=>{if(!B){c.value=!1;return}if(g==1){let D=sessionStorage.getItem("basicsToken");D?d=JSON.parse(D):(d=(await lt()).data||{},sessionStorage.setItem("basicsToken",JSON.stringify(d))),P().basicInfo=d}else _.value=!1;let e=await ot({uuid:B});if(e.code!=0){c.value=!1,h.value=e.msg,L({message:e.msg,type:"warning"});return}let w=e.data||{},U=w.paper||{};u.value=w.paperData||{},P().paperInfo=U,P().isUserPaper=U.isUserPaper,P().bloomLevelEnabled=w.bloomLevelEnabled,r.value=U;let S={stageId:U.stageId,courseCode:U.courseCode};if(g==1){let D=u.value.content_split_type_id;D=="WordManual"?J.value={content_split_type_id:D,step:2,url:u.value.url}:D=="PdfManual"&&(J.value={content_split_type_id:D,step:3,url:u.value.url}),A.value=w.aiKnowledgeEnabled,w.aiKnowledgeEnabled&&(O.value=!0),P().knowledgePointsFalg=!!w.knowledgeEnabled,w.knowledgeEnabled&&st(S).then(F=>{P().knowledgePointsList=F.data||[]}),nt(S).then(F=>{P().textbookLists=F.data||[]});let N=await at(S);P().categoriesInfo=N.data||[];let _e=await it(S);P().scenesInfo=_e.data||[];let f=[],I=sessionStorage.getItem("cityToken");I?f=JSON.parse(I):(f=(await dt(S)).data||[],sessionStorage.setItem("cityToken",JSON.stringify(f))),P().cityList=f}ut(S).then(D=>{i.value=D.data||{}});let ae=w.items.length,G=[];if(ae==0){p.value=G,await ge(),_.value=!0;return}if(w.items[0].children)i.value.paperAnswerEnabled=!1,p.value=w.items;else{for(let D=0;D<ae;D++){let N=w.items[D];N.itemTypeId===fe.Subsection?G.push(N):N.itemTypeId===fe.H1headline?G.push({...N,uuid:xe(),children:[]}):(N=await rt(N),G.length==0?G[0]={...De,uuid:xe(),children:[N]}:G[G.length-1].children.push(N))}p.value=G}await ge(),_.value=!0};Q(1);const b=(g,e)=>{let w=0;for(let S=0;S<g;S++)w+=p.value[S].children.length;let U=w+e+1;return p.value[g].children[e].pagesIndex=U,U},n=m(!1),V=m(null),E=(g=null,e=null)=>{V.value.opens(g,e)},X=m(null),K=(g,e)=>{X.value.opens(g,e)},R=(g,e)=>{p.value[e]=g},Y=m(null),me=(g,e,w)=>{Y.value.opens(g,e,w)},be=(g,e,w)=>{p.value[e].children[w]=g},Le=async({idx:g,j:e},w)=>{if(e===null){let U=g+1;p.value.splice(U,0,w),await ge(),de("paperList"+U)}else!p.value[g].children||p.value[g].children.length==0?p.value[g].children=[w]:p.value[g].children.splice(e+1,0,w),await ge(),de("topicId"+g+"c"+(e+1));L({message:"添加成功",type:"success"})},he=(g=null,e=null)=>{let w="确定删除该题目吗？";if(g===null)w="是否确认清空所有题目?";else if(e===null&&(w="确定删除该大题及该大题下所有题目吗？",p.value.length==1))return L.error({message:"请至少保留一个大题！"});let U=`
			<p> 
			<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_523_1139)"><path d="M20 10C20 11.9778 19.4135 13.9112 18.3147 15.5557C17.2159 17.2002 15.6541 18.4819 13.8268 19.2388C11.9996 19.9957 9.98891 20.1937 8.0491 19.8079C6.10929 19.422 4.32746 18.4696 2.92894 17.0711C1.53041 15.6725 0.578004 13.8907 0.192152 11.9509C-0.193701 10.0111 0.00433281 8.00043 0.761209 6.17317C1.51809 4.3459 2.79981 2.78412 4.4443 1.6853C6.08879 0.58649 8.02219 0 10 0C12.6522 0 15.1957 1.05357 17.0711 2.92893C18.9464 4.8043 20 7.34784 20 10ZM10.5818 12.2318L10.8773 4.79091C10.8773 4.55824 10.7849 4.3351 10.6203 4.17058C10.4558 4.00606 10.2327 3.91364 10 3.91364C9.76734 3.91364 9.5442 4.00606 9.37968 4.17058C9.21516 4.3351 9.12273 4.55824 9.12273 4.79091L9.41819 12.2318C9.41819 12.4364 9.68637 12.5955 10 12.5955C10.3136 12.5955 10.5727 12.4364 10.5818 12.2318ZM10 13.9091C9.8202 13.9091 9.64444 13.9624 9.49494 14.0623C9.34544 14.1622 9.22892 14.3042 9.16011 14.4703C9.09131 14.6364 9.0733 14.8192 9.10838 14.9955C9.14346 15.1719 9.23004 15.3339 9.35718 15.461C9.48432 15.5881 9.6463 15.6747 9.82265 15.7098C9.999 15.7449 10.1818 15.7269 10.3479 15.6581C10.514 15.5893 10.656 15.4727 10.7559 15.3232C10.8558 15.1737 10.9091 14.998 10.9091 14.8182C10.9091 14.5771 10.8133 14.3458 10.6428 14.1754C10.4723 14.0049 10.2411 13.9091 10 13.9091Z" fill="#FB871D"/></g><defs><clipPath id="clip0_523_1139"><rect width="20" height="20" fill="white"/></clipPath></defs></svg>
			${w}<p>
			<p style=" font-size:12px; color: #999;padding-left: 24px;margin-top: 5px;">删除后将不可恢复<p>
		`;Pe.alert(U,{confirmButtonText:"确认",cancelButtonText:"取消",showCancelButton:!0,dangerouslyUseHTMLString:!0}).then(()=>{g===null?p.value=[]:e===null?p.value.splice(g,1):p.value[g].children.splice(e,1)}).catch(()=>{})},de=g=>{const e=document.getElementById(g);e&&e.scrollIntoView({behavior:"smooth"})},ue=m(!1),ke=async g=>{const e=Qe.service({lock:!0,text:g==1?"暂存中...":"保存中...",background:"rgba(0, 0, 0, 0.7)"});ue.value=!1;let w={uuid:B,paper:r.value,paperData:u.value,items:[],isSign:O.value};g==1&&(w.staging=g);let U=JSON.parse(JSON.stringify(p.value)),S=U.length,ae=[];for(let D=0;D<S;D++){const{children:N,..._e}=U[D];let f=N.length;for(let I=0;I<f;I++){let F=N[I];if(g==2){if(!F.question.categoryId)return p.value[D].children[I].reportErrors=!0,de("topicId"+D+"c"+I),e.close(),L({message:"请选择题目题型",type:"warning"});if(P().categoriesInfo.findIndex(ee=>ee.id==F.question.categoryId)==-1)return p.value[D].children[I].reportErrors=!0,de("topicId"+D+"c"+I),e.close(),L({message:"选择了不支持的题型，请重新选择",type:"warning"})}try{N[I]=await pt(F,g)}catch(H){return p.value[D].children[I].reportErrors=!0,de("topicId"+D+"c"+I),e.close(),L({message:H.msg,type:"warning"})}}ae.push(_e,...N)}w.items=ae;let G=await ct(w);e.close(),G.code==0?L({message:"操作成功",type:"success"}):L({message:G.msg,type:"warning"})};return(g,e)=>{const w=Ke,U=Ie,S=ie,ae=ce("CircleCloseFilled"),G=pe,D=He,N=re,_e=Fe;return l(c)?Re((a(),y("div",{key:0,class:oe(["analysis-box",{on:l(J).step}]),"element-loading-text":"加载中...","element-loading-background":"rgba(122, 122, 122, 0.9)"},[l(J).step?(a(),y("header",kl,[o(w,{pageData:l(J),types:"analysis"},null,8,["pageData"])])):z("",!0),t("main",Cl,[t("div",wl,[t("div",Il,[t("div",$l,[o(U,{modelValue:l(r).title,"onUpdate:modelValue":e[0]||(e[0]=f=>l(r).title=f),autosize:"",type:"text",maxlength:"50",class:"page-title el-textareas",placeholder:"请输入标题"},null,8,["modelValue"])]),l(_)?(a(),y("div",Vl,[(a(!0),y(le,null,te(l(p),(f,I)=>{var F;return a(),y("div",{class:"topic-item-box",key:I,id:"paperList"+I},[f.itemTypeId==l(fe).Subsection?(a(),y("div",Ql,Z(f.content),1)):(a(),y("div",El,[t("div",Dl,[o(U,{modelValue:f.content,"onUpdate:modelValue":H=>f.content=H,autosize:"",type:"text",maxlength:"50",class:"page-H1 el-textareas",placeholder:"请输入标题"},null,8,["modelValue","onUpdate:modelValue"]),t("div",ql,[o(S,{class:"btn",icon:l(ye),onClick:H=>E(I)},{default:s(()=>e[12]||(e[12]=[C("新增大题")])),_:2,__:[12]},1032,["icon","onClick"]),o(S,{class:"btn",icon:l(Te),onClick:H=>he(I)},{default:s(()=>e[13]||(e[13]=[C("删除大题")])),_:2,__:[13]},1032,["icon","onClick"]),o(S,{icon:l(ye),onClick:H=>K(f,I)},{default:s(()=>e[14]||(e[14]=[C("批量设置题型")])),_:2,__:[14]},1032,["icon","onClick"])])]),((F=f==null?void 0:f.children)==null?void 0:F.length)>0?(a(),y("div",Sl,[(a(!0),y(le,null,te(f.children,(H,ee)=>(a(),y("div",{key:H.uuid||ee},[o(Xt,{onAddQuestionOpen:Ce=>E(I,ee),childItem:H,idx:I,jdx:ee,onDellQues:Ce=>he(I,ee),onBatchQue:Ce=>me(H,I,ee),batchEditable:l(r).batchEditable},null,8,["onAddQuestionOpen","childItem","idx","jdx","onDellQues","onBatchQue","batchEditable"])]))),128))])):(a(),y("div",Ll,[o(S,{icon:l(ye),onClick:H=>E(I,-1)},{default:s(()=>e[15]||(e[15]=[C("新增题目")])),_:2,__:[15]},1032,["icon","onClick"])]))]))],8,Tl)}),128))])):z("",!0)]),t("div",Bl,[t("div",Ol,[e[23]||(e[23]=t("div",{class:"title-box pmm2_flex_between"},[t("div",{class:"title pmm2_flex_acenter"},[t("span",{class:"bt"},"试卷结构")]),t("div",{class:"tig"},"*拖拽题号可调整顺序")],-1)),t("div",Ul,[o(l(we),{modelValue:l(p),"onUpdate:modelValue":e[1]||(e[1]=f=>W(p)?p.value=f:null),"item-key":"uuid",move:k},{item:s(({element:f,index:I})=>[f.itemTypeId==l(fe).Subsection?(a(),y("div",Al,[t("span",null,Z(f.content),1)])):(a(),y("div",Nl,[f.itemTypeId==l(fe).H1headline?(a(),y("div",zl,[t("span",null,Z(f.content),1)])):z("",!0),t("ul",null,[o(l(we),{group:"children",modelValue:f.children,"onUpdate:modelValue":F=>f.children=F,"item-key":"index",move:k,class:"children pmm2_flex_acenter"},{item:s(({element:F,index:H})=>[t("li",{class:oe(["huiA",{reportErrors:F.reportErrors}]),onClick:ee=>(de("topicId"+I+"c"+H),F.reportErrors=!1)},[t("span",null,Z(b(I,H)),1),o(G,{class:"icon",onClick:ee=>he(I,H)},{default:s(()=>[o(ae)]),_:2},1032,["onClick"]),e[16]||(e[16]=t("img",{src:Ze,class:"FrameSvg"},null,-1))],10,Ml)]),_:2},1032,["modelValue","onUpdate:modelValue"])])]))]),_:1},8,["modelValue"]),l(p).length==0?(a(),y("div",Jl,e[17]||(e[17]=[t("div",{class:"allDel"},"暂无试题",-1)]))):z("",!0)]),t("div",Rl,[t("div",Fl,[l(i).paperAnswerEnabled?(a(),M(S,{key:0,type:"success",plain:"",onClick:e[2]||(e[2]=f=>x())},{default:s(()=>e[18]||(e[18]=[C(" 上传答案")])),_:1,__:[18]})):z("",!0),o(S,{class:"btn1",type:"primary",plain:"",onClick:e[3]||(e[3]=f=>n.value=!0)},{default:s(()=>e[19]||(e[19]=[C("查看原卷")])),_:1,__:[19]}),o(S,{class:"btn1",type:"primary",plain:"",onClick:e[4]||(e[4]=f=>ke(1))},{default:s(()=>e[20]||(e[20]=[C("暂存")])),_:1,__:[20]}),o(S,{type:"primary",onClick:e[5]||(e[5]=f=>ue.value=!0)},{default:s(()=>e[21]||(e[21]=[C(" 保存")])),_:1,__:[21]})]),e[22]||(e[22]=t("div",{class:"tig"}," *提示：暂存或关闭页面都可在自传试卷列表中查看 ",-1))])])])])]),o(kt,{ref_key:"addQuestionRef",ref:V,onAddQuestion:Le},null,512),o(sl,{paperData:l(u),modelValue:l(n),"onUpdate:modelValue":e[6]||(e[6]=f=>W(n)?n.value=f:null)},null,8,["paperData","modelValue"]),o(Ot,{ref_key:"batchQueTypesRef",ref:Y,onLogicTypeConfirm:be},null,512),o(Ht,{ref_key:"batchQueTypesAllRef",ref:X,onLogicTypeAllConfirm:R},null,512),o(N,{"append-to-body":"","close-on-click-modal":!1,class:"hhypt-dialog-boxs","show-close":!1,modelValue:l(ue),"onUpdate:modelValue":e[10]||(e[10]=f=>W(ue)?ue.value=f:null),width:"450",zIndex:9,"align-center":""},{header:s(({close:f,titleClass:I})=>[t("div",Hl,[t("span",{class:oe(I)},"提示",2)])]),footer:s(()=>[t("div",Pl,[t("div",null,[l(A)?(a(),M(D,{key:0,size:"large",modelValue:l(O),"onUpdate:modelValue":e[7]||(e[7]=f=>W(O)?O.value=f:null),label:"智能标注知识点"},null,8,["modelValue"])):z("",!0)]),t("div",jl,[o(S,{style:{width:"70px"},onClick:e[8]||(e[8]=f=>ue.value=!1)},{default:s(()=>e[24]||(e[24]=[C("取消")])),_:1,__:[24]}),o(S,{style:{width:"70px"},type:"primary",onClick:e[9]||(e[9]=f=>ke(2))},{default:s(()=>e[25]||(e[25]=[C(" 保存 ")])),_:1,__:[25]})])])]),default:s(()=>[e[26]||(e[26]=t("div",{class:"saveTigOpen-box",style:{"padding-top":"10px","font-size":"16px","line-height":"32px"}},[C(" 如果试题内容还没有全部编辑完，请点击"),t("b",{style:{color:"coral"}},"【暂存】"),C("按钮。 【保存】之后将无法再批量编辑试卷内容！！！ ")],-1))]),_:1,__:[26]},8,["modelValue"]),l(i).paperAnswerEnabled?(a(),M(cl,{key:1,ref_key:"uploadanswersDialogRef",ref:v,onConfirm:q,uuid:l(B)},null,8,["uuid"])):z("",!0),l(i).paperAnswerEnabled?(a(),M(xl,{key:2,onGetLists:e[11]||(e[11]=f=>Q(2)),ref_key:"uploadanswersDrawerRef",ref:$,uuid:l(B)},null,8,["uuid"])):z("",!0)],2)),[[_e,!l(_)]]):(a(),y("div",Kl,[e[27]||(e[27]=t("img",{style:{width:"200px"},src:"https://oss.hhypt.com/resources/mini-parent-helper/err.png"},null,-1)),t("span",Zl,Z(l(h)),1)]))}}}),wo=ne(Gl,[["__scopeId","data-v-87148a36"]]);export{wo as default};

import{d as b,r as n,at as T,aF as k,aK as s,c as g,K as q,L as x,u as c,e as P,a as w,au as B,f as V,p as D,o as f}from"./index.ZZ6UQeaF.js";import{g as A}from"./answerSheet.XzQSe7IO.js";import{g as E}from"./index.pTwlcQaF.js";import{s as M}from"./index.CCoS0DfY.js";/* empty css                   */import{_ as R}from"./_plugin-vue_export-helper.DlAUqK2U.js";import"./request.CsKOOJzG.js";import"./index.xsH4HHeE.js";/* empty css                 */import"./index.6W4rOsHv.js";import"./question.IyuOoK5G.js";/* empty css                *//* empty css                  */import"./itemDom.vue_vue_type_script_setup_true_lang.CbGURBLf.js";/* empty css                          *//* empty css                       */import"./index.vue_vue_type_style_index_0_lang.Cq6RDaj7.js";/* empty css                        */const W={class:"cards-boxs",id:"targetDivs"},j={class:"main-boxs scrollBar"},F={key:0,class:"containers"},$={class:"topic-boxs scrollBar",id:"family-box"},H=b({__name:"print",setup(J){function L(i){return new URLSearchParams(window.location.search).get(i)}let y=L("uuid"),t={};const d=n({}),v=n({});let a=n({headNum:4,HhyptType:"answerSheet",familysStyle:{}});const h=n([]),S=n(null),p=n(!0);(async()=>{var r,_;const i=await A({uuid:y});if(i.code!=0)return D.error(i.msg);let o=i.data||{};t=o.cardInfo;let{printLayout:u}=t.setting.pageLayout;u.page[0]&&(t.setting.pageLayout.columnWidth.width=u.page[0],t.setting.pageLayout.columnWidth.height=u.page[1]),t.setting.contentTypeId=t.contentTypeId||1,d.value=t.setting,d.scoringResultTypeId===void 0&&(d.scoringResultTypeId="score"),s().setting=t.setting,v.value=t.partScorePlan,a.value.title=t.title,a.value.id=t.id,a.value.created_by_name=t.created_by_name||"-",a.value.createdAt=t.createdAt;let l=((_=(r=d.value.pageLayout)==null?void 0:r.printLayout)==null?void 0:_.column)||2;a.value.headNum=l*2,a.value.column=l,a.value.showCreatorInfoVal=t.showCreatorInfoVal||"",h.value=[],S.value=o.cardInfo.cardList||o.cardInfo.cardLsit,s().questionsScoreList=o.questionsScoreList,s().scoreSetting=o.scoreSetting,s().pageStyles=t.pageStyle;const e=o.scoreSetting.reduce((N,C)=>N+(C.full_score||0),0);a.value.fullMarks=e,E(s(),!0,a.value.column),p.value=!1})(),T(()=>{window.$previewSaveFun=I}),k(()=>{delete window.$previewSaveFun}),n(null);const m=n(null),I=()=>{let i=m.value.getZB();t.cardList=m.value.quesListCopy;let o=JSON.parse(JSON.stringify(s().scoreSetting));return o.forEach(l=>{l.children.forEach(e=>{if(e.logicTypeId==1||e.logicTypeId==4)e.answer=[e.answer];else if(e.logicTypeId==2)e.answer=[e.answer.join("")];else if(e.logicTypeId==3&&e.mergeChecked){if(!e.topicSort){let r=e.frontNum?e.frontNum+"~"+e.afterNum:e.topicSortNum;e.topicSort=r,e.topicSortH1=r,e.topicSortNum=r}e.children&&(e.mergeChildren=e.children,delete e.children)}})}),{uuid:y,coordinateInfo:i,cardInfo:{...t,title:a.value.title,showCreatorInfoVal:a.value.showCreatorInfoVal,setting:s().setting,pageStyle:s().pageStyles,partScorePlan:v.value},scoreSetting:o,questionsScoreList:s().questionsScoreList}};return(i,o)=>{var l,e,r;const u=x;return f(),g("div",W,[q((f(),g("main",j,[c(p)?P("",!0):(f(),g("div",F,[w("div",$,[w("div",{style:B(`margin: 0 auto;width: ${(r=(e=(l=c(d))==null?void 0:l.pageLayout)==null?void 0:e.columnWidth)==null?void 0:r.width}px;`)},[V(M,{ref_key:"singleCardRef",ref:m,isPreview:!0,questionsArr:c(h),layout:c(a),cardList:c(S)},null,8,["questionsArr","layout","cardList"])],4)])]))])),[[u,c(p)]])])}}}),ce=R(H,[["__scopeId","data-v-1122b19e"]]);export{ce as default};

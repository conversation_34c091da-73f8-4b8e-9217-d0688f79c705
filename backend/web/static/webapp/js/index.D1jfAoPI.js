import{d as he,r as f,b as A,w as p,i as Ve,u as i,E as ze,o as v,a as t,f as r,aP as Ue,g as Be,az as Ne,aQ as $e,h as z,j as ke,n as qe,k as Fe,l as ne,m as Re,aK as s,c as h,t as G,K as Se,L as Oe,e as oe,au as se,U as De,v as je,aJ as Pe,C as Ae,R as Je,G as Ke,p as T,J as Ce,F as Ye,q as Qe,T as Ge,s as Ze}from"./index.ZZ6UQeaF.js";/* empty css                    *//* empty css                        *//* empty css                  *//* empty css                     *//* empty css                  *//* empty css                  *//* empty css                 *//* empty css                        *//* empty css                       *//* empty css                  *//* empty css                *//* empty css                     */import{_ as We}from"./_plugin-vue_export-helper.DlAUqK2U.js";import{g as Ie}from"./index.pTwlcQaF.js";import{s as Xe}from"./index.CCoS0DfY.js";import{p as et,_ as tt}from"./previewDom.vue_vue_type_style_index_0_lang.BzEnMuYY.js";import{b as it,a as ot,u as st}from"./index.6W4rOsHv.js";import{a as lt}from"./downloads.B3gkFdce.js";import{g as nt,a as at,p as rt,s as dt,d as ut,i as mt}from"./writing.OS0S12Z4.js";/* empty css                   */import"./question.IyuOoK5G.js";import"./itemDom.vue_vue_type_script_setup_true_lang.CbGURBLf.js";/* empty css                          */import"./index.vue_vue_type_style_index_0_lang.Cq6RDaj7.js";import"./index.xsH4HHeE.js";import"./request.CsKOOJzG.js";const pt={class:"pmm2_flex_between"},gt={class:"addTopic-box scrollBar-C"},ct={style:{width:"100%"}},yt={class:"pmm2_flex_between"},ft={class:"dialog-footer"},_t=he({__name:"addWriting",props:["step_score","chineseWritingTypes","basicInfo"],emits:["addSubmit"],setup(ae,{expose:U,emit:R}){const W=ae,C=f(!1),g=f({}),M=f(null);f({});let m=f(!1);const d=x=>{var N;W.chineseWritingTypes&&W.chineseWritingTypes[0].value,m.value=!1,x.uuid;let l=x.children[0],w={minWordNum:x.minWordNum||l.minWordNum,type:x.type,name:l.name||x.title,uuid:l.uuid,wordNum:l.wordNum};w.bodyFlag=l.bodyFlag||!1,w.body=(l.body||"").replace(/<br\s*\/?>/gi,`
`),g.value=w,C.value=!0,(N=M==null?void 0:M.value)==null||N.resetFields()},L=R,J=()=>{M.value.validate(x=>{if(x){let{type:l,minWordNum:w,wordNum:N,name:B}=g.value;l=="ChineseWriting"&&w>N&&(w=N);let $=Z(g.value.body);s().scoreSetting[0].title=B||"";const V={minWordNum:w,body:$,...g.value};s().scoreSetting[0].children[0]={...s().scoreSetting[0].children[0],...V},B?V.isFalg=!1:V.isFalg=!0,L("addSubmit",V),C.value=!1}else console.log("error submit!")})};function Z(x){return x?x.replace(/\n/g,"<br>"):""}return U({editOpens:d}),(x,l)=>{const w=ne("CloseBold"),N=Fe,B=Re,$=Be,V=Ue,E=Ne,X=$e,K=ke,O=ze;return v(),A(O,{"append-to-body":"",class:"hhypt-dialog-boxs hhypt-dialog-boxs-addTopicDom","show-close":!1,modelValue:i(C),"onUpdate:modelValue":l[5]||(l[5]=c=>Ve(C)?C.value=c:null),title:"作文设置",width:"600","align-center":""},{header:p(({close:c,titleClass:ee})=>[t("div",pt,[t("span",{class:qe(ee)},"作文设置",2),r(B,{onClick:c,underline:!1},{default:p(()=>[r(N,{size:"20"},{default:p(()=>[r(w)]),_:1})]),_:2},1032,["onClick"])])]),footer:p(()=>[t("div",yt,[l[13]||(l[13]=t("div",null,null,-1)),t("div",ft,[r(K,{style:{width:"70px"},onClick:l[4]||(l[4]=c=>C.value=!1)},{default:p(()=>l[11]||(l[11]=[z("取消")])),_:1,__:[11]}),r(K,{style:{width:"70px"},type:"primary",onClick:J},{default:p(()=>l[12]||(l[12]=[z(" 确定 ")])),_:1,__:[12]})])])]),default:p(()=>[t("div",gt,[r(X,{ref_key:"ruleFormRef",ref:M,style:{width:"100%"},model:i(g),"inline-message":""},{default:p(()=>[l[10]||(l[10]=t("br",null,null,-1)),r(V,{prop:"name",class:"item-box"},{default:p(()=>[l[6]||(l[6]=t("span",{class:"bt"},"大题题目：",-1)),r($,{modelValue:i(g).name,"onUpdate:modelValue":l[0]||(l[0]=c=>i(g).name=c),style:{width:"360px"},placeholder:"请输入题目"},null,8,["modelValue"])]),_:1,__:[6]}),t("div",ct,[i(g).type=="ChineseWriting"?(v(),A(V,{key:0,prop:"minWordNum",class:"item-box",rules:[{required:!0,message:"请输入最少字数",trigger:"blur"}]},{default:p(()=>[l[7]||(l[7]=t("span",{class:"bt"},"最少字数：",-1)),r(E,{"controls-position":"right","step-strictly":!0,class:"item-w",modelValue:i(g).minWordNum,"onUpdate:modelValue":l[1]||(l[1]=c=>i(g).minWordNum=c),min:1,max:i(g).wordNum,step:1},null,8,["modelValue","max"])]),_:1,__:[7]})):(v(),A(V,{key:1,prop:"minWordNum",class:"item-box",rules:[{required:!0,message:"请输入词数要求",trigger:"blur"}]},{default:p(()=>[l[8]||(l[8]=t("span",{class:"bt"},"词数要求：",-1)),r(E,{"controls-position":"right","step-strictly":!0,class:"item-w",modelValue:i(g).minWordNum,"onUpdate:modelValue":l[2]||(l[2]=c=>i(g).minWordNum=c),min:1,max:300,step:1},null,8,["modelValue"])]),_:1,__:[8]})),r(V,{prop:"body",class:"item-box item-box22"},{default:p(()=>[l[9]||(l[9]=t("span",{class:"bt"},"题目要求：",-1)),r($,{modelValue:i(g).body,"onUpdate:modelValue":l[3]||(l[3]=c=>i(g).body=c),style:{width:"450px"},autosize:{minRows:2,maxRows:8},type:"textarea",placeholder:"请输入题目要求"},null,8,["modelValue"])]),_:1,__:[9]})])]),_:1,__:[10]},8,["model"])])]),_:1},8,["modelValue"])}}}),vt=We(_t,[["__scopeId","data-v-379c527c"]]),wt=[{title:"作文",itemTypeId:"H1",uuid:"",type:"ChineseWriting"},{type:"writing",itemTypeId:"QUESTION",uuid:"",wordNum:800,compositionSize:20,minWordNum:600,wordKing:1,topicSortNum:1,full_score:50,step_score:.5,logicTypeId:16,bodyFlag:!1,body:""}],bt=[{title:"作文",itemTypeId:"H1",uuid:"",type:"EnglishWriting"},{type:"writing",itemTypeId:"QUESTION",uuid:"",minWordNum:60,topicSortNum:1,full_score:50,step_score:.5,logicTypeId:15,bodyFlag:!1,body:""}],Lt={key:0,class:"pmm2_flex_center",style:{"flex-direction":"column"},id:"targetDivs"},xt={style:{color:"#999"}},St={key:1,class:"cards-box writing-cards-box scrollBar-C",id:"targetDivs"},Ct={class:"header-box"},It={class:"containers pmm2_flex_between"},ht={class:"left-box"},Vt={href:"/"},Nt=["src"],kt={class:"right-box"},Ft={class:"main-box"},Wt={key:0,class:"containers"},Tt={class:"setup-com",style:{"margin-bottom":"20px"}},Mt={key:0},Et={class:"com"},Ht={class:"com"},zt={class:"com"},Ut={key:0,style:{width:"100%"}},Bt={class:"com"},$t={class:"com"},qt={class:"com"},Rt={key:1,style:{width:"100%"}},Ot={class:"com"},Dt={class:"com"},jt={key:2,style:{width:"100%"}},Pt={class:"com"},At={class:"com"},Jt={style:{"align-items":"initial"}},Kt={key:0,class:"com"},Yt={class:"up-item-com pmm2_flex_center"},Qt={key:1,class:"com"},le=5,Gt=he({__name:"index",setup(ae){const U=f(""),R=f(null);let W=it("uuid");const C=f([]),g=f(!0),M=f(-1);let m={};const d=f([]),L=f({HhyptType:"writing",layoutOpen:!0,step_score:.5,familysStyle:{}}),J=f([]),Z=async()=>{var e,a,I,k;if(!W){U.value="非法请求";return}let o={uuid:W};try{const _=await nt(o);if(_.code!=0)return g.value=!1,U.value=_.msg||"",T.error(_.msg);const b=_.data||{};b.cardInfo.courseCode=="YingYu"&&at(o).then(S=>{J.value=S.data||[]}),m=b.cardInfo;let y=m.setting;y.scoringResultTypeId===void 0&&(y.scoringResultTypeId="score"),y.contentTypeId=1,C.value=m.metadata;let Y=((a=(e=y.pageLayout)==null?void 0:e.printLayout)==null?void 0:a.column)||2;if(L.value={...L.value,headNum:Y*2,column:Y,title:m.title,id:m.id,created_by_name:m.created_by_name||"-",createdAt:m.createdAt,showCreatorInfoVal:m.showCreatorInfoVal||"",courseCode:m.courseCode},y.contentFontFamily&&(L.value.familysStyle={"--font-ckeditor-familys":y.contentFontFamily}),y.pageLayoutId||(y.pageLayoutId="A4_1"),s().setting=y,b.scoreSetting)s().questionsScoreList=b.questionsScoreList,s().scoreSetting=b.scoreSetting,s().pageStyles=m.pageStyle;else{let S=JSON.parse(JSON.stringify(m.metadata.cardPageStyles[y.pageLayoutId]));if(S.contentLineHeightMM||(S.contentLineHeightMM=Math.round((S==null?void 0:S.contentLineHeight)/4.75)),!S.contentLineHeightMMH){let F=S.contentLineHeightMM;F<=5?S.contentLineHeightMMH=1.5:S.contentLineHeightMMH=(1.5+(F-5)*.3).toFixed(1)}s().pageStyles=S;let j=((I=m.cardList)==null?void 0:I.length)||0;j==0&&(m.cardList=m.courseCode=="YuWen"?wt:bt,j=m.cardList.length);let H="",Q=[],P=[];for(let F=0;F<j;F++){let u=m.cardList[F];if(u.uuid||(u.uuid=st()),u.itemTypeId=="H1")H=u.uuid,P.push({...u,children:[]});else{u.full_score&&(u.full_score=u.full_score*1),!u.wordNum&&m.courseCode!="YuWen"&&(u.wordNum=10),u.questionUuid="",u.H1uuid=H,u.body=x(u.body),u.topicSort=1,u.topicSortH1=1;let q="";u.bodyFlag&&(q=u.body,u.topicImage&&(q+=l(u.topicImage))),m.cardList[F]={itemTypeId:u.itemTypeId,H1uuid:H,type:u.type,writingList:[{...u,bodys:q}]},P[0].children.push(u),Q.push({uuid:u.uuid,H1uuid:H,full_score:u.full_score})}}s().questionsScoreList=Q,s().scoreSetting=P}(k=m.cardList[0])!=null&&k.title||(m.cardList[0].isFalg=!0),d.value=m.cardList,Ie(s(),!1,L.value.column),g.value=!1}catch(_){console.log(_),g.value=!1}};function x(o){if(o)return o.replace(/\n/g,"<br>")}Z();const l=o=>`<img src="${o}" style="max-width:100%;display: block;margin: 0 auto;max-height:350px;">`,w=(o,e)=>{if(s().scoreSetting[0].children[0][e]=o,e=="full_score")s().questionsScoreList[0].full_score=o;else if(e=="bodyFlag"){let a="";o&&(a=x(d.value[1].writingList[0].body),d.value[1].writingList[0].topicImage&&(a+=l(d.value[1].writingList[0].topicImage))),d.value[1].writingList[0].bodys=a}c()},N=f(null),B=()=>{s().scoreSetting[0]&&N.value.editOpens(s().scoreSetting[0])},$=o=>{let e=d.value;e[0].title=o.name,e[0].isFalg=o.isFalg;let a="";o.bodyFlag&&(a=o.body,e[1].writingList[0].topicImage&&(a+=l(e[1].writingList[0].topicImage))),e[1].writingList[0]={...e[1].writingList[0],...o,bodys:a,type:"writing"},d.value=e,c()};let V=["jpeg","jpg","png"];const E=f(!1),X=async o=>{if(!(o.size/1024/1024<le))return T.error("上传文件大小不能超过 "+le+"MB!"),!1;let a=o.type||"",I=o.name.lastIndexOf(".");if(I!==-1?a=o.name.substring(I+1):a=o.type,V.indexOf(a)==-1)return T.error("请上传正确的图片格式"),!1;E.value=!0;const k=new FormData;k.append("file",o),mt(k).then(_=>{if(_.code!=0)return E.value=!1,T.error(_.msg),!1;let b=_.data||{},y=JSON.parse(JSON.stringify(d.value));y[1].writingList[0].bodyFlag&&(y[1].writingList[0].bodys+=l(b.url)),s().scoreSetting[0].children[0].topicImage=b.url,s().scoreSetting[0].children[0].topicImageContent=b.content,y[1].writingList[0].topicImage=b.url,y[1].writingList[0].topicImageContent=b.content,setTimeout(()=>{d.value=y,E.value=!1,c()},1e3)})},K=()=>{let o=JSON.parse(JSON.stringify(d.value));o[1].writingList[0].bodyFlag&&(o[1].writingList[0].bodys=o[1].writingList[0].body),s().scoreSetting[0].children[0].topicImage="",s().scoreSetting[0].children[0].topicImageContent="",o[1].writingList[0].topicImage="",o[1].writingList[0].topicImageContent="",d.value=o,c()},O=f(null),c=o=>{s().ckeditorUuid="",M.value--},ee=(o={})=>{var a,I;let e=((I=(a=s().setting.pageLayout)==null?void 0:a.printLayout)==null?void 0:I.column)||2;o.ispageLayoutId&&Ie(s(),!1,L.value.column),L.value.headNum=e*2,c()},Te=o=>{s().pageStyles.contentLineHeight=Math.round(o*4.75),o<=5?s().pageStyles.contentLineHeightMMH=1.5:s().pageStyles.contentLineHeightMMH=(1.5+(o-5)*.3).toFixed(1),c()},te=f(null),D=f(!1),Me=async()=>{D.value=!0,te.value.dialogVisible=!0;let o=ie(),e=await rt({uuid:W,data:o});if(e.code==0)te.value.show(e.data),D.value=!1;else return T.error(e.msg)},Ee=async()=>{const o=Ce.service({lock:!0,text:"保存中..",background:"rgba(0, 0, 0, 0.7)"});let e=ie(),a={};if(a=await dt({uuid:W,data:e}),o.close(),a.code==0)T({message:"保存成功",type:"success",plain:!0});else return T.error("保存失败")},He=async()=>{var I;const o=Ce.service({lock:!0,text:"下载中..",background:"rgba(0, 0, 0, 0.7)"});let e=ie(),a=await ut({uuid:W,data:e});try{if(a.code&&a.code!==0)return o.close(),T.error(a.msg);let k=L.value.title+"-"+m.courseName;(I=a==null?void 0:a.data)!=null&&I.url&&lt(a.data.url,k+".pdf")}catch{}setTimeout(()=>{if(a.msg)return T.error(a.msg);o.close()},1e3)},ie=()=>{var a;const o=O.value.getZB();return m.cardList=O.value.quesListCopy,{uuid:W,coordinateInfo:o,cardInfo:{...m,full_score:(a=s().questionsScoreList[0])==null?void 0:a.full_score,title:L.value.title,showCreatorInfoVal:L.value.showCreatorInfoVal,setting:s().setting,pageStyle:s().pageStyles},scoreSetting:s().scoreSetting,questionsScoreList:s().questionsScoreList}};return(o,e)=>{var u,q,re,de,ue,me,pe,ge,ce,ye,fe,_e,ve,we,be,Le,xe;const a=ke,I=Ge,k=De,_=Ne,b=Ze,y=je,Y=Pe,S=Ae,j=ne("CloseBold"),H=Fe,Q=ne("upload-filled"),P=Ke,F=Oe;return i(U)?(v(),h("div",Lt,[e[22]||(e[22]=t("img",{style:{width:"200px"},src:"https://oss.hhypt.com/resources/mini-parent-helper/err.png"},null,-1)),t("span",xt,G(i(U)),1)])):(v(),h("div",St,[t("header",Ct,[t("div",It,[t("div",ht,[t("a",Vt,[t("img",{src:i(ot)(),alt:"Logo",width:"218",height:"38"},null,8,Nt)])]),e[26]||(e[26]=t("div",{class:"title",style:{flex:"1","text-align":"center"}},"制作答题卡",-1)),t("div",kt,[r(a,{onClick:e[0]||(e[0]=n=>Me())},{default:p(()=>e[23]||(e[23]=[z("预览")])),_:1,__:[23]}),r(a,{onClick:e[1]||(e[1]=n=>Ee()),type:"primary"},{default:p(()=>e[24]||(e[24]=[z("保存")])),_:1,__:[24]}),r(a,{onClick:e[2]||(e[2]=n=>He()),type:"primary"},{default:p(()=>e[25]||(e[25]=[z("下载题卡")])),_:1,__:[25]})])])]),Se((v(),h("main",Ft,[!i(g)&&i(s)().setting.pageLayout?(v(),h("div",Wt,[(v(),h("div",{class:"topic-box scrollBar-C",style:se(`min-width: ${(re=(q=(u=i(s)().setting)==null?void 0:u.pageLayout)==null?void 0:q.columnWidth)==null?void 0:re.width}px;`),key:i(M),id:"family-box"},[t("div",{ref:"topicBoxs",style:se(`transform-origin: top left;transition: transform 0.2s; transform: translate(0, 0);margin: 0 auto;
						 width: ${(me=(ue=(de=i(s)().setting)==null?void 0:de.pageLayout)==null?void 0:ue.columnWidth)==null?void 0:me.width}px;
						 min-width: ${(ce=(ge=(pe=i(s)().setting)==null?void 0:pe.pageLayout)==null?void 0:ge.columnWidth)==null?void 0:ce.width}px;
						 max-width: ${(_e=(fe=(ye=i(s)().setting)==null?void 0:ye.pageLayout)==null?void 0:fe.columnWidth)==null?void 0:_e.width}px;
						`)},[r(Xe,{cardList:i(d),ref_key:"singleCardRef",ref:O,layout:i(L)},null,8,["cardList","layout"])],4)],4)),t("div",{class:"setup-box scrollBar",style:se(`width: calc(100% - ${((be=(we=(ve=i(s)().setting)==null?void 0:ve.pageLayout)==null?void 0:we.columnWidth)==null?void 0:be.width)+4}px)`)},[t("div",Tt,[e[41]||(e[41]=t("div",{class:"title-box pmm2_flex_between"},[t("div",{class:"title"},[t("span",{class:"bt"},"版面设计")])],-1)),(Le=i(d)[1])!=null&&Le.writingList[0]?(v(),h("ul",Mt,[t("li",null,[e[27]||(e[27]=t("div",{class:"name"},"考号识别",-1)),t("div",Et,[r(k,{modelValue:i(s)().setting.examNoTypeId,"onUpdate:modelValue":e[3]||(e[3]=n=>i(s)().setting.examNoTypeId=n),onChange:c},{default:p(()=>[(v(!0),h(Ye,null,Qe(i(C).examNoTypes,n=>(v(),A(I,{value:n.value,size:"large"},{default:p(()=>[t("div",null,G(n.label),1)]),_:2},1032,["value"]))),256))]),_:1},8,["modelValue"])])]),t("li",null,[e[28]||(e[28]=t("div",{class:"name"},"页面布局",-1)),t("div",Ht,[t("span",null,G((xe=i(s)().setting.pageLayout)==null?void 0:xe.label),1),t("span",{class:"huiA",style:{"padding-left":"5px"},onClick:e[4]||(e[4]=(...n)=>i(R).opens&&i(R).opens(...n))},"修改")])]),t("li",null,[e[29]||(e[29]=t("div",{class:"name"},"作文设置",-1)),t("div",{class:"com"},[t("span",{class:"huiA",style:{"padding-left":"5px"},onClick:B},"修改")])]),t("li",null,[e[30]||(e[30]=t("div",{class:"name"},"分值",-1)),t("div",zt,[r(_,{onChange:e[5]||(e[5]=n=>w(n,"full_score")),modelValue:i(d)[1].writingList[0].full_score,"onUpdate:modelValue":e[6]||(e[6]=n=>i(d)[1].writingList[0].full_score=n),min:0,style:{width:"100px"},size:"small"},null,8,["modelValue"])])]),i(L).courseCode=="YuWen"?(v(),h("div",Ut,[t("li",null,[e[31]||(e[31]=t("div",{class:"name"},"方格数量",-1)),t("div",Bt,[r(_,{onChange:e[7]||(e[7]=n=>w(n,"wordNum")),modelValue:i(d)[1].writingList[0].wordNum,"onUpdate:modelValue":e[8]||(e[8]=n=>i(d)[1].writingList[0].wordNum=n),min:i(d)[1].writingList[0].minWordNum,style:{width:"100px"},size:"small"},null,8,["modelValue","min"])])]),t("li",null,[e[32]||(e[32]=t("div",{class:"name"},"每行字数",-1)),t("div",$t,[r(_,{onChange:e[9]||(e[9]=n=>w(n,"compositionSize")),modelValue:i(d)[1].writingList[0].compositionSize,"onUpdate:modelValue":e[10]||(e[10]=n=>i(d)[1].writingList[0].compositionSize=n),min:10,max:50,style:{width:"100px"},size:"small"},null,8,["modelValue"])])]),t("li",null,[e[33]||(e[33]=t("div",{class:"name"},"字数标记",-1)),t("div",qt,[r(y,{style:{width:"120px"},size:"small",onChange:e[11]||(e[11]=n=>w(n,"wordKing")),modelValue:i(d)[1].writingList[0].wordKing,"onUpdate:modelValue":e[12]||(e[12]=n=>i(d)[1].writingList[0].wordKing=n),class:"item-w"},{default:p(()=>[r(b,{label:"每隔100字显示",value:1}),r(b,{label:"每隔200字显示",value:3}),r(b,{label:"仅最少字数处显示",value:2})]),_:1},8,["modelValue"])])])])):(v(),h("div",Rt,[t("li",null,[e[34]||(e[34]=t("div",{class:"name"},"行数",-1)),t("div",Ot,[r(_,{onChange:e[13]||(e[13]=n=>w(n,"wordNum")),modelValue:i(d)[1].writingList[0].wordNum,"onUpdate:modelValue":e[14]||(e[14]=n=>i(d)[1].writingList[0].wordNum=n),min:1,max:25,style:{width:"100px"},size:"small"},null,8,["modelValue"])])])])),t("li",null,[e[35]||(e[35]=t("div",{class:"name"},"题目要求",-1)),t("div",Dt,[r(Y,{modelValue:i(d)[1].writingList[0].bodyFlag,"onUpdate:modelValue":e[15]||(e[15]=n=>i(d)[1].writingList[0].bodyFlag=n),onChange:e[16]||(e[16]=n=>w(n,"bodyFlag")),"active-text":"显示","inactive-text":"不显示"},null,8,["modelValue"])])]),i(d)[1].writingList[0].bodyFlag?(v(),h("div",jt,[t("li",null,[e[36]||(e[36]=t("div",{class:"name"},"内容字号",-1)),t("div",Pt,[r(_,{modelValue:i(s)().pageStyles.contentFontSize,"onUpdate:modelValue":e[17]||(e[17]=n=>i(s)().pageStyles.contentFontSize=n),min:14,max:28,style:{width:"80px"},size:"small","controls-position":"right",onChange:e[18]||(e[18]=n=>c())},null,8,["modelValue"])])]),t("li",null,[e[38]||(e[38]=t("div",{class:"name"},"行距",-1)),t("div",At,[r(_,{onChange:Te,modelValue:i(s)().pageStyles.contentLineHeightMM,"onUpdate:modelValue":e[19]||(e[19]=n=>i(s)().pageStyles.contentLineHeightMM=n),min:4,max:20,"controls-position":"right",style:{width:"80px"},size:"small"},null,8,["modelValue"]),e[37]||(e[37]=z(" mm "))])]),t("li",Jt,[e[40]||(e[40]=t("div",{class:"name"},"上传图片",-1)),i(d)[1].writingList[0].topicImage?(v(),h("div",Kt,[t("div",Yt,[r(S,{"preview-src-list":[i(d)[1].writingList[0].topicImage],"initial-index":0,class:"img",src:i(d)[1].writingList[0].topicImage},null,8,["preview-src-list","src"]),t("div",{class:"del huiA",onClick:e[20]||(e[20]=Je(n=>K(),["stop"]))},[r(H,null,{default:p(()=>[r(j,{style:{color:"#fff"}})]),_:1})])])])):(v(),h("div",Qt,[Se((v(),A(P,{class:"upload-demo",action:"#",multiple:"","http-request":()=>{},"before-upload":X,accept:".jpg, .jpeg, .png","show-file-list":!1,drag:"","element-loading-text":"上传中..."},{tip:p(()=>[t("div",{class:"el-upload__tip"}," 只能上传jpg/png文件，且不超过"+G(le)+"MB ")]),default:p(()=>[r(H,{class:"el-icon--upload"},{default:p(()=>[r(Q)]),_:1}),e[39]||(e[39]=t("div",{class:"el-upload__text"},[z(" 将文件拖放到此处或"),t("em",null,"点击上传")],-1))]),_:1,__:[39]})),[[F,i(E)]])]))])])):oe("",!0)])):oe("",!0)])],4)])):oe("",!0)])),[[F,i(g)]]),r(vt,{ref_key:"addWritingRef",ref:N,chineseWritingTypes:i(C).chineseWritingTypes,onAddSubmit:$,step_score:i(L).step_score,basicInfo:i(J)},null,8,["chineseWritingTypes","step_score","basicInfo"]),r(et,{paperCardPageLayouts:i(C).answerSheetPageLayouts,paperCardPageLayoutsNo:!0,ref_key:"papeSizeRef",ref:R,onSetCardSetting:ee,cardPageStyles:i(C).cardPageStyles,cardContentTypes:i(C).cardContentTypes,dialogWidth:550},null,8,["paperCardPageLayouts","cardPageStyles","cardContentTypes"]),r(tt,{ref_key:"previewDomRef",ref:te,modelValue:i(D),"onUpdate:modelValue":e[21]||(e[21]=n=>Ve(D)?D.value=n:null)},null,8,["modelValue"])]))}}}),Ii=We(Gt,[["__scopeId","data-v-6aaeaa51"]]);export{Ii as default};

# Augment Rules for PHP8.3+ Yii2 + Vue3 + Element Plus Project

## 技术栈概述
- **后端**: PHP 8.3+, Yii2 Framework
- **数据库**: MySQL 8.0+
- **前端**: Vue 3 + Element Plus (非打包模式)
- **架构**: 前后端分离

## 代码规范

### PHP/Yii2 规范
1. **命名约定**
   - 类名使用 PascalCase: `UserController`, `OrderModel`
   - 方法名使用 camelCase: `getUserInfo()`, `createOrder()`
   - 变量名使用 camelCase: `$userName`, `$orderList`
   - 常量使用 UPPER_SNAKE_CASE: `MAX_RETRY_COUNT`

2. **文件结构**
   - Controllers 放在 `controllers/` 目录
   - Models 放在 `models/` 目录
   - Views 放在 `views/` 目录
   - Components 放在 `components/` 目录

3. **数据库操作**
   - 优先使用 Active Record 模式
   - 复杂查询使用 Query Builder
   - 避免直接写 SQL，除非必要
   ```php
   // 推荐
   $users = User::find()->where(['status' => 1])->all();
   
   // 复杂查询
   $query = (new \yii\db\Query())
       ->select(['u.id', 'u.name', 'p.title'])
       ->from('user u')
       ->leftJoin('profile p', 'u.id = p.user_id')
       ->where(['u.status' => 1]);
   ```

4. **错误处理**
   - 使用 try-catch 处理异常
   - 返回统一的 JSON 格式
   ```php
   try {
       // 业务逻辑
       return $this->asJson(['code' => 200, 'data' => $result]);
   } catch (\Exception $e) {
       return $this->asJson(['code' => 500, 'message' => $e->getMessage()]);
   }
   ```

### Vue3 + Element Plus 规范
1. **组件命名**
   - 组件文件使用 PascalCase: `UserList.vue`, `OrderForm.vue`
   - 组件内部使用 kebab-case: `<user-list></user-list>`

2. **组合式API优先**
   ```javascript
   <script setup>
   import { ref, reactive, onMounted } from 'vue'
   import { ElMessage } from 'element-plus'
   
   const userList = ref([])
   const loading = ref(false)
   
   const fetchUsers = async () => {
     loading.value = true
     try {
       const response = await fetch('/api/users')
       const data = await response.json()
       userList.value = data.data
     } catch (error) {
       ElMessage.error('获取用户列表失败')
     } finally {
       loading.value = false
     }
   }
   
   onMounted(() => {
     fetchUsers()
   })
   </script>
   ```

3. **Element Plus 使用规范**
   - 按需引入组件
   - 统一使用 ElMessage 进行消息提示
   - 表单验证使用 Element Plus 的验证规则

## API 设计规范

### RESTful API 约定
- GET `/api/users` - 获取用户列表
- GET `/api/users/{id}` - 获取单个用户
- POST `/api/users` - 创建用户
- PUT `/api/users/{id}` - 更新用户
- DELETE `/api/users/{id}` - 删除用户

### 响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": 1640995200
}
```

### 错误码约定
- 200: 成功
- 400: 请求参数错误
- 401: 未授权
- 403: 禁止访问
- 404: 资源不存在
- 500: 服务器内部错误

## 数据库设计规范

### 表命名
- 使用小写字母和下划线: `user_profile`, `order_item`
- 表名使用复数形式: `users`, `orders`

### 字段命名
- 主键统一使用 `id`
- 外键使用 `表名_id`: `user_id`, `order_id`
- 时间字段: `created_at`, `updated_at`
- 软删除字段: `deleted_at`

### 索引规范
- 主键自动创建索引
- 外键字段创建索引
- 经常查询的字段创建索引
- 联合索引遵循最左前缀原则

## 安全规范

### 输入验证
```php
// Yii2 模型验证
public function rules()
{
    return [
        [['username', 'email'], 'required'],
        ['email', 'email'],
        ['username', 'string', 'min' => 3, 'max' => 20],
        ['password', 'string', 'min' => 6],
    ];
}
```

### SQL注入防护
- 使用参数化查询
- 避免字符串拼接SQL

### XSS防护
- 输出时进行HTML转义
- 使用 Yii2 的 `Html::encode()`

## 性能优化

### 数据库优化
- 合理使用索引
- 避免 N+1 查询问题
- 使用 `with()` 进行关联查询预加载

### 前端优化
- 组件懒加载
- 图片懒加载
- 合理使用 v-show 和 v-if

## 测试规范

### 单元测试
- 使用 PHPUnit 进行后端测试
- 测试覆盖率不低于 80%

### 前端测试
- 使用 Vitest 进行 Vue 组件测试
- 关键业务逻辑必须有测试覆盖

## 部署规范

### 环境配置
- 开发环境: `dev`
- 测试环境: `test`
- 生产环境: `prod`

### 配置文件
- 敏感信息使用环境变量
- 不同环境使用不同配置文件

## 日志规范

### 日志级别
- ERROR: 错误信息
- WARNING: 警告信息
- INFO: 一般信息
- DEBUG: 调试信息

### 日志格式
```php
Yii::error('用户登录失败: ' . $username, 'user.login');
Yii::info('订单创建成功: ' . $orderId, 'order.create');
```

## Git 提交规范

### 提交信息格式
```
<type>(<scope>): <subject>

<body>

<footer>
```

### 类型说明
- feat: 新功能
- fix: 修复bug
- docs: 文档更新
- style: 代码格式调整
- refactor: 重构
- test: 测试相关
- chore: 构建过程或辅助工具的变动

## 注意事项

1. **PHP 8.3+ 特性使用**
   - 优先使用类型声明
   - 使用 match 表达式替代复杂的 switch
   - 合理使用 Attributes

2. **Yii2 最佳实践**
   - 遵循 MVC 模式
   - 合理使用组件和行为
   - 利用 Yii2 的缓存机制

3. **Vue3 最佳实践**
   - 优先使用 Composition API
   - 合理拆分组件
   - 使用 Pinia 进行状态管理（如需要）

4. **Element Plus 使用**
   - 保持 UI 风格一致
   - 合理使用主题定制
   - 注意组件的响应式设计
